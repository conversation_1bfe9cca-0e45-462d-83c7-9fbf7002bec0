# .emaillist 檔案格式範例
# 此檔案定義郵件白名單，用於過濾允許處理的郵件寄件者
# 
# 支援的格式：
# 1. 完整郵件地址: <EMAIL>
# 2. 網域匹配: @example.com (匹配該網域下的所有郵件)
# 3. 註解: 以 # 開頭的行或行末註解
#
# 範例:

# ===== 管理員和系統帳號 =====
<EMAIL>              # 系統管理員
<EMAIL>            # 技術支援
<EMAIL>     # 系統通知

# ===== 信任的供應商網域 =====
@jcet.com                      # JCET 半導體封測
@asen.com                      # ASE 日月光
@spil.com                      # SPIL 矽品
@powertech.com.tw              # 力成科技
@kyec.com.tw                   # 京元電子

# ===== 測試用郵件 =====
<EMAIL>               # 測試帳號
<EMAIL>                 # 品質保證
<EMAIL>                # 開發團隊

# ===== 合作夥伴 =====
@partner-vendor.com            # 合作夥伴
@trusted-supplier.net          # 信任的供應商

# ===== 個人郵件 (謹慎使用) =====
# <EMAIL>           # 個人郵件 (已註解，不啟用)
# @gmail.com                   # Gmail 網域 (已註解，風險較高)

# 注意事項：
# - 模式匹配不區分大小寫
# - 重複的條目會被警告但不會阻止載入
# - 空行會被忽略
# - 無效的格式會被記錄為錯誤
# - 建議定期檢查和更新白名單
# - 避免使用過於寬泛的網域匹配 (如 @gmail.com)

# 使用方法：
# 1. 複製此檔案為 .emaillist (去掉 .example 後綴)
# 2. 根據實際需求修改白名單條目
# 3. 重新啟動郵件處理服務或手動重新載入
# 4. 檢查日誌確認白名單載入成功

# 環境變數配置：
# EMAIL_WHITELIST_ENABLED=true/false           # 啟用/停用白名單功能
# EMAIL_WHITELIST_DEFAULT_ACTION=allow/deny    # 不在白名單時的預設行為
# EMAIL_WHITELIST_AUTO_RELOAD=true/false       # 自動重新載入白名單檔案
# EMAIL_CONFIG_DIR=./config                    # 配置檔案目錄