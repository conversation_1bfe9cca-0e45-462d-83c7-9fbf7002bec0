#!/usr/bin/env python3
"""
Import 優化器
檢查並優化導入語句，建議使用現有模組而非重寫功能
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict
import logging

class ImportOptimizer:
    """Import 優化器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('ImportOptimizer')
        
        # 優化配置
        self.auto_apply_safe_changes = config.get('auto_apply_safe_changes', False)
        self.preserve_existing_imports = config.get('preserve_existing_imports', True)
        self.suggest_stdlib_replacements = config.get('suggest_stdlib_replacements', True)
        
    async def optimize_imports(self, import_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """優化導入語句"""
        self.logger.info("🔧 開始 Import 優化...")
        
        optimizations = []
        errors = []
        
        try:
            # 分析現有模組功能
            available_modules = await self._analyze_available_modules()
            
            # 檢查未使用的導入
            unused_imports = await self._find_unused_imports(import_analysis)
            optimizations.extend(unused_imports)
            
            # 檢查重複導入
            duplicate_imports = await self._find_duplicate_imports(import_analysis)
            optimizations.extend(duplicate_imports)
            
            # 檢查可替換的實現
            replacement_opportunities = await self._find_replacement_opportunities(
                import_analysis, available_modules
            )
            optimizations.extend(replacement_opportunities)
            
            # 檢查循環依賴
            circular_dependencies = await self._detect_circular_dependencies(import_analysis)
            optimizations.extend(circular_dependencies)
            
            # 建議標準庫替換
            if self.suggest_stdlib_replacements:
                stdlib_suggestions = await self._suggest_stdlib_replacements(import_analysis)
                optimizations.extend(stdlib_suggestions)
            
            # 自動應用安全的變更
            applied_changes = []
            if self.auto_apply_safe_changes:
                applied_changes = await self._apply_safe_optimizations(optimizations)
            
            result = {
                'status': 'success',
                'total_files_analyzed': len(import_analysis.get('file_imports', {})),
                'optimizations_found': len(optimizations),
                'safe_changes_applied': len(applied_changes),
                'optimizations': optimizations,
                'applied_changes': applied_changes,
                'available_modules': available_modules,
                'errors': errors
            }
            
            self.logger.info(f"✅ Import 優化完成: 發現 {len(optimizations)} 個優化機會")
            return result
            
        except Exception as e:
            self.logger.error(f"Import 優化失敗: {e}")
            return {
                'status': 'error',
                'message': f'優化失敗: {str(e)}',
                'errors': errors + [str(e)]
            }
    
    async def _analyze_available_modules(self) -> Dict[str, Any]:
        """分析專案中可用的模組"""
        modules = {}
        
        for py_file in self.repo_root.rglob('*.py'):
            if any(ignore_dir in py_file.parts for ignore_dir in ['.git', '__pycache__', '.claude']):
                continue
            
            try:
                relative_path = py_file.relative_to(self.repo_root)
                module_name = str(relative_path.with_suffix(''))
                
                with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                # 分析模組提供的功能
                module_info = {
                    'file_path': str(relative_path),
                    'classes': [],
                    'functions': [],
                    'constants': [],
                    'imports': [],
                    'docstring': ast.get_docstring(tree),
                    'lines_of_code': len(content.split('\n'))
                }
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef):
                        module_info['classes'].append({
                            'name': node.name,
                            'methods': [n.name for n in node.body if isinstance(n, ast.FunctionDef)],
                            'docstring': ast.get_docstring(node),
                            'line': node.lineno
                        })
                    
                    elif isinstance(node, ast.FunctionDef):
                        # 只收集頂層函數
                        if isinstance(node, ast.FunctionDef) and node.col_offset == 0:
                            module_info['functions'].append({
                                'name': node.name,
                                'args': [arg.arg for arg in node.args.args],
                                'is_async': isinstance(node, ast.AsyncFunctionDef),
                                'docstring': ast.get_docstring(node),
                                'line': node.lineno
                            })
                    
                    elif isinstance(node, ast.Assign):
                        # 收集常數
                        for target in node.targets:
                            if isinstance(target, ast.Name) and target.id.isupper():
                                module_info['constants'].append({
                                    'name': target.id,
                                    'line': node.lineno
                                })
                    
                    elif isinstance(node, (ast.Import, ast.ImportFrom)):
                        module_info['imports'].append(self._extract_import_info(node))
                
                modules[module_name] = module_info
                
            except Exception as e:
                self.logger.warning(f"分析模組 {py_file} 失敗: {e}")
        
        return modules
    
    async def _find_unused_imports(self, import_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """找出未使用的導入"""
        optimizations = []
        
        for file_path, file_data in import_analysis.get('file_imports', {}).items():
            try:
                full_path = self.repo_root / file_path
                with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                # 收集所有使用的名稱
                used_names = set()
                for node in ast.walk(tree):
                    if isinstance(node, ast.Name):
                        used_names.add(node.id)
                    elif isinstance(node, ast.Attribute):
                        if isinstance(node.value, ast.Name):
                            used_names.add(node.value.id)
                
                # 檢查每個導入
                for import_info in file_data.get('imports', []):
                    unused_items = []
                    
                    if import_info['type'] == 'import':
                        for module in import_info['modules']:
                            module_name = import_info.get('aliases', {}).get(module, module)
                            if module_name not in used_names:
                                unused_items.append(module)
                    
                    elif import_info['type'] == 'from_import':
                        if import_info['names'] != ['*']:  # 不檢查 import *
                            for name in import_info['names']:
                                actual_name = import_info.get('aliases', {}).get(name, name)
                                if actual_name not in used_names:
                                    unused_items.append(name)
                    
                    if unused_items:
                        optimizations.append({
                            'type': 'unused_imports',
                            'severity': 'low',
                            'file_path': file_path,
                            'line': import_info['line'],
                            'unused_items': unused_items,
                            'import_statement': self._reconstruct_import_statement(import_info),
                            'suggestion': '移除未使用的導入以清理代碼',
                            'auto_fixable': True
                        })
                
            except Exception as e:
                self.logger.warning(f"檢查未使用導入失敗 {file_path}: {e}")
        
        return optimizations
    
    async def _find_duplicate_imports(self, import_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """找出重複的導入"""
        optimizations = []
        
        for file_path, file_data in import_analysis.get('file_imports', {}).items():
            imports_by_module = defaultdict(list)
            
            # 按模組分組導入
            for import_info in file_data.get('imports', []):
                if import_info['type'] == 'import':
                    for module in import_info['modules']:
                        imports_by_module[module].append(import_info)
                elif import_info['type'] == 'from_import':
                    module_key = f"from_{import_info['module']}"
                    imports_by_module[module_key].append(import_info)
            
            # 檢查重複
            for module, import_list in imports_by_module.items():
                if len(import_list) > 1:
                    optimizations.append({
                        'type': 'duplicate_imports',
                        'severity': 'medium',
                        'file_path': file_path,
                        'module': module,
                        'duplicate_lines': [imp['line'] for imp in import_list],
                        'suggestions': [
                            '合併重複的導入語句',
                            '將相關導入組織在一起'
                        ],
                        'auto_fixable': True
                    })
        
        return optimizations
    
    async def _find_replacement_opportunities(self, import_analysis: Dict[str, Any], 
                                           available_modules: Dict[str, Any]) -> List[Dict[str, Any]]:
        """找出可替換的實現機會"""
        optimizations = []
        
        # 建立功能索引
        function_index = self._build_function_index(available_modules)
        class_index = self._build_class_index(available_modules)
        
        for file_path, file_data in import_analysis.get('file_imports', {}).items():
            try:
                # 分析檔案中定義的函數和類別
                full_path = self.repo_root / file_path
                with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef) and node.col_offset == 0:
                        # 檢查是否有類似的現有函數
                        similar_functions = self._find_similar_functions(
                            node, function_index, file_path
                        )
                        
                        if similar_functions:
                            optimizations.append({
                                'type': 'function_replacement_opportunity',
                                'severity': 'medium',
                                'file_path': file_path,
                                'function_name': node.name,
                                'line': node.lineno,
                                'similar_functions': similar_functions,
                                'suggestion': f'考慮使用現有函數而非重新實現',
                                'auto_fixable': False
                            })
                    
                    elif isinstance(node, ast.ClassDef) and node.col_offset == 0:
                        # 檢查是否有類似的現有類別
                        similar_classes = self._find_similar_classes(
                            node, class_index, file_path
                        )
                        
                        if similar_classes:
                            optimizations.append({
                                'type': 'class_replacement_opportunity',
                                'severity': 'medium',
                                'file_path': file_path,
                                'class_name': node.name,
                                'line': node.lineno,
                                'similar_classes': similar_classes,
                                'suggestion': f'考慮繼承或使用現有類別',
                                'auto_fixable': False
                            })
                
            except Exception as e:
                self.logger.warning(f"檢查替換機會失敗 {file_path}: {e}")
        
        return optimizations
    
    async def _detect_circular_dependencies(self, import_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """檢測循環依賴"""
        optimizations = []
        
        # 建立依賴圖
        dependency_graph = {}
        
        for file_path, file_data in import_analysis.get('file_imports', {}).items():
            dependencies = set()
            
            for import_info in file_data.get('imports', []):
                if import_info['type'] == 'from_import' and import_info.get('level', 0) > 0:
                    # 相對導入
                    module_path = self._resolve_relative_import(file_path, import_info)
                    if module_path:
                        dependencies.add(module_path)
                elif import_info['type'] in ['import', 'from_import']:
                    # 絕對導入（專案內）
                    module = import_info.get('module') or import_info.get('modules', [None])[0]
                    if module:
                        module_path = self._resolve_module_path(module)
                        if module_path:
                            dependencies.add(module_path)
            
            dependency_graph[file_path] = dependencies
        
        # 檢測循環
        visited = set()
        rec_stack = set()
        
        def has_cycle(node, path):
            if node in rec_stack:
                # 發現循環
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                return cycle
            
            if node in visited:
                return None
            
            visited.add(node)
            rec_stack.add(node)
            
            for neighbor in dependency_graph.get(node, []):
                if neighbor in dependency_graph:
                    cycle = has_cycle(neighbor, path + [node])
                    if cycle:
                        return cycle
            
            rec_stack.remove(node)
            return None
        
        # 檢查每個節點
        for node in dependency_graph:
            if node not in visited:
                cycle = has_cycle(node, [])
                if cycle:
                    optimizations.append({
                        'type': 'circular_dependency',
                        'severity': 'high',
                        'cycle': cycle,
                        'suggestion': '重構代碼以消除循環依賴',
                        'recommendations': [
                            '將共用代碼提取到獨立模組',
                            '使用依賴注入模式',
                            '延遲導入 (lazy import)'
                        ],
                        'auto_fixable': False
                    })
        
        return optimizations
    
    async def _suggest_stdlib_replacements(self, import_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """建議標準庫替換"""
        optimizations = []
        
        # 常見的標準庫替換建議
        stdlib_alternatives = {
            'requests': {
                'stdlib': 'urllib.request, urllib.parse',
                'note': '對於簡單請求可考慮使用標準庫',
                'complexity': 'medium'
            },
            'json': {
                'custom_json_parsers': 'json',
                'note': '使用標準庫 json 模組',
                'complexity': 'low'
            },
            're': {
                'custom_regex': 're',
                'note': '使用標準庫正則表達式',
                'complexity': 'low'
            },
            'datetime': {
                'custom_date_utils': 'datetime',
                'note': '使用標準庫日期時間處理',
                'complexity': 'low'
            },
            'pathlib': {
                'os.path': 'pathlib',
                'note': '現代路徑處理，推薦使用 pathlib',
                'complexity': 'low'
            }
        }
        
        for file_path, file_data in import_analysis.get('file_imports', {}).items():
            # 檢查是否可以使用標準庫替換
            for import_info in file_data.get('imports', []):
                module_name = None
                
                if import_info['type'] == 'import':
                    module_name = import_info['modules'][0] if import_info['modules'] else None
                elif import_info['type'] == 'from_import':
                    module_name = import_info['module']
                
                if module_name and module_name in stdlib_alternatives:
                    alt_info = stdlib_alternatives[module_name]
                    optimizations.append({
                        'type': 'stdlib_replacement',
                        'severity': 'low',
                        'file_path': file_path,
                        'line': import_info['line'],
                        'current_import': module_name,
                        'suggested_stdlib': alt_info['stdlib'],
                        'note': alt_info['note'],
                        'complexity': alt_info['complexity'],
                        'auto_fixable': alt_info['complexity'] == 'low'
                    })
        
        return optimizations
    
    async def _apply_safe_optimizations(self, optimizations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """自動應用安全的優化"""
        applied_changes = []
        
        for opt in optimizations:
            if not opt.get('auto_fixable', False):
                continue
            
            try:
                if opt['type'] == 'unused_imports':
                    success = await self._remove_unused_imports(opt)
                    if success:
                        applied_changes.append({
                            'type': 'removed_unused_imports',
                            'file_path': opt['file_path'],
                            'line': opt['line'],
                            'removed_items': opt['unused_items']
                        })
                
                elif opt['type'] == 'duplicate_imports':
                    success = await self._merge_duplicate_imports(opt)
                    if success:
                        applied_changes.append({
                            'type': 'merged_duplicate_imports',
                            'file_path': opt['file_path'],
                            'module': opt['module'],
                            'merged_lines': opt['duplicate_lines']
                        })
                
            except Exception as e:
                self.logger.warning(f"自動優化失敗 {opt.get('type', 'unknown')}: {e}")
        
        return applied_changes
    
    async def _remove_unused_imports(self, optimization: Dict[str, Any]) -> bool:
        """移除未使用的導入"""
        try:
            file_path = self.repo_root / optimization['file_path']
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            line_num = optimization['line'] - 1
            if line_num >= len(lines):
                return False
            
            original_line = lines[line_num]
            unused_items = optimization['unused_items']
            
            # 重構導入語句
            new_line = self._remove_items_from_import_line(original_line, unused_items)
            
            if new_line != original_line:
                if new_line.strip():
                    lines[line_num] = new_line
                else:
                    # 如果整行都被移除，刪除該行
                    lines.pop(line_num)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(lines)
                
                return True
            
        except Exception as e:
            self.logger.error(f"移除未使用導入失敗: {e}")
        
        return False
    
    async def _merge_duplicate_imports(self, optimization: Dict[str, Any]) -> bool:
        """合併重複的導入"""
        # 簡化實現：記錄但不自動合併，因為需要更複雜的邏輯
        return False
    
    def _build_function_index(self, available_modules: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """建立函數索引"""
        index = defaultdict(list)
        
        for module_name, module_info in available_modules.items():
            for func in module_info['functions']:
                index[func['name']].append({
                    'module': module_name,
                    'file_path': module_info['file_path'],
                    'args': func['args'],
                    'docstring': func.get('docstring', ''),
                    'line': func['line']
                })
        
        return dict(index)
    
    def _build_class_index(self, available_modules: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """建立類別索引"""
        index = defaultdict(list)
        
        for module_name, module_info in available_modules.items():
            for cls in module_info['classes']:
                index[cls['name']].append({
                    'module': module_name,
                    'file_path': module_info['file_path'],
                    'methods': cls['methods'],
                    'docstring': cls.get('docstring', ''),
                    'line': cls['line']
                })
        
        return dict(index)
    
    def _find_similar_functions(self, func_node: ast.FunctionDef, 
                              function_index: Dict[str, List[Dict[str, Any]]], 
                              current_file: str) -> List[Dict[str, Any]]:
        """找出相似的函數"""
        similar = []
        
        # 檢查同名函數
        if func_node.name in function_index:
            for existing_func in function_index[func_node.name]:
                if existing_func['file_path'] != current_file:
                    # 比較參數列表
                    current_args = [arg.arg for arg in func_node.args.args]
                    existing_args = existing_func['args']
                    
                    arg_similarity = self._calculate_arg_similarity(current_args, existing_args)
                    
                    if arg_similarity > 0.7:  # 70% 相似度
                        similar.append({
                            'module': existing_func['module'],
                            'file_path': existing_func['file_path'],
                            'similarity': arg_similarity,
                            'args': existing_args,
                            'docstring': existing_func.get('docstring', '')
                        })
        
        return similar
    
    def _find_similar_classes(self, class_node: ast.ClassDef, 
                            class_index: Dict[str, List[Dict[str, Any]]], 
                            current_file: str) -> List[Dict[str, Any]]:
        """找出相似的類別"""
        similar = []
        
        # 提取當前類別的方法
        current_methods = [node.name for node in class_node.body if isinstance(node, ast.FunctionDef)]
        
        # 檢查同名類別
        if class_node.name in class_index:
            for existing_class in class_index[class_node.name]:
                if existing_class['file_path'] != current_file:
                    method_similarity = self._calculate_method_similarity(
                        current_methods, existing_class['methods']
                    )
                    
                    if method_similarity > 0.6:  # 60% 相似度
                        similar.append({
                            'module': existing_class['module'],
                            'file_path': existing_class['file_path'],
                            'similarity': method_similarity,
                            'methods': existing_class['methods'],
                            'docstring': existing_class.get('docstring', '')
                        })
        
        return similar
    
    def _calculate_arg_similarity(self, args1: List[str], args2: List[str]) -> float:
        """計算參數列表相似度"""
        if not args1 and not args2:
            return 1.0
        
        if not args1 or not args2:
            return 0.0
        
        # 簡單的集合交集計算
        set1 = set(args1)
        set2 = set(args2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_method_similarity(self, methods1: List[str], methods2: List[str]) -> float:
        """計算方法列表相似度"""
        if not methods1 and not methods2:
            return 1.0
        
        if not methods1 or not methods2:
            return 0.0
        
        set1 = set(methods1)
        set2 = set(methods2)
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def _extract_import_info(self, import_node: ast.AST) -> Dict[str, Any]:
        """提取導入資訊"""
        if isinstance(import_node, ast.Import):
            return {
                'type': 'import',
                'line': import_node.lineno,
                'modules': [alias.name for alias in import_node.names],
                'aliases': {alias.name: alias.asname for alias in import_node.names if alias.asname}
            }
        elif isinstance(import_node, ast.ImportFrom):
            return {
                'type': 'from_import',
                'line': import_node.lineno,
                'module': import_node.module,
                'names': [alias.name for alias in import_node.names],
                'aliases': {alias.name: alias.asname for alias in import_node.names if alias.asname},
                'level': import_node.level
            }
    
    def _resolve_relative_import(self, file_path: str, import_info: Dict[str, Any]) -> Optional[str]:
        """解析相對導入路徑"""
        # 簡化實現
        try:
            file_dir = Path(file_path).parent
            level = import_info.get('level', 0)
            module = import_info.get('module', '')
            
            # 計算相對路徑
            target_dir = file_dir
            for _ in range(level - 1):
                target_dir = target_dir.parent
            
            if module:
                target_path = target_dir / f"{module.replace('.', '/')}.py"
            else:
                target_path = target_dir / "__init__.py"
            
            return str(target_path) if target_path.exists() else None
            
        except:
            return None
    
    def _resolve_module_path(self, module_name: str) -> Optional[str]:
        """解析模組路徑"""
        # 檢查是否是專案內模組
        module_path = self.repo_root / f"{module_name.replace('.', '/')}.py"
        if module_path.exists():
            return str(module_path.relative_to(self.repo_root))
        
        # 檢查是否是包
        package_path = self.repo_root / module_name.replace('.', '/') / "__init__.py"
        if package_path.exists():
            return str(package_path.relative_to(self.repo_root))
        
        return None
    
    def _reconstruct_import_statement(self, import_info: Dict[str, Any]) -> str:
        """重構導入語句"""
        if import_info['type'] == 'import':
            modules = import_info['modules']
            aliases = import_info.get('aliases', {})
            
            module_parts = []
            for module in modules:
                if module in aliases:
                    module_parts.append(f"{module} as {aliases[module]}")
                else:
                    module_parts.append(module)
            
            return f"import {', '.join(module_parts)}"
        
        elif import_info['type'] == 'from_import':
            module = import_info['module'] or ''
            names = import_info['names']
            aliases = import_info.get('aliases', {})
            level = import_info.get('level', 0)
            
            name_parts = []
            for name in names:
                if name in aliases:
                    name_parts.append(f"{name} as {aliases[name]}")
                else:
                    name_parts.append(name)
            
            return f"from {'.' * level}{module} import {', '.join(name_parts)}"
    
    def _remove_items_from_import_line(self, line: str, items_to_remove: List[str]) -> str:
        """從導入行中移除指定項目"""
        # 簡化實現：這需要更複雜的解析邏輯
        for item in items_to_remove:
            # 移除項目（需要處理逗號等）
            patterns = [
                f"{item},",
                f", {item}",
                f"{item}"
            ]
            
            for pattern in patterns:
                if pattern in line:
                    line = line.replace(pattern, "", 1)
                    break
        
        # 清理多餘的逗號和空格
        line = line.replace(",,", ",").replace(", ,", ",")
        line = line.strip()
        
        return line