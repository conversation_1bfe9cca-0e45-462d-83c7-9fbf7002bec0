"""
LLM 郵件解析器
使用大型語言模型進行郵件內容解析
"""

import os
from typing import Optional, Dict, Any, List
from dataclasses import asdict

from src.infrastructure.parsers.base_parser import BaseParser, VendorIdentificationResult, EmailParsingResult, ParsingContext
from src.data_models.email_models import EmailData
from src.infrastructure.llm.unified_llm_client import UnifiedLLMClient, UnifiedLLMResult
from src.infrastructure.logging.logger_manager import LoggerManager


class LLMParser(BaseParser):
    """LLM 郵件解析器 - 單例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LLMParser, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 避免重複初始化
        if self._initialized:
            return
            
        super().__init__()
        self.logger = LoggerManager().get_logger("LLMParser")
        
        # 檢查是否啟用 LLM 解析
        self.enabled = os.getenv('LLM_PARSING_ENABLED', 'false').lower() == 'true'
        self.parsing_mode = os.getenv('LLM_PARSING_MODE', 'fallback')
        
        if self.enabled:
            self.client = UnifiedLLMClient()
            # 測試連接
            if not self.client.is_available():
                self.logger.error("LLM 服務連接失敗，已禁用 LLM 解析")
                self.enabled = False
        else:
            self.client = None
            
        self.logger.info(f"LLM 解析器初始化: 啟用={self.enabled}, 模式={self.parsing_mode}")
        self._initialized = True
    
    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return "LLM"
    
    @property
    def vendor_name(self) -> str:
        """廠商名稱"""
        return "LLM Parser"
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return ["llm", "ai", "language_model"]
    
    def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
        """使用 LLM 識別廠商"""
        if not self.enabled:
            return VendorIdentificationResult(
                vendor_code=None,
                vendor_name=None,
                confidence_score=0.0,
                is_identified=False,
                identification_method="llm_disabled"
            )
        
        try:
            # 使用統一 LLM 解析郵件
            llm_result = self.client.parse_email(email_data.subject, email_data.body, email_data.sender or "")
            
            if llm_result.is_success and llm_result.vendor_code:
                return VendorIdentificationResult(
                    vendor_code=llm_result.vendor_code,
                    vendor_name=llm_result.vendor_name,
                    confidence_score=llm_result.confidence_score,
                    is_identified=True,
                    identification_method="unified_llm",
                    metadata={
                        "llm_provider": llm_result.llm_provider,
                        "llm_model": llm_result.llm_model,
                        "parsing_methods": llm_result.parsing_methods_used
                    }
                )
            else:
                return VendorIdentificationResult(
                    vendor_code=None,
                    vendor_name=None,
                    confidence_score=0.0,
                    is_identified=False,
                    identification_method="unified_llm_failed",
                    metadata={"error": llm_result.error_message}
                )
                
        except Exception as e:
            self.logger.error(f"LLM 廠商識別失敗: {e}")
            return VendorIdentificationResult(
                vendor_code=None,
                vendor_name=None,
                confidence_score=0.0,
                is_identified=False,
                identification_method="llm_error",
                metadata={"error": str(e)}
            )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """使用 LLM 解析郵件"""
        if not self.enabled:
            return EmailParsingResult(
                is_success=False,
                error_message="LLM 解析已禁用",
                vendor_code=None,
                extraction_method="llm_disabled"
            )
        
        try:
            # 使用統一 LLM 解析郵件
            llm_result = self.client.parse_email(
                context.email_data.subject, 
                context.email_data.body,
                context.email_data.sender or ""
            )
            
            if llm_result.is_success:
                # 轉換為 EmailParsingResult
                parsing_result = EmailParsingResult(
                    is_success=True,
                    vendor_code=llm_result.vendor_code,
                    lot_number=llm_result.lot_number,
                    mo_number=llm_result.mo_number,
                    extraction_method="unified_llm",
                    confidence_score=llm_result.confidence_score,
                    extracted_data={
                        'product': llm_result.product_code,
                        'product_name': llm_result.product_code,
                        'yield_value': llm_result.yield_rate,
                        'test_batch': llm_result.test_batch,
                        'device_type': llm_result.device_type,
                        'quantity': llm_result.quantity,
                        'llm_provider': llm_result.llm_provider,
                        'llm_model': llm_result.llm_model,
                        'parsing_methods_used': llm_result.parsing_methods_used,
                        'analysis_reasoning': llm_result.analysis_reasoning,
                        'raw_llm_response': llm_result.raw_response
                    }
                )
                
                self.logger.info(f"統一 LLM 解析成功: 廠商={llm_result.vendor_code}, 提供者={llm_result.llm_provider}, 信心分數={llm_result.confidence_score}")
                return parsing_result
                
            else:
                return EmailParsingResult(
                    is_success=False,
                    error_message=llm_result.error_message or "統一 LLM 解析失敗",
                    vendor_code=llm_result.vendor_code,
                    extraction_method="unified_llm_failed",
                    extracted_data={
                        'llm_provider': llm_result.llm_provider,
                        'llm_model': llm_result.llm_model,
                        'raw_llm_response': llm_result.raw_response
                    }
                )
                
        except Exception as e:
            self.logger.error(f"LLM 郵件解析失敗: {e}")
            return EmailParsingResult(
                is_success=False,
                error_message=str(e),
                vendor_code=None,
                extraction_method="llm_error"
            )
    
    def can_parse(self, email_data: EmailData) -> bool:
        """檢查是否可以使用 LLM 解析"""
        return self.enabled and self.client is not None
    
    def get_confidence_score(self, email_data: EmailData) -> float:
        """取得 LLM 解析的信心分數"""
        if not self.enabled:
            return 0.0
            
        try:
            result = self.identify_vendor(email_data)
            return result.confidence_score
        except Exception:
            return 0.0


class HybridLLMParser(BaseParser):
    """混合 LLM 解析器 - 結合傳統解析器和 LLM"""
    
    def __init__(self, traditional_parser: BaseParser):
        super().__init__()
        self.logger = LoggerManager().get_logger("HybridLLMParser")
        self.traditional_parser = traditional_parser
        self.llm_parser = LLMParser()
        
        self.parsing_mode = os.getenv('LLM_PARSING_MODE', 'fallback')
        # 降低日誌級別，避免重複的初始化訊息
        self.logger.debug(f"混合 LLM 解析器初始化: 模式={self.parsing_mode}, 廠商={traditional_parser.vendor_code}")
    
    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self.traditional_parser.vendor_code
    
    @property
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self.traditional_parser.vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self.traditional_parser.supported_patterns
    
    def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
        """混合廠商識別"""
        # 優先使用傳統解析器
        traditional_result = self.traditional_parser.identify_vendor(email_data)
        
        if self.parsing_mode == "primary":
            # 主要使用 LLM
            llm_result = self.llm_parser.identify_vendor(email_data)
            if llm_result.is_identified:
                return llm_result
            else:
                return traditional_result
                
        elif self.parsing_mode == "hybrid":
            # 結合兩者結果
            llm_result = self.llm_parser.identify_vendor(email_data)
            
            if traditional_result.is_identified and llm_result.is_identified:
                # 選擇信心分數較高的
                if llm_result.confidence_score > traditional_result.confidence_score:
                    return llm_result
                else:
                    return traditional_result
            elif traditional_result.is_identified:
                return traditional_result
            elif llm_result.is_identified:
                return llm_result
            else:
                return traditional_result
                
        else:  # fallback 模式
            # 傳統解析失敗時才使用 LLM
            if traditional_result.is_identified:
                return traditional_result
            else:
                return self.llm_parser.identify_vendor(email_data)
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """混合解析"""
        # 優先使用傳統解析器
        traditional_result = self.traditional_parser.parse_email(context)
        
        if self.parsing_mode == "primary":
            # 主要使用 LLM
            llm_result = self.llm_parser.parse_email(context)
            if llm_result.is_success:
                return llm_result
            else:
                # 回退到傳統解析，但標記為 fallback
                traditional_result.extraction_method = "fallback"
                return traditional_result
                
        elif self.parsing_mode == "hybrid":
            # 結合兩者結果
            llm_result = self.llm_parser.parse_email(context)
            
            if traditional_result.is_success and llm_result.is_success:
                # 合併結果，優先使用傳統解析的結果，LLM 補充缺失資料
                merged_result = traditional_result
                
                # 如果傳統解析缺少某些資料，從 LLM 結果補充
                if not merged_result.lot_number and llm_result.lot_number:
                    merged_result.lot_number = llm_result.lot_number
                
                if not merged_result.mo_number and llm_result.mo_number:
                    merged_result.mo_number = llm_result.mo_number
                
                # 合併提取的資料
                if llm_result.extracted_data:
                    merged_result.extracted_data = merged_result.extracted_data or {}
                    merged_result.extracted_data.update({
                        k: v for k, v in llm_result.extracted_data.items() 
                        if k not in merged_result.extracted_data or not merged_result.extracted_data[k]
                    })
                
                merged_result.extraction_method = "hybrid"
                return merged_result
                
            elif traditional_result.is_success:
                return traditional_result
            elif llm_result.is_success:
                return llm_result
            else:
                return traditional_result
                
        else:  # fallback 模式
            # 傳統解析失敗時才使用 LLM
            if traditional_result.is_success:
                return traditional_result
            else:
                llm_result = self.llm_parser.parse_email(context)
                self.logger.info(f"傳統解析失敗，使用 LLM 解析: 成功={llm_result.is_success}")
                return llm_result
    
    def can_parse(self, email_data: EmailData) -> bool:
        """檢查是否可以解析"""
        return (self.traditional_parser.can_parse(email_data) or 
                self.llm_parser.can_parse(email_data))
    
    def get_confidence_score(self, email_data: EmailData) -> float:
        """取得信心分數"""
        traditional_score = self.traditional_parser.get_confidence_score(email_data)
        llm_score = self.llm_parser.get_confidence_score(email_data)
        
        if self.parsing_mode == "primary":
            return max(llm_score, traditional_score)
        else:
            return max(traditional_score, llm_score)