"""
GTK 廠商檔案處理器
對應 VBA 的 CopyFilesGTK 和 CopyFilesGTK2 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class GTKFileHandler(BaseFileHandler):
    """
    GTK 廠商檔案處理器
    
    VBA 邏輯：
    1. 先從 \GTK\temp\ 搜尋包含 MO 的壓縮檔
    2. 找不到則用 LOT 搜尋
    3. 最後嘗試從 \GTK\FT\{PD}\{LOT} 複製整個資料夾
    """
    
    def __init__(self, source_base_path: str):
        """初始化 GTK 檔案處理器"""
        super().__init__(source_base_path, "GTK")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """
        GTK 有兩個可能的來源路徑
        
        VBA:
        sourcePathGTK = sourcePath & "\GTK\temp\"
        sourcePathGTK = sourcePath & "\GTK\FT\" & pd & "\" & lot
        """
        paths = [
            self.source_base_path / "GTK" / "temp",  # 主要路徑
        ]
        
        # 如果有 PD 和 LOT，加入 FT 路徑
        if pd != "default" and lot != "default":
            paths.append(self.source_base_path / "GTK" / "FT" / pd / lot)
            
        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        GTK 的檔案搜尋模式
        
        VBA:
        file = Dir(sourcePathGTK & fileName & "*")  ' MO
        file = Dir(sourcePathGTK & lot & "*")       ' LOT
        """
        patterns = []
        
        if mo:
            patterns.extend([
                f"{mo}*",      # VBA 原始模式：MO 開頭
                f"*{mo}*",     # 包含 MO 的檔案
            ])
            
        if lot:
            patterns.extend([
                f"{lot}*",     # VBA 原始模式：LOT 開頭
                f"*{lot}*"     # 包含 LOT 的檔案
            ])
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """GTK 支援資料夾複製（對應 CopyFilesGTK2）"""
        return True
        
    def copy_files(self, file_name: str, file_temp: str,
                  pd: str = "default", lot: str = "default") -> bool:
        """
        覆寫基礎方法以實作 GTK 特定邏輯

        VBA CopyFilesGTK 的搜尋順序：
        1. 在 temp 目錄用 MO 搜尋壓縮檔
        2. 在 temp 目錄用 LOT 搜尋所有檔案
        3. 從 FT 目錄複製整個資料夾
        """
        self.logger.info(f"GTK 檔案處理開始: MO={file_name}, LOT={lot}, PD={pd}")

        # file_temp 已經是最終的目標目錄，直接使用
        destination_path = Path(file_temp)
        destination_path.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"建立目標資料夾: {destination_path}")
            
        # 1. 先嘗試從 temp 目錄搜尋
        temp_path = self.source_base_path / "GTK" / "temp"
        if temp_path.exists():
            # 1.1 用 MO 搜尋壓縮檔
            if self._copy_by_mo(temp_path, destination_path, file_name):
                return True
                
            # 1.2 用 LOT 搜尋檔案
            if lot != "default" and self._copy_by_lot(temp_path, destination_path, lot):
                return True
                
        # 2. 嘗試從 FT 目錄複製整個資料夾
        if pd != "default" and lot != "default":
            ft_path = self.source_base_path / "GTK" / "FT"
            if ft_path.exists():
                if self._copy_entire_folder(ft_path, destination_path, pd, lot):
                    return True
                    
        self.logger.error(f"GTK 檔案處理失敗: 找不到任何符合的檔案")
        return False