"""LLM 智慧搜尋服務
整合現有 Ollama 基礎設施，提供自然語言查詢解析和智慧搜尋功能
"""

import asyncio
import json
import re
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from loguru import logger

from ..domain.entities.file_search import (
    FileInfo, ProductSearchResult, SearchFilters, SearchStatus, TimeRange
)
from ..data_models.search_models import TimeRangeType
from ..infrastructure.llm.unified_llm_client import UnifiedLLMClient
from .product_search_service import ProductSearchService


class LLMSearchService:
    """LLM 智慧搜尋服務
    
    提供基於自然語言的智慧搜尋功能，包括：
    - 自然語言查詢解析
    - 時間相關查詢解析
    - 搜尋結果智慧分析
    - 行動建議生成
    """
    
    def __init__(self, product_search_service: ProductSearchService):
        """初始化 LLM 搜尋服務
        
        Args:
            product_search_service: 產品搜尋服務實例
        """
        self.product_search_service = product_search_service
        self.llm_client = UnifiedLLMClient()
        
        # 檢查 LLM 服務可用性
        if not self.llm_client.is_available():
            logger.warning("LLM 服務不可用，智慧搜尋功能將受限")
    
    async def interpret_natural_query(self, query: str) -> Dict[str, Any]:
        """解析自然語言查詢
        
        Args:
            query: 自然語言查詢字串
            
        Returns:
            Dict[str, Any]: 解析後的搜尋參數
        """
        try:
            logger.info(f"開始解析自然語言查詢: {query}")
            
            # 構建查詢解析提示
            prompt = self._build_query_interpretation_prompt(query)
            
            # 調用 LLM 解析
            if not self.llm_client.is_available():
                # 如果 LLM 不可用，使用規則式解析作為後備
                return self._fallback_query_parsing(query)
            
            # 使用統一 LLM 客戶端解析查詢
            response = await self._call_llm_for_query_parsing(prompt)
            
            if response:
                parsed_params = self._parse_query_response(response)
                logger.info(f"查詢解析完成: {parsed_params}")
                return parsed_params
            else:
                logger.warning("LLM 查詢解析失敗，使用後備解析")
                return self._fallback_query_parsing(query)
                
        except Exception as e:
            logger.error(f"自然語言查詢解析失敗: {e}")
            return self._fallback_query_parsing(query)
    
    async def smart_search(
        self, 
        query: str, 
        base_path: Path,
        max_results: int = 100
    ) -> Dict[str, Any]:
        """執行智慧搜尋
        
        Args:
            query: 自然語言查詢
            base_path: 基礎搜尋路徑
            max_results: 最大結果數量
            
        Returns:
            Dict[str, Any]: 搜尋結果和分析
        """
        start_time = time.time()
        
        try:
            logger.info(f"開始智慧搜尋: {query}")
            
            # 1. 解析自然語言查詢
            search_params = await self.interpret_natural_query(query)
            
            # 2. 執行搜尋
            search_results = []
            
            if search_params.get('product_names'):
                # 如果識別出產品名稱，執行產品搜尋
                for product_name in search_params['product_names']:
                    time_range = self._create_time_range_from_params(search_params)
                    filters = self._create_filters_from_params(search_params, time_range)
                    
                    result = await self.product_search_service.search_product_folder(
                        product_name, base_path, time_range, filters
                    )
                    
                    if result.success:
                        search_results.append(result)
            else:
                # 如果沒有明確的產品名稱，執行廣泛搜尋
                logger.info("未識別出具體產品名稱，執行廣泛搜尋")
                # 這裡可以實作更廣泛的搜尋邏輯
                pass
            
            # 3. 分析搜尋結果
            analysis = await self.analyze_search_results(search_results, query)
            
            # 4. 生成建議
            suggestions = await self.generate_action_suggestions(search_results, search_params)
            
            # 5. 構建回應
            response = {
                "status": "success",
                "query": query,
                "interpretation": search_params,
                "results": self._format_search_results(search_results, max_results),
                "analysis": analysis,
                "suggestions": suggestions,
                "search_duration": time.time() - start_time,
                "timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"智慧搜尋完成，找到 {len(search_results)} 個產品結果")
            return response
            
        except Exception as e:
            logger.error(f"智慧搜尋失敗: {e}")
            return {
                "status": "error",
                "query": query,
                "error_message": str(e),
                "search_duration": time.time() - start_time,
                "timestamp": datetime.now().isoformat()
            }
    
    async def analyze_search_results(
        self, 
        results: List[ProductSearchResult], 
        original_query: str
    ) -> Dict[str, Any]:
        """分析搜尋結果
        
        Args:
            results: 搜尋結果列表
            original_query: 原始查詢
            
        Returns:
            Dict[str, Any]: 分析結果
        """
        try:
            if not results:
                return {
                    "summary": "未找到匹配的結果",
                    "total_products": 0,
                    "total_files": 0,
                    "recommendations": ["嘗試使用不同的產品名稱", "擴大時間範圍", "檢查路徑是否正確"]
                }
            
            # 統計分析
            total_files = sum(len(r.matched_files) for r in results)
            total_size_mb = sum(r.total_size_mb for r in results)
            
            # 檔案類型分析
            file_types = {}
            for result in results:
                for file_info in result.matched_files:
                    if not file_info.is_directory:
                        file_type = file_info.file_type
                        file_types[file_type] = file_types.get(file_type, 0) + 1
            
            # 時間分佈分析
            time_distribution = self._analyze_time_distribution(results)
            
            # 如果 LLM 可用，生成智慧分析
            llm_analysis = None
            if self.llm_client.is_available():
                llm_analysis = await self._generate_llm_analysis(results, original_query)
            
            analysis = {
                "summary": f"找到 {len(results)} 個產品，共 {total_files} 個檔案",
                "total_products": len(results),
                "total_files": total_files,
                "total_size_mb": round(total_size_mb, 2),
                "file_types": file_types,
                "time_distribution": time_distribution,
                "product_breakdown": [
                    {
                        "product_name": r.product_name,
                        "files_count": len(r.matched_files),
                        "size_mb": r.total_size_mb,
                        "folder_path": str(r.product_folder)
                    }
                    for r in results
                ]
            }
            
            if llm_analysis:
                analysis["llm_insights"] = llm_analysis
            
            return analysis
            
        except Exception as e:
            logger.error(f"搜尋結果分析失敗: {e}")
            return {
                "summary": "分析過程中發生錯誤",
                "error": str(e)
            }
    
    async def generate_action_suggestions(
        self, 
        results: List[ProductSearchResult],
        search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成行動建議
        
        Args:
            results: 搜尋結果
            search_params: 搜尋參數
            
        Returns:
            List[Dict[str, Any]]: 建議列表
        """
        suggestions = []
        
        try:
            if not results:
                suggestions.extend([
                    {
                        "type": "search_refinement",
                        "title": "調整搜尋條件",
                        "description": "嘗試使用不同的產品名稱或擴大時間範圍",
                        "priority": "high"
                    },
                    {
                        "type": "path_check",
                        "title": "檢查路徑",
                        "description": "確認網路路徑是否正確且可存取",
                        "priority": "medium"
                    }
                ])
            else:
                # 根據結果生成建議
                total_files = sum(len(r.matched_files) for r in results)
                
                if total_files > 0:
                    suggestions.append({
                        "type": "process_files",
                        "title": "處理檔案",
                        "description": f"對找到的 {total_files} 個檔案執行處理操作",
                        "priority": "high",
                        "actions": ["generate_summary", "compare_code"]
                    })
                
                if len(results) > 1:
                    suggestions.append({
                        "type": "compare_products",
                        "title": "產品比較",
                        "description": f"比較 {len(results)} 個產品的測試資料",
                        "priority": "medium"
                    })
                
                # 檢查是否有大量檔案
                if total_files > 100:
                    suggestions.append({
                        "type": "filter_results",
                        "title": "篩選結果",
                        "description": "結果數量較多，建議添加更多篩選條件",
                        "priority": "medium"
                    })
            
            # 如果 LLM 可用，生成更智慧的建議
            if self.llm_client.is_available() and results:
                llm_suggestions = await self._generate_llm_suggestions(results, search_params)
                if llm_suggestions:
                    suggestions.extend(llm_suggestions)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"生成行動建議失敗: {e}")
            return [{
                "type": "error",
                "title": "建議生成失敗",
                "description": str(e),
                "priority": "low"
            }]
    
    def _build_query_interpretation_prompt(self, query: str) -> str:
        """構建查詢解析提示"""
        prompt = f"""你是一個專業的檔案搜尋查詢解析器。請分析以下自然語言查詢，並提取搜尋參數。

查詢: "{query}"

請識別以下資訊並以 JSON 格式回應：

1. 產品名稱 (product_names): 從查詢中識別的產品代碼或名稱列表
2. 時間範圍 (time_range): 
   - "最近一週" -> "last_week"
   - "最近一個月" -> "last_month" 
   - "最近3個月" -> "last_3_months"
   - "最近6個月" -> "last_6_months"
   - "本季" -> "current_quarter"
   - 具體日期 -> "custom" (需提供 start_date, end_date)
3. 檔案類型 (file_types): 如 .csv, .xlsx, .txt 等
4. 檔案大小限制 (size_constraints): 最小/最大大小
5. 搜尋意圖 (intent): 如 "find_files", "analyze_data", "compare_products"
6. 關鍵字 (keywords): 其他重要關鍵字

回應格式：
{{
  "product_names": ["產品名稱1", "產品名稱2"],
  "time_range": "時間範圍類型",
  "custom_start_date": "YYYY-MM-DD",
  "custom_end_date": "YYYY-MM-DD", 
  "file_types": [".csv", ".xlsx"],
  "min_size_mb": 數字或null,
  "max_size_mb": 數字或null,
  "intent": "搜尋意圖",
  "keywords": ["關鍵字1", "關鍵字2"],
  "confidence": 0.95
}}"""
        
        return prompt
    
    async def _call_llm_for_query_parsing(self, prompt: str) -> Optional[str]:
        """調用 LLM 進行查詢解析"""
        try:
            # 使用統一 LLM 客戶端的 parse_email 方法
            # 這裡我們將查詢作為 subject 傳入
            result = self.llm_client.parse_email(subject=prompt, body="")
            
            if result.is_success and result.raw_response:
                return result.raw_response
            else:
                logger.warning(f"LLM 查詢解析失敗: {result.error_message}")
                return None
                
        except Exception as e:
            logger.error(f"調用 LLM 進行查詢解析失敗: {e}")
            return None
    
    def _parse_query_response(self, response: str) -> Dict[str, Any]:
        """解析 LLM 的查詢回應"""
        try:
            # 提取 JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                data = json.loads(json_str)
                
                # 清理和驗證資料
                cleaned_data = {
                    "product_names": data.get("product_names", []),
                    "time_range": data.get("time_range", "last_6_months"),
                    "custom_start_date": data.get("custom_start_date"),
                    "custom_end_date": data.get("custom_end_date"),
                    "file_types": data.get("file_types", []),
                    "min_size_mb": data.get("min_size_mb"),
                    "max_size_mb": data.get("max_size_mb"),
                    "intent": data.get("intent", "find_files"),
                    "keywords": data.get("keywords", []),
                    "confidence": data.get("confidence", 0.5)
                }
                
                return cleaned_data
            else:
                raise ValueError("未找到 JSON 格式回應")
                
        except Exception as e:
            logger.error(f"解析查詢回應失敗: {e}")
            return self._fallback_query_parsing("")
    
    def _fallback_query_parsing(self, query: str) -> Dict[str, Any]:
        """後備查詢解析（規則式）"""
        query_lower = query.lower()
        
        # 簡單的規則式解析
        product_names = []
        time_range = "last_6_months"
        file_types = []
        
        # 產品名稱識別（簡單模式匹配）
        product_patterns = [
            r'\b([A-Z]{2,4}\d+[A-Z]*)\b',  # 產品代碼模式
            r'\b(AAA|BBB|CCC)\b'  # 常見產品名稱
        ]
        
        for pattern in product_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            product_names.extend(matches)
        
        # 時間範圍識別
        if any(word in query_lower for word in ['最近一週', 'last week', '一週']):
            time_range = "last_week"
        elif any(word in query_lower for word in ['最近一個月', 'last month', '一個月']):
            time_range = "last_month"
        elif any(word in query_lower for word in ['最近3個月', '3個月', 'last 3 months']):
            time_range = "last_3_months"
        elif any(word in query_lower for word in ['本季', 'current quarter', '這季']):
            time_range = "current_quarter"
        
        # 檔案類型識別
        if 'csv' in query_lower:
            file_types.append('.csv')
        if 'excel' in query_lower or 'xlsx' in query_lower:
            file_types.append('.xlsx')
        
        return {
            "product_names": list(set(product_names)),  # 去重
            "time_range": time_range,
            "custom_start_date": None,
            "custom_end_date": None,
            "file_types": file_types,
            "min_size_mb": None,
            "max_size_mb": None,
            "intent": "find_files",
            "keywords": query.split(),
            "confidence": 0.6  # 規則式解析的信心分數較低
        }
    
    def _create_time_range_from_params(self, params: Dict[str, Any]) -> TimeRange:
        """從參數建立時間範圍"""
        time_range_type = params.get("time_range", "last_6_months")
        
        if time_range_type == "custom" and params.get("custom_start_date") and params.get("custom_end_date"):
            start_date = datetime.fromisoformat(params["custom_start_date"])
            end_date = datetime.fromisoformat(params["custom_end_date"])
            return TimeRange(start_date, end_date)
        
        # 使用產品搜尋服務的時間範圍建立方法
        time_range_enum = getattr(TimeRangeType, time_range_type.upper(), TimeRangeType.LAST_6_MONTHS)
        return self.product_search_service.create_time_range(time_range_enum)
    
    def _create_filters_from_params(self, params: Dict[str, Any], time_range: TimeRange) -> SearchFilters:
        """從參數建立搜尋篩選條件"""
        return SearchFilters(
            time_range=time_range,
            file_types=params.get("file_types") if params.get("file_types") else None,
            min_size=int(params["min_size_mb"] * 1024 * 1024) if params.get("min_size_mb") else None,
            max_size=int(params["max_size_mb"] * 1024 * 1024) if params.get("max_size_mb") else None,
            include_directories=True
        )
    
    def _format_search_results(self, results: List[ProductSearchResult], max_results: int) -> List[Dict[str, Any]]:
        """格式化搜尋結果"""
        formatted_results = []
        
        for result in results:
            # 限制每個產品的檔案數量
            limited_files = result.matched_files[:max_results // len(results) if results else max_results]
            
            formatted_result = {
                "product_name": result.product_name,
                "product_folder": str(result.product_folder),
                "files_count": len(result.matched_files),
                "displayed_files_count": len(limited_files),
                "total_size_mb": result.total_size_mb,
                "search_duration": result.search_duration,
                "files": [
                    {
                        "path": str(f.path),
                        "name": f.name,
                        "size_mb": f.size_mb,
                        "modified_time": f.modified_time.isoformat(),
                        "file_type": f.file_type,
                        "is_directory": f.is_directory
                    }
                    for f in limited_files
                ]
            }
            formatted_results.append(formatted_result)
        
        return formatted_results
    
    def _analyze_time_distribution(self, results: List[ProductSearchResult]) -> Dict[str, int]:
        """分析時間分佈"""
        distribution = {
            "last_week": 0,
            "last_month": 0,
            "last_3_months": 0,
            "older": 0
        }
        
        now = datetime.now()
        week_ago = now - timedelta(days=7)
        month_ago = now - timedelta(days=30)
        three_months_ago = now - timedelta(days=90)
        
        for result in results:
            for file_info in result.matched_files:
                if not file_info.is_directory:
                    mod_time = file_info.modified_time
                    
                    if mod_time >= week_ago:
                        distribution["last_week"] += 1
                    elif mod_time >= month_ago:
                        distribution["last_month"] += 1
                    elif mod_time >= three_months_ago:
                        distribution["last_3_months"] += 1
                    else:
                        distribution["older"] += 1
        
        return distribution
    
    async def _generate_llm_analysis(self, results: List[ProductSearchResult], query: str) -> Optional[Dict[str, Any]]:
        """使用 LLM 生成智慧分析"""
        try:
            # 構建分析提示
            results_summary = []
            for result in results:
                results_summary.append({
                    "product": result.product_name,
                    "files_count": len(result.matched_files),
                    "size_mb": result.total_size_mb
                })
            
            analysis_prompt = f"""分析以下搜尋結果，提供洞察和建議：

原始查詢: {query}
搜尋結果摘要: {json.dumps(results_summary, ensure_ascii=False)}

請提供：
1. 資料品質評估
2. 趨勢分析
3. 異常檢測
4. 改進建議

以 JSON 格式回應：
{{
  "data_quality": "評估",
  "trends": "趨勢分析", 
  "anomalies": "異常情況",
  "recommendations": ["建議1", "建議2"]
}}"""
            
            response = await self._call_llm_for_query_parsing(analysis_prompt)
            if response:
                return self._parse_query_response(response)
            
        except Exception as e:
            logger.error(f"LLM 分析生成失敗: {e}")
        
        return None
    
    async def _generate_llm_suggestions(self, results: List[ProductSearchResult], params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用 LLM 生成智慧建議"""
        try:
            # 這裡可以實作更複雜的 LLM 建議生成邏輯
            # 暫時返回空列表，避免過度複雜化
            return []
            
        except Exception as e:
            logger.error(f"LLM 建議生成失敗: {e}")
            return []