(venv_win_3_11_12) PS D:\project\python\outlook_summary> python test_deep_duplicate_analysis.py
🧪 深度重複功能分析
================================================================================
🔍 分析函數簽名重複
==================================================
⚠️ 重複函數簽名: __post_init__(self)
   📁 batch_csv_to_excel_processor.py:55
   📁 src\infrastructure\adapters\excel\csv_to_excel_converter.py:42
   📁 src\infrastructure\adapters\llm\grok_smart_parser.py:42
   📁 src\infrastructure\llm\unified_llm_client.py:56

⚠️ 重複函數簽名: parse_arguments()
   📁 code_comparison.py:70
   📁 csv_to_summary.py:29

⚠️ 重複函數簽名: validate_folder_path(folder_path)
   📁 code_comparison.py:124
   📁 csv_to_summary.py:60
   📁 src\presentation\api\services\api_utils.py:90

⚠️ 重複函數簽名: validate_config(self)
   📁 email_config.py:163
   📁 .claude\run_hooks.py:40

⚠️ 重複函數簽名: stop(self)
   📁 email_inbox_app.py:75
   📁 src\infrastructure\logging\logger_manager.py:251

⚠️ 重複函數簽名: cleanup(self)
   📁 email_inbox_app.py:1050
   📁 src\infrastructure\adapters\email_inbox\email_sync_service.py:645

⚠️ 重複函數簽名: email_detail(email_id)
   📁 email_inbox_app.py:142
   📁 src\infrastructure\adapters\web_api\email_web_service.py:740

⚠️ 重複函數簽名: safe_print(text)
   📁 .claude\run_hooks.py:329
   📁 .claude\hooks\update_docs_safe.py:20
   📁 .claude\hooks\update_docs_simple.py:23

⚠️ 重複函數簽名: _setup_logging(self)
   📁 .claude\hooks\main.py:66
   📁 .claude\hooks\main_simple.py:30

⚠️ 重複函數簽名: _get_default_config(self)
   📁 .claude\hooks\main.py:94
   📁 .claude\hooks\main_simple.py:58

⚠️ 重複函數簽名: collect_project_stats(project_root)
   📁 .claude\hooks\update_docs_safe.py:66
   📁 .claude\hooks\update_docs_simple.py:69

⚠️ 重複函數簽名: update_readme(project_root, current_date, stats)
   📁 .claude\hooks\update_docs_safe.py:150
   📁 .claude\hooks\update_docs_simple.py:146

⚠️ 重複函數簽名: update_changelog(project_root, current_date, stats)
   📁 .claude\hooks\update_docs_safe.py:197
   📁 .claude\hooks\update_docs_simple.py:193

⚠️ 重複函數簽名: update_project_info(project_root, current_datetime, stats)
   📁 .claude\hooks\update_docs_safe.py:268
   📁 .claude\hooks\update_docs_simple.py:264

⚠️ 重複函數簽名: create_doc_summary(project_root, current_datetime, stats)
   📁 .claude\hooks\update_docs_safe.py:311
   📁 .claude\hooks\update_docs_simple.py:307

⚠️ 重複函數簽名: print_summary(stats)
   📁 .claude\hooks\update_docs_safe.py:374
   📁 .claude\hooks\update_docs_simple.py:370

⚠️ 重複函數簽名: _calculate_complexity(self, func_node)
   📁 .claude\hooks\validator.py:491
   📁 .claude\hooks\refactors\file_splitter.py:795

⚠️ 重複函數簽名: _find_python_files(self)
   📁 .claude\hooks\analyzers\duplicate_detector.py:345
   📁 .claude\hooks\analyzers\import_analyzer.py:389
   📁 .claude\hooks\analyzers\quality_checker.py:531

⚠️ 重複函數簽名: _get_end_line(self, node, lines)
   📁 .claude\hooks\analyzers\duplicate_detector.py:357
   📁 .claude\hooks\analyzers\file_size_analyzer.py:408
   📁 .claude\hooks\analyzers\quality_checker.py:395
   📁 .claude\hooks\refactors\file_splitter.py:774

⚠️ 重複函數簽名: _extract_import_info(self, import_node)
   📁 .claude\hooks\refactors\file_splitter.py:170
   📁 .claude\hooks\refactors\import_optimizer.py:628

⚠️ 重複函數簽名: capture_eqc_processing_logs()
   📁 analysis_examples\analyze_temp_failure.py:15
   📁 nouse\analyze_temp_failure.py:15

⚠️ 重複函數簽名: analyze_csv_discovery()
   📁 analysis_examples\analyze_temp_failure.py:98
   📁 nouse\analyze_temp_failure.py:98

⚠️ 重複函數簽名: analyze_eqc_bin1_search()
   📁 analysis_examples\analyze_temp_failure.py:144
   📁 nouse\analyze_temp_failure.py:144

⚠️ 重複函數簽名: decode_mime_header(self, header_value)
   📁 debug_temp\fix_database_encoding.py:31
   📁 src\infrastructure\adapters\database\email_database.py:56

⚠️ 重複函數簽名: decode_sender(self, sender_value)
   📁 debug_temp\fix_database_encoding.py:75
   📁 src\infrastructure\adapters\database\email_database.py:115

⚠️ 重複函數簽名: performance_test()
   📁 documentation\multicore_optimization_guide.py:136
   📁 documentation\optimize_eqc_ft_processing.py:174

⚠️ 重複函數簽名: _setup_logger(self)
   📁 documentation\windows_multicore_optimization.py:32
   📁 old_processors\eqc_standard_processor.py:46
   📁 src\infrastructure\adapters\excel\eqc\eqc_simple_detector.py:36
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:46

⚠️ 重複函數簽名: test_batch_processor()
   📁 nouse\simple_test.py:6
   📁 unused_py\simple_test.py:6

⚠️ 重複函數簽名: process_standard_eqc_pipeline(self, doc_directory, include_inseqcrtdata2, include_step5_testflow, include_step6_excel, include_final_excel_conversion, code_regions)
   📁 old_processors\eqc_standard_processor.py:57
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:57

⚠️ 重複函數簽名: process_from_stage2_only(self, doc_directory, include_inseqcrtdata2, include_step5_testflow, include_step6_excel, include_final_excel_conversion, code_regions)
   📁 old_processors\eqc_standard_processor.py:295
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:295

⚠️ 重複函數簽名: process_code_comparison_pipeline(self, doc_directory, code_regions)
   📁 old_processors\eqc_standard_processor.py:470
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:479

⚠️ 重複函數簽名: view_eqctotaldata_structure(self, doc_directory)
   📁 old_processors\eqc_standard_processor.py:572
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:614

⚠️ 重複函數簽名: _generate_eqctotaldata(self, doc_directory, output_path)
   📁 old_processors\eqc_standard_processor.py:606
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:648

⚠️ 重複函數簽名: _select_optimal_csv_file(self, csv_files)
   📁 old_processors\eqc_standard_processor.py:631
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:673

⚠️ 重複函數簽名: _select_latest_file_by_timestamp(self, files)
   📁 old_processors\eqc_standard_processor.py:679
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:721

⚠️ 重複函數簽名: _execute_dual_search_only(self, doc_directory, region_result, code_regions)
   📁 old_processors\eqc_standard_processor.py:716
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:758

⚠️ 重複函數簽名: _generate_standard_report(self, processing_data)
   📁 old_processors\eqc_standard_processor.py:755
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:797

⚠️ 重複函數簽名: _execute_inseqcrtdata2_step1234_processing(self, doc_directory, region_result)
   📁 old_processors\eqc_standard_processor.py:933
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:975

⚠️ 重複函數簽名: _execute_step4_code_matching_debug(self, rows, fail_details, region_result, doc_directory)
   📁 old_processors\eqc_standard_processor.py:1078
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:1120

⚠️ 重複函數簽名: _execute_step5_testflow_generation(self, doc_directory, inseqcrtdata2_result)
   📁 old_processors\eqc_standard_processor.py:1185
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:1227

⚠️ 重複函數簽名: _execute_step6_excel_generation(self, doc_directory, step5_result, region_result)
   📁 old_processors\eqc_standard_processor.py:1223
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:1265

⚠️ 重複函數簽名: _execute_final_excel_conversion(self, doc_directory)
   📁 old_processors\eqc_standard_processor.py:1310
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:1352

⚠️ 重複函數簽名: _rename_step6_to_final(self, doc_directory, step6_excel_filename)
   📁 old_processors\eqc_standard_processor.py:1370
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:1401

⚠️ 重複函數簽名: _generate_final_report(self, doc_directory, region_result, dual_search_result, inseqcrtdata2_result, step5_result, step6_result, final_excel_result)
   📁 old_processors\eqc_standard_processor.py:1413
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:1444

⚠️ 重複函數簽名: show_progress(self, current, total, filename)
   📁 REF\eqc_bin1_final_processor_original.py:56
   📁 src\infrastructure\adapters\excel\eqc\monitoring\progress_monitor.py:32

⚠️ 重複函數簽名: show_line_progress(self, lines_processed, filename, interval)
   📁 REF\eqc_bin1_final_processor_original.py:74
   📁 src\infrastructure\adapters\excel\eqc\monitoring\progress_monitor.py:53

⚠️ 重複函數簽名: record_success(self)
   📁 REF\eqc_bin1_final_processor_original.py:79
   📁 src\infrastructure\adapters\excel\eqc\monitoring\progress_monitor.py:65

⚠️ 重複函數簽名: record_failure(self)
   📁 REF\eqc_bin1_final_processor_original.py:83
   📁 src\infrastructure\adapters\excel\eqc\monitoring\progress_monitor.py:69

⚠️ 重複函數簽名: show_summary(self)
   📁 REF\eqc_bin1_final_processor_original.py:87
   📁 src\infrastructure\adapters\excel\eqc\monitoring\progress_monitor.py:73

⚠️ 重複函數簽名: _init_log(self)
   📁 REF\eqc_bin1_final_processor_original.py:118
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:37

⚠️ 重複函數簽名: log_section(self, title)
   📁 REF\eqc_bin1_final_processor_original.py:134
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:53

⚠️ 重複函數簽名: log_file_scan(self, file_type, files)
   📁 REF\eqc_bin1_final_processor_original.py:145
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:69

⚠️ 重複函數簽名: log_file_timestamp(self, file_path, timestamp, readable_time)
   📁 REF\eqc_bin1_final_processor_original.py:155
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:85

⚠️ 重複函數簽名: log_pairing_result(self, ft_file, eqc_file, match_method)
   📁 REF\eqc_bin1_final_processor_original.py:166
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:103

⚠️ 重複函數簽名: log_statistics(self, file_type, file_path, pass_count, fail_count, total_count)
   📁 REF\eqc_bin1_final_processor_original.py:175
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:119

⚠️ 重複函數簽名: log_unmatched_files(self, file_type, files, processor_instance)
   📁 REF\eqc_bin1_final_processor_original.py:183
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:136

⚠️ 重複函數簽名: log_summary(self, online_eqc_fail, eqc_rt_pass, total_pairs)
   📁 REF\eqc_bin1_final_processor_original.py:208
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:168

⚠️ 重複函數簽名: save_log(self)
   📁 REF\eqc_bin1_final_processor_original.py:223
   📁 src\infrastructure\adapters\excel\eqc\monitoring\debug_logger.py:190

⚠️ 重複函數簽名: convert_to_network_path(self, local_path)
   📁 REF\eqc_bin1_final_processor_original.py:254
   📁 src\infrastructure\adapters\excel\eqc\hyperlinks\hyperlink_processor.py:34

⚠️ 重複函數簽名: add_hyperlink_to_data(self, data_row, file_path, column_index)
   📁 REF\eqc_bin1_final_processor_original.py:307
   📁 src\infrastructure\adapters\excel\eqc\hyperlinks\hyperlink_processor.py:93

⚠️ 重複函數簽名: convert_csv_to_excel_hyperlinks(self, csv_file_path, output_excel_path)
   📁 REF\eqc_bin1_final_processor_original.py:325
   📁 src\infrastructure\adapters\excel\eqc\hyperlinks\hyperlink_processor.py:119

⚠️ 重複函數簽名: _perform_excel_hyperlink_conversion(self, csv_file_path, output_excel_path)
   📁 REF\eqc_bin1_final_processor_original.py:354
   📁 src\infrastructure\adapters\excel\eqc\hyperlinks\hyperlink_processor.py:155

⚠️ 重複函數簽名: find_online_eqc_bin1_datalog(self, eqc_files)
   📁 REF\eqc_bin1_final_processor_original.py:467
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:585

⚠️ 重複函數簽名: fill_eqc_bin1_statistics(self, content_lines, online_eqc_fail_count, eqc_rt_pass_count)
   📁 REF\eqc_bin1_final_processor_original.py:552
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_statistics_calculator.py:137

⚠️ 重複函數簽名: _extract_internal_timestamp(self, file_path)
   📁 REF\eqc_bin1_final_processor_original.py:664
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:1072

⚠️ 重複函數簽名: generate_ft_eqc_fail_data_with_hyperlinks(self, matched_pairs)
   📁 REF\eqc_bin1_final_processor_original.py:722
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_file_scanner.py:338

⚠️ 重複函數簽名: process_complete_eqc_integration(self, folder_path, enable_debug_log)
   📁 REF\eqc_bin1_final_processor_original.py:977
   📁 src\infrastructure\adapters\excel\eqc\eqc_bin1_final_processor.py:234

⚠️ 重複函數簽名: generate_eqc_total_data(self, folder_path)
   📁 REF\eqc_bin1_final_processor_original.py:1160
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:756
   📁 src\infrastructure\adapters\excel\eqc\eqc_bin1_final_processor.py:328

⚠️ 重複函數簽名: convert_windows_path_to_wsl(windows_path)
   📁 REF\ft_eqc_api_original_full.py:122
   📁 src\presentation\api\services\api_utils.py:616
   📁 src\presentation\api\services\api_utils.py:37
   📁 src\presentation\api\utils\api_utils.py:16

⚠️ 重複函數簽名: process_folder_path(original_path)
   📁 REF\ft_eqc_api_original_full.py:147
   📁 src\presentation\api\services\api_utils.py:621
   📁 src\presentation\api\services\api_utils.py:64
   📁 src\presentation\api\utils\api_utils.py:39

⚠️ 重複函數簽名: get_global_upload_processor()
   📁 REF\ft_eqc_api_original_full.py:157
   📁 src\presentation\api\services\api_utils.py:626

⚠️ 重複函數簽名: parse_summary_sheet_data(excel_path)
   📁 REF\ft_eqc_api_original_full.py:1381
   📁 src\presentation\api\services\api_utils.py:631
   📁 src\presentation\api\services\api_utils.py:170
   📁 src\presentation\api\utils\api_utils.py:148

⚠️ 重複函數簽名: count_debug_matches(debug_file_path)
   📁 REF\ft_eqc_api_original_full.py:1477
   📁 src\presentation\api\services\api_utils.py:662
   📁 src\presentation\api\services\api_utils.py:266
   📁 src\presentation\api\utils\api_utils.py:242

⚠️ 重複函數簽名: get_unread_count(self)
   📁 src\application\interfaces\email_reader.py:70
   📁 src\infrastructure\adapters\pop3\pop3_email_reader.py:229

⚠️ 重複函數簽名: is_connected(self)
   📁 src\application\interfaces\email_reader.py:93
   📁 src\infrastructure\adapters\outlook\outlook_adapter.py:60
   📁 src\infrastructure\adapters\outlook\outlook_adapter.py:243
   📁 src\infrastructure\adapters\pop3\pop3_adapter.py:109
   📁 src\infrastructure\adapters\pop3\pop3_email_reader.py:74

⚠️ 重複函數簽名: get_statistics(self)
   📁 src\application\interfaces\email_reader.py:118
   📁 src\application\interfaces\task_queue.py:207
   📁 src\infrastructure\adapters\attachments\attachment_manager.py:313
   📁 src\infrastructure\adapters\database\email_database.py:718
   📁 src\infrastructure\adapters\pop3\pop3_email_reader.py:298
   📁 src\infrastructure\adapters\smtp\smtp_sender.py:304

⚠️ 重複函數簽名: mark_started(self)
   📁 src\data_models\email_models.py:354
   📁 src\data_models\email_models.py:399

⚠️ 重複函數簽名: mark_failed(self, error_message)
   📁 src\data_models\email_models.py:366
   📁 src\data_models\email_models.py:410

⚠️ 重複函數簽名: to_dict(self)
   📁 src\domain\exceptions\base.py:29
   📁 src\infrastructure\config\settings.py:171
   📁 src\infrastructure\parsers\base_parser.py:48
   📁 src\infrastructure\parsers\gtk_parser.py:25

⚠️ 重複函數簽名: get_session(self)
   📁 src\infrastructure\adapters\database\email_database.py:149
   📁 src\infrastructure\adapters\database\models.py:169

⚠️ 重複函數簽名: _safe_read_csv(self, csv_file_path)
   📁 src\infrastructure\adapters\excel\csv_to_excel_converter.py:69
   📁 src\infrastructure\adapters\excel\ft_summary_converter.py:195

⚠️ 重複函數簽名: _is_test_pass(self, test_value, max_limit, min_limit)
   📁 src\infrastructure\adapters\excel\csv_to_excel_converter.py:541
   📁 src\infrastructure\adapters\excel\strategy_b_processor.py:303

⚠️ 重複函數簽名: _is_pure_number(self, value)
   📁 src\infrastructure\adapters\excel\csv_to_excel_converter.py:782
   📁 src\infrastructure\adapters\excel\ft_summary_converter.py:266
   📁 src\infrastructure\adapters\excel\cta\cta_file_manager.py:212

⚠️ 重複函數簽名: _smart_convert_to_number(self, value)
   📁 src\infrastructure\adapters\excel\csv_to_excel_converter.py:802
   📁 src\infrastructure\adapters\excel\ft_summary_converter.py:247
   📁 src\infrastructure\adapters\excel\cta\cta_file_manager.py:232

⚠️ 重複函數簽名: log_processing_time(csv_filename, processing_time, file_size, start_time, end_time, success, error_msg)
   📁 src\infrastructure\adapters\excel\fixed_test.py:15
   📁 src\infrastructure\adapters\excel\cta\cta_integrated_processor.py:94

⚠️ 重複函數簽名: __new__(cls)
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:22
   📁 src\infrastructure\adapters\llm\grok_client.py:45
   📁 src\infrastructure\adapters\llm\grok_parser_factory.py:24
   📁 src\infrastructure\adapters\llm\grok_parsing_classifier.py:59
   📁 src\infrastructure\adapters\llm\grok_smart_parser.py:56
   📁 src\infrastructure\llm\unified_llm_client.py:67
   📁 src\infrastructure\parsers\base_parser.py:225
   📁 src\infrastructure\parsers\llm_parser.py:22

⚠️ 重複函數簽名: classify_ft_files(self, csv_files)
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:140
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:1330

⚠️ 重複函數簽名: classify_eqc_files(self, csv_files)
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:150
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:1334

⚠️ 重複函數簽名: detect_cta_format(self, file_path)
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:348
   📁 src\infrastructure\adapters\excel\ft_eqc_grouping_processor.py:1346

⚠️ 重複函數簽名: _log(self, message)
   📁 src\infrastructure\adapters\excel\ft_summary_generator.py:24
   📁 src\infrastructure\adapters\excel\eqc\code_region_detailed_detector.py:266

⚠️ 重複函數簽名: delete_files_by_extensions(folder_path)
   📁 src\infrastructure\adapters\excel\cta\cta_integrated_processor.py:36
   📁 src\infrastructure\adapters\excel\eqc\eqc_bin1_final_processor.py:62

⚠️ 重複函數簽名: _is_eqc_bin1_file(self, file_path)
   📁 src\infrastructure\adapters\excel\eqc\code_region_detailed_detector.py:75
   📁 src\infrastructure\adapters\excel\eqc\eqc_simple_detector.py:232

⚠️ 重複函數簽名: _is_numeric(self, value)
   📁 src\infrastructure\adapters\excel\eqc\code_region_detailed_detector.py:210
   📁 src\infrastructure\adapters\excel\eqc\eqc_simple_detector.py:454

⚠️ 重複函數簽名: get_source_paths(self, pd, lot, mo)
   📁 src\infrastructure\adapters\file_handlers\base_file_handler.py:45
   📁 src\infrastructure\adapters\file_handlers\etd_file_handler.py:25
   📁 src\infrastructure\adapters\file_handlers\gtk_file_handler.py:26
   📁 src\infrastructure\adapters\file_handlers\jcet_file_handler.py:26
   📁 src\infrastructure\adapters\file_handlers\nfme_file_handler.py:26
   📁 src\infrastructure\adapters\file_handlers\xaht_file_handler.py:25

⚠️ 重複函數簽名: get_file_patterns(self, mo, lot, pd)
   📁 src\infrastructure\adapters\file_handlers\base_file_handler.py:62
   📁 src\infrastructure\adapters\file_handlers\etd_file_handler.py:44
   📁 src\infrastructure\adapters\file_handlers\gtk_file_handler.py:45
   📁 src\infrastructure\adapters\file_handlers\jcet_file_handler.py:55
   📁 src\infrastructure\adapters\file_handlers\nfme_file_handler.py:39
   📁 src\infrastructure\adapters\file_handlers\xaht_file_handler.py:34

⚠️ 重複函數簽名: _copy_by_mo(self, source_path, dest_path, mo)
   📁 src\infrastructure\adapters\file_handlers\base_file_handler.py:193
   📁 src\infrastructure\adapters\file_handlers\nfme_file_handler.py:53

⚠️ 重複函數簽名: _supports_folder_copy(self)
   📁 src\infrastructure\adapters\file_handlers\base_file_handler.py:361
   📁 src\infrastructure\adapters\file_handlers\etd_file_handler.py:59
   📁 src\infrastructure\adapters\file_handlers\gtk_file_handler.py:69
   📁 src\infrastructure\adapters\file_handlers\jcet_file_handler.py:68
   📁 src\infrastructure\adapters\file_handlers\nfme_file_handler.py:49
   📁 src\infrastructure\adapters\file_handlers\xaht_file_handler.py:47

⚠️ 重複函數簽名: copy_files(self, file_name, file_temp, pd, lot)
   📁 src\infrastructure\adapters\file_handlers\etd_file_handler.py:63
   📁 src\infrastructure\adapters\file_handlers\gtk_file_handler.py:73

⚠️ 重複函數簽名: _validate_config(self)
   📁 src\infrastructure\adapters\llm\grok_client.py:79
   📁 src\infrastructure\adapters\notification\line_notification_service.py:42

⚠️ 重複函數簽名: test_connection(self)
   📁 src\infrastructure\adapters\llm\grok_client.py:274
   📁 src\infrastructure\adapters\pop3\pop3_adapter.py:324
   📁 src\infrastructure\adapters\smtp\smtp_sender.py:267
   📁 src\infrastructure\llm\ollama_client.py:63

⚠️ 重複函數簽名: get_status(self)
   📁 src\infrastructure\adapters\llm\grok_client.py:301
   📁 src\presentation\api\services\file_cleanup_scheduler.py:113
   📁 src\services\scheduler.py:157

⚠️ 重複函數簽名: parse_email_smart(self, email_data)
   📁 src\infrastructure\adapters\llm\grok_parser_factory.py:88
   📁 src\infrastructure\adapters\llm\grok_smart_parser.py:140

⚠️ 重複函數簽名: batch_parse_emails(self, emails)
   📁 src\infrastructure\adapters\llm\grok_parser_factory.py:227
   📁 src\infrastructure\parsers\base_parser.py:465

⚠️ 重複函數簽名: vendor_code(self)
   📁 src\infrastructure\adapters\llm\grok_smart_parser.py:84
   📁 src\infrastructure\parsers\base_parser.py:89
   📁 src\infrastructure\parsers\etd_parser.py:55
   📁 src\infrastructure\parsers\gtk_parser.py:71
   📁 src\infrastructure\parsers\jcet_parser.py:58
   📁 src\infrastructure\parsers\lingsen_parser.py:52
   📁 src\infrastructure\parsers\llm_parser.py:52
   📁 src\infrastructure\parsers\llm_parser.py:212
   📁 src\infrastructure\parsers\xaht_parser.py:51

⚠️ 重複函數簽名: vendor_name(self)
   📁 src\infrastructure\adapters\llm\grok_smart_parser.py:89
   📁 src\infrastructure\parsers\base_parser.py:95
   📁 src\infrastructure\parsers\etd_parser.py:60
   📁 src\infrastructure\parsers\gtk_parser.py:76
   📁 src\infrastructure\parsers\jcet_parser.py:63
   📁 src\infrastructure\parsers\lingsen_parser.py:57
   📁 src\infrastructure\parsers\llm_parser.py:57
   📁 src\infrastructure\parsers\llm_parser.py:217
   📁 src\infrastructure\parsers\xaht_parser.py:56

⚠️ 重複函數簽名: supported_patterns(self)
   📁 src\infrastructure\adapters\llm\grok_smart_parser.py:94
   📁 src\infrastructure\parsers\base_parser.py:101
   📁 src\infrastructure\parsers\etd_parser.py:65
   📁 src\infrastructure\parsers\gtk_parser.py:81
   📁 src\infrastructure\parsers\jcet_parser.py:68
   📁 src\infrastructure\parsers\lingsen_parser.py:62
   📁 src\infrastructure\parsers\llm_parser.py:62
   📁 src\infrastructure\parsers\llm_parser.py:222
   📁 src\infrastructure\parsers\xaht_parser.py:61

⚠️ 重複函數簽名: identify_vendor(self, email_data)
   📁 src\infrastructure\adapters\llm\grok_smart_parser.py:101
   📁 src\infrastructure\parsers\base_parser.py:106
   📁 src\infrastructure\parsers\base_parser.py:323
   📁 src\infrastructure\parsers\etd_parser.py:69
   📁 src\infrastructure\parsers\gtk_parser.py:85
   📁 src\infrastructure\parsers\jcet_parser.py:72
   📁 src\infrastructure\parsers\lingsen_parser.py:66
   📁 src\infrastructure\parsers\llm_parser.py:66
   📁 src\infrastructure\parsers\llm_parser.py:226
   📁 src\infrastructure\parsers\xaht_parser.py:65

⚠️ 重複函數簽名: parse_email(self, context)
   📁 src\infrastructure\adapters\llm\grok_smart_parser.py:130
   📁 src\infrastructure\parsers\base_parser.py:111
   📁 src\infrastructure\parsers\etd_parser.py:110
   📁 src\infrastructure\parsers\gtk_parser.py:127
   📁 src\infrastructure\parsers\jcet_parser.py:135
   📁 src\infrastructure\parsers\lingsen_parser.py:136
   📁 src\infrastructure\parsers\llm_parser.py:115
   📁 src\infrastructure\parsers\llm_parser.py:263
   📁 src\infrastructure\parsers\xaht_parser.py:125

⚠️ 重複函數簽名: get_outlook_version(self)
   📁 src\infrastructure\adapters\outlook\outlook_adapter.py:70
   📁 src\infrastructure\adapters\outlook\outlook_adapter.py:247

⚠️ 重複函數簽名: is_monitoring(self)
   📁 src\infrastructure\adapters\outlook\outlook_adapter.py:144
   📁 src\infrastructure\adapters\outlook\outlook_adapter.py:573

⚠️ 重複函數簽名: get_client_info(self)
   📁 src\infrastructure\llm\ollama_client.py:49
   📁 src\infrastructure\llm\unified_llm_client.py:121

⚠️ 重複函數簽名: _call_ollama(self, prompt)
   📁 src\infrastructure\llm\ollama_client.py:85
   📁 src\infrastructure\llm\unified_llm_client.py:248

⚠️ 重複函數簽名: format(self, record)
   📁 src\infrastructure\logging\logger_manager.py:147
   📁 src\infrastructure\logging\logger_manager.py:180

⚠️ 重複函數簽名: can_parse(self, email_data)
   📁 src\infrastructure\parsers\base_parser.py:115
   📁 src\infrastructure\parsers\llm_parser.py:182
   📁 src\infrastructure\parsers\llm_parser.py:320

⚠️ 重複函數簽名: get_confidence_score(self, email_data)
   📁 src\infrastructure\parsers\base_parser.py:123
   📁 src\infrastructure\parsers\llm_parser.py:186
   📁 src\infrastructure\parsers\llm_parser.py:325

⚠️ 重複函數簽名: get_parser(self, vendor_code)
   📁 src\infrastructure\parsers\base_parser.py:254
   📁 src\infrastructure\parsers\base_parser.py:369

⚠️ 重複函數簽名: get_parser_info(self)
   📁 src\infrastructure\parsers\etd_parser.py:314
   📁 src\infrastructure\parsers\gtk_parser.py:453
   📁 src\infrastructure\parsers\jcet_parser.py:576
   📁 src\infrastructure\parsers\lingsen_parser.py:261
   📁 src\infrastructure\parsers\xaht_parser.py:354

⚠️ 重複函數簽名: get_eqc_processing_service()
   📁 src\presentation\api\ft_eqc_api.py:131
   📁 src\presentation\api\services\eqc_processing_service.py:826

⚠️ 重複函數簽名: get_file_management_service()
   📁 src\presentation\api\ft_eqc_api.py:135
   📁 src\presentation\api\services\file_management_service.py:696

⚠️ 重複函數簽名: get_cleanup_service()
   📁 src\presentation\api\ft_eqc_api.py:139
   📁 src\presentation\api\services\cleanup_service.py:366

⚠️ 重複函數簽名: validate_folder_path(cls, v)
   📁 src\presentation\api\models.py:21
   📁 src\presentation\api\models.py:209
   📁 src\presentation\api\models.py:297
   📁 src\presentation\api\models.py:365
   📁 src\presentation\api\models.py:412
   📁 src\presentation\api\models.py:534
   📁 src\presentation\api\models.py:1033

⚠️ 重複函數簽名: format_file_size(size_bytes)
   📁 src\presentation\api\services\api_utils.py:566
   📁 src\presentation\api\utils\api_utils.py:138

⚠️ 重複函數簽名: manual_cleanup(self, target_directories, retention_hours)
   📁 src\presentation\api\services\file_cleanup_scheduler.py:20
   📁 src\services\scheduler.py:109


🏗️ 分析類結構重複
==================================================
⚠️ 重複類結構: StandardEQCProcessor[__init__, _execute_dual_search_only, _execute_final_excel_conversion, _execute_inseqcrtdata2_step1234_processing, _execute_step4_code_matching_debug, _execute_step5_testflow_generation, _execute_step6_excel_generatiion, _generate_eqctotaldata, _generate_final_report, _generate_standard_report, _rename_step6_to_final, _select_latest_file_by_timestamp, _select_optimal_csv_file, _setup_logger, process_code_comparison_pipeline, process_from_stage2_only, process_standard_eqc_pipeline, view_eqctotaldata_structure]
   📁 old_processors\eqc_standard_processor.py:34
   📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py:34

⚠️ 重複類結構: FTSummaryProcessRequest[validate_folder_path]
   📁 src\presentation\api\models.py:346
   📁 src\presentation\api\models.py:1014


📦 分析導入模式
==================================================
⚠️ 發現可能的重複導入模式:
   模式: ('src.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor.EQCBin1FinalProcessor', 'src.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor.EQCBin1FinalProcessor', 'src.infrastructure.adapters.excel.ft_eqc_grouping_processor.CSVFileDiscovery')... (4 個導入)
     📁 analysis_examples\analyze_temp_failure.py
     📁 nouse\analyze_temp_failure.py

   模式: ('src.infrastructure.adapters.excel.csv_to_excel_converter.CsvToExcelConverter', 'src.infrastructure.adapters.excel.eqc.code_region_detailed_detector.CodeRegionDetailedDetector', 'src.infrastructure.adapters.excel.eqc.eqc_dual_search_corrected.EQCDualSearchCorrected')... (9 個導入)
     📁 old_processors\eqc_standard_processor.py
     📁 src\infrastructure\adapters\excel\eqc\processors\eqc_standard_processor.py

   模式: ('src.data_models.email_models.EmailData', 'src.data_models.email_models.EmailParsingResult', 'src.data_models.email_models.VendorIdentificationResult')... (6 個導入)
     📁 src\infrastructure\parsers\etd_parser.py
     📁 src\infrastructure\parsers\lingsen_parser.py
     📁 src\infrastructure\parsers\xaht_parser.py

   模式: ('src.data_models.email_models.EmailData', 'src.data_models.email_models.EmailParsingResult', 'src.data_models.email_models.VendorIdentificationResult')... (7 個導入)
     📁 src\infrastructure\parsers\gtk_parser.py
     📁 src\infrastructure\parsers\jcet_parser.py


🔍 分析代碼相似性
==================================================
⚠️ 發現可能的相似代碼塊:
   src/application/services/unified_email_processor.py ↔ src/infrastructure/adapters/email_inbox/email_sync_service.py: 28 行相同代碼
   src/application/services/unified_email_processor.py ↔ src/presentation/web/api/parser_api.py: 20 行相同代碼
   src/infrastructure/adapters/email_inbox/email_sync_service.py ↔ src/presentation/web/api/parser_api.py: 32 行相同代碼
   email_inbox_app.py ↔ src/application/services/unified_email_processor.py: 16 行相同代碼
   email_inbox_app.py ↔ src/infrastructure/adapters/email_inbox/email_sync_service.py: 31 行相同代碼
   email_inbox_app.py ↔ src/presentation/web/api/parser_api.py: 47 行相同代碼

🚀 分析服務初始化模式
==================================================
📊 服務初始化統計:
   模式 ('EmailDatabase()',): 3 個文件
     📁 debug_temp\fix_database_encoding.py
     📁 src\infrastructure\adapters\web_api\email_web_service.py
     📁 src\presentation\web\api\attachment_api.py

✅ 服務初始化模式正常

================================================================================
🎯 深度分析結果總結
  函數簽名分析: ❌ 發現問題
  類結構分析: ❌ 發現問題
  導入模式分析: ❌ 發現問題
  代碼相似性分析: ❌ 發現問題
  服務初始化分析: ✅ 通過

❌ 發現需要進一步檢查的問題