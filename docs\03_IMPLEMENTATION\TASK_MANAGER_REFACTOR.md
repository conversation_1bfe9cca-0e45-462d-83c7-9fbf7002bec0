# 任務管理器重構指南

## 📋 概覽

本文檔提供現有併發任務管理器的完整重構方案，設計異步任務佇列和調度器，實現任務優先級管理、超時處理、狀態追蹤和錯誤恢復機制。

## 🎯 核心目標

- **真正異步調度**：基於 asyncio 的非阻塞任務調度
- **智能優先級管理**：動態優先級調整和任務排序
- **完整狀態追蹤**：任務全生命週期監控
- **強化錯誤恢復**：自動重試和故障恢復機制
- **高效資源管理**：內存和計算資源優化使用

## 🏗️ 現有任務管理分析

### 當前任務佇列接口分析

基於 `src/application/interfaces/task_queue.py` 的現有設計：

```python
# 現有接口優點：
CURRENT_STRENGTHS = {
    "well_defined_interface": "清晰的抽象基類定義",
    "comprehensive_status": "完整的任務狀態枚舉",
    "priority_support": "內建優先級支援", 
    "error_handling": "基礎錯誤處理機制",
    "statistics_tracking": "統計資訊收集"
}

# 需要改進的地方：
IMPROVEMENT_AREAS = {
    "async_implementation": "缺乏真正的異步實現",
    "advanced_scheduling": "缺少高級調度策略",
    "resource_management": "資源管理不足",
    "monitoring_alerts": "缺少監控和告警",
    "persistence_strategy": "持久化策略有限"
}
```

## 🚀 重構後的任務管理架構

### 1. 增強的任務模型

```python
import asyncio
import uuid
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field, asdict
from enum import Enum, auto
import logging

class TaskStatus(Enum):
    """增強的任務狀態"""
    PENDING = "pending"
    QUEUED = "queued"
    PROCESSING = "processing"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"
    EXPIRED = "expired"

class TaskPriority(Enum):
    """動態任務優先級"""
    CRITICAL = 100    # 關鍵任務
    HIGH = 75        # 高優先級
    MEDIUM = 50      # 中等優先級  
    LOW = 25         # 低優先級
    BACKGROUND = 1   # 背景任務

class TaskType(Enum):
    """任務類型分類"""
    EMAIL_PROCESSING = "email_processing"
    FILE_PROCESSING = "file_processing"
    NOTIFICATION = "notification"
    DATABASE_OPERATION = "database_operation"
    SYSTEM_MAINTENANCE = "system_maintenance"

@dataclass
class TaskContext:
    """任務上下文資訊"""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    source_component: Optional[str] = None
    correlation_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TaskConfiguration:
    """任務配置"""
    timeout_seconds: float = 300.0
    max_retries: int = 3
    retry_delay_seconds: float = 1.0
    retry_backoff_multiplier: float = 2.0
    priority_decay_rate: float = 0.1  # 優先級衰減率
    resource_requirements: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)  # 任務依賴

@dataclass
class TaskResult:
    """任務執行結果"""
    success: bool
    result_data: Any = None
    error_message: Optional[str] = None
    error_type: Optional[str] = None
    execution_time_seconds: float = 0.0
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    output_artifacts: List[str] = field(default_factory=list)

@dataclass
class EnhancedTaskInfo:
    """增強的任務資訊"""
    # 基本資訊
    task_id: str
    task_type: TaskType
    status: TaskStatus
    priority: TaskPriority
    
    # 時間戳
    created_at: float
    queued_at: Optional[float] = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    expires_at: Optional[float] = None
    
    # 執行資訊
    retry_count: int = 0
    execution_history: List[Dict[str, Any]] = field(default_factory=list)
    current_worker_id: Optional[str] = None
    
    # 配置和上下文
    configuration: TaskConfiguration = field(default_factory=TaskConfiguration)
    context: TaskContext = field(default_factory=TaskContext)
    
    # 結果
    result: Optional[TaskResult] = None
    
    # 任務數據 (序列化後的)
    task_data: str = ""  # JSON序列化的任務數據
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        data = asdict(self)
        # 處理枚舉類型
        data['task_type'] = self.task_type.value
        data['status'] = self.status.value  
        data['priority'] = self.priority.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnhancedTaskInfo':
        """從字典創建實例"""
        # 重建枚舉類型
        data['task_type'] = TaskType(data['task_type'])
        data['status'] = TaskStatus(data['status'])
        data['priority'] = TaskPriority(data['priority'])
        
        # 重建嵌套對象
        if 'configuration' in data and isinstance(data['configuration'], dict):
            data['configuration'] = TaskConfiguration(**data['configuration'])
        if 'context' in data and isinstance(data['context'], dict):
            data['context'] = TaskContext(**data['context'])
        if 'result' in data and isinstance(data['result'], dict):
            data['result'] = TaskResult(**data['result'])
            
        return cls(**data)
```

### 2. 智能任務調度器

```python
import heapq
import asyncio
from collections import defaultdict, deque
from typing import Dict, List, Set, Optional, Callable, Any
import weakref

class TaskScheduler:
    """智能任務調度器"""
    
    def __init__(self, max_concurrent_tasks: int = 50):
        self.logger = logging.getLogger(__name__)
        self.max_concurrent_tasks = max_concurrent_tasks
        
        # 任務隊列 (使用優先級堆)
        self._priority_queue: List[tuple] = []  # (priority, created_at, task_info)
        self._task_registry: Dict[str, EnhancedTaskInfo] = {}
        
        # 執行中的任務
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._worker_pool: Set[str] = set()
        
        # 依賴關係管理
        self._dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self._waiting_for_dependencies: Dict[str, Set[str]] = defaultdict(set)
        
        # 調度統計
        self._stats = {
            'total_scheduled': 0,
            'total_completed': 0,
            'total_failed': 0,
            'total_cancelled': 0,
            'avg_wait_time': 0.0,
            'avg_execution_time': 0.0
        }
        
        # 調度策略配置
        self._scheduling_config = {
            'priority_boost_threshold': 300.0,  # 等待超過5分鐘提升優先級
            'starvation_prevention': True,      # 防止低優先級任務餓死
            'load_balancing': True,             # 負載均衡
            'dependency_timeout': 3600.0        # 依賴等待超時 (1小時)
        }
        
        # 啟動調度器
        self._scheduler_task = None
        self._running = False
    
    async def start(self):
        """啟動調度器"""
        if self._running:
            return
            
        self._running = True
        self._scheduler_task = asyncio.create_task(self._scheduling_loop())
        self.logger.info("任務調度器已啟動")
    
    async def stop(self):
        """停止調度器"""
        self._running = False
        
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有運行中的任務
        for task in self._running_tasks.values():
            task.cancel()
        
        if self._running_tasks:
            await asyncio.gather(*self._running_tasks.values(), return_exceptions=True)
        
        self.logger.info("任務調度器已停止")
    
    async def schedule_task(
        self, 
        task_func: Callable,
        task_args: tuple = (),
        task_kwargs: Dict[str, Any] = None,
        task_type: TaskType = TaskType.EMAIL_PROCESSING,
        priority: TaskPriority = TaskPriority.MEDIUM,
        configuration: Optional[TaskConfiguration] = None,
        context: Optional[TaskContext] = None
    ) -> str:
        """調度新任務"""
        task_kwargs = task_kwargs or {}
        configuration = configuration or TaskConfiguration()
        context = context or TaskContext()
        
        # 創建任務資訊
        task_id = str(uuid.uuid4())
        current_time = time.time()
        
        # 序列化任務數據
        task_data = {
            'func_name': task_func.__name__,
            'args': task_args,
            'kwargs': task_kwargs
        }
        
        task_info = EnhancedTaskInfo(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.PENDING,
            priority=priority,
            created_at=current_time,
            configuration=configuration,
            context=context,
            task_data=json.dumps(task_data, default=str)
        )
        
        # 設置過期時間
        if configuration.timeout_seconds > 0:
            task_info.expires_at = current_time + configuration.timeout_seconds
        
        # 註冊任務
        self._task_registry[task_id] = task_info
        
        # 檢查依賴關係
        if configuration.dependencies:
            await self._handle_task_dependencies(task_info)
        else:
            # 直接加入調度隊列
            await self._enqueue_task(task_info)
        
        self._stats['total_scheduled'] += 1
        self.logger.info(f"任務已調度: {task_id} ({task_type.value}, {priority.value})")
        
        return task_id
    
    async def _handle_task_dependencies(self, task_info: EnhancedTaskInfo):
        """處理任務依賴關係"""
        unresolved_deps = set()
        
        for dep_task_id in task_info.configuration.dependencies:
            if dep_task_id not in self._task_registry:
                self.logger.warning(f"依賴任務不存在: {dep_task_id}")
                continue
            
            dep_task = self._task_registry[dep_task_id]
            if dep_task.status not in [TaskStatus.COMPLETED]:
                unresolved_deps.add(dep_task_id)
                self._dependency_graph[dep_task_id].add(task_info.task_id)
        
        if unresolved_deps:
            self._waiting_for_dependencies[task_info.task_id] = unresolved_deps
            task_info.status = TaskStatus.PENDING
            self.logger.info(f"任務等待依賴: {task_info.task_id} -> {unresolved_deps}")
        else:
            await self._enqueue_task(task_info)
    
    async def _enqueue_task(self, task_info: EnhancedTaskInfo):
        """將任務加入調度隊列"""
        task_info.status = TaskStatus.QUEUED
        task_info.queued_at = time.time()
        
        # 計算調度優先級 (考慮等待時間)
        wait_time = task_info.queued_at - task_info.created_at
        adjusted_priority = self._calculate_adjusted_priority(task_info.priority, wait_time)
        
        # 加入優先級隊列
        heapq.heappush(self._priority_queue, (
            -adjusted_priority,  # 負值使其成為最大堆
            task_info.created_at,
            task_info.task_id
        ))
        
        self.logger.debug(f"任務入隊: {task_info.task_id} (調整後優先級: {adjusted_priority})")
    
    def _calculate_adjusted_priority(self, base_priority: TaskPriority, wait_time: float) -> float:
        """計算調整後的優先級"""
        base_value = base_priority.value
        
        # 防止餓死：等待時間越長，優先級提升越多
        if self._scheduling_config['starvation_prevention'] and wait_time > self._scheduling_config['priority_boost_threshold']:
            boost = min(wait_time / 60.0, 50)  # 最多提升50點
            return min(base_value + boost, TaskPriority.CRITICAL.value)
        
        return base_value
    
    async def _scheduling_loop(self):
        """主調度循環"""
        while self._running:
            try:
                await self._process_scheduling_cycle()
                await asyncio.sleep(0.1)  # 避免過度消耗CPU
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"調度循環錯誤: {e}")
                await asyncio.sleep(1.0)
    
    async def _process_scheduling_cycle(self):
        """處理一次調度週期"""
        # 清理已完成的任務
        await self._cleanup_completed_tasks()
        
        # 檢查過期任務
        await self._handle_expired_tasks()
        
        # 調度新任務
        available_slots = self.max_concurrent_tasks - len(self._running_tasks)
        
        while available_slots > 0 and self._priority_queue:
            # 從隊列中取出最高優先級任務
            try:
                _, _, task_id = heapq.heappop(self._priority_queue)
            except IndexError:
                break
            
            if task_id not in self._task_registry:
                continue
            
            task_info = self._task_registry[task_id]
            
            # 檢查任務是否仍然有效
            if task_info.status != TaskStatus.QUEUED:
                continue
            
            # 啟動任務執行
            await self._execute_task(task_info)
            available_slots -= 1
    
    async def _execute_task(self, task_info: EnhancedTaskInfo):
        """執行任務"""
        task_info.status = TaskStatus.PROCESSING
        task_info.started_at = time.time()
        
        # 創建工作者ID
        worker_id = f"worker_{len(self._running_tasks)}"
        task_info.current_worker_id = worker_id
        self._worker_pool.add(worker_id)
        
        # 反序列化任務數據
        try:
            task_data = json.loads(task_info.task_data)
        except json.JSONDecodeError as e:
            await self._handle_task_error(task_info, f"任務數據反序列化失敗: {e}")
            return
        
        # 創建異步任務
        async_task = asyncio.create_task(
            self._run_task_with_monitoring(task_info, task_data)
        )
        self._running_tasks[task_info.task_id] = async_task
        
        self.logger.info(f"任務開始執行: {task_info.task_id} (工作者: {worker_id})")
    
    async def _run_task_with_monitoring(self, task_info: EnhancedTaskInfo, task_data: Dict[str, Any]):
        """運行任務並監控執行"""
        start_time = time.time()
        
        try:
            # 模拟任務執行 (實際應用中需要動態調用函數)
            # 這裡需要根據實際需求實現函數調用機制
            result = await self._invoke_task_function(task_data)
            
            # 記錄成功結果
            execution_time = time.time() - start_time
            task_result = TaskResult(
                success=True,
                result_data=result,
                execution_time_seconds=execution_time
            )
            
            await self._handle_task_completion(task_info, task_result)
            
        except asyncio.CancelledError:
            await self._handle_task_cancellation(task_info)
            
        except Exception as e:
            execution_time = time.time() - start_time
            task_result = TaskResult(
                success=False,
                error_message=str(e),
                error_type=type(e).__name__,
                execution_time_seconds=execution_time
            )
            
            await self._handle_task_failure(task_info, task_result)
    
    async def _invoke_task_function(self, task_data: Dict[str, Any]) -> Any:
        """調用任務函數 (需要根據實際需求實現)"""
        # 這是一個簡化的實現，實際應用中需要：
        # 1. 函數註冊機制
        # 2. 安全的函數調用
        # 3. 參數驗證
        
        func_name = task_data['func_name']
        args = task_data.get('args', ())
        kwargs = task_data.get('kwargs', {})
        
        # 模擬異步執行
        await asyncio.sleep(0.1)
        return f"Task {func_name} completed with args {args} and kwargs {kwargs}"
    
    async def _handle_task_completion(self, task_info: EnhancedTaskInfo, result: TaskResult):
        """處理任務完成"""
        task_info.status = TaskStatus.COMPLETED
        task_info.completed_at = time.time()
        task_info.result = result
        
        # 更新統計
        self._stats['total_completed'] += 1
        wait_time = task_info.started_at - task_info.created_at
        self._update_average_stat('avg_wait_time', wait_time)
        self._update_average_stat('avg_execution_time', result.execution_time_seconds)
        
        # 處理依賴的任務
        await self._resolve_task_dependencies(task_info.task_id)
        
        self.logger.info(f"任務完成: {task_info.task_id} (耗時: {result.execution_time_seconds:.2f}s)")
    
    async def _handle_task_failure(self, task_info: EnhancedTaskInfo, result: TaskResult):
        """處理任務失敗"""
        task_info.execution_history.append({
            'attempt': task_info.retry_count + 1,
            'timestamp': time.time(),
            'error': result.error_message,
            'execution_time': result.execution_time_seconds
        })
        
        # 檢查是否需要重試
        if task_info.retry_count < task_info.configuration.max_retries:
            await self._schedule_task_retry(task_info)
        else:
            # 最終失敗
            task_info.status = TaskStatus.FAILED
            task_info.completed_at = time.time()
            task_info.result = result
            
            self._stats['total_failed'] += 1
            self.logger.error(f"任務最終失敗: {task_info.task_id} - {result.error_message}")
    
    async def _schedule_task_retry(self, task_info: EnhancedTaskInfo):
        """調度任務重試"""
        task_info.retry_count += 1
        task_info.status = TaskStatus.RETRYING
        
        # 計算重試延遲
        delay = (task_info.configuration.retry_delay_seconds * 
                (task_info.configuration.retry_backoff_multiplier ** (task_info.retry_count - 1)))
        
        self.logger.info(f"任務重試調度: {task_info.task_id} (第{task_info.retry_count}次，延遲{delay:.1f}s)")
        
        # 延遲後重新加入隊列
        asyncio.create_task(self._delayed_requeue(task_info, delay))
    
    async def _delayed_requeue(self, task_info: EnhancedTaskInfo, delay: float):
        """延遲重新加入隊列"""
        await asyncio.sleep(delay)
        await self._enqueue_task(task_info)
    
    async def _resolve_task_dependencies(self, completed_task_id: str):
        """解決任務依賴關係"""
        if completed_task_id not in self._dependency_graph:
            return
        
        dependent_task_ids = self._dependency_graph[completed_task_id]
        
        for dep_task_id in dependent_task_ids:
            if dep_task_id not in self._waiting_for_dependencies:
                continue
            
            # 移除已完成的依賴
            self._waiting_for_dependencies[dep_task_id].discard(completed_task_id)
            
            # 如果所有依賴都已解決，將任務加入隊列
            if not self._waiting_for_dependencies[dep_task_id]:
                del self._waiting_for_dependencies[dep_task_id]
                
                if dep_task_id in self._task_registry:
                    task_info = self._task_registry[dep_task_id]
                    await self._enqueue_task(task_info)
                    self.logger.info(f"依賴解決，任務入隊: {dep_task_id}")
        
        # 清理依賴圖
        del self._dependency_graph[completed_task_id]
    
    async def _cleanup_completed_tasks(self):
        """清理已完成的任務"""
        completed_task_ids = []
        
        for task_id, async_task in self._running_tasks.items():
            if async_task.done():
                completed_task_ids.append(task_id)
                
                # 清理工作者
                if task_id in self._task_registry:
                    worker_id = self._task_registry[task_id].current_worker_id
                    if worker_id:
                        self._worker_pool.discard(worker_id)
        
        for task_id in completed_task_ids:
            del self._running_tasks[task_id]
    
    async def _handle_expired_tasks(self):
        """處理過期任務"""
        current_time = time.time()
        expired_task_ids = []
        
        for task_id, task_info in self._task_registry.items():
            if (task_info.expires_at and 
                current_time > task_info.expires_at and 
                task_info.status in [TaskStatus.PENDING, TaskStatus.QUEUED]):
                
                expired_task_ids.append(task_id)
        
        for task_id in expired_task_ids:
            task_info = self._task_registry[task_id]
            task_info.status = TaskStatus.EXPIRED
            task_info.completed_at = current_time
            
            self.logger.warning(f"任務過期: {task_id}")
    
    def _update_average_stat(self, stat_name: str, new_value: float):
        """更新平均統計值"""
        current_avg = self._stats[stat_name]
        total_count = self._stats['total_completed']
        
        # 計算加權平均
        self._stats[stat_name] = ((current_avg * (total_count - 1)) + new_value) / total_count
    
    # 查詢和管理方法
    async def get_task_info(self, task_id: str) -> Optional[EnhancedTaskInfo]:
        """獲取任務資訊"""
        return self._task_registry.get(task_id)
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        if task_id not in self._task_registry:
            return False
        
        task_info = self._task_registry[task_id]
        
        if task_info.status == TaskStatus.PROCESSING:
            # 取消運行中的任務
            if task_id in self._running_tasks:
                self._running_tasks[task_id].cancel()
        
        task_info.status = TaskStatus.CANCELLED
        task_info.completed_at = time.time()
        
        self._stats['total_cancelled'] += 1
        self.logger.info(f"任務已取消: {task_id}")
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取調度器統計資訊"""
        return {
            **self._stats,
            'queue_size': len(self._priority_queue),
            'running_tasks': len(self._running_tasks),
            'waiting_for_dependencies': len(self._waiting_for_dependencies),
            'total_tasks': len(self._task_registry)
        }
```

### 3. 高級任務佇列實現

```python
import aiosqlite
import pickle
from pathlib import Path

class EnhancedAsyncTaskQueue:
    """增強的異步任務佇列實現"""
    
    def __init__(
        self, 
        scheduler: TaskScheduler,
        persistence_enabled: bool = True,
        db_path: str = "task_queue.db"
    ):
        self.scheduler = scheduler
        self.logger = logging.getLogger(__name__)
        self.persistence_enabled = persistence_enabled
        self.db_path = db_path
        
        # 任務函數註冊表
        self._task_functions: Dict[str, Callable] = {}
        
        # 初始化數據庫
        if persistence_enabled:
            asyncio.create_task(self._initialize_database())
    
    async def _initialize_database(self):
        """初始化持久化數據庫"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS task_queue (
                    task_id TEXT PRIMARY KEY,
                    task_data BLOB,
                    status TEXT,
                    priority INTEGER,
                    created_at REAL,
                    updated_at REAL
                )
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_status_priority 
                ON task_queue(status, priority DESC)
            """)
            
            await db.commit()
        
        self.logger.info("任務佇列數據庫已初始化")
    
    def register_task_function(self, name: str, func: Callable):
        """註冊任務函數"""
        self._task_functions[name] = func
        self.logger.info(f"任務函數已註冊: {name}")
    
    async def enqueue(
        self,
        task_name: str,
        *args,
        priority: TaskPriority = TaskPriority.MEDIUM,
        task_type: TaskType = TaskType.EMAIL_PROCESSING,
        configuration: Optional[TaskConfiguration] = None,
        context: Optional[TaskContext] = None,
        **kwargs
    ) -> str:
        """加入任務到佇列"""
        if task_name not in self._task_functions:
            raise ValueError(f"未註冊的任務函數: {task_name}")
        
        task_func = self._task_functions[task_name]
        
        task_id = await self.scheduler.schedule_task(
            task_func=task_func,
            task_args=args,
            task_kwargs=kwargs,
            task_type=task_type,
            priority=priority,
            configuration=configuration,
            context=context
        )
        
        # 持久化任務資訊
        if self.persistence_enabled:
            await self._persist_task(task_id)
        
        return task_id
    
    async def _persist_task(self, task_id: str):
        """持久化任務到數據庫"""
        task_info = await self.scheduler.get_task_info(task_id)
        if not task_info:
            return
        
        try:
            async with aiosqlite.connect(self.db_path) as db:
                task_data = pickle.dumps(task_info.to_dict())
                
                await db.execute("""
                    INSERT OR REPLACE INTO task_queue 
                    (task_id, task_data, status, priority, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    task_id,
                    task_data,
                    task_info.status.value,
                    task_info.priority.value,
                    task_info.created_at,
                    time.time()
                ))
                
                await db.commit()
                
        except Exception as e:
            self.logger.error(f"持久化任務失敗: {e}")
    
    async def dequeue(self) -> Optional[EnhancedTaskInfo]:
        """從佇列取出任務 (由調度器自動處理)"""
        # 此方法由調度器內部使用，外部通常不直接調用
        pass
    
    async def get_task_info(self, task_id: str) -> Optional[EnhancedTaskInfo]:
        """獲取任務資訊"""
        return await self.scheduler.get_task_info(task_id)
    
    async def update_task_status(
        self,
        task_id: str,
        status: TaskStatus,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """更新任務狀態"""
        task_info = await self.scheduler.get_task_info(task_id)
        if not task_info:
            return False
        
        task_info.status = status
        
        if result or error_message:
            task_result = TaskResult(
                success=status == TaskStatus.COMPLETED,
                result_data=result,
                error_message=error_message
            )
            task_info.result = task_result
        
        # 持久化更新
        if self.persistence_enabled:
            await self._persist_task(task_id)
        
        return True
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        return await self.scheduler.cancel_task(task_id)
    
    async def retry_task(self, task_id: str) -> bool:
        """重試任務"""
        task_info = await self.scheduler.get_task_info(task_id)
        if not task_info or task_info.status != TaskStatus.FAILED:
            return False
        
        # 重置任務狀態
        task_info.status = TaskStatus.PENDING
        task_info.retry_count = 0
        task_info.result = None
        
        # 重新調度
        await self.scheduler._enqueue_task(task_info)
        
        return True
    
    def get_queue_size(self) -> int:
        """獲取佇列大小"""
        return len(self.scheduler._priority_queue)
    
    def get_processing_count(self) -> int:
        """獲取處理中的任務數量"""
        return len(self.scheduler._running_tasks)
    
    async def get_pending_tasks(self) -> List[EnhancedTaskInfo]:
        """獲取待處理任務列表"""
        pending_tasks = []
        for task_info in self.scheduler._task_registry.values():
            if task_info.status in [TaskStatus.PENDING, TaskStatus.QUEUED]:
                pending_tasks.append(task_info)
        
        return pending_tasks
    
    async def get_failed_tasks(self) -> List[EnhancedTaskInfo]:
        """獲取失敗任務列表"""
        failed_tasks = []
        for task_info in self.scheduler._task_registry.values():
            if task_info.status == TaskStatus.FAILED:
                failed_tasks.append(task_info)
        
        return failed_tasks
    
    async def clear_completed_tasks(self) -> int:
        """清理已完成任務"""
        completed_task_ids = []
        
        for task_id, task_info in self.scheduler._task_registry.items():
            if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                # 保留最近24小時的任務
                if time.time() - task_info.completed_at > 86400:  # 24小時
                    completed_task_ids.append(task_id)
        
        # 從註冊表中移除
        for task_id in completed_task_ids:
            del self.scheduler._task_registry[task_id]
        
        # 從數據庫中清理
        if self.persistence_enabled and completed_task_ids:
            async with aiosqlite.connect(self.db_path) as db:
                placeholders = ','.join(['?' for _ in completed_task_ids])
                await db.execute(
                    f"DELETE FROM task_queue WHERE task_id IN ({placeholders})",
                    completed_task_ids
                )
                await db.commit()
        
        self.logger.info(f"已清理 {len(completed_task_ids)} 個已完成任務")
        return len(completed_task_ids)
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計資訊"""
        return self.scheduler.get_statistics()
    
    async def start_workers(self, worker_function: Optional[Callable] = None) -> None:
        """啟動工作者 (由調度器處理)"""
        await self.scheduler.start()
    
    async def stop_workers(self) -> None:
        """停止工作者"""
        await self.scheduler.stop()
    
    def is_running(self) -> bool:
        """檢查是否運行中"""
        return self.scheduler._running
```

### 4. 業務集成示例

```python
# 集成到現有的UnifiedEmailProcessor
class TaskManagedEmailProcessor:
    """任務管理的郵件處理器"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("TaskManagedEmailProcessor")
        
        # 初始化任務管理組件
        self.scheduler = TaskScheduler(max_concurrent_tasks=20)
        self.task_queue = EnhancedAsyncTaskQueue(
            scheduler=self.scheduler,
            persistence_enabled=True
        )
        
        # 註冊任務函數
        self._register_task_functions()
        
        # 啟動調度器
        asyncio.create_task(self.task_queue.start_workers())
    
    def _register_task_functions(self):
        """註冊任務處理函數"""
        self.task_queue.register_task_function(
            "process_email_complete",
            self._process_email_task
        )
        
        self.task_queue.register_task_function(
            "process_attachment",
            self._process_attachment_task
        )
        
        self.task_queue.register_task_function(
            "send_notification",
            self._send_notification_task
        )
    
    async def process_email_async(
        self,
        email_data: EmailData,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """異步處理郵件 (使用任務管理)"""
        
        # 創建任務配置
        config = TaskConfiguration(
            timeout_seconds=600.0,  # 10分鐘超時
            max_retries=2,
            retry_delay_seconds=30.0
        )
        
        # 創建任務上下文
        context = TaskContext(
            source_component="EmailProcessor",
            correlation_id=email_data.message_id,
            metadata={
                'sender': email_data.sender,
                'subject': email_data.subject
            }
        )
        
        # 調度郵件處理任務
        task_id = await self.task_queue.enqueue(
            "process_email_complete",
            email_data,
            priority=priority,
            task_type=TaskType.EMAIL_PROCESSING,
            configuration=config,
            context=context
        )
        
        self.logger.info(f"郵件處理任務已調度: {task_id}")
        return task_id
    
    async def _process_email_task(self, email_data: EmailData) -> Dict[str, Any]:
        """實際的郵件處理任務"""
        # 這裡實現具體的郵件處理邏輯
        start_time = time.time()
        
        try:
            # 模擬郵件處理
            await asyncio.sleep(2.0)  # 模擬處理時間
            
            result = {
                'email_id': email_data.message_id,
                'processing_time': time.time() - start_time,
                'status': 'completed',
                'vendor_code': 'TSMC',  # 模擬解析結果
                'pd': 'TEST_PD',
                'mo': 'TEST_MO'
            }
            
            self.logger.info(f"郵件處理完成: {email_data.message_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"郵件處理失敗: {e}")
            raise
    
    async def _process_attachment_task(self, attachment_data: Dict[str, Any]) -> Dict[str, Any]:
        """附件處理任務"""
        # 實現附件處理邏輯
        await asyncio.sleep(1.0)  # 模擬處理時間
        
        return {
            'attachment_id': attachment_data.get('id'),
            'status': 'processed',
            'saved_path': f"/tmp/{attachment_data.get('filename')}"
        }
    
    async def _send_notification_task(self, notification_data: Dict[str, Any]) -> Dict[str, Any]:
        """通知發送任務"""
        # 實現通知發送邏輯
        await asyncio.sleep(0.5)  # 模擬發送時間
        
        return {
            'notification_id': notification_data.get('id'),
            'status': 'sent',
            'recipient': notification_data.get('recipient')
        }
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取任務狀態"""
        task_info = await self.task_queue.get_task_info(task_id)
        if not task_info:
            return None
        
        return {
            'task_id': task_id,
            'status': task_info.status.value,
            'priority': task_info.priority.value,
            'created_at': task_info.created_at,
            'started_at': task_info.started_at,
            'completed_at': task_info.completed_at,
            'retry_count': task_info.retry_count,
            'result': task_info.result.to_dict() if task_info.result else None
        }
    
    async def cancel_email_processing(self, task_id: str) -> bool:
        """取消郵件處理任務"""
        return await self.task_queue.cancel_task(task_id)
    
    async def get_processing_statistics(self) -> Dict[str, Any]:
        """獲取處理統計資訊"""
        return self.task_queue.get_statistics()
    
    async def cleanup(self):
        """清理資源"""
        await self.task_queue.stop_workers()
        await self.task_queue.clear_completed_tasks()
```

### 5. 監控和告警系統

```python
class TaskMonitoringService:
    """任務監控服務"""
    
    def __init__(self, task_queue: EnhancedAsyncTaskQueue):
        self.task_queue = task_queue
        self.logger = logging.getLogger(__name__)
        self.monitoring_active = False
        
        # 告警閾值配置
        self.alert_thresholds = {
            'queue_size_warning': 100,
            'queue_size_critical': 500,
            'failure_rate_warning': 0.1,  # 10%
            'failure_rate_critical': 0.25,  # 25%
            'avg_wait_time_warning': 300.0,  # 5分鐘
            'avg_wait_time_critical': 900.0,  # 15分鐘
        }
        
        # 告警歷史
        self.alert_history = deque(maxlen=1000)
    
    async def start_monitoring(self, interval: float = 30.0):
        """開始監控"""
        self.monitoring_active = True
        self.logger.info("任務監控服務已啟動")
        
        while self.monitoring_active:
            try:
                await self._check_system_health()
                await asyncio.sleep(interval)
            except Exception as e:
                self.logger.error(f"監控檢查失敗: {e}")
                await asyncio.sleep(interval)
    
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring_active = False
        self.logger.info("任務監控服務已停止")
    
    async def _check_system_health(self):
        """檢查系統健康狀態"""
        stats = self.task_queue.get_statistics()
        current_time = time.time()
        
        # 檢查隊列大小
        queue_size = stats.get('queue_size', 0)
        if queue_size >= self.alert_thresholds['queue_size_critical']:
            await self._trigger_alert(
                'CRITICAL',
                'Queue Size',
                f'任務隊列過載: {queue_size} 個任務等待處理'
            )
        elif queue_size >= self.alert_thresholds['queue_size_warning']:
            await self._trigger_alert(
                'WARNING',
                'Queue Size',
                f'任務隊列較高: {queue_size} 個任務等待處理'
            )
        
        # 檢查失敗率
        total_tasks = stats.get('total_completed', 0) + stats.get('total_failed', 0)
        if total_tasks > 0:
            failure_rate = stats.get('total_failed', 0) / total_tasks
            
            if failure_rate >= self.alert_thresholds['failure_rate_critical']:
                await self._trigger_alert(
                    'CRITICAL',
                    'Failure Rate',
                    f'任務失敗率過高: {failure_rate:.1%}'
                )
            elif failure_rate >= self.alert_thresholds['failure_rate_warning']:
                await self._trigger_alert(
                    'WARNING',
                    'Failure Rate',
                    f'任務失敗率較高: {failure_rate:.1%}'
                )
        
        # 檢查平均等待時間
        avg_wait_time = stats.get('avg_wait_time', 0.0)
        if avg_wait_time >= self.alert_thresholds['avg_wait_time_critical']:
            await self._trigger_alert(
                'CRITICAL',
                'Wait Time',
                f'任務平均等待時間過長: {avg_wait_time:.1f}s'
            )
        elif avg_wait_time >= self.alert_thresholds['avg_wait_time_warning']:
            await self._trigger_alert(
                'WARNING',
                'Wait Time',
                f'任務平均等待時間較長: {avg_wait_time:.1f}s'  
            )
    
    async def _trigger_alert(self, level: str, category: str, message: str):
        """觸發告警"""
        alert = {
            'timestamp': time.time(),
            'level': level,
            'category': category,
            'message': message
        }
        
        self.alert_history.append(alert)
        self.logger.warning(f"[{level}] {category}: {message}")
        
        # 這裡可以整合實際的告警系統 (EMAIL, SMS, Slack等)
        # await self._send_alert_notification(alert)
    
    def get_recent_alerts(self, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取最近的告警"""
        return list(self.alert_history)[-limit:]
    
    async def generate_health_report(self) -> Dict[str, Any]:
        """生成健康報告"""
        stats = self.task_queue.get_statistics()
        current_time = time.time()
        
        # 計算健康分數 (0-100)
        health_score = 100
        
        # 隊列大小影響
        queue_size = stats.get('queue_size', 0)
        if queue_size > self.alert_thresholds['queue_size_warning']:
            health_score -= min(30, queue_size / 10)
        
        # 失敗率影響
        total_tasks = stats.get('total_completed', 0) + stats.get('total_failed', 0)
        if total_tasks > 0:
            failure_rate = stats.get('total_failed', 0) / total_tasks
            health_score -= failure_rate * 50
        
        # 等待時間影響
        avg_wait_time = stats.get('avg_wait_time', 0.0)
        if avg_wait_time > self.alert_thresholds['avg_wait_time_warning']:
            health_score -= min(20, avg_wait_time / 60)
        
        health_score = max(0, health_score)
        
        return {
            'timestamp': current_time,
            'health_score': round(health_score, 1),
            'status': self._get_health_status(health_score),
            'statistics': stats,
            'recent_alerts': len([a for a in self.alert_history if current_time - a['timestamp'] < 3600]),
            'recommendations': self._generate_recommendations(stats)
        }
    
    def _get_health_status(self, score: float) -> str:
        """根據分數獲取健康狀態"""
        if score >= 90:
            return 'EXCELLENT'
        elif score >= 70:
            return 'GOOD'
        elif score >= 50:
            return 'WARNING'
        else:
            return 'CRITICAL'
    
    def _generate_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """生成改進建議"""
        recommendations = []
        
        queue_size = stats.get('queue_size', 0)
        if queue_size > 50:
            recommendations.append("考慮增加並發工作者數量以處理隊列積壓")
        
        failure_rate = (stats.get('total_failed', 0) / 
                       max(stats.get('total_completed', 0) + stats.get('total_failed', 0), 1))
        if failure_rate > 0.1:
            recommendations.append("檢查失敗任務的錯誤日誌，優化錯誤處理邏輯")
        
        avg_wait_time = stats.get('avg_wait_time', 0.0)
        if avg_wait_time > 180:
            recommendations.append("優化任務調度策略，減少任務等待時間")
        
        return recommendations
```

## 🧪 測試和驗證

### 1. 任務管理測試套件

```python
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock

class TestTaskManager:
    """任務管理器測試套件"""
    
    @pytest.fixture
    async def task_manager_setup(self):
        """測試設置"""
        scheduler = TaskScheduler(max_concurrent_tasks=5)
        task_queue = EnhancedAsyncTaskQueue(
            scheduler=scheduler,
            persistence_enabled=False  # 測試時不使用持久化
        )
        
        # 註冊測試任務函數
        async def test_task(data: str, delay: float = 0.1):
            await asyncio.sleep(delay)
            return f"processed_{data}"
        
        task_queue.register_task_function("test_task", test_task)
        
        await task_queue.start_workers()
        
        yield scheduler, task_queue
        
        await task_queue.stop_workers()
    
    @pytest.mark.asyncio
    async def test_basic_task_scheduling(self, task_manager_setup):
        """測試基本任務調度"""
        scheduler, task_queue = task_manager_setup
        
        # 調度任務
        task_id = await task_queue.enqueue(
            "test_task",
            "test_data",
            delay=0.1,
            priority=TaskPriority.HIGH
        )
        
        assert task_id is not None
        
        # 等待任務完成
        await asyncio.sleep(0.5)
        
        # 檢查任務狀態
        task_info = await task_queue.get_task_info(task_id)
        assert task_info is not None
        assert task_info.status == TaskStatus.COMPLETED
        assert task_info.result.success is True
        assert task_info.result.result_data == "processed_test_data"
    
    @pytest.mark.asyncio
    async def test_priority_scheduling(self, task_manager_setup):
        """測試優先級調度"""
        scheduler, task_queue = task_manager_setup
        
        # 調度不同優先級的任務
        low_task_id = await task_queue.enqueue(
            "test_task",
            "low",
            delay=0.1,
            priority=TaskPriority.LOW
        )
        
        high_task_id = await task_queue.enqueue(
            "test_task", 
            "high",
            delay=0.1,
            priority=TaskPriority.HIGH
        )
        
        # 等待任務完成
        await asyncio.sleep(0.5)
        
        # 高優先級任務應該先完成
        high_task = await task_queue.get_task_info(high_task_id)
        low_task = await task_queue.get_task_info(low_task_id)
        
        assert high_task.started_at <= low_task.started_at
    
    @pytest.mark.asyncio
    async def test_task_retry_mechanism(self, task_manager_setup):
        """測試任務重試機制"""
        scheduler, task_queue = task_manager_setup
        
        # 註冊會失敗的任務
        fail_count = 0
        async def failing_task():
            nonlocal fail_count
            fail_count += 1
            if fail_count < 3:
                raise Exception(f"Failure {fail_count}")
            return "success_after_retries"
        
        task_queue.register_task_function("failing_task", failing_task)
        
        # 調度任務
        config = TaskConfiguration(max_retries=3, retry_delay_seconds=0.1)
        task_id = await task_queue.enqueue(
            "failing_task",
            configuration=config
        )
        
        # 等待重試完成
        await asyncio.sleep(1.0)
        
        # 檢查最終成功
        task_info = await task_queue.get_task_info(task_id)
        assert task_info.status == TaskStatus.COMPLETED
        assert task_info.retry_count == 2  # 失敗2次後成功
        assert len(task_info.execution_history) == 2
    
    @pytest.mark.asyncio
    async def test_task_cancellation(self, task_manager_setup):
        """測試任務取消"""
        scheduler, task_queue = task_manager_setup
        
        # 調度長時間運行的任務
        task_id = await task_queue.enqueue(
            "test_task",
            "long_running",
            delay=2.0
        )
        
        # 等待任務開始
        await asyncio.sleep(0.1)
        
        # 取消任務
        cancelled = await task_queue.cancel_task(task_id)
        assert cancelled is True
        
        # 檢查任務狀態
        task_info = await task_queue.get_task_info(task_id)
        assert task_info.status == TaskStatus.CANCELLED
    
    @pytest.mark.asyncio
    async def test_concurrent_task_limit(self, task_manager_setup):
        """測試並發任務限制"""
        scheduler, task_queue = task_manager_setup
        
        # 調度超過並發限制的任務
        task_ids = []
        for i in range(10):  # 超過max_concurrent_tasks=5
            task_id = await task_queue.enqueue(
                "test_task",
                f"concurrent_{i}",
                delay=0.5
            )
            task_ids.append(task_id)
        
        # 檢查運行中的任務數量不超過限制
        await asyncio.sleep(0.1)
        running_count = task_queue.get_processing_count()
        assert running_count <= 5
        
        # 等待所有任務完成
        await asyncio.sleep(2.0)
        
        # 檢查所有任務都完成了
        completed_count = 0
        for task_id in task_ids:
            task_info = await task_queue.get_task_info(task_id)
            if task_info.status == TaskStatus.COMPLETED:
                completed_count += 1
        
        assert completed_count == 10
```

### 2. 性能基準測試

```python
class TaskManagerBenchmark:
    """任務管理器性能基準測試"""
    
    @staticmethod
    async def benchmark_task_throughput(task_count: int = 1000):
        """基準測試：任務吞吐量"""
        scheduler = TaskScheduler(max_concurrent_tasks=20)
        task_queue = EnhancedAsyncTaskQueue(
            scheduler=scheduler,
            persistence_enabled=False
        )
        
        # 註冊快速任務
        async def fast_task(data: int):
            return data * 2
        
        task_queue.register_task_function("fast_task", fast_task)
        await task_queue.start_workers()
        
        # 測量調度時間
        start_time = time.time()
        
        task_ids = []
        for i in range(task_count):
            task_id = await task_queue.enqueue("fast_task", i)
            task_ids.append(task_id)
        
        scheduling_time = time.time() - start_time
        
        # 測量完成時間
        completion_start = time.time()
        
        # 等待所有任務完成
        while True:
            stats = task_queue.get_statistics()
            if stats['total_completed'] >= task_count:
                break
            await asyncio.sleep(0.1)
        
        completion_time = time.time() - completion_start
        total_time = time.time() - start_time
        
        await task_queue.stop_workers()
        
        return {
            'task_count': task_count,
            'scheduling_time': scheduling_time,
            'completion_time': completion_time,
            'total_time': total_time,
            'scheduling_throughput': task_count / scheduling_time,
            'execution_throughput': task_count / completion_time,
            'overall_throughput': task_count / total_time
        }
    
    @staticmethod
    async def benchmark_memory_usage():
        """基準測試：記憶體使用"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        scheduler = TaskScheduler(max_concurrent_tasks=10)
        task_queue = EnhancedAsyncTaskQueue(scheduler=scheduler)
        
        # 創建大量任務
        for i in range(5000):
            await task_queue.enqueue("test_task", f"data_{i}")
        
        gc.collect()
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 清理任務
        await task_queue.clear_completed_tasks()
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            'initial_memory_mb': initial_memory,
            'peak_memory_mb': peak_memory,
            'final_memory_mb': final_memory,
            'memory_overhead_mb': peak_memory - initial_memory,
            'memory_cleanup_efficiency': (peak_memory - final_memory) / (peak_memory - initial_memory)
        }
```

## 📊 性能優化建議

### 1. 調度器優化

```python
# 優化配置建議
OPTIMIZED_SCHEDULER_CONFIG = {
    "max_concurrent_tasks": "CPU核心數 * 2",
    "priority_boost_threshold": "根據業務需求調整 (60-300秒)",  
    "starvation_prevention": True,
    "load_balancing": True,
    "task_batch_size": 10,  # 批量處理任務
    "scheduling_interval": 0.05,  # 調度週期優化
}

# 記憶體優化
MEMORY_OPTIMIZATION_CONFIG = {
    "task_registry_cleanup_interval": 3600,  # 1小時清理一次
    "max_task_history_size": 1000,
    "enable_task_compression": True,  # 壓縮存儲任務數據
    "persistence_batch_size": 50,  # 批量持久化
}
```

### 2. 數據庫優化

```python
# SQLite 優化配置
DATABASE_OPTIMIZATION = {
    "pragma_settings": [
        "PRAGMA journal_mode=WAL",
        "PRAGMA synchronous=NORMAL", 
        "PRAGMA cache_size=10000",
        "PRAGMA temp_store=MEMORY",
        "PRAGMA mmap_size=268435456"  # 256MB
    ],
    "connection_pool_size": 5,
    "batch_operations": True,
    "index_optimization": [
        "CREATE INDEX IF NOT EXISTS idx_task_status_priority ON tasks(status, priority DESC, created_at)",
        "CREATE INDEX IF NOT EXISTS idx_task_type_status ON tasks(task_type, status)",
        "CREATE INDEX IF NOT EXISTS idx_task_created_at ON tasks(created_at)"
    ]
}
```

## 🎯 實施檢查清單

### ✅ 設計階段
- [ ] 分析現有任務管理需求
- [ ] 設計任務模型和狀態機
- [ ] 制定調度策略和優先級規則
- [ ] 設計持久化方案
- [ ] 制定監控和告警策略

### ✅ 實施階段
- [ ] 實施增強的任務模型
- [ ] 實施智能任務調度器
- [ ] 實施異步任務佇列
- [ ] 整合業務邏輯
- [ ] 實施監控和告警系統

### ✅ 測試階段
- [ ] 單元測試覆蓋
- [ ] 整合測試驗證
- [ ] 性能基準測試
- [ ] 壓力測試
- [ ] 故障恢復測試

### ✅ 部署階段
- [ ] 漸進式部署
- [ ] 監控指標設置
- [ ] 告警規則配置
- [ ] 性能調優
- [ ] 文檔和培訓

## 📈 預期改進效果

### 任務管理效能提升
- **調度效率**: 提升 200-300%
- **資源利用率**: 提升 40-60%
- **任務吞吐量**: 提升 150-250%
- **錯誤恢復能力**: 提升 300-400%

### 系統可靠性改進  
- **任務失敗率**: 減少 50-70%
- **系統響應時間**: 減少 30-50%
- **記憶體使用效率**: 提升 25-40%
- **運維複雜度**: 減少 40-60%

---

*本重構指南提供了完整的任務管理器升級方案，實現真正的異步任務調度和智能資源管理，大幅提升系統的處理能力和可靠性。*