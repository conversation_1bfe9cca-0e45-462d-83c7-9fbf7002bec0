"""
LINGSEN 廠商檔案處理器
對應 VBA 的 CopyFilesLINGSEN 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class LINGSENFileHandler(BaseFileHandler):
    """
    LINGSEN 廠商檔案處理器

    VBA 邏輯：
    - 來源路徑：sourcePath & "\Lingsen\temp\"
    - 搜尋模式：Dir(sourcePathLINGSEN & "*" & fileName & "*")
    - 檔案類型：所有檔案（主要是壓縮檔）
    """

    def __init__(self, source_base_path: str):
        """初始化 LINGSEN 檔案處理器"""
        super().__init__(source_base_path, "LINGSEN")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default",
                        mo: str = "") -> List[Path]:
        """
        LINGSEN 的來源路徑
        
        VBA: sourcePathLINGSEN = sourcePath & "\Lingsen\temp\"
        """
        paths = []
        
        # LINGSEN 固定路徑：\Lingsen\temp\
        lingsen_path = self.source_base_path / "Lingsen" / "temp"
        paths.append(lingsen_path)
        
        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        LINGSEN 的檔案搜尋模式
        
        VBA: file = Dir(sourcePathLINGSEN & "*" & fileName & "*")
        """
        patterns = []
        
        if mo:
            patterns.append(f"*{mo}*")  # VBA 模式：包含 MO
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """LINGSEN 不支援資料夾複製"""
        return False