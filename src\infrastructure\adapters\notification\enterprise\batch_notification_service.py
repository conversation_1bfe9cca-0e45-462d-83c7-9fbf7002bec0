"""
企業級批量通知服務
提供高效能的批量通知處理：訊息去重、批次調整、異步處理
與 EnterpriseLineService 深度整合
"""

import time
import asyncio
import hashlib
from collections import defaultdict, deque
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple, Any
from threading import RLock, Thread
import queue
import threading

from src.infrastructure.logging.logger_manager import LoggerManager
from ..models.notification_models import (
    NotificationRequest, BatchNotificationRequest, NotificationResult,
    NotificationStatus, NotificationPriority, NotificationType,
    NotificationChannel, NotificationStatistics
)


class MessageDeduplicator:
    """訊息去重處理器"""
    
    def __init__(self, window_minutes: int = 30):
        """
        初始化去重處理器
        
        Args:
            window_minutes: 去重時間窗口（分鐘）
        """
        self.window_minutes = window_minutes
        self._message_hashes: Dict[str, datetime] = {}
        self._lock = RLock()
    
    def get_message_hash(self, request: NotificationRequest) -> str:
        """計算訊息雜湊值"""
        content = f"{request.type.value}:{request.message}:{sorted(request.recipients)}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def is_duplicate(self, request: NotificationRequest) -> bool:
        """檢查是否為重複訊息"""
        with self._lock:
            msg_hash = self.get_message_hash(request)
            now = datetime.now()
            
            # 清理過期的雜湊值
            self._cleanup_expired_hashes(now)
            
            # 檢查是否為重複
            if msg_hash in self._message_hashes:
                return True
            
            # 記錄新的雜湊值
            self._message_hashes[msg_hash] = now
            return False
    
    def _cleanup_expired_hashes(self, now: datetime):
        """清理過期的雜湊值"""
        expiry_time = now - timedelta(minutes=self.window_minutes)
        expired_hashes = [
            hash_key for hash_key, timestamp in self._message_hashes.items()
            if timestamp < expiry_time
        ]
        for hash_key in expired_hashes:
            del self._message_hashes[hash_key]


class BatchSizeOptimizer:
    """批次大小動態調整器"""
    
    def __init__(self, initial_size: int = 50, min_size: int = 10, max_size: int = 200):
        """
        初始化批次大小調整器
        
        Args:
            initial_size: 初始批次大小
            min_size: 最小批次大小
            max_size: 最大批次大小
        """
        self.current_size = initial_size
        self.min_size = min_size
        self.max_size = max_size
        self._performance_history = deque(maxlen=50)
        self._lock = RLock()
    
    def record_batch_performance(self, batch_size: int, processing_time_ms: float, success_rate: float):
        """記錄批次處理效能"""
        with self._lock:
            self._performance_history.append({
                'size': batch_size,
                'time_ms': processing_time_ms,
                'success_rate': success_rate,
                'throughput': batch_size / max(processing_time_ms / 1000.0, 0.001)
            })
    
    def get_optimal_batch_size(self) -> int:
        """取得最佳批次大小"""
        with self._lock:
            if len(self._performance_history) < 10:
                return self.current_size
            
            # 分析效能資料
            recent_data = list(self._performance_history)[-10:]
            avg_throughput = sum(d['throughput'] for d in recent_data) / len(recent_data)
            avg_success_rate = sum(d['success_rate'] for d in recent_data) / len(recent_data)
            
            # 調整策略
            if avg_success_rate > 0.95 and avg_throughput > 50:
                # 效能良好，可以增加批次大小
                self.current_size = min(self.current_size + 10, self.max_size)
            elif avg_success_rate < 0.80 or avg_throughput < 20:
                # 效能不佳，減少批次大小
                self.current_size = max(self.current_size - 10, self.min_size)
            
            return self.current_size


class BatchNotificationService:
    """企業級批量通知服務"""
    
    def __init__(
        self,
        enterprise_service=None,
        logger_manager: Optional[LoggerManager] = None,
        max_workers: int = 5,
        enable_deduplication: bool = True
    ):
        """
        初始化批量通知服務
        
        Args:
            enterprise_service: 企業級 LINE 服務實例
            logger_manager: 日誌管理器
            max_workers: 最大工作者數量
            enable_deduplication: 是否啟用去重功能
        """
        self.logger_manager = logger_manager or LoggerManager()
        self.logger = self.logger_manager.get_logger("BatchNotificationService")
        
        # 延遲導入避免循環依賴
        if enterprise_service:
            self.enterprise_service = enterprise_service
        else:
            from .enterprise_line_service import EnterpriseLineService
            self.enterprise_service = EnterpriseLineService()
        
        # 核心元件
        self.deduplicator = MessageDeduplicator() if enable_deduplication else None
        self.batch_optimizer = BatchSizeOptimizer()
        
        # 執行緒池和佇列
        self.executor = ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="BatchNotification"
        )
        self.batch_queue: queue.Queue = queue.Queue()
        
        # 統計和監控
        self._batch_statistics = {
            'total_batches': 0,
            'total_notifications': 0,
            'successful_notifications': 0,
            'failed_notifications': 0,
            'duplicate_notifications': 0,
            'average_batch_time_ms': 0.0
        }
        self._processing_lock = RLock()
        
        # 背景處理執行緒
        self._running = True
        self._batch_processor_thread = Thread(
            target=self._process_batch_queue,
            daemon=True,
            name="BatchQueueProcessor"
        )
        self._batch_processor_thread.start()
        
        self.logger.info("批量通知服務已初始化")
    
    def send_batch_notifications(self, batch_request: BatchNotificationRequest) -> Dict[str, Any]:
        """
        發送批量通知
        
        Args:
            batch_request: 批量通知請求
            
        Returns:
            Dict: 批次處理結果
        """
        try:
            start_time = time.time()
            
            # 去重處理
            if self.deduplicator:
                original_count = len(batch_request.requests)
                batch_request.requests = [
                    req for req in batch_request.requests
                    if not self.deduplicator.is_duplicate(req)
                ]
                duplicate_count = original_count - len(batch_request.requests)
                
                with self._processing_lock:
                    self._batch_statistics['duplicate_notifications'] += duplicate_count
                
                if duplicate_count > 0:
                    self.logger.info(f"批次 {batch_request.name} 去重: {duplicate_count} 個重複通知")
            
            if not batch_request.requests:
                return {
                    'batch_id': batch_request.id,
                    'status': 'completed',
                    'total_requests': 0,
                    'successful': 0,
                    'failed': 0,
                    'processing_time_ms': 0,
                    'message': '所有通知都已去重，無需處理'
                }
            
            # 分批處理
            optimal_batch_size = self.batch_optimizer.get_optimal_batch_size()
            batches = self._split_into_batches(batch_request.requests, optimal_batch_size)
            
            # 並行處理批次
            results = self._process_batches_parallel(batches, batch_request.max_concurrent)
            
            # 統計結果
            processing_time = (time.time() - start_time) * 1000
            total_successful = sum(r['successful'] for r in results)
            total_failed = sum(r['failed'] for r in results)
            success_rate = total_successful / max(total_successful + total_failed, 1)
            
            # 記錄效能
            self.batch_optimizer.record_batch_performance(
                optimal_batch_size, processing_time, success_rate
            )
            
            # 更新統計
            self._update_batch_statistics(
                total_requests=len(batch_request.requests),
                successful=total_successful,
                failed=total_failed,
                processing_time_ms=processing_time
            )
            
            return {
                'batch_id': batch_request.id,
                'batch_name': batch_request.name,
                'status': 'completed',
                'total_requests': len(batch_request.requests),
                'successful': total_successful,
                'failed': total_failed,
                'success_rate': success_rate,
                'processing_time_ms': processing_time,
                'batch_count': len(batches),
                'optimal_batch_size': optimal_batch_size
            }
            
        except Exception as e:
            self.logger.error(f"批量通知處理失敗: {e}")
            return {
                'batch_id': batch_request.id,
                'status': 'failed',
                'error': str(e),
                'processing_time_ms': (time.time() - start_time) * 1000
            }
    
    def _split_into_batches(self, requests: List[NotificationRequest], batch_size: int) -> List[List[NotificationRequest]]:
        """將請求分割成批次"""
        batches = []
        for i in range(0, len(requests), batch_size):
            batch = requests[i:i + batch_size]
            batches.append(batch)
        return batches
    
    def _process_batches_parallel(
        self, 
        batches: List[List[NotificationRequest]], 
        max_concurrent: int
    ) -> List[Dict[str, Any]]:
        """並行處理批次"""
        results = []
        
        # 提交批次任務
        future_to_batch = {}
        for i, batch in enumerate(batches):
            future = self.executor.submit(self._process_single_batch, batch, i)
            future_to_batch[future] = i
        
        # 收集結果
        for future in as_completed(future_to_batch):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                batch_idx = future_to_batch[future]
                self.logger.error(f"批次 {batch_idx} 處理失敗: {e}")
                results.append({
                    'batch_index': batch_idx,
                    'successful': 0,
                    'failed': len(batches[batch_idx]),
                    'error': str(e)
                })
        
        return results
    
    def _process_single_batch(self, batch: List[NotificationRequest], batch_index: int) -> Dict[str, Any]:
        """處理單一批次"""
        start_time = time.time()
        successful = 0
        failed = 0
        
        try:
            for request in batch:
                try:
                    notification_id = self.enterprise_service.send_notification(request)
                    if notification_id:
                        successful += 1
                    else:
                        failed += 1
                except Exception as e:
                    self.logger.error(f"發送通知失敗 {request.id}: {e}")
                    failed += 1
            
            processing_time = (time.time() - start_time) * 1000
            
            self.logger.info(
                f"批次 {batch_index} 完成: {successful} 成功, {failed} 失敗, "
                f"耗時 {processing_time:.1f}ms"
            )
            
            return {
                'batch_index': batch_index,
                'successful': successful,
                'failed': failed,
                'processing_time_ms': processing_time
            }
            
        except Exception as e:
            self.logger.error(f"批次 {batch_index} 處理異常: {e}")
            return {
                'batch_index': batch_index,
                'successful': 0,
                'failed': len(batch),
                'error': str(e)
            }
    
    def _process_batch_queue(self):
        """處理批次佇列的背景執行緒"""
        while self._running:
            try:
                batch_request = self.batch_queue.get(timeout=1.0)
                self.send_batch_notifications(batch_request)
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"批次佇列處理錯誤: {e}")
    
    def queue_batch_notification(self, batch_request: BatchNotificationRequest):
        """將批量通知加入佇列"""
        self.batch_queue.put(batch_request)
        self.logger.info(f"批量通知已加入佇列: {batch_request.name}")
    
    def _update_batch_statistics(self, total_requests: int, successful: int, failed: int, processing_time_ms: float):
        """更新批次統計"""
        with self._processing_lock:
            stats = self._batch_statistics
            stats['total_batches'] += 1
            stats['total_notifications'] += total_requests
            stats['successful_notifications'] += successful
            stats['failed_notifications'] += failed
            
            # 更新平均處理時間
            total_batches = stats['total_batches']
            current_avg = stats['average_batch_time_ms']
            stats['average_batch_time_ms'] = (
                (current_avg * (total_batches - 1) + processing_time_ms) / total_batches
            )
    
    def get_batch_statistics(self) -> Dict[str, Any]:
        """取得批次統計資料"""
        with self._processing_lock:
            stats = self._batch_statistics.copy()
            
            # 計算成功率
            total_notifications = stats['successful_notifications'] + stats['failed_notifications']
            if total_notifications > 0:
                stats['success_rate'] = stats['successful_notifications'] / total_notifications * 100
            else:
                stats['success_rate'] = 0.0
            
            # 新增當前批次大小
            stats['current_optimal_batch_size'] = self.batch_optimizer.get_optimal_batch_size()
            stats['queue_size'] = self.batch_queue.qsize()
            
            return stats
    
    def create_batch_request(
        self,
        name: str,
        requests: List[NotificationRequest],
        max_concurrent: int = 5,
        delay_between_batches_ms: int = 100
    ) -> BatchNotificationRequest:
        """建立批量通知請求"""
        return BatchNotificationRequest(
            name=name,
            requests=requests,
            max_concurrent=max_concurrent,
            delay_between_batches_ms=delay_between_batches_ms
        )
    
    def shutdown(self):
        """關閉服務"""
        self.logger.info("正在關閉批量通知服務...")
        self._running = False
        
        if self._batch_processor_thread.is_alive():
            self._batch_processor_thread.join(timeout=5)
        
        self.executor.shutdown(wait=True)
        self.logger.info("批量通知服務已關閉")