# 需求文件

## 簡介

本文件定義了為 Outlook Summary System 開發即時監控儀表板功能的詳細需求。該系統是一個企業級的半導體製造測試資料處理平台，目前具備基本的監控能力，但缺乏一個統一的、使用者友善的即時監控介面。

### 系統現況深度分析（基於52.2%完成進度）

**已完成架構（24/46任務完成）：**
- **PHASE_1-2完成**：基礎設施層、數據模型層、解析器層（5個廠商解析器全部完成）
- **PHASE_4完成**：核心處理器、Excel處理系統、資料分析引擎、報表生成系統
- **Flask 郵件收件夾服務**（埠 5000）- 完整的 Web UI 和 RESTful API，包含郵件同步排程器
- **FastAPI FT-EQC 處理服務**（埠 8010）- 數據處理和 API 服務，提供 `/ui` 和 `/docs` 端點
- **網路瀏覽器API服務**（埠 8009）- 網路共享資料夾瀏覽和下載功能
- **六角架構設計**：領域層、應用層、基礎設施層、展示層（完全實現）
- **兩階段EQC處理流程**：第一階段檔案整合 + 第二階段完整處理（8個步驟）

**現有監控能力詳細分析：**
- **SyncMonitor 類別**：每 60 秒檢查同步狀態，調用 `/api/sync/status`，監控同步錯誤和統計
- **現有 API 端點**：
  - `/api/statistics` - 返回 total_emails, total_senders, unread_emails, processed_emails, emails_with_attachments, latest_email_time
  - `/api/sync/status` - 返回 is_syncing, auto_sync_enabled, auto_sync_interval, last_sync_time, sync_stats
  - `/api/connection/status` - 返回 connected, connection_info, reader_stats
  - `/api/emails` - 支援分頁、排序、篩選的郵件列表
  - `/api/senders` - 寄件者統計列表
  - `/api/process_eqc_advanced` - EQC兩階段處理流程API
  - `/api/health` - 系統健康檢查端點
- **EmailSyncService**：提供完整的同步狀態管理，包含 sync_stats（total_synced, last_sync_count, sync_errors, last_error_details）
- **LoggerManager**：結構化彩色日誌記錄系統（DEBUG=藍色、INFO=綠色、WARNING=黃色、ERROR=紅色、CRITICAL=背景紅色、PERFORMANCE=洋紅色）
- **效能監控系統**：AdvancedPerformanceManager 提供詳細的處理時間統計和瓶頸分析

**資料來源深度分析（已完成實現）：**
- **EmailDB 表**：包含 parse_status（pending/parsed/failed）、extraction_method（traditional/llm/hybrid/fallback）、vendor_code、pd、lot、mo、yield_value、llm_analysis_result、llm_analysis_timestamp、llm_service_used
- **EmailProcessStatusDB 表**：包含具體處理步驟（eqctotaldata, code_detection, dual_search, report_generation）、status（pending/processing/completed/failed）、progress_percentage
- **廠商解析器系統（100%完成）**：
  - GTK：識別條件 `ft hold`, `ft lot`，93%測試覆蓋率
  - ETD：識別條件 `anf`，85%測試覆蓋率，支援斜線分隔格式
  - XAHT：識別條件 `tianshui`, `西安`，79%測試覆蓋率，雙重解析機制
  - JCET：識別條件 `jcet`，93%測試覆蓋率，支援複雜的 KUI/GYC 模式解析
  - LINGSEN：識別條件 `lingsen`，90%測試覆蓋率，正則表達式產品代碼解析
- **LLM 整合系統**：UnifiedLLMClient 支援 Ollama 和 Grok，提供 confidence_score、parsing_methods_used、analysis_reasoning
- **Excel處理系統（已完成）**：
  - CSV到Excel轉換器：8步驟處理流程，BIN1保護機制，Summary工作表生成
  - CTA整合處理器：自動格式檢測，雙重處理路徑
  - 效能監控：AdvancedPerformanceManager提供詳細的處理時間統計和瓶頸分析
- **EQC處理系統（已完成）**：
  - 兩階段處理架構：第一階段檔案整合，第二階段8步驟完整處理
  - CODE區間檢測：支援前端自訂區間設定
  - 雙重搜尋機制：主要區間100%匹配 + 備用區間映射匹配
  - InsEqcRtData2處理：ALL0移動、FAIL檢測、匹配搜尋

**業務流程深度分析（已完成實現）：**
- **郵件處理流程**：郵件監控 → 廠商識別 → 資料解析 → 檔案處理 → 報表生成 → 郵件發送
- **檔案處理系統**：FileHandlerFactory 支援各廠商的檔案下載、解壓、轉換
- **附件處理**：SyncAttachmentHandler 處理郵件附件
- **Excel 處理**：支援 CTA、EQC、FT 等多種資料格式轉換，包含8步驟處理流程
- **自動化流程**：AutoEmailProcessor 提供自動解析新郵件功能
- **API整合系統**：api_integration.py 統一管理多個API服務，支援優雅關閉和進程監控
- **效能分析系統**：AdvancedPerformanceManager 提供詳細的處理時間統計和瓶頸分析

**測試覆蓋率統計（已完成）：**
- **總測試數**：210+ 個測試，100% 通過率
- **廠商解析器測試覆蓋率**：GTK(93%)、ETD(85%)、XAHT(79%)、JCET(93%)、LINGSEN(90%)
- **核心處理器測試覆蓋率**：EmailProcessor(76%)、Excel轉換器(100%)
- **TDD開發方法**：100%測試先行開發，確保程式碼品質

**現有監控資料來源（可直接利用）：**
- **處理統計API**：`/api/statistics` 提供即時郵件處理統計
- **同步狀態API**：`/api/sync/status` 提供同步服務狀態
- **連接狀態API**：`/api/connection/status` 提供連接品質監控
- **健康檢查API**：`/api/health` 提供系統健康狀態
- **EQC處理API**：`/api/process_eqc_advanced` 提供EQC處理狀態
- **資料庫資訊API**：`/api/database/info` 提供資料庫狀態
- **效能日誌系統**：logs/datalog.txt 記錄詳細的處理時間和效能分析

即時監控儀表板將深度整合這些現有資源，提供統一的視覺化監控介面，支援主動警報和歷史趨勢分析。

## 需求

### 需求 1

**使用者故事：** 身為系統管理員，我希望在集中式儀表板上檢視即時系統健康指標，以便快速識別並回應系統問題。

#### 驗收標準

1. 當存取儀表板時，系統應顯示所有服務的目前狀態，包括：
   - Flask 郵件收件夾服務（埠 5000）的連線狀態和回應時間
   - FastAPI FT-EQC 處理服務（埠 8010）的連線狀態和回應時間
   - 郵件同步服務的執行狀態和最後同步時間
   - 資料庫連線狀態和查詢回應時間
2. 當服務變為不可用時，系統應在 30 秒內以紅色突出顯示受影響的服務，並顯示具體錯誤訊息
3. 當系統資源使用率超過 80% 時，系統應顯示警告指示器，包括：
   - CPU 使用率超過 80%
   - 記憶體使用率超過 80%
   - 磁碟空間使用率超過 80%
4. 當儀表板載入時，系統應顯示每個服務元件的運行時間，精確到分鐘
5. 若發生任何嚴重錯誤，系統應立即顯示警報通知，包括錯誤時間、影響範圍和建議處理方式

### 需求 2

**使用者故事：** 身為營運經理，我希望即時監控郵件處理效能，以確保製造測試資料的高效處理。

#### 驗收標準

1. 當正在處理郵件時，系統應顯示目前處理速率，包括：
   - 每分鐘處理的郵件數量
   - 每分鐘解析成功的郵件數量
   - 每分鐘處理的附件數量
   - 平均每封郵件的處理時間
2. 當同步服務執行時，系統應顯示詳細的會話資訊：
   - 目前會話開始時間
   - 已處理的郵件總數
   - 成功/失敗的郵件數量比例
   - 預估剩餘處理時間
3. 當解析失敗時，系統應提供詳細的失敗分析：
   - 按廠商分類的失敗計數（GTK、ETD、XAHT、JCET、LINGSEN）
   - 失敗原因分類統計
   - 最近失敗的郵件主題和寄件者
4. 當處理佇列累積時，系統應顯示佇列管理資訊：
   - 待處理郵件佇列長度
   - 按優先級分類的佇列狀態
   - 基於歷史資料的預估處理時間
5. 若處理速率低於預設閾值（每分鐘 5 封郵件），系統應顯示效能警告並提供可能原因

### 需求 3

**使用者故事：** 身為品質保證工程師，我希望追蹤特定廠商的處理統計資料，以識別不同半導體測試廠商的模式和問題。

#### 驗收標準

1. 當處理廠商資料時，系統應按廠商顯示詳細統計，包括：
   - GTK 廠商：識別條件 `ft hold`、`ft lot`，顯示解析成功率
   - ETD 廠商：識別條件 `anf`，顯示解析成功率
   - XAHT 廠商：識別條件 `tianshui`、`西安`，顯示解析成功率
   - JCET 廠商：識別條件 `jcet`、KUI 模式解析，顯示解析成功率
   - LINGSEN 廠商：識別條件 `lingsen`，顯示解析成功率
2. 當發生解析錯誤時，系統應提供詳細的錯誤分析：
   - 按廠商分類的 parse_status 統計（pending、parsed、failed）
   - 按 extraction_method 分類統計（traditional、llm、hybrid、fallback）
   - 顯示 parse_error 欄位中的常見錯誤模式
3. 當儀表板重新整理時，系統應顯示廠商處理趨勢：
   - 過去 24 小時內每個廠商的郵件接收量
   - 每個廠商的平均良率（yield_value）趨勢
   - MO（製造訂單）和 LOT（批次號）處理統計
4. 當廠商顯示效能下降時，系統應以警告指示器突出顯示：
   - 解析成功率低於 85% 時顯示黃色警告
   - 解析成功率低於 70% 時顯示紅色警報
5. 若廠商在 2 小時內成功處理數為零，系統應顯示嚴重警報並建議檢查對應的解析器

### 需求 4

**使用者故事：** 身為系統管理員，我希望檢視詳細的錯誤日誌和系統事件，以便有效地排除故障。

#### 驗收標準

1. 當發生錯誤時，系統應顯示最近 50 條帶有時間戳記的錯誤訊息，包括：
   - LoggerManager 記錄的結構化日誌
   - 郵件解析錯誤（parse_error 欄位內容）
   - 服務連線錯誤和逾時
   - 資料庫操作錯誤
2. 當點擊錯誤時，系統應顯示詳細的錯誤資訊：
   - 完整的堆疊追蹤資訊
   - 相關的郵件 ID 和廠商資訊
   - 錯誤發生時的系統狀態
   - 建議的修復步驟
3. 當套用篩選時，系統應允許多維度篩選：
   - 按錯誤等級篩選（ERROR、WARNING、INFO、DEBUG）
   - 按服務類型篩選（Flask、FastAPI、EmailSync、Database）
   - 按廠商篩選（GTK、ETD、XAHT、JCET、LINGSEN）
   - 按時間範圍篩選（最近 1 小時、6 小時、24 小時）
4. 當錯誤日誌更新時，系統應在無使用者介入的情況下自動重新整理顯示，並突出顯示新增的錯誤
5. 若每小時嚴重錯誤超過 10 個，系統應顯示升級警報並自動通知相關人員

### 需求 5

**使用者故事：** 身為業務分析師，我希望檢視處理統計資料和趨勢，以分析系統效能和容量規劃。

#### 驗收標準

1. 當儀表板載入時，系統應顯示基於 EmailDB 表的統計資料：
   - 今日、本週和本月處理的郵件總數（基於 received_time 欄位）
   - 已讀/未讀郵件比例（基於 is_read 欄位）
   - 已處理/未處理郵件比例（基於 is_processed 欄位）
   - 帶附件郵件統計（基於 has_attachments 和 attachment_count 欄位）
2. 當資料可用時，系統應顯示處理效能趨勢：
   - 基於 parsed_at 欄位的解析時間趨勢
   - 各廠商的平均良率趨勢（基於 yield_value 欄位）
   - 不同 extraction_method 的處理時間比較
   - EmailProcessStatusDB 表中各步驟的完成時間統計
3. 當時間週期變更時，系統應動態查詢資料庫並更新圖表：
   - 支援最近 1 小時、6 小時、24 小時、7 天、30 天的時間範圍
   - 根據選擇的時間範圍調整資料聚合粒度
4. 當滑鼠懸停在圖表元素上時，系統應顯示詳細的工具提示：
   - 具體的數值和百分比
   - 相關的廠商或處理步驟資訊
   - 與前一週期的比較資料
5. 若處理量超過歷史平均值 50%，系統應基於 SenderDB 表的統計資料建議容量擴展

### 需求 6

**使用者故事：** 身為系統操作員，我希望接收嚴重事件的即時通知，以便立即回應系統問題。

#### 驗收標準

1. 當發生嚴重錯誤時，系統應根據錯誤類型顯示通知：
   - 廠商解析器失敗（parse_status = 'failed'）時顯示瀏覽器通知
   - Flask 或 FastAPI 服務無回應時顯示緊急通知
   - 資料庫連線中斷時顯示系統級警報
   - 郵件同步服務停止時顯示服務警報
2. 當服務離線時，系統應顯示顯著的警報橫幅：
   - 埠 5000（Flask）無法連線時顯示「郵件服務離線」警報
   - 埠 8010（FastAPI）無法連線時顯示「處理服務離線」警報
   - SyncMonitor 檢測到同步失敗時顯示「同步服務異常」警報
3. 當點擊通知時，系統應導航到儀表板的相關部分：
   - 廠商錯誤通知導航到廠商統計區域
   - 服務錯誤通知導航到系統健康狀態區域
   - 處理錯誤通知導航到錯誤日誌區域
4. 當存在多個警報時，系統應按嚴重程度優先順序顯示：
   - 嚴重（Critical）：服務完全離線、資料庫無法連線
   - 警告（Warning）：效能下降、解析失敗率高
   - 資訊（Info）：一般狀態更新、統計資料變化
5. 若使用者關閉警報，系統在 30 分鐘內不應再次顯示相同類型的警報，但應在日誌中保留記錄

### 需求 7

**使用者故事：** 身為系統管理員，我希望配置儀表板重新整理速率和警報閾值，以根據營運需求自訂監控。

#### 驗收標準

1. 當存取設定時，系統應允許配置多種參數：
   - 儀表板重新整理間隔（5-300 秒，預設 30 秒）
   - SyncMonitor 檢查間隔（目前固定 60 秒，應可調整為 30-300 秒）
   - 資料庫查詢逾時設定（目前固定 5 秒，應可調整為 3-30 秒）
2. 當設定閾值時，系統應接受各種警報的自訂值：
   - CPU、記憶體、磁碟使用率警報閾值（預設 80%，可調整為 50-95%）
   - 郵件處理速率警報閾值（預設每分鐘 5 封，可調整為 1-50 封）
   - 廠商解析成功率警報閾值（預設 85% 警告、70% 嚴重，可調整）
   - 每小時錯誤數量警報閾值（預設 10 個，可調整為 5-100 個）
3. 當儲存配置變更時，系統應立即套用新設定：
   - 更新 SyncMonitor 的檢查間隔而無需重新啟動服務
   - 動態調整前端儀表板的重新整理頻率
   - 即時生效警報閾值變更
4. 當輸入無效值時，系統應顯示詳細的驗證錯誤：
   - 數值範圍驗證（如重新整理間隔必須在 5-300 秒之間）
   - 邏輯驗證（如警告閾值必須高於嚴重閾值）
   - 格式驗證（如必須為正整數）
5. 若設定損壞或遺失，系統應恢復為預設配置值並記錄恢復事件到日誌中

### 需求 8

**使用者故事：** 身為系統管理員，我希望監控 LLM 服務和檔案處理系統的狀態，以確保 AI 輔助解析和檔案處理功能正常運作。

#### 驗收標準

1. 當監控 LLM 服務時，系統應顯示詳細的服務狀態：
   - UnifiedLLMClient 的可用性狀態（Ollama/Grok）
   - LLM 服務的回應時間和成功率
   - 基於 llm_analysis_timestamp 的最近分析活動
   - 基於 llm_service_used 的服務使用統計（grok vs ollama）
   - confidence_score 的平均值和分佈統計
2. 當監控檔案處理系統時，系統應顯示處理狀態：
   - FileHandlerFactory 各廠商檔案處理器的狀態
   - SyncAttachmentHandler 附件處理成功率
   - Excel 處理系統（CTA、EQC、FT）的處理統計
   - 檔案下載、解壓、轉換各步驟的成功率
3. 當監控 EmailProcessStatusDB 時，系統應顯示具體處理步驟狀態：
   - eqctotaldata 步驟的完成率和平均處理時間
   - code_detection 步驟的成功率和錯誤統計
   - dual_search 步驟的處理效能
   - report_generation 步驟的完成狀態
4. 當 LLM 服務出現問題時，系統應顯示具體警報：
   - LLM 服務連線失敗時顯示「AI 解析服務離線」警報
   - confidence_score 平均值低於 0.7 時顯示「AI 解析品質下降」警告
   - LLM 回應時間超過 30 秒時顯示「AI 服務回應緩慢」警告
5. 若檔案處理失敗率超過 20%，系統應顯示「檔案處理系統異常」警報並提供詳細的失敗原因分析

### 需求 9

**使用者故事：** 身為品質保證工程師，我希望監控良率數據和異常檢測功能，以確保製造測試數據的準確性和及時發現異常。

#### 驗收標準

1. 當監控良率數據時，系統應顯示詳細的良率統計：
   - 基於 yield_value 欄位的即時良率分佈圖
   - 各廠商的良率趨勢對比（過去 24 小時、7 天、30 天）
   - 良率異常檢測結果（低於歷史平均值 2 個標準差的案例）
   - 高良率和低良率的郵件數量統計
2. 當檢測到良率異常時，系統應提供詳細分析：
   - 異常良率的具體數值和偏差程度
   - 相關的廠商、產品代碼（pd）、批次號（lot）資訊
   - 異常發生的時間趨勢和頻率統計
   - 與同期其他廠商良率的對比分析
3. 當監控產品和批次數據時，系統應顯示：
   - 基於 pd 欄位的產品代碼分佈統計
   - 基於 lot 和 mo 欄位的批次處理統計
   - 重複批次號的檢測和警報
   - 批次處理時間的趨勢分析
4. 當良率數據出現異常模式時，系統應自動警報：
   - 單一廠商良率連續 3 次低於 90% 時顯示「廠商良率異常」警報
   - 整體良率低於歷史平均值 10% 時顯示「系統良率下降」警告
   - 檢測到可能的數據錯誤（如良率超過 100% 或為負值）時顯示「數據異常」警報
5. 若 24 小時內未收到任何良率數據，系統應顯示「良率數據中斷」嚴重警報

### 需求 10

**使用者故事：** 身為系統管理員，我希望監控系統的網路連線品質和編碼處理狀況，以確保郵件接收和數據處理的穩定性。

#### 驗收標準

1. 當監控網路連線時，系統應顯示詳細的連線狀態：
   - POP3/Outlook 郵件服務器的連線品質和延遲
   - 基於 EmailSyncService.get_connection_status() 的連線統計
   - 網路逾時和重連次數統計
   - 郵件接收速度和穩定性指標
2. 當監控編碼處理時，系統應顯示 Unicode 處理狀況：
   - unicode_fix_global 模組的運作狀態
   - 編碼錯誤的發生頻率和類型統計
   - 中英文混合內容的處理成功率
   - MIME 編碼解析的成功率統計
3. 當監控自動化流程時，系統應顯示：
   - AutoEmailProcessor 的自動解析執行狀態
   - 自動同步服務的運行狀態和間隔設定
   - 背景任務的執行佇列和完成狀態
   - 定時任務的執行記錄和成功率
4. 當網路或編碼出現問題時，系統應顯示相應警報：
   - 郵件服務器連線失敗超過 5 分鐘時顯示「郵件服務器離線」嚴重警報
   - Unicode 編碼錯誤率超過 5% 時顯示「編碼處理異常」警告
   - 自動同步服務停止超過 10 分鐘時顯示「自動同步中斷」警報
5. 若系統檢測到持續的網路不穩定（連線成功率低於 90%），系統應建議檢查網路設定和防火牆配置

### 需求 11

**使用者故事：** 身為系統管理員，我希望監控資料庫管理功能和批量操作的效能，以確保資料庫健康和批量處理的效率。

#### 驗收標準

1. 當監控資料庫管理時，系統應顯示詳細的資料庫狀態：
   - 基於 `/api/database/info` 的資料庫大小和增長趨勢
   - 各表的記錄數量變化（emails、senders、attachments、email_process_status）
   - 資料庫查詢效能統計和慢查詢檢測
   - 資料庫連線池的使用狀況和等待時間
2. 當監控批量操作時，系統應顯示操作效能：
   - `/api/emails/batch-delete` 的執行頻率和成功率
   - `/api/emails/batch-mark-read` 的處理量和平均時間
   - `/api/emails/batch-process` 的批量處理效能
   - 批量操作的並發處理能力和資源使用
3. 當監控 SQL 查詢執行時，系統應顯示：
   - 基於 `/api/database/execute` 的查詢執行統計
   - 查詢回應時間分佈和異常查詢檢測
   - 使用者查詢模式分析和頻率統計
   - 資料庫搜尋功能的使用情況
4. 當資料庫出現效能問題時，系統應顯示警報：
   - 資料庫大小增長超過 1GB/天時顯示「資料庫快速增長」警告
   - 查詢回應時間超過 5 秒時顯示「資料庫效能下降」警報
   - 批量操作失敗率超過 10% 時顯示「批量操作異常」警告
5. 若資料庫連線數超過最大限制的 80%，系統應顯示「資料庫連線池接近滿載」嚴重警報

### 需求 12

**使用者故事：** 身為系統管理員，我希望監控通知系統和 SMTP 郵件發送功能，以確保重要通知能及時送達。

#### 驗收標準

1. 當監控 LINE 通知服務時，系統應顯示詳細狀態：
   - LineNotificationService 的連線狀態和 API 可用性
   - 通知發送的成功率和失敗統計
   - 基於 line_notifications.json 的通知歷史分析
   - 解析失敗和成功通知的發送頻率
2. 當監控 SMTP 郵件發送時，系統應顯示：
   - SMTPSender 的連線狀態和伺服器回應時間
   - 郵件發送的成功率和失敗原因統計
   - 發送佇列的狀態和等待時間
   - 附件處理的成功率和大小統計
3. 當監控通知內容時，系統應分析：
   - 不同類型通知的發送頻率（parsing_failure vs parsing_success）
   - 通知訊息的長度分佈和內容分析
   - 收件人的回應率和互動統計
   - 通知發送的時間分佈模式
4. 當通知系統出現問題時，系統應顯示警報：
   - LINE API 連線失敗時顯示「LINE 通知服務離線」嚴重警報
   - SMTP 伺服器無法連線時顯示「郵件發送服務異常」警報
   - 通知發送失敗率超過 20% 時顯示「通知系統效能下降」警告
5. 若 24 小時內未成功發送任何通知，系統應顯示「通知系統可能故障」嚴重警報

### 需求 13

**使用者故事：** 身為系統管理員，我希望監控所有 API 端點的使用情況和效能，以優化系統效能和識別使用模式。

#### 驗收標準

1. 當監控 API 使用統計時，系統應顯示詳細資訊：
   - 所有 API 端點的呼叫頻率和回應時間統計
   - API 錯誤率分析和常見錯誤類型
   - 高頻使用的 API 端點識別和效能分析
   - API 使用的時間分佈和尖峰時段分析
2. 當監控特定 API 群組時，系統應分類顯示：
   - 郵件相關 API（/api/emails/*）的使用統計
   - 同步相關 API（/api/sync/*）的執行頻率
   - 資料庫相關 API（/api/database/*）的查詢統計
   - 統計相關 API（/api/statistics, /api/senders）的存取模式
3. 當分析 API 效能時，系統應提供：
   - 各端點的平均回應時間和 95% 百分位數
   - 慢速 API 呼叫的識別和原因分析
   - API 併發處理能力和瓶頸檢測
   - 資源消耗最高的 API 端點統計
4. 當 API 出現異常時，系統應顯示警報：
   - API 回應時間超過 10 秒時顯示「API 效能異常」警告
   - API 錯誤率超過 5% 時顯示「API 穩定性下降」警報
   - 單一 API 呼叫頻率異常高時顯示「可能的 API 濫用」警告
5. 若檢測到 API 使用模式異常（如深夜大量呼叫），系統應顯示「異常 API 使用模式」警報並記錄詳細資訊

### 需求 14

**使用者故事：** 身為系統管理員，我希望監控雙服務架構的整合狀態和檔案系統使用情況，以確保系統穩定運行。

#### 驗收標準

1. 當監控雙服務架構時，系統應顯示整合狀態：
   - Flask（埠 5000）和 FastAPI（埠 8010）之間的通訊狀態
   - 服務間資料同步的延遲和一致性檢查
   - 負載分配和服務回應時間對比
   - 服務重啟和故障轉移的統計
2. 當監控檔案系統時，系統應顯示使用情況：
   - 檔案複製和處理的成功率統計
   - 目標路徑（temp_base_path）的磁碟使用率
   - 附件儲存空間的增長趨勢
   - 檔案清理和歸檔的執行狀態
3. 當監控系統資源時，系統應提供詳細資訊：
   - Python 進程的記憶體使用和 GC 統計
   - CPU 使用率的詳細分析和核心分配
   - 磁碟 I/O 統計和讀寫效能
   - 網路頻寬使用和連線數統計
4. 當系統出現資源問題時，系統應顯示警報：
   - 服務間通訊延遲超過 5 秒時顯示「服務整合異常」警報
   - 磁碟使用率超過 90% 時顯示「磁碟空間不足」嚴重警報
   - 記憶體使用率超過 85% 時顯示「記憶體使用過高」警告
5. 若檢測到服務不同步或資料不一致，系統應顯示「資料同步異常」嚴重警報並提供修復建議

