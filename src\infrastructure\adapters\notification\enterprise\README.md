# 企業級 LINE 通知系統

## 概述

企業級 LINE 通知系統提供高級通知功能，包括：
- 批量通知處理
- 通知範本系統
- 統計管理
- 通知規則
- 佇列處理和重試機制
- 速率限制

## 快速開始

### 基本使用

```python
from src.infrastructure.adapters.notification.enterprise import EnterpriseLineService
from src.infrastructure.adapters.notification.models.notification_models import (
    NotificationRequest, NotificationType, NotificationPriority
)

# 初始化服務
service = EnterpriseLineService()

# 發送通知
request = NotificationRequest(
    type=NotificationType.SYSTEM_ALERT,
    recipients=['default_admin'],
    message="系統警報：伺服器 CPU 使用率過高",
    priority=NotificationPriority.HIGH
)

notification_id = service.send_notification(request)
print(f"通知已排程，ID: {notification_id}")
```

### 郵件解析通知

```python
# 解析失敗通知
email_data = {
    'id': 'email_001',
    'subject': '測試郵件',
    'sender': '<EMAIL>',
    'vendor_code': 'TEST_VENDOR',
    'error_message': '解析錯誤原因'
}

failure_id = service.send_parsing_failure_notification(email_data)

# 解析成功通知
success_data = {
    'id': 'email_002',
    'subject': '成功郵件',
    'vendor_code': 'SUCCESS_VENDOR',
    'pd': 'PRODUCT_001',
    'lot': 'LOT_123',
    'yield_value': '95.5%'
}

success_id = service.send_parsing_success_notification(success_data)
```

### 查看統計和歷史

```python
# 獲取統計資料
stats = service.get_statistics()
print(f"總發送: {stats.total_sent}")
print(f"成功率: {stats.success_rate:.1f}%")

# 獲取歷史記錄
history = service.get_history(limit=10)
for record in history:
    print(f"{record.type.value}: {record.status.value}")
```

## 功能特點

### 1. 智能佇列處理
- 優先級排序（LOW, NORMAL, HIGH, URGENT）
- 自動重試機制（指數退避）
- 速率限制控制

### 2. 範本系統
- 預定義通知範本
- 變數替換支援
- 多通道支援

### 3. 統計管理
- 即時統計資料
- 按類型和通道分類
- 效能指標追蹤

### 4. 與現有系統整合
- 重用現有 LineNotificationService
- 重用現有 LoggerManager 和 ConfigManager
- 向下相容

## 架構設計

```
enterprise/
├── __init__.py                    # 模組入口
└── enterprise_line_service.py     # 核心服務 (<500行)

models/
└── notification_models.py         # 資料模型 (<500行)

config/
└── (擴展配置檔案)
```

## 設計原則

1. **功能替換原則**：新功能完全替代舊功能，不保留冗餘代碼
2. **500行限制**：每個檔案嚴格控制在500行以內
3. **測試驅動**：所有功能必須經過測試驗證
4. **企業級標準**：支援高併發、錯誤處理、監控等

## 擴展接口

### 新增接收者
```python
from src.infrastructure.adapters.notification.models.notification_models import (
    NotificationRecipient, NotificationChannel
)

recipient = NotificationRecipient(
    id="new_admin",
    name="新管理員",
    channel=NotificationChannel.LINE,
    address="LINE_USER_ID",
    preferences={"urgent_only": True}
)

service.add_recipient(recipient)
```

### 新增範本
```python
from src.infrastructure.adapters.notification.models.notification_models import (
    NotificationTemplate, NotificationType
)

template = NotificationTemplate(
    id="custom_alert",
    name="自訂警報範本",
    type=NotificationType.SYSTEM_ALERT,
    channel=NotificationChannel.LINE,
    subject_template="🚨 {alert_type} 警報",
    body_template="系統：{system_name}\n時間：{timestamp}\n詳情：{details}",
    variables=["alert_type", "system_name", "timestamp", "details"]
)

service.add_template(template)
```

## 注意事項

1. **環境變數**：需要設定 LINE_CHANNEL_ACCESS_TOKEN 和 LINE_USER_ID
2. **資源管理**：使用完畢後請呼叫 `service.shutdown()` 來清理資源
3. **錯誤處理**：服務內建重試機制，但仍需處理最終失敗的情況
4. **效能考量**：預設最大併發數為10，可透過配置調整

## 與現有系統的整合

此企業級服務是現有 `LineNotificationService` 的擴展，提供：
- 完全向下相容
- 擴展功能（佇列、統計、範本等）
- 企業級特性（併發控制、錯誤處理、監控）

舊代碼可以無縫升級，新功能可以逐步導入。