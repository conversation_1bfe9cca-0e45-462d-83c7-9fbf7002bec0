"""
增強任務排程器 API 模組
提供GTK特殊排程系統的所有API端點

核心功能：
- 郵件處理統一入口
- GTK排程任務管理
- 排程狀態查詢
- 系統監控和統計
"""

from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from fastapi import APIRouter, HTTPException, Form, Query
from fastapi.responses import HTMLResponse
from loguru import logger

from src.services.email_processing_coordinator import get_email_coordinator
from src.services.enhanced_task_scheduler import ScheduledTaskStatus, TaskPriority

# 建立 APIRouter 實例
router = APIRouter(
    tags=["增強任務排程器"],
    responses={404: {"description": "Not found"}}
)

# ================================
# 郵件處理統一入口 API
# ================================

@router.post("/api/scheduler/process_email")
async def process_email(
    vendor_code: str = Form(...),
    email_id: str = Form(...),
    subject: str = Form(...),
    sender: str = Form(...),
    received_time: str = Form(...),
    priority: str = Form("normal"),
    additional_data: str = Form("{}")
) -> Dict[str, Any]:
    """
    統一郵件處理入口點
    
    根據廠商代碼自動選擇處理策略：
    - GTK廠商：使用排程邏輯（01分/31分執行）
    - 其他廠商：立即處理
    
    Args:
        vendor_code: 廠商代碼
        email_id: 郵件ID
        subject: 郵件主旨
        sender: 寄件者
        received_time: 接收時間（ISO格式）
        priority: 任務優先級 (low, normal, high, critical)
        additional_data: 額外資料（JSON格式）
    
    Returns:
        Dict: 處理結果
    """
    try:
        # 解析優先級
        priority_map = {
            "low": TaskPriority.LOW,
            "normal": TaskPriority.NORMAL,
            "high": TaskPriority.HIGH,
            "critical": TaskPriority.CRITICAL
        }
        task_priority = priority_map.get(priority.lower(), TaskPriority.NORMAL)
        
        # 解析額外資料
        import json
        try:
            extra_data = json.loads(additional_data)
        except:
            extra_data = {}
        
        # 構建郵件資料
        email_data = {
            'id': email_id,
            'subject': subject,
            'sender': sender,
            'received_time': received_time,
            **extra_data
        }
        
        # 處理選項
        processing_options = {
            'priority': task_priority
        }
        
        # 獲取協調器並處理郵件
        coordinator = get_email_coordinator()
        result = coordinator.process_email(
            vendor_code=vendor_code,
            email_data=email_data,
            processing_options=processing_options
        )
        
        logger.info(f"[API] 郵件處理請求: {email_id} ({vendor_code}) - {result.get('status')}")
        
        return {
            "status": "success",
            "data": result,
            "api_version": "3.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except ValueError as e:
        logger.error(f"[API_ERROR] 郵件處理參數錯誤: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"[API_ERROR] 郵件處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 排程任務管理 API
# ================================

@router.get("/api/scheduler/tasks/scheduled")
async def list_scheduled_tasks(
    vendor_filter: Optional[str] = Query(None, description="廠商過濾器"),
    status_filter: Optional[str] = Query(None, description="狀態過濾器"),
    limit: int = Query(50, description="返回數量限制")
) -> Dict[str, Any]:
    """
    列出排程任務
    
    Args:
        vendor_filter: 廠商過濾器
        status_filter: 狀態過濾器
        limit: 返回數量限制
    
    Returns:
        Dict: 排程任務列表
    """
    try:
        coordinator = get_email_coordinator()
        tasks = coordinator.get_scheduled_tasks(vendor_filter)
        
        # 狀態過濾
        if status_filter:
            try:
                status_enum = ScheduledTaskStatus(status_filter.lower())
                tasks = [task for task in tasks if task.get('status') == status_enum.value]
            except ValueError:
                logger.warning(f"無效的狀態過濾器: {status_filter}")
        
        # 限制數量
        tasks = tasks[:limit]
        
        logger.info(f"[API] 排程任務查詢: {len(tasks)} 個任務")
        
        return {
            "status": "success",
            "data": tasks,
            "count": len(tasks),
            "filters": {
                "vendor": vendor_filter,
                "status": status_filter
            },
            "limit": limit,
            "api_version": "3.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[API_ERROR] 獲取排程任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/api/scheduler/tasks/{task_id}")
async def cancel_scheduled_task(task_id: str) -> Dict[str, Any]:
    """
    取消排程任務
    
    Args:
        task_id: 任務ID
        
    Returns:
        Dict: 取消結果
    """
    try:
        coordinator = get_email_coordinator()
        cancelled = coordinator.cancel_scheduled_task(task_id)
        
        if not cancelled:
            raise HTTPException(status_code=400, detail="任務無法取消（可能已完成或不存在）")
        
        logger.info(f"[API] 排程任務已取消: {task_id}")
        
        return {
            "status": "success",
            "message": "排程任務已取消",
            "task_id": task_id,
            "api_version": "3.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API_ERROR] 取消排程任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# GTK排程查詢 API
# ================================

@router.get("/api/scheduler/gtk/next_executions")
async def get_next_gtk_executions(
    hours_ahead: int = Query(24, description="向前查看的小時數", ge=1, le=168)
) -> Dict[str, Any]:
    """
    獲取未來的GTK執行時間
    
    Args:
        hours_ahead: 向前查看的小時數（1-168小時）
    
    Returns:
        Dict: GTK執行時間列表
    """
    try:
        coordinator = get_email_coordinator()
        executions = coordinator.get_next_gtk_executions(hours_ahead)
        
        logger.info(f"[API] GTK執行時間查詢: 未來{hours_ahead}小時，{len(executions)}個執行時間點")
        
        return {
            "status": "success",
            "data": executions,
            "count": len(executions),
            "hours_ahead": hours_ahead,
            "api_version": "3.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[API_ERROR] 獲取GTK執行時間失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 系統狀態和監控 API
# ================================

@router.get("/api/scheduler/status")
async def get_scheduler_status() -> Dict[str, Any]:
    """
    獲取排程器系統狀態
    
    Returns:
        Dict: 系統狀態資訊
    """
    try:
        coordinator = get_email_coordinator()
        status = coordinator.get_processing_status()
        
        logger.info(f"[API] 系統狀態查詢: 排程器狀態={status.get('coordinator_status')}")
        
        return {
            "status": "success",
            "data": status,
            "api_version": "3.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[API_ERROR] 獲取系統狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/scheduler/statistics")
async def get_scheduler_statistics() -> Dict[str, Any]:
    """
    獲取排程器統計資訊
    
    Returns:
        Dict: 統計資訊
    """
    try:
        coordinator = get_email_coordinator()
        status = coordinator.get_processing_status()
        
        # 提取統計資訊
        scheduler_stats = status.get('scheduler_status', {}).get('statistics', {})
        task_manager_stats = status.get('task_manager_status', {})
        
        statistics = {
            'scheduler_statistics': scheduler_stats,
            'task_manager_statistics': {
                'active_tasks': task_manager_stats.get('active_tasks', 0),
                'total_tasks': task_manager_stats.get('total_tasks', 0),
                'status_distribution': task_manager_stats.get('status_distribution', {})
            },
            'system_info': {
                'gtk_scheduling_enabled': status.get('gtk_scheduling_enabled', False),
                'supported_vendors': status.get('supported_vendors', []),
                'uptime_seconds': status.get('scheduler_status', {}).get('uptime_seconds', 0)
            }
        }
        
        logger.info(f"[API] 統計資訊查詢完成")
        
        return {
            "status": "success",
            "data": statistics,
            "api_version": "3.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[API_ERROR] 獲取統計資訊失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 配置管理 API
# ================================

@router.post("/api/scheduler/config/gtk_scheduling")
async def toggle_gtk_scheduling(
    enabled: bool = Form(..., description="是否啟用GTK排程")
) -> Dict[str, Any]:
    """
    切換GTK排程功能
    
    Args:
        enabled: 是否啟用GTK排程
        
    Returns:
        Dict: 配置更新結果
    """
    try:
        coordinator = get_email_coordinator()
        coordinator.enable_gtk_scheduling = enabled
        coordinator.scheduler.enable_gtk_scheduling = enabled
        
        logger.info(f"[API] GTK排程功能已{'啟用' if enabled else '停用'}")
        
        return {
            "status": "success",
            "message": f"GTK排程功能已{'啟用' if enabled else '停用'}",
            "gtk_scheduling_enabled": enabled,
            "api_version": "3.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[API_ERROR] 配置GTK排程失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 前端介面 API
# ================================

@router.get("/", response_class=HTMLResponse)
async def scheduler_root():
    """
    排程器根路徑，重定向到儀表板
    """
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/scheduler/scheduler_dashboard", status_code=302)

@router.get("/logs", response_class=HTMLResponse)
async def scheduler_logs():
    """
    排程器日誌查看頁面 - 終端機風格

    Returns:
        HTMLResponse: 終端機風格的日誌查看介面
    """
    try:
        # 獲取最近的日誌
        from src.services.unified_logger import UnifiedLogger
        import os
        from pathlib import Path

        unified_logger = UnifiedLogger()
        log_stats = unified_logger.get_log_statistics()

        # 讀取最新的日誌內容
        log_content = ""
        log_directory = Path("logs")

        # 獲取最新的幾個重要日誌文件
        important_logs = [
            "integrated_services.log",
            "EmailInboxApp.log",
            "EmailSyncService.log",
            "EmailProcessingCoordinator.log",
            "EnhancedTaskScheduler.log"
        ]

        for log_file in important_logs:
            log_path = log_directory / log_file
            if log_path.exists():
                try:
                    # 讀取最後 50 行
                    with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                        recent_lines = lines[-50:] if len(lines) > 50 else lines

                    log_content += f"\\n=== {log_file} (最近 {len(recent_lines)} 行) ===\\n"
                    log_content += "".join(recent_lines)
                    log_content += "\\n" + "="*80 + "\\n"

                except Exception as e:
                    log_content += f"\\n=== {log_file} - 讀取失敗: {e} ===\\n"

        if not log_content:
            log_content = "沒有找到日誌文件或日誌為空"

        # 終端機風格的 HTML
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-TW">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>排程器日誌 - 終端機檢視</title>
            <style>
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    background: #1a1a1a;
                    color: #00ff00;
                    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
                    font-size: 12px;
                    line-height: 1.4;
                }}

                .terminal-header {{
                    background: #333;
                    padding: 10px 20px;
                    border-bottom: 1px solid #555;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }}

                .terminal-title {{
                    color: #fff;
                    font-weight: bold;
                }}

                .terminal-controls {{
                    display: flex;
                    gap: 10px;
                }}

                .back-btn {{
                    background: #007bff;
                    color: white;
                    padding: 5px 15px;
                    text-decoration: none;
                    border-radius: 3px;
                    font-size: 11px;
                    transition: background 0.3s;
                }}

                .back-btn:hover {{
                    background: #0056b3;
                }}

                .refresh-btn {{
                    background: #28a745;
                    color: white;
                    padding: 5px 15px;
                    border: none;
                    border-radius: 3px;
                    font-size: 11px;
                    cursor: pointer;
                    transition: background 0.3s;
                }}

                .refresh-btn:hover {{
                    background: #1e7e34;
                }}

                .terminal-stats {{
                    background: #2a2a2a;
                    padding: 10px 20px;
                    border-bottom: 1px solid #444;
                    color: #ffff00;
                    font-size: 11px;
                }}

                .terminal-content {{
                    padding: 20px;
                    height: calc(100vh - 120px);
                    overflow-y: auto;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                }}

                .log-line {{
                    margin-bottom: 2px;
                }}

                .log-error {{
                    color: #ff4444;
                }}

                .log-warning {{
                    color: #ffaa00;
                }}

                .log-info {{
                    color: #00aaff;
                }}

                .log-success {{
                    color: #00ff00;
                }}

                .log-timestamp {{
                    color: #888;
                }}

                .section-header {{
                    color: #ffff00;
                    background: #333;
                    padding: 5px;
                    margin: 10px 0;
                    border-left: 4px solid #ffff00;
                }}

                /* 滾動條樣式 */
                .terminal-content::-webkit-scrollbar {{
                    width: 12px;
                }}

                .terminal-content::-webkit-scrollbar-track {{
                    background: #2a2a2a;
                }}

                .terminal-content::-webkit-scrollbar-thumb {{
                    background: #555;
                    border-radius: 6px;
                }}

                .terminal-content::-webkit-scrollbar-thumb:hover {{
                    background: #777;
                }}
            </style>
        </head>
        <body>
            <div class="terminal-header">
                <div class="terminal-title">📋 排程器日誌終端機 - {log_stats.get('total_files', 0)} 個日誌文件</div>
                <div class="terminal-controls">
                    <button class="refresh-btn" onclick="location.reload()">🔄 刷新</button>
                    <a href="/scheduler/scheduler_dashboard" class="back-btn">← 返回儀表板</a>
                </div>
            </div>

            <div class="terminal-stats">
                📊 統計: 總大小 {log_stats.get('total_size_mb', 0):.2f} MB |
                最新: {log_stats.get('latest_file', 'N/A')} |
                更新時間: {log_stats.get('last_updated', 'N/A')}
            </div>

            <div class="terminal-content" id="logContent">{log_content}</div>

            <script>
                // 自動滾動到底部
                const content = document.getElementById('logContent');
                content.scrollTop = content.scrollHeight;

                // 高亮日誌級別
                function highlightLogs() {{
                    const content = document.getElementById('logContent');
                    let html = content.innerHTML;

                    // 高亮不同級別的日誌
                    html = html.replace(/ERROR|CRITICAL|FATAL/g, '<span class="log-error">$&</span>');
                    html = html.replace(/WARNING|WARN/g, '<span class="log-warning">$&</span>');
                    html = html.replace(/INFO/g, '<span class="log-info">$&</span>');
                    html = html.replace(/SUCCESS|OK/g, '<span class="log-success">$&</span>');

                    // 高亮時間戳
                    html = html.replace(/(\\d{{4}}-\\d{{2}}-\\d{{2}}\\s+\\d{{2}}:\\d{{2}}:\\d{{2}})/g, '<span class="log-timestamp">$1</span>');

                    content.innerHTML = html;
                }}

                // 頁面載入後執行高亮
                document.addEventListener('DOMContentLoaded', highlightLogs);
            </script>
        </body>
        </html>
        """

        return HTMLResponse(content=html_content)

    except Exception as e:
        logger.error(f"載入日誌頁面失敗: {e}")
        return HTMLResponse(content=f"<h1>載入日誌頁面失敗: {e}</h1>", status_code=500)

@router.get("/logs/content")
async def get_logs_content():
    """
    獲取日誌內容 API - 用於動態刷新

    Returns:
        JSON: 包含日誌內容和統計信息
    """
    try:
        from src.services.unified_logger import UnifiedLogger
        import os
        from pathlib import Path

        unified_logger = UnifiedLogger()
        log_stats = unified_logger.get_log_statistics()

        # 讀取最新的日誌內容
        log_content = ""
        log_directory = Path("logs")

        # 獲取最新的幾個重要日誌文件
        important_logs = [
            "integrated_services.log",
            "EmailInboxApp.log",
            "EmailSyncService.log",
            "EmailProcessingCoordinator.log",
            "EnhancedTaskScheduler.log"
        ]

        for log_file in important_logs:
            log_path = log_directory / log_file
            if log_path.exists():
                try:
                    # 讀取最後 50 行
                    with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                        recent_lines = lines[-50:] if len(lines) > 50 else lines

                    log_content += f"\\n=== {log_file} (最近 {len(recent_lines)} 行) ===\\n"
                    log_content += "".join(recent_lines)
                    log_content += "\\n" + "="*80 + "\\n"

                except Exception as e:
                    log_content += f"\\n=== {log_file} - 讀取失敗: {e} ===\\n"

        if not log_content:
            log_content = "沒有找到日誌文件或日誌為空"

        return {
            "success": True,
            "content": log_content,
            "stats": {
                "total_files": log_stats.get('total_files', 0),
                "total_size_mb": log_stats.get('total_size_mb', 0),
                "latest_file": log_stats.get('latest_file', 'N/A')
            },
            "timestamp": log_stats.get('last_updated', 'N/A')
        }

    except Exception as e:
        logger.error(f"獲取日誌內容失敗: {e}")
        return {
            "success": False,
            "error": str(e),
            "content": f"獲取日誌內容失敗: {e}"
        }

@router.get("/scheduler_dashboard", response_class=HTMLResponse)
async def scheduler_dashboard():
    """
    增強任務排程器儀表板

    Returns:
        HTMLResponse: 排程器管理前端介面
    """
    try:
        # 讀取 HTML 模板
        template_path = Path(__file__).parent.parent / "web" / "templates" / "scheduler_dashboard.html"
        
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            return HTMLResponse(content=html_content)
        else:
            # 如果模板檔案不存在，返回簡單的 HTML
            simple_html = f"""
            <!DOCTYPE html>
            <html lang="zh-TW">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>增強任務排程器儀表板</title>
                <style>
                    body {{ 
                        font-family: 'Microsoft JhengHei', Arial, sans-serif; 
                        margin: 40px; 
                        background-color: #f5f5f5;
                    }}
                    .container {{ 
                        max-width: 1200px; 
                        margin: 0 auto; 
                        background: white;
                        padding: 20px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }}
                    .header {{
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }}
                    .btn {{ 
                        padding: 12px 24px; 
                        margin: 10px; 
                        background: #007bff; 
                        color: white; 
                        text-decoration: none; 
                        border-radius: 5px; 
                        display: inline-block;
                        border: none;
                        cursor: pointer;
                    }}
                    .btn:hover {{
                        background: #0056b3;
                    }}
                    .btn-secondary {{
                        background: #6c757d;
                    }}
                    .btn-success {{
                        background: #28a745;
                    }}
                    .btn-warning {{
                        background: #ffc107;
                        color: #212529;
                    }}
                    .api-section {{
                        margin: 20px 0;
                        padding: 15px;
                        background: #f8f9fa;
                        border-left: 4px solid #007bff;
                    }}
                    .feature-grid {{
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                        gap: 20px;
                        margin: 20px 0;
                    }}
                    .feature-card {{
                        background: #ffffff;
                        padding: 20px;
                        border: 1px solid #dee2e6;
                        border-radius: 8px;
                    }}
                    .status-indicator {{
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        border-radius: 50%;
                        background: #28a745;
                        margin-right: 8px;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🚀 增強任務排程器儀表板</h1>
                        <p><span class="status-indicator"></span>系統運行中 - GTK特殊排程系統</p>
                    </div>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>📧 郵件處理</h3>
                            <p>統一郵件處理入口，支援GTK排程邏輯</p>
                            <a href="/api/scheduler/process_email" class="btn btn-primary">郵件處理API</a>
                        </div>
                        
                        <div class="feature-card">
                            <h3>⏰ GTK排程</h3>
                            <p>每小時01分和31分精確執行</p>
                            <a href="/api/scheduler/gtk/next_executions" class="btn btn-success">查看執行時間</a>
                        </div>
                        
                        <div class="feature-card">
                            <h3>📋 任務管理</h3>
                            <p>排程任務狀態監控和管理</p>
                            <a href="/api/scheduler/tasks/scheduled" class="btn btn-warning">查看排程任務</a>
                        </div>
                        
                        <div class="feature-card">
                            <h3>📊 系統監控</h3>
                            <p>系統狀態和統計資訊</p>
                            <a href="/api/scheduler/status" class="btn btn-secondary">系統狀態</a>
                        </div>
                    </div>
                    
                    <div class="api-section">
                        <h3>🔗 主要API端點</h3>
                        <ul>
                            <li><strong>POST /api/scheduler/process_email</strong> - 統一郵件處理入口</li>
                            <li><strong>GET /api/scheduler/tasks/scheduled</strong> - 查看排程任務</li>
                            <li><strong>GET /api/scheduler/gtk/next_executions</strong> - GTK執行時間</li>
                            <li><strong>GET /api/scheduler/status</strong> - 系統狀態</li>
                            <li><strong>GET /api/scheduler/statistics</strong> - 統計資訊</li>
                            <li><strong>DELETE /api/scheduler/tasks/{{task_id}}</strong> - 取消任務</li>
                        </ul>
                    </div>
                    
                    <div class="api-section">
                        <h3>🎯 核心功能</h3>
                        <ul>
                            <li><strong>GTK特殊排程</strong>：01-30分收到 → 31分執行，31-59分收到 → 下小時01分執行</li>
                            <li><strong>其他廠商立即處理</strong>：JCET、AMIC、OSE、SPIL、ASE、MSEC等</li>
                            <li><strong>企業級錯誤處理</strong>：重試機制、失敗恢復、通知服務</li>
                            <li><strong>高精度時間觸發</strong>：APScheduler驅動的cron觸發器</li>
                            <li><strong>任務生命週期管理</strong>：創建、排程、執行、完成、取消</li>
                        </ul>
                    </div>
                    
                    <div style="text-align: center; margin-top: 40px;">
                        <a href="/docs" class="btn btn-primary">📖 完整API文檔</a>
                        <a href="/task_manager" class="btn btn-secondary">🔄 並發任務管理器</a>
                    </div>
                    
                    <p style="text-align: center; color: #6c757d; margin-top: 30px;">
                        增強任務排程器 v3.0.0 | 企業級自動化郵件處理系統
                    </p>
                </div>
            </body>
            </html>
            """
            return HTMLResponse(content=simple_html)
    except Exception as e:
        logger.error(f"載入排程器儀表板失敗：{str(e)}")
        raise HTTPException(status_code=500, detail="無法載入儀表板")

# ================================
# 排程器生命週期管理
# ================================

def initialize_enhanced_scheduler() -> None:
    """
    初始化增強任務排程器
    
    這個函數被主應用程式在啟動時調用
    """
    try:
        coordinator = get_email_coordinator(enable_gtk_scheduling=True)
        logger.info("[OK] 增強任務排程器已初始化")
    except Exception as e:
        logger.error(f"[ERROR] 增強任務排程器初始化失敗: {str(e)}")

def shutdown_enhanced_scheduler() -> None:
    """
    優雅關閉增強任務排程器
    
    這個函數被主應用程式在關閉時調用
    """
    try:
        from src.services.email_processing_coordinator import _coordinator_instance
        if _coordinator_instance:
            _coordinator_instance.shutdown()
            logger.info("[OK] 增強任務排程器已優雅關閉")
    except Exception as e:
        logger.error(f"[ERROR] 關閉增強任務排程器時發生錯誤: {str(e)}")