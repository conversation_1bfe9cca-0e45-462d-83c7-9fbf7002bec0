# Simple activation script that stays in current session
# Usage: . .\dev_env.ps1

if (Test-Path ".\venv_win_3_11_12\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..." -ForegroundColor Green
    . ".\venv_win_3_11_12\Scripts\Activate.ps1"
    Write-Host "Environment ready! Available commands:" -ForegroundColor Yellow
    Write-Host "  python start_integrated_services.py" -ForegroundColor Cyan
    Write-Host "  make help" -ForegroundColor Cyan
    Write-Host "  pytest" -ForegroundColor Cyan
} else {
    Write-Host "Virtual environment not found!" -ForegroundColor Red
}