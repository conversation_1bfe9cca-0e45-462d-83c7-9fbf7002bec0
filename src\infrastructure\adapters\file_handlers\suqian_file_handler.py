"""
SUQIAN (宿迁) 廠商檔案處理器
對應 VBA 的 CopyFilesSuqian 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class SuqianFileHandler(BaseFileHandler):
    """
    SUQIAN (宿迁) 廠商檔案處理器

    VBA 邏輯：
    - 來源路徑：sourcePath & "\JCET\SUQIAN\" & pd & "\" & lot & "\"
    - 搜尋模式：包含 MO 編號的檔案
    - 檔案類型：所有檔案（主要是壓縮檔）
    - 特殊邏輯：需要 pd 和 lot 參數來構建完整路徑
    """

    def __init__(self, source_base_path: str):
        """初始化 SUQIAN 檔案處理器"""
        super().__init__(source_base_path, "SUQIAN")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default",
                        mo: str = "") -> List[Path]:
        """
        SUQIAN 的來源路徑
        
        VBA: sourcePathSuqian = sourcePath & "\JCET\SUQIAN\" & pd & "\" & lot & "\"
        """
        paths = []
        
        # SUQIAN 需要 pd 和 lot 來構建完整路徑
        if pd != "default" and lot != "default":
            suqian_path = self.source_base_path / "JCET" / "SUQIAN" / pd / lot
            paths.append(suqian_path)
            self.logger.info(f"🏭 SUQIAN 路徑: {suqian_path}")
        else:
            self.logger.warning("⚠️  SUQIAN 需要有效的 PD 和 LOT 參數來構建路徑")
            self.logger.warning(f"   當前 PD: '{pd}', LOT: '{lot}'")
            
            # 如果缺少參數，嘗試基礎路徑
            base_suqian_path = self.source_base_path / "JCET" / "SUQIAN"
            if base_suqian_path.exists():
                paths.append(base_suqian_path)
                self.logger.info(f"   退回使用基礎路徑: {base_suqian_path}")
        
        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        SUQIAN 的檔案搜尋模式
        
        VBA: If InStr(1, file.name, mo, vbTextCompare) > 0 Then
        """
        patterns = []
        
        if mo:
            patterns.append(f"*{mo}*")  # VBA 模式：包含 MO
            
        return patterns
        
    def _copy_by_mo(self, source_path: Path, dest_path: Path, mo: str) -> bool:
        """
        覆寫 MO 複製邏輯，實作 SUQIAN 特殊的檔案過濾邏輯
        
        VBA 邏輯：
        For Each file In fso.GetFolder(sourcePathSuqian).files
            If InStr(1, file.name, mo, vbTextCompare) > 0 Then
        """
        try:
            self.logger.info(f"      🔍 SUQIAN 特殊邏輯: 搜尋包含 MO '{mo}' 的檔案")
            
            if not mo:
                self.logger.warning(f"      ❌ MO 參數為空")
                return False
            
            if not source_path.exists():
                self.logger.warning(f"      ❌ 來源路徑不存在: {source_path}")
                return False
            
            # 取得資料夾中的所有檔案
            all_files = [f for f in source_path.iterdir() if f.is_file()]
            
            # 篩選包含 MO 的檔案（不區分大小寫）
            matching_files = [f for f in all_files if mo.lower() in f.name.lower()]
            
            self.logger.info(f"      📁 找到 {len(all_files)} 個檔案")
            self.logger.info(f"      🎯 其中 {len(matching_files)} 個包含 MO '{mo}'")
            
            if not matching_files:
                self.logger.warning(f"      ❌ 沒有找到包含 MO '{mo}' 的檔案")
                return False
            
            # 顯示找到的檔案
            for f in matching_files[:5]:  # 只顯示前5個
                self.logger.info(f"         - {f.name}")
            if len(matching_files) > 5:
                self.logger.info(f"         ... 還有 {len(matching_files) - 5} 個檔案")
            
            # 複製所有符合的檔案
            success = False
            copied_count = 0
            
            for file_path in matching_files:
                try:
                    if self._copy_file_with_check(file_path, dest_path):
                        copied_count += 1
                        success = True
                        self.logger.info(f"      ✅ 成功複製: {file_path.name}")
                    else:
                        self.logger.warning(f"      ❌ 複製失敗: {file_path.name}")
                except Exception as e:
                    self.logger.error(f"      ❌ 複製檔案錯誤 {file_path.name}: {e}")
            
            if success:
                self.logger.info(f"      🎉 SUQIAN 複製完成: {copied_count}/{len(matching_files)} 個檔案成功")
            
            return success
                
        except Exception as e:
            self.logger.error(f"SUQIAN MO 複製失敗: {e}")
            return False
        
    def _supports_folder_copy(self) -> bool:
        """SUQIAN 不支援資料夾複製"""
        return False