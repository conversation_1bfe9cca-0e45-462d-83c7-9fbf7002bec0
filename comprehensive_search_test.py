#!/usr/bin/env python3
"""
Comprehensive Search Testing Suite
Tests all four identified issues with both backend and frontend validation
"""

import asyncio
import json
import time
from pathlib import Path
from playwright.async_api import async_playwright

class SearchTestSuite:
    def __init__(self):
        self.results = {
            "issue_1_data_display": {"status": "pending", "details": []},
            "issue_2_result_organization": {"status": "pending", "details": []},
            "issue_3_search_scope": {"status": "pending", "details": []},
            "issue_4_string_matching": {"status": "pending", "details": []}
        }
    
    async def run_all_tests(self):
        """Run all test scenarios"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, slow_mo=500)
            context = await browser.new_context()
            
            try:
                # Test Issue 1: G2735 Backend vs Frontend
                await self.test_issue_1_g2735_display(context)
                
                # Test Issue 2: Result organization
                await self.test_issue_2_organization(context)
                
                # Test Issue 3: Search scope
                await self.test_issue_3_scope(context)
                
                # Test Issue 4: String matching
                await self.test_issue_4_string_matching(context)
                
            finally:
                await browser.close()
        
        # Generate test report
        self.generate_report()
    
    async def test_issue_1_g2735_display(self, context):
        """Test Issue 1: Backend finds G2735 but frontend doesn't display"""
        print("🧪 Testing Issue 1: G2735 Backend vs Frontend Data Display")
        
        page = await context.new_page()
        api_responses = []
        
        # Capture API responses
        def handle_response(response):
            if "/api/search/product" in response.url or "/api/task/status" in response.url:
                api_responses.append({
                    "url": response.url,
                    "status": response.status,
                    "timestamp": time.time()
                })
        
        page.on("response", handle_response)
        
        try:
            await page.goto("http://localhost:8009/network/ui")
            await page.wait_for_selector("#productNameInput", timeout=30000)
            
            # Perform G2735 search
            await page.fill("#productNameInput", "G2735")
            await page.select_option("#timeRangeSelect", "last_6_months")
            await page.check("#includeDirsCheckbox")
            await page.click("#productSearchBtn")
            
            # Wait for completion
            await self.wait_for_search_completion(page)
            
            # Check results
            file_items = page.locator(".file-item")
            item_count = await file_items.count()
            
            # Check file count display
            file_count_element = page.locator("#fileCount")
            file_count_text = await file_count_element.text_content() if await file_count_element.is_visible() else "Not visible"
            
            # Check search status
            status_element = page.locator("#productSearchStatus")
            status_html = await status_element.inner_html() if await status_element.is_visible() else "No status"
            
            # Check current path
            path_element = page.locator("#currentPath")
            current_path = await path_element.text_content() if await path_element.is_visible() else "No path"
            
            self.results["issue_1_data_display"] = {
                "status": "completed",
                "details": {
                    "search_term": "G2735",
                    "api_responses": len(api_responses),
                    "ui_file_count": item_count,
                    "file_count_display": file_count_text,
                    "search_status": status_html,
                    "current_path": current_path,
                    "success": item_count > 0 and "G2735" in current_path
                }
            }
            
            print(f"   ✅ API responses: {len(api_responses)}")
            print(f"   📁 UI file count: {item_count}")
            print(f"   📊 File count display: {file_count_text}")
            print(f"   🎯 Success: {self.results['issue_1_data_display']['details']['success']}")
            
        except Exception as e:
            self.results["issue_1_data_display"]["status"] = "error"
            self.results["issue_1_data_display"]["error"] = str(e)
            print(f"   ❌ Error: {e}")
        
        finally:
            await page.close()
    
    async def test_issue_2_organization(self, context):
        """Test Issue 2: Search result organization (directories first)"""
        print("🧪 Testing Issue 2: Search Result Organization")
        
        page = await context.new_page()
        
        try:
            await page.goto("http://localhost:8009/network/ui")
            await page.wait_for_selector("#productNameInput", timeout=30000)
            
            # Search for something that should return both files and directories
            await page.fill("#productNameInput", "test")
            await page.check("#includeDirsCheckbox")
            await page.click("#productSearchBtn")
            
            await self.wait_for_search_completion(page)
            
            # Analyze file list organization
            file_items = page.locator(".file-item")
            item_count = await file_items.count()
            
            directories_first = True
            found_file_after_dir = False
            organization_details = []
            
            for i in range(min(10, item_count)):  # Check first 10 items
                file_item = file_items.nth(i)
                file_icon = file_item.locator(".file-icon")
                file_name = file_item.locator("h4")
                
                if await file_icon.is_visible() and await file_name.is_visible():
                    icon_class = await file_icon.get_attribute("class")
                    name = await file_name.text_content()
                    
                    is_directory = "folder" in icon_class
                    organization_details.append({
                        "index": i,
                        "name": name,
                        "is_directory": is_directory
                    })
                    
                    if not is_directory:
                        found_file_after_dir = True
                    elif found_file_after_dir:
                        directories_first = False
                        break
            
            self.results["issue_2_result_organization"] = {
                "status": "completed",
                "details": {
                    "search_term": "test",
                    "total_items": item_count,
                    "directories_first": directories_first,
                    "organization_details": organization_details,
                    "success": directories_first
                }
            }
            
            print(f"   📁 Total items: {item_count}")
            print(f"   📋 Directories first: {directories_first}")
            print(f"   🎯 Success: {directories_first}")
            
        except Exception as e:
            self.results["issue_2_result_organization"]["status"] = "error"
            self.results["issue_2_result_organization"]["error"] = str(e)
            print(f"   ❌ Error: {e}")
        
        finally:
            await page.close()
    
    async def test_issue_3_scope(self, context):
        """Test Issue 3: Search scope limitations"""
        print("🧪 Testing Issue 3: Search Scope Coverage")
        
        page = await context.new_page()
        search_results = {}
        
        # Test various search terms to check scope
        test_terms = ["G2735", "test", "KDD", "data", "FT"]
        
        try:
            await page.goto("http://localhost:8009/network/ui")
            await page.wait_for_selector("#productNameInput", timeout=30000)
            
            for term in test_terms:
                print(f"   🔍 Testing scope for: {term}")
                
                await page.fill("#productNameInput", term)
                await page.click("#productSearchBtn")
                await self.wait_for_search_completion(page)
                
                # Check results
                file_items = page.locator(".file-item")
                item_count = await file_items.count()
                
                # Check current path to see if it found anything
                path_element = page.locator("#currentPath")
                current_path = await path_element.text_content() if await path_element.is_visible() else ""
                
                search_results[term] = {
                    "item_count": item_count,
                    "current_path": current_path,
                    "found_results": item_count > 0
                }
                
                print(f"     📊 Found {item_count} items")
                
                # Clear search for next test
                await page.click(".btn.btn-secondary")  # Clear button
                await page.wait_for_timeout(1000)
            
            # Analyze scope coverage
            total_searches = len(test_terms)
            successful_searches = sum(1 for result in search_results.values() if result["found_results"])
            scope_coverage = successful_searches / total_searches
            
            self.results["issue_3_search_scope"] = {
                "status": "completed",
                "details": {
                    "test_terms": test_terms,
                    "search_results": search_results,
                    "successful_searches": successful_searches,
                    "total_searches": total_searches,
                    "scope_coverage": scope_coverage,
                    "success": scope_coverage >= 0.6  # At least 60% should find results
                }
            }
            
            print(f"   📈 Scope coverage: {scope_coverage:.1%}")
            print(f"   🎯 Success: {scope_coverage >= 0.6}")
            
        except Exception as e:
            self.results["issue_3_search_scope"]["status"] = "error"
            self.results["issue_3_search_scope"]["error"] = str(e)
            print(f"   ❌ Error: {e}")
        
        finally:
            await page.close()
    
    async def test_issue_4_string_matching(self, context):
        """Test Issue 4: Search string matching issues"""
        print("🧪 Testing Issue 4: String Matching Capabilities")
        
        page = await context.new_page()
        matching_tests = {}
        
        # Test various string patterns
        test_cases = [
            {"term": "KDD0530D1", "description": "Alphanumeric with numbers"},
            {"term": "G2735", "description": "Simple product code"},
            {"term": "test", "description": "Simple lowercase"},
            {"term": "TEST", "description": "Simple uppercase"},
            {"term": "Test", "description": "Mixed case"},
        ]
        
        try:
            await page.goto("http://localhost:8009/network/ui")
            await page.wait_for_selector("#productNameInput", timeout=30000)
            
            for test_case in test_cases:
                term = test_case["term"]
                description = test_case["description"]
                
                print(f"   🔍 Testing matching for: {term} ({description})")
                
                await page.fill("#productNameInput", term)
                await page.click("#productSearchBtn")
                await self.wait_for_search_completion(page)
                
                # Check results
                file_items = page.locator(".file-item")
                item_count = await file_items.count()
                
                # Check if any results contain the search term
                found_matches = False
                match_details = []
                
                for i in range(min(5, item_count)):
                    file_item = file_items.nth(i)
                    file_name = file_item.locator("h4")
                    
                    if await file_name.is_visible():
                        name = await file_name.text_content()
                        if term.lower() in name.lower():
                            found_matches = True
                            match_details.append(name)
                
                matching_tests[term] = {
                    "description": description,
                    "item_count": item_count,
                    "found_matches": found_matches,
                    "match_details": match_details,
                    "success": found_matches and item_count > 0
                }
                
                print(f"     📊 Found {item_count} items, matches: {found_matches}")
                
                # Clear for next test
                await page.click(".btn.btn-secondary")
                await page.wait_for_timeout(1000)
            
            # Analyze matching capabilities
            total_tests = len(test_cases)
            successful_matches = sum(1 for result in matching_tests.values() if result["success"])
            matching_accuracy = successful_matches / total_tests
            
            self.results["issue_4_string_matching"] = {
                "status": "completed",
                "details": {
                    "test_cases": test_cases,
                    "matching_tests": matching_tests,
                    "successful_matches": successful_matches,
                    "total_tests": total_tests,
                    "matching_accuracy": matching_accuracy,
                    "success": matching_accuracy >= 0.8  # At least 80% should work
                }
            }
            
            print(f"   🎯 Matching accuracy: {matching_accuracy:.1%}")
            print(f"   ✅ Success: {matching_accuracy >= 0.8}")
            
        except Exception as e:
            self.results["issue_4_string_matching"]["status"] = "error"
            self.results["issue_4_string_matching"]["error"] = str(e)
            print(f"   ❌ Error: {e}")
        
        finally:
            await page.close()
    
    async def wait_for_search_completion(self, page, timeout=60):
        """Wait for search to complete"""
        for i in range(timeout):
            await page.wait_for_timeout(1000)
            
            # Check if search button is re-enabled (indicates completion)
            search_btn = page.locator("#productSearchBtn")
            if await search_btn.is_enabled():
                # Wait a bit more for UI to update
                await page.wait_for_timeout(2000)
                break
            
            # Check for completion status
            status_element = page.locator("#productSearchStatus")
            if await status_element.is_visible():
                status_html = await status_element.inner_html()
                if "完成" in status_html or "失敗" in status_html:
                    break
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("📋 COMPREHENSIVE SEARCH TEST REPORT")
        print("="*80)
        
        for issue_key, result in self.results.items():
            issue_name = issue_key.replace("_", " ").title()
            status = result["status"]
            
            print(f"\n🔍 {issue_name}")
            print("-" * 60)
            
            if status == "completed":
                details = result["details"]
                success = details.get("success", False)
                print(f"Status: {'✅ PASS' if success else '❌ FAIL'}")
                
                # Print key metrics
                for key, value in details.items():
                    if key not in ["success", "organization_details", "match_details", "search_results", "matching_tests"]:
                        print(f"  {key}: {value}")
            
            elif status == "error":
                print(f"Status: ❌ ERROR")
                print(f"  Error: {result.get('error', 'Unknown error')}")
            
            else:
                print(f"Status: ⏳ {status.upper()}")
        
        # Overall summary
        completed_tests = sum(1 for r in self.results.values() if r["status"] == "completed")
        successful_tests = sum(1 for r in self.results.values() 
                             if r["status"] == "completed" and r["details"].get("success", False))
        
        print(f"\n" + "="*80)
        print(f"📊 OVERALL SUMMARY")
        print(f"   Tests Completed: {completed_tests}/4")
        print(f"   Tests Passed: {successful_tests}/4")
        print(f"   Success Rate: {successful_tests/4:.1%}")
        print("="*80)
        
        # Save detailed report
        with open("search_test_report.json", "w") as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved to: search_test_report.json")

async def main():
    """Main test execution"""
    print("🚀 Starting Comprehensive Search Test Suite")
    print("=" * 80)
    
    test_suite = SearchTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())