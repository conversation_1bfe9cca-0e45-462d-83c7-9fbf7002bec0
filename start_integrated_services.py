#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企業級整合服務啟動程式
統一所有服務到單一 5000 端口
支援 Flask + FastAPI 無縫整合

核心功能：
- 單一端口 (5000) 整合所有服務
- FastAPI 作為主框架，WSGIMiddleware 整合 Flask
- 統一配置管理和日誌系統
- 自動服務發現和健康檢查
- 企業級監控和管理介面
"""

import os
import sys
import asyncio
import signal
import threading
from pathlib import Path
from typing import Dict, Any, Optional
import traceback

# Windows UTF-8 支援和全局編碼修復
if os.name == 'nt':  # Windows
    os.system('chcp 65001 >nul 2>&1')
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

# 應用全局編碼修復
try:
    from unicode_fix_global import setup_unicode_environment
    setup_unicode_environment()
except ImportError:
    # 如果無法導入，直接設置基本編碼
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
        except Exception:
            pass

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 先設置 Unicode 環境
try:
    from unicode_fix_global import setup_unicode_environment, apply_unicode_patches
    setup_unicode_environment()
    apply_unicode_patches()
except ImportError as e:
    print(f"警告：無法導入 unicode_fix_global：{e}")

# 導入核心模組
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
from loguru import logger

# 導入自定義服務模組
from src.services.config_manager import get_config_manager, init_config_manager
from src.services.service_integrator import get_service_integrator, create_integrated_app
from src.services.route_manager import get_route_manager
from src.services.unified_logger import get_unified_logger, init_unified_logger


class IntegratedServiceManager:
    """整合服務管理器
    
    負責管理所有服務的生命週期，包括啟動、停止、監控等
    """
    
    def __init__(self):
        # 初始化配置和日誌
        self.config_manager = init_config_manager()
        self.unified_logger = init_unified_logger()
        self.service_integrator = get_service_integrator()
        self.route_manager = get_route_manager()
        
        self.app: Optional[FastAPI] = None
        self.server_process: Optional[threading.Thread] = None
        self.is_running = False
        
        # 設置信號處理
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信號 {signum}，正在關閉服務...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self) -> bool:
        """初始化所有服務"""
        try:
            logger.info("正在初始化企業級整合服務...")
            
            # 驗證配置
            validation_result = self.config_manager.validate_configuration()
            if not validation_result["valid"]:
                logger.error("配置驗證失敗:")
                for error in validation_result["errors"]:
                    logger.error(f"  - {error}")
                return False
            
            if validation_result["warnings"]:
                for warning in validation_result["warnings"]:
                    logger.warning(f"  - {warning}")
            
            # 創建整合應用程式
            self.app = self.service_integrator.create_integrated_app()
            
            # 添加管理介面
            self._add_management_interface()
            
            # 啟動所有服務
            await self.service_integrator.start_all_services()
            
            logger.success("企業級整合服務初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化服務失敗: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def _add_management_interface(self):
        """添加管理介面"""
        
        # Favicon 路由
        @self.app.get("/favicon.ico")
        async def favicon():
            """提供 favicon.ico"""
            favicon_path = Path(__file__).parent / "src" / "presentation" / "web" / "static" / "favicon.ico"
            if favicon_path.exists():
                return FileResponse(favicon_path, media_type="image/x-icon")
            else:
                raise HTTPException(status_code=404, detail="Favicon not found")
        
        # 管理介面首頁
        @self.app.get("/admin", response_class=HTMLResponse)
        async def admin_dashboard():
            """管理後台首頁"""
            return self._generate_admin_dashboard()
        
        # 服務狀態 API
        @self.app.get("/admin/api/status")
        async def get_service_status():
            """獲取所有服務狀態"""
            try:
                services = {}
                
                for name, config in self.config_manager.get_enabled_services().items():
                    # 檢查服務健康狀態
                    service_app = self.service_integrator.get_service_app(name)
                    
                    services[name] = {
                        "name": config.name,
                        "type": config.service_type.value,
                        "port": config.port,
                        "path_prefix": config.path_prefix,
                        "enabled": config.enabled,
                        "status": "running" if service_app else "stopped",
                        "metadata": config.metadata
                    }
                
                return {
                    "main_port": self.config_manager.get_main_port(),
                    "services": services,
                    "total_services": len(services),
                    "running_services": sum(1 for s in services.values() if s["status"] == "running")
                }
                
            except Exception as e:
                logger.error(f"獲取服務狀態失敗: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # 路由信息 API
        @self.app.get("/admin/api/routes")
        async def get_route_info():
            """獲取路由信息"""
            try:
                return self.route_manager.get_route_mapping()
            except Exception as e:
                logger.error(f"獲取路由信息失敗: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # 日誌統計 API
        @self.app.get("/admin/api/logs")
        async def get_log_statistics():
            """獲取日誌統計"""
            try:
                return self.unified_logger.get_log_statistics()
            except Exception as e:
                logger.error(f"獲取日誌統計失敗: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # 配置信息 API
        @self.app.get("/admin/api/config")
        async def get_configuration():
            """獲取配置信息"""
            try:
                return {
                    "main_port": self.config_manager.get_main_port(),
                    "cors_origins": self.config_manager.get_cors_origins(),
                    "services": {
                        name: {
                            "name": config.name,
                            "type": config.service_type.value,
                            "port": config.port,
                            "path_prefix": config.path_prefix,
                            "enabled": config.enabled,
                            "host": config.host,
                            "timeout": config.timeout
                        }
                        for name, config in self.config_manager.get_enabled_services().items()
                    }
                }
            except Exception as e:
                logger.error(f"獲取配置信息失敗: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    def _generate_admin_dashboard(self) -> str:
        """生成管理後台 HTML"""
        return """
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企業級整合服務管理後台</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .status.running { background: #d4edda; color: #155724; }
        .status.stopped { background: #f8d7da; color: #721c24; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>企業級整合服務管理後台</h1>
            <p>統一管理所有 Email 處理、檔案分析和任務排程服務</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>服務狀態</h3>
                <div id="service-status">載入中...</div>
            </div>
            
            <div class="card">
                <h3>路由信息</h3>
                <div id="route-info">載入中...</div>
            </div>
        </div>
        
        <div class="card">
            <h3>服務列表</h3>
            <table id="services-table">
                <thead>
                    <tr>
                        <th>服務名稱</th>
                        <th>類型</th>
                        <th>路徑前綴</th>
                        <th>狀態</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="services-body">
                    <tr><td colspan="5">載入中...</td></tr>
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h3>快速連結</h3>
            <p>
                <a href="/docs" class="btn">API 文檔</a>
                <a href="/health" class="btn">健康檢查</a>
                <a href="/inbox" class="btn">郵件收件夾</a>
                <a href="/ft-eqc" class="btn">FT-EQC 處理</a>
                <a href="/scheduler" class="btn">任務排程器</a>
                <a href="/network" class="btn">網路瀏覽器 (原版)</a>
                <a href="/network/ui_new" class="btn" style="background: #28a745;">網路瀏覽器 (新版)</a>
            </p>
        </div>
    </div>
    
    <script>
        // 載入服務狀態
        async function loadServiceStatus() {
            try {
                const response = await fetch('/admin/api/status');
                const data = await response.json();
                
                document.getElementById('service-status').innerHTML = `
                    <p>主端口: <strong>${data.main_port}</strong></p>
                    <p>總服務數: <strong>${data.total_services}</strong></p>
                    <p>運行中: <strong>${data.running_services}</strong></p>
                `;
                
                // 更新服務表格
                const tbody = document.getElementById('services-body');
                tbody.innerHTML = Object.entries(data.services).map(([key, service]) => `
                    <tr>
                        <td>${service.name}</td>
                        <td>${service.type}</td>
                        <td>${service.path_prefix}</td>
                        <td><span class="status ${service.status}">${service.status}</span></td>
                        <td><a href="${service.path_prefix}" class="btn">訪問</a></td>
                    </tr>
                `).join('');
                
            } catch (error) {
                console.error('載入服務狀態失敗:', error);
            }
        }
        
        // 載入路由信息
        async function loadRouteInfo() {
            try {
                const response = await fetch('/admin/api/routes');
                const data = await response.json();
                
                document.getElementById('route-info').innerHTML = `
                    <p>總路由數: <strong>${data.statistics.total_routes}</strong></p>
                    <p>服務數: <strong>${data.statistics.total_services}</strong></p>
                    <p>衝突數: <strong>${data.statistics.total_conflicts}</strong></p>
                `;
            } catch (error) {
                console.error('載入路由信息失敗:', error);
            }
        }
        
        // 頁面載入時執行
        document.addEventListener('DOMContentLoaded', function() {
            loadServiceStatus();
            loadRouteInfo();
            
            // 每30秒自動刷新
            setInterval(() => {
                loadServiceStatus();
                loadRouteInfo();
            }, 30000);
        });
    </script>
</body>
</html>
        """
    
    async def start_server(self):
        """啟動服務器"""
        if not self.app:
            logger.error("應用程式未初始化")
            return False
        
        try:
            main_port = self.config_manager.get_main_port()
            
            logger.info(f"正在啟動企業級整合服務，端口: {main_port}")
            
            # 使用 uvicorn 啟動服務器
            config = uvicorn.Config(
                self.app,
                host="0.0.0.0",
                port=main_port,
                log_level="debug",  # 改為 debug 級別以查看詳細日誌
                access_log=True,
                reload=False
            )
            
            server = uvicorn.Server(config)
            
            self.is_running = True
            
            # 打印啟動信息
            self._print_startup_info()
            
            # 啟動服務器
            await server.serve()
            
        except Exception as e:
            logger.error(f"啟動服務器失敗: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def _print_startup_info(self):
        """打印啟動信息"""
        main_port = self.config_manager.get_main_port()
        
        print("\n" + "=" * 70)
        print("🚀 企業級整合服務啟動成功")
        print("=" * 70)
        print(f"📍 主服務地址: http://localhost:{main_port}")
        print(f"📊 管理後台: http://localhost:{main_port}/admin")
        print(f"📚 API 文檔: http://localhost:{main_port}/docs")
        print(f"💚 健康檢查: http://localhost:{main_port}/health")
        print("\n🔗 集成服務:")
        
        for name, config in self.config_manager.get_enabled_services().items():
            print(f"   • {config.name}: http://localhost:{main_port}{config.path_prefix}")
        
        print("\n" + "=" * 70)
        print("按 Ctrl+C 停止服務")
        print("=" * 70 + "\n")
    
    async def shutdown(self):
        """關閉所有服務"""
        if not self.is_running:
            return
        
        logger.info("正在關閉企業級整合服務...")
        
        try:
            # 停止所有服務
            await self.service_integrator.stop_all_services()
            
            # 清理日誌
            self.unified_logger.cleanup_old_logs()
            
            self.is_running = False
            logger.success("企業級整合服務已安全關閉")
            
        except Exception as e:
            logger.error(f"關閉服務時發生錯誤: {e}")


async def main():
    """主函數"""
    manager = IntegratedServiceManager()
    
    try:
        # 初始化服務
        if not await manager.initialize():
            logger.error("服務初始化失敗，退出程式")
            sys.exit(1)
        
        # 啟動服務器
        await manager.start_server()
        
    except KeyboardInterrupt:
        logger.info("收到中斷信號，正在關閉服務...")
    except Exception as e:
        logger.error(f"運行時發生錯誤: {e}")
        logger.error(traceback.format_exc())
    finally:
        await manager.shutdown()


if __name__ == "__main__":
    try:
        # Windows 環境特殊處理
        if os.name == 'nt':  
            # 設置事件循環策略
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 運行主程式
        asyncio.run(main())
        
    except Exception as e:
        print(f"程式啟動失敗: {e}")
        traceback.print_exc()
        sys.exit(1)