# 性能基准测试与监控计划

## 🎯 测试策略概览

### 测试目标
1. **建立性能基线** - 记录当前系统各项性能指标
2. **验证异步优化效果** - 量化异步升级带来的性能提升
3. **发现性能瓶颈** - 识别系统在高负载下的薄弱环节
4. **确保系统稳定性** - 验证优化后系统的可靠性和稳定性

### 测试方法论
- **黑盒测试**: 从用户角度测试 API 端点性能
- **白盒测试**: 深入代码层面分析性能瓶颈
- **负载测试**: 模拟真实用户负载场景
- **压力测试**: 测试系统极限承载能力
- **长时间测试**: 验证系统长期运行稳定性

## 📋 测试场景设计

### 1. API 端点性能测试

#### 1.1 FastAPI 端点测试场景
```yaml
健康检查端点测试:
  端点: GET /health, /api/health
  测试用例:
    - 单用户连续请求 (1000次)
    - 并发用户测试 (10, 50, 100用户)
    - 长时间持续测试 (30分钟)
  预期指标:
    - 平均响应时间 < 20ms
    - P95 响应时间 < 50ms
    - 成功率 = 100%

EQC 处理端点测试:
  端点: POST /api/scan_eqc_bin1, /api/process_eqc_standard
  测试用例:
    - 不同大小文件处理 (1MB, 10MB, 100MB)
    - 并发文件处理 (5, 10, 20个并发)
    - 错误恢复测试 (文件损坏、格式错误)
  预期指标:
    - 小文件 (<10MB): 响应时间 < 5秒
    - 大文件 (>10MB): 异步处理，即时响应
    - 并发处理数 > 10个

文件上传端点测试:
  端点: POST /api/upload_file, /api/upload_and_process
  测试用例:
    - 不同大小文件上传 (1MB-500MB)
    - 并发上传测试 (10, 20个并发)
    - 网络中断恢复测试
  预期指标:
    - 上传响应时间 < 100ms (异步)
    - 并发上传数 > 20个
    - 上传成功率 > 99%
```

#### 1.2 Flask 端点测试场景
```yaml
邮件查询端点测试:
  端点: /api/emails, /api/emails/<id>
  测试用例:
    - 不同数据量查询 (100, 1000, 5000条)
    - 复杂查询测试 (搜索、过滤、排序)
    - 分页查询性能测试
  预期指标:
    - 简单查询: < 200ms
    - 复杂查询: < 800ms
    - 分页查询: < 300ms

批量操作端点测试:
  端点: POST /api/emails/batch-delete, /api/emails/batch-mark-read
  测试用例:
    - 小批量操作 (10, 50条)
    - 大批量操作 (100, 500, 1000条)
    - 极限批量测试 (5000条)
  预期指标:
    - 小批量 (<50): < 2秒
    - 大批量 (500): < 10秒
    - 极限批量: < 30秒

邮件同步端点测试:
  端点: POST /api/sync, /api/sync/auto/start
  测试用例:
    - 同步不同数量邮件 (10, 50, 100, 500)
    - 并发同步测试
    - 网络异常处理测试
  预期指标:
    - 同步速度: > 5封/秒
    - 大批量同步: 500封 < 2分钟
    - 错误恢复率: > 95%
```

### 2. 数据库性能测试

#### 2.1 查询性能测试场景
```yaml
基础查询测试:
  测试类型: SELECT, INSERT, UPDATE, DELETE
  数据规模: 1K, 10K, 100K, 1M条记录
  测试用例:
    - 单表查询性能
    - JOIN 查询性能
    - 聚合查询性能 (COUNT, SUM, AVG)
    - 索引效果验证
  预期指标:
    - 单表查询: < 50ms
    - JOIN 查询: < 200ms
    - 聚合查询: < 600ms

并发访问测试:
  测试场景:
    - 并发读取测试 (10, 20, 50个连接)
    - 读写混合测试 (70%读, 30%写)
    - 死锁和锁等待测试
  预期指标:
    - 并发读取: 支持 20个连接
    - 锁等待时间: < 100ms
    - 死锁发生率: < 0.1%

事务性能测试:
  测试场景:
    - 短事务处理 (单表操作)
    - 长事务处理 (多表操作)
    - 事务回滚性能
  预期指标:
    - 短事务: < 10ms
    - 长事务: < 100ms
    - 回滚时间: < 50ms
```

#### 2.2 数据库压力测试
```yaml
大数据量测试:
  数据规模: 100万邮件记录 + 50万附件记录
  测试场景:
    - 全表扫描性能
    - 分页查询性能
    - 统计查询性能
  预期指标:
    - 全表扫描: < 30秒
    - 分页查询: < 500ms
    - 统计查询: < 5秒

存储空间测试:
  测试内容:
    - 数据库文件大小增长
    - 索引空间占用
    - 碎片整理效果
  预期指标:
    - 数据增长率: < 150% 原始数据
    - 索引占用: < 30% 数据大小
    - 碎片率: < 10%
```

### 3. 文件处理性能测试

#### 3.1 文件上传测试场景
```yaml
单文件上传测试:
  文件大小: 1MB, 10MB, 100MB, 500MB, 1GB
  测试用例:
    - 不同格式文件 (ZIP, RAR, 7Z)
    - 不同网络条件 (快速, 慢速, 不稳定)
    - 上传中断恢复测试
  预期指标:
    - 响应时间: 即时 (<100ms)
    - 处理成功率: > 99%
    - 内存占用: < 200MB (不随文件大小增长)

并发上传测试:
  并发数: 5, 10, 20, 50个文件
  测试用例:
    - 同时上传相同大小文件
    - 混合大小文件并发上传
    - 系统资源监控
  预期指标:
    - 最大并发数: > 20个
    - 系统响应性: 不受影响
    - 资源利用: CPU < 90%, 内存 < 1GB

压缩解压测试:
  测试内容:
    - 不同压缩格式 (ZIP, RAR, 7Z)
    - 不同压缩率文件
    - 大文件压缩解压
  预期指标:
    - 解压速度: > 50MB/s
    - 内存使用: < 500MB
    - CPU 利用: 充分利用多核
```

#### 3.2 文件处理流程测试
```yaml
FT Summary 处理测试:
  测试场景:
    - 不同数量 CSV 文件 (10, 50, 100, 500个)
    - 不同大小文件 (1MB, 10MB, 100MB)
    - 复杂数据结构处理
  预期指标:
    - 处理速度: > 10文件/秒
    - 内存效率: < 文件总大小的 2倍
    - 错误率: < 1%

EQC 处理测试:
  测试场景:
    - 标准 EQC 流程处理
    - 进阶 EQC 双阶段处理
    - 异常数据处理
  预期指标:
    - 标准处理: < 30秒
    - 进阶处理: < 60秒
    - 异常处理: 优雅降级
```

### 4. 邮件处理性能测试

#### 4.1 邮件同步测试场景
```yaml
IMAP 连接测试:
  测试内容:
    - 连接建立时间
    - 连接池效率
    - 连接异常恢复
  预期指标:
    - 连接时间: < 2秒
    - 连接复用率: > 95%
    - 异常恢复: < 5秒

邮件批量同步测试:
  邮件数量: 10, 50, 100, 500, 1000封
  测试场景:
    - 纯文本邮件同步
    - 带附件邮件同步
    - 大附件邮件处理
  预期指标:
    - 同步速度: > 5封/秒
    - 大批量 (1000封): < 4分钟
    - 附件处理: 并行下载

邮件处理流程测试:
  测试内容:
    - 统一邮件处理器性能
    - 附件解析和存储
    - 厂商文件识别
    - LINE 通知发送
  预期指标:
    - 单邮件处理: < 2秒
    - 附件处理: < 5秒/个
    - 通知发送: < 1秒
```

#### 4.2 邮件数据处理测试
```yaml
邮件解析测试:
  测试内容:
    - 不同编码邮件解析
    - HTML 邮件处理
    - 多媒体附件处理
  预期指标:
    - 解析准确率: > 99%
    - 解析速度: > 10封/秒
    - 内存使用: < 50MB/封

邮件存储测试:
  测试内容:
    - 数据库存储性能
    - 附件文件系统存储
    - 数据一致性验证
  预期指标:
    - 存储速度: > 20封/秒
    - 数据一致性: 100%
    - 存储空间效率: > 80%
```

## 🔧 测试工具和环境

### 负载测试工具配置
```yaml
Apache JMeter:
  用途: HTTP API 负载测试
  配置:
    - 线程组: 1-100个虚拟用户
    - 循环次数: 1000次
    - 渐增负载: 10用户/秒
  测试场景:
    - API 端点压力测试
    - 长时间稳定性测试
    - 并发用户模拟

K6 (JavaScript):
  用途: 轻量级性能测试
  配置:
    - VUs (Virtual Users): 1-200
    - Duration: 30s-30m
    - 阶段性负载: 渐增-稳定-下降
  测试场景:
    - 快速 API 性能验证
    - CI/CD 集成测试
    - 回归性能测试

Locust (Python):
  用途: 分布式负载测试
  配置:
    - 用户类: Web用户, API用户
    - 并发数: 1-500
    - 地理分布: 多节点测试
  测试场景:
    - 大规模用户模拟
    - 复杂业务流程测试
    - 分布式压力测试
```

### 性能监控工具
```yaml
系统监控:
  工具: htop, iotop, netstat
  监控指标:
    - CPU 使用率和负载
    - 内存使用和交换
    - 磁盘 I/O 和网络 I/O
    - 进程和线程状态

应用监控:
  工具: 
    - Python: memory_profiler, cProfile
    - 数据库: SQLite EXPLAIN QUERY PLAN
    - Web: Chrome DevTools, Lighthouse
  监控指标:
    - 函数执行时间
    - 内存分配和释放
    - 数据库查询计划
    - HTTP 请求时序

实时监控:
  工具: Prometheus + Grafana
  配置:
    - 指标收集间隔: 15秒
    - 数据保留期: 30天
    - 警报规则: 响应时间, 错误率, 资源使用
  仪表板:
    - API 性能总览
    - 系统资源监控
    - 业务指标监控
```

### 测试环境配置
```yaml
测试环境规格:
  硬件配置:
    - CPU: 4核心 (模拟生产环境)
    - 内存: 8GB
    - 磁盘: SSD 100GB
    - 网络: 1Gbps
  
  软件环境:
    - OS: Windows 10/11
    - Python: 3.8+
    - 数据库: SQLite (与生产一致)
    - 网络: 局域网测试

数据准备:
  测试数据量:
    - 邮件记录: 10K, 50K, 100K条
    - 附件文件: 1K, 5K, 10K个
    - 用户账户: 100个测试账户
  
  数据类型:
    - 真实邮件样本 (脱敏)
    - 各种格式附件
    - 不同大小文件
    - 异常数据样本
```

## 📊 测试执行计划

### 第1阶段: 基线测试 (1周)
```yaml
Day 1-2: 环境准备
  - [ ] 测试环境搭建
  - [ ] 测试工具安装配置
  - [ ] 测试数据准备
  - [ ] 监控系统部署

Day 3-4: 基线建立
  - [ ] 当前系统性能基线测试
  - [ ] API 端点基准测试
  - [ ] 数据库性能基准测试
  - [ ] 文件处理基准测试
  - [ ] 邮件处理基准测试

Day 5-7: 问题分析
  - [ ] 性能瓶颈分析
  - [ ] 测试结果整理
  - [ ] 优化优先级确定
  - [ ] 基线报告生成
```

### 第2阶段: 异步优化验证 (2周)
```yaml
Week 1: 核心组件测试
  - [ ] 数据库连接池性能测试
  - [ ] 异步 API 端点测试
  - [ ] 文件处理异步化测试
  - [ ] 内存使用优化验证

Week 2: 集成测试
  - [ ] 完整业务场景测试
  - [ ] 并发用户场景测试
  - [ ] 长时间稳定性测试
  - [ ] 错误恢复能力测试
```

### 第3阶段: 压力测试 (1周)
```yaml
Day 1-2: 负载压力测试
  - [ ] 渐增负载测试
  - [ ] 峰值负载测试
  - [ ] 极限压力测试
  - [ ] 资源饱和测试

Day 3-4: 稳定性测试
  - [ ] 24小时连续运行测试
  - [ ] 内存泄漏检测
  - [ ] 异常场景测试
  - [ ] 恢复能力测试

Day 5-7: 回归测试
  - [ ] 功能回归测试
  - [ ] 性能回归测试
  - [ ] 兼容性测试
  - [ ] 最终验证测试
```

## 📈 性能指标收集

### 自动化指标收集
```python
# 性能测试指标收集脚本示例
import time
import psutil
import requests
from datetime import datetime

class PerformanceCollector:
    def __init__(self):
        self.metrics = []
    
    def collect_api_metrics(self, url, iterations=100):
        """收集 API 性能指标"""
        response_times = []
        
        for i in range(iterations):
            start_time = time.perf_counter()
            try:
                response = requests.get(url)
                end_time = time.perf_counter()
                
                response_time = (end_time - start_time) * 1000  # ms
                response_times.append({
                    'timestamp': datetime.now(),
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'success': response.status_code == 200
                })
            except Exception as e:
                response_times.append({
                    'timestamp': datetime.now(),
                    'response_time': None,
                    'status_code': None,
                    'success': False,
                    'error': str(e)
                })
        
        return response_times
    
    def collect_system_metrics(self):
        """收集系统资源指标"""
        return {
            'timestamp': datetime.now(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_io': psutil.disk_io_counters(),
            'network_io': psutil.net_io_counters()
        }
```

### 测试结果分析脚本
```python
# 性能测试结果分析
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

class PerformanceAnalyzer:
    def __init__(self, metrics_data):
        self.data = pd.DataFrame(metrics_data)
    
    def calculate_percentiles(self, column='response_time'):
        """计算响应时间百分位数"""
        return {
            'mean': self.data[column].mean(),
            'median': self.data[column].median(),
            'p95': self.data[column].quantile(0.95),
            'p99': self.data[column].quantile(0.99),
            'min': self.data[column].min(),
            'max': self.data[column].max()
        }
    
    def generate_report(self):
        """生成性能测试报告"""
        report = {
            'test_summary': {
                'total_requests': len(self.data),
                'success_rate': (self.data['success'].sum() / len(self.data)) * 100,
                'error_rate': ((len(self.data) - self.data['success'].sum()) / len(self.data)) * 100
            },
            'response_time_stats': self.calculate_percentiles(),
            'recommendations': self.generate_recommendations()
        }
        return report
```

## 🚨 测试通过标准

### 必须通过的测试标准
```yaml
功能正确性:
  - 所有 API 端点正常响应: 100%
  - 业务流程完整性: 100%
  - 数据一致性: 100%
  - 错误处理正确性: 100%

性能要求:
  - API 响应时间 P95 < 800ms: PASS
  - 文件上传异步响应 < 100ms: PASS
  - 邮件处理速度 > 5封/秒: PASS
  - 系统稳定运行 24小时: PASS

资源使用:
  - 内存使用 < 800MB: PASS
  - CPU 利用率 60-85%: PASS
  - 无内存泄漏: PASS
  - 无资源死锁: PASS

并发能力:
  - 50个并发用户: PASS
  - 20个并发文件上传: PASS
  - 数据库并发访问: PASS
  - 无数据竞争条件: PASS
```

### 性能回归检测
```yaml
基线对比:
  - 性能不得低于当前基线 90%
  - 关键功能响应时间不得增加 20%
  - 资源使用不得增加 50%
  - 错误率不得超过 1%

持续监控:
  - 每日性能回归测试
  - 每周完整性能测试
  - 月度性能趋势分析
  - 季度性能优化评估
```

## 📋 测试报告模板

### 测试执行报告
```markdown
# 性能测试执行报告

## 测试概览
- 测试日期: {date}
- 测试环境: {environment}
- 测试工具: {tools}
- 测试负责人: {tester}

## 测试结果摘要
- 总测试用例: {total_cases}
- 通过用例: {passed_cases}
- 失败用例: {failed_cases}
- 通过率: {pass_rate}%

## 性能指标
- 平均响应时间: {avg_response_time}ms
- P95 响应时间: {p95_response_time}ms
- P99 响应时间: {p99_response_time}ms
- 错误率: {error_rate}%

## 关键发现
- 性能瓶颈: {bottlenecks}
- 优化建议: {recommendations}
- 风险点: {risks}

## 下一步行动
- 立即修复: {immediate_fixes}
- 短期优化: {short_term_improvements}
- 长期规划: {long_term_plans}
```

---

*此性能测试计划提供了全面的测试策略和实施方案，确保异步升级后的系统性能能够达到预期目标，并建立持续的性能监控和优化机制。*