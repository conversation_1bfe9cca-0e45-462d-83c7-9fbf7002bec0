# 實作計畫

## 現狀分析
基於現有程式碼分析，以下功能已實作：
- ✅ 基本網路檔案瀏覽 (network_browser_api.py)
- ✅ SMB/CIFS 連線和驗證 (network_utils.py)
- ✅ 檔案下載功能
- ✅ 基本 UI 介面 (network_browser.html)
- ✅ 處理工具存在 (csv_to_summary.py, code_comparison.py)

## 需要實作的核心功能

- [x] 1. 基本網路檔案系統存取
  - 已實作 SMB/CIFS 協定存取和網路驗證功能
  - 已實作目錄列表、檔案下載功能
  - 已整合 .env 檔案憑證讀取
  - _需求: 1, 7_

- [x] 2. 實作智慧產品搜尋服務





  - 建立 ProductSearchService 類別，支援產品資料夾定位
  - 實作基於產品名稱的資料夾搜尋邏輯
  - 實作時間範圍篩選功能（最近 6 個月等）
  - 實作並行搜尋以提升效能
  - 新增 /network/api/search/product 端點
  - _需求: 2_

- [x] 3. 整合 LLM 智慧搜尋功能
  - 實作 LLMSearchService 類別，整合現有 Ollama 基礎設施
  - 實作自然語言查詢解析功能
  - 實作時間相關查詢解析（「最近 6 個月」、「本季」等）
  - 更新現有的 /network/api/smart-search 端點實作
  - 實作搜尋結果智慧分析和建議
  - _需求: 3_

- [x] 4. 建立檔案處理服務整合
  - 建立 FileProcessingService 類別
  - 整合 csv_to_summary.py 為可呼叫的服務函式
  - 整合 code_comparison.py 為可呼叫的服務函式
  - 新增 /network/api/process/csv-summary 端點
  - 新增 /network/api/process/code-comparison 端點
  - 實作處理進度追蹤和狀態回報
  - _需求: 4_

- [ ] 5. 實作自動檔案暫存功能
  - 實作檔案暫存到 d:\temp\[產品名稱] 的邏輯
  - 建立暫存目錄管理和清理機制
  - 實作檔案完整性驗證
  - 整合到處理服務工作流程中
  - 實作暫存失敗的錯誤處理
  - _需求: 5_

- [ ] 6. 實作自動結果上傳功能
  - 擴展現有 network_utils.py 的上傳功能
  - 實作結果上傳到 \\192.168.1.60\temp_7days\[產品名稱]
  - 實作上傳重試機制（最多 3 次）
  - 實作上傳完成後的暫存檔案清理
  - 整合到處理服務工作流程中
  - _需求: 6_

- [x] 7. 擴展網路瀏覽器 UI 功能
  - 修改 network_browser.html，新增產品搜尋表單
  - 新增時間範圍選擇器（最近 6 個月、本季等）
  - 實作 LLM 自然語言搜尋輸入框
  - 新增「產生摘要」和「比較程式碼」按鈕到檔案操作區
  - 實作檔案選擇功能（支援多選）
  - 新增處理進度指示器和即時狀態更新
  - _需求: 2, 3, 4, 8_

- [ ] 8. 實作錯誤處理和重試機制
  - 建立檔案處理相關的例外類別
  - 實作網路操作自動重試邏輯（已部分實作，需擴展）
  - 實作處理工具執行的錯誤恢復
  - 實作使用者友善的錯誤訊息顯示
  - 新增詳細的錯誤日誌記錄
  - _需求: 7_

- [ ] 9. 建立領域實體和資料模型
  - 建立 src/domain/entities/file_search.py
  - 定義 ProductSearchResult、ProcessingTask 等實體
  - 建立 src/data_models/ 中的 DTO 類別
  - 實作資料驗證和序列化
  - _需求: 1, 2, 3, 4_

- [ ] 10. 效能最佳化和快取
  - 實作搜尋結果快取機制
  - 實作並行檔案操作優化
  - 實作大量搜尋結果的分頁載入
  - 驗證搜尋回應時間 < 5 秒的需求
  - _需求: 1, 2_

- [ ] 11. 安全性增強
  - 實作路徑驗證，防止路徑遍歷攻擊
  - 實作檔案類型和大小限制檢查
  - 確保憑證不在日誌中暴露（已部分實作）
  - 實作暫存檔案自動清理機制
  - _需求: 7_

- [ ] 12. 整合測試和驗證
  - 撰寫完整搜尋到處理工作流程的端對端測試
  - 驗證所有需求的實作完整性
  - 測試多使用者並行存取場景
  - 驗證處理工具整合的正確性
  - _需求: 1, 2, 3, 4, 5, 6, 7, 8_

## 實作優先順序
1. **高優先級**: 任務 2, 4, 7 (核心功能：產品搜尋、工具整合、UI 擴展)
2. **中優先級**: 任務 3, 5, 6 (智慧搜尋、檔案暫存、結果上傳)
3. **低優先級**: 任務 8-12 (錯誤處理、最佳化、測試)