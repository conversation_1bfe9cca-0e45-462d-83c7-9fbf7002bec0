#!/bin/bash
# Git 自動更新文檔腳本

echo "🚀 開始自動更新專案文檔..."

# 取得當前時間
current_date=$(date '+%Y-%m-%d')
current_time=$(date '+%H:%M:%S')
current_datetime="$current_date $current_time"

# 專案統計
echo "📊 收集專案統計資料..."

# 計算檔案數量
total_files=$(find . -type f | grep -v ".git" | grep -v ".venv" | grep -v "__pycache__" | wc -l)
python_files=$(find . -name "*.py" | grep -v ".venv" | grep -v "__pycache__" | wc -l)
test_files=$(find . -name "*test*.py" | wc -l)
doc_files=$(find . -name "*.md" | wc -l)

# 計算程式碼行數
if command -v cloc &> /dev/null; then
    python_lines=$(cloc --include-lang=Python . 2>/dev/null | grep "Python" | awk '{print $5}' || echo "N/A")
else
    python_lines=$(find . -name "*.py" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo "0")
fi

# 計算函數和類別數量
function_count=$(grep -r "^def " . --include="*.py" | wc -l)
class_count=$(grep -r "^class " . --include="*.py" | wc -l)

# Git 統計
if git rev-parse --git-dir > /dev/null 2>&1; then
    commit_count=$(git rev-list --all --count)
    contributors=$(git log --format='%an' | sort -u | wc -l)
    current_branch=$(git branch --show-current)
else
    commit_count="N/A"
    contributors="N/A" 
    current_branch="main"
fi

# 更新 README.md
echo "📝 更新 README.md..."

# 讀取 README.md 並找到需要更新的區塊
if [ -f "README.md" ]; then
    # 備份原始檔案
    cp README.md README.md.backup
    
    # 更新最後更新時間
    if grep -q "最新更新" README.md; then
        sed -i "s/## \[ROCKET\] \*\*最新更新.*\*\*/## [ROCKET] **最新更新 ($current_date)**/" README.md
    fi
    
    # 更新專案統計表格
    # 使用臨時檔案來更新統計資料
    cat > temp_stats.txt << EOF
| 項目 | 數量 | 備註 |
|------|------|------|
| 📁 總檔案數 | $total_files | 專案所有檔案 |
| 🐍 Python 檔案 | $python_files | 源碼檔案數 |
| 📝 程式碼行數 | $python_lines | Python 程式碼行數 |
| 🧪 測試檔案 | $test_files | 單元/整合測試 |
| 📚 文檔檔案 | $doc_files | Markdown 文檔 |
| 🏭 支援廠商 | 6 | 半導體測試廠商 |
| 📦 函數數量 | $function_count | Python 函數總數 |
| 🔧 類別數量 | $class_count | Python 類別總數 |
| 🌿 Git 提交 | $commit_count | 版本控制歷史 |
| 👥 貢獻者 | $contributors | 開發團隊成員 |
EOF
    
    # 替換統計表格（從 "## 📊 專案統計" 到下一個標題）
    awk '
    /^## 📊 專案統計/ { 
        print $0
        while (getline line < "temp_stats.txt") print line
        close("temp_stats.txt")
        # 跳過到下一個標題
        while (getline && !/^##/) continue
        if (/^##/) print $0
        next
    }
    { print }
    ' README.md > README.md.tmp && mv README.md.tmp README.md
    
    # 清理臨時檔案
    rm -f temp_stats.txt
    
    echo "✅ README.md 已更新"
else
    echo "⚠️ README.md 不存在"
fi

# 更新 CHANGELOG.md
echo "📋 更新 CHANGELOG.md..."

if [ -f "CHANGELOG.md" ]; then
    # 獲取最新的 Git 變更
    if git rev-parse --git-dir > /dev/null 2>&1; then
        recent_changes=$(git log --oneline -5 --pretty=format:"- %s (%h)" | head -5)
    else
        recent_changes="- 手動更新專案文檔"
    fi
    
    # 在 CHANGELOG.md 頂部添加新條目
    temp_changelog=$(mktemp)
    cat > "$temp_changelog" << EOF
# 變更日誌

## [$current_date] - 自動更新

### 🔄 變更內容
$recent_changes

### 📊 專案統計
- 總檔案數: $total_files
- Python 檔案: $python_files  
- 程式碼行數: $python_lines
- 測試檔案: $test_files
- Git 提交: $commit_count

EOF
    
    # 將原始內容追加（跳過原有的標題）
    if grep -q "# 變更日誌" CHANGELOG.md; then
        sed '1,/^# 變更日誌/d' CHANGELOG.md >> "$temp_changelog"
    else
        cat CHANGELOG.md >> "$temp_changelog"
    fi
    
    mv "$temp_changelog" CHANGELOG.md
    echo "✅ CHANGELOG.md 已更新"
else
    # 建立新的 CHANGELOG.md
    cat > CHANGELOG.md << EOF
# 變更日誌

## [$current_date] - 專案初始化

### 🚀 初始功能
- Outlook 郵件處理系統
- 多廠商解析器支援
- Web UI 介面
- 自動化測試框架

### 📊 專案統計
- 總檔案數: $total_files
- Python 檔案: $python_files
- 程式碼行數: $python_lines
- 測試檔案: $test_files
EOF
    echo "✅ 新建 CHANGELOG.md"
fi

# 更新專案資訊 JSON
echo "📄 更新專案資訊..."

cat > project_info.json << EOF
{
  "project_name": "Outlook Summary System",
  "last_updated": "$current_datetime",
  "update_source": "git_hooks",
  "statistics": {
    "total_files": $total_files,
    "python_files": $python_files,
    "code_lines": "$python_lines",
    "test_files": $test_files,
    "doc_files": $doc_files,
    "function_count": $function_count,
    "class_count": $class_count,
    "commit_count": "$commit_count",
    "contributors": "$contributors"
  },
  "git_info": {
    "current_branch": "$current_branch",
    "last_commit": "$(git log -1 --pretty=format:'%h - %s' 2>/dev/null || echo 'N/A')"
  },
  "generated_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

echo "✅ project_info.json 已更新"

# 更新版本號（如果有 package.json 或 pyproject.toml）
if [ -f "pyproject.toml" ]; then
    echo "🔢 更新版本號..."
    # 這裡可以實作版本號自動遞增邏輯
    echo "📦 pyproject.toml 版本檢查完成"
fi

# 生成文檔摘要
echo "📑 生成文檔摘要..."

cat > DOC_SUMMARY.md << EOF
# 文檔摘要

**更新時間:** $current_datetime  
**更新來源:** Git Hooks 自動更新

## 📚 主要文檔

### 核心文檔
- **README.md** - 專案概述與快速開始
- **CLAUDE.md** - AI 程式設計指導規則
- **PYTHON_MIGRATION_PLAN.md** - Python 遷移計畫
- **PROJECT_STATUS_TEMPLATE.md** - 專案狀態追蹤

### 技術文檔
- **VBA_TO_PYTHON_MAPPING.md** - 架構對照表
- **DOMAIN_MODELS_DESIGN.md** - 領域模型設計
- **UPDATED_ARCHITECTURE_WITH_DATABASE.md** - 完整架構文檔

### 資料檔案
- **CHANGELOG.md** - 變更歷史記錄
- **project_info.json** - 專案統計資料

## 📊 文檔統計
- Markdown 檔案: $doc_files 個
- 總字數: $(find . -name "*.md" -exec wc -w {} + 2>/dev/null | tail -1 | awk '{print $1}' || echo "N/A")
- 最後更新: $current_datetime

## 🔄 自動更新功能
- ✅ 專案統計自動更新
- ✅ 變更日誌自動生成  
- ✅ 版本資訊自動同步
- ✅ Git 資訊自動提取

---
*此文檔由 Git Hooks 自動生成*
EOF

echo "✅ DOC_SUMMARY.md 已生成"

echo ""
echo "🎉 文檔自動更新完成！"
echo "📋 更新摘要:"
echo "   ✅ README.md - 專案統計和更新時間"
echo "   ✅ CHANGELOG.md - 最新變更記錄"  
echo "   ✅ project_info.json - 專案資訊"
echo "   ✅ DOC_SUMMARY.md - 文檔摘要"
echo ""
echo "📈 目前專案統計:"
echo "   📁 總檔案: $total_files"
echo "   🐍 Python 檔案: $python_files"
echo "   📝 程式碼行數: $python_lines"
echo "   🧪 測試檔案: $test_files"
echo "   🌿 Git 提交: $commit_count"
echo ""
