<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>網路共享瀏覽器</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/network_browser_style.css" rel="stylesheet">
</head>
<body>
    <!-- 登入模態框 (暫時隱藏) -->
    <div id="loginModal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-lock"></i> 網路共享瀏覽器 - 登入驗證</h2>
            </div>
            <div class="modal-body">
                <p style="color:var(--muted);margin-bottom:20px;">請輸入您的帳號密碼以存取網路共享瀏覽器</p>
                <div class="login-form">
                    <div class="form-group">
                        <label for="loginUsername"><i class="fas fa-user"></i> 使用者名稱</label>
                        <input type="text" id="loginUsername" placeholder="請輸入使用者名稱" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword"><i class="fas fa-key"></i> 密碼</label>
                        <input type="password" id="loginPassword" placeholder="請輸入密碼" required>
                    </div>
                    <div class="form-actions">
                        <button class="btn btn-primary" onclick="performLogin()" id="loginBtn">
                            <i class="fas fa-sign-in-alt"></i> 登入
                        </button>
                    </div>
                </div>
                <div id="loginStatus"></div>
            </div>
        </div>
    </div>
    
    <div class="container" id="mainContainer" style="display:block;">
        <div class="header">
            <div>
                <h1><i class="fas fa-network-wired"></i> 網路共享瀏覽器</h1>
                <p>瀏覽和下載網路共享資料夾中的檔案</p>
                <div id="welcomeMessage" style="margin-top:10px;padding:10px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);color:white;border-radius:8px;display:none;">
                    <i class="fas fa-user-check"></i> <span id="welcomeText">載入中...</span>
                </div>
            </div>
        </div>
        <div class="controls">
            <div id="navigationBar" style="display:none;margin-bottom:15px;">
                <div class="breadcrumb" style="display:flex;align-items:center;gap:10px;margin-bottom:10px;">
                    <button class="btn btn-secondary" onclick="navigateUp()"><i class="fas fa-arrow-up"></i> 上一層</button>
                    <span style="color:var(--muted);font-size:0.9rem;"><i class="fas fa-folder-open"></i> <span id="currentPath">等待連接...</span></span>
                </div>
                <div style="display:flex;align-items:center;gap:10px;">
                    <span id="connectionStatus" style="color:var(--success);font-weight:500;">已連接</span>
                    <button class="btn btn-success" onclick="loadFiles()" id="refreshBtn"><i class="fas fa-sync-alt"></i> 重新整理</button>
                </div>
            </div>
            <div id="filterBar" style="display:block;background:white;padding:var(--spacing);border-radius:var(--radius);box-shadow:var(--shadow);margin-bottom:var(--spacing);">
                <div id="productSearchPanel" style="margin-top:15px;padding:15px;background:var(--bg);border-radius:var(--radius);border:2px solid var(--primary);">
                    <h4 style="margin-bottom:15px;color:var(--primary);"><i class="fas fa-search"></i> 產品搜尋</h4>
                    <div style="display:grid;grid-template-columns:1fr 1fr 1fr;gap:10px;margin-bottom:15px;">
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">產品名稱</label>
                            <input type="text" id="productNameInput" placeholder="輸入產品名稱 (例: test, G2517A)" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">搜尋目錄</label>
                            <select id="searchDirectorySelect" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                                <option value="auto">自動選擇</option>
                                <option value="ETD">ETD</option>
                                <option value="FT">FT</option>
                                <option value="GTK">GTK</option>
                                <option value="JCAP">JCAP</option>
                                <option value="JCET">JCET</option>
                                <option value="JSSI">JSSI</option>
                                <option value="JCAP_JCET">JCAP_JCET</option>
                                <option value="AMAT">AMAT</option>
                                <option value="ASML">ASML</option>
                                <option value="LAM">LAM</option>
                                <option value="TEL">TEL</option>
                                <option value="KLA">KLA</option>
                                <option value="all">搜尋所有目錄</option>
                            </select>
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">時間範圍</label>
                            <select id="timeRangeSelect" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                                <option value="last_week">最近一週</option>
                                <option value="last_month">最近一個月</option>
                                <option value="last_3_months">最近三個月</option>
                                <option value="last_6_months" selected>最近六個月</option>
                                <option value="current_quarter">本季</option>
                                <option value="custom">自訂範圍</option>
                            </select>
                        </div>
                    </div>
                    <div id="customDateRange" style="display:none;grid-template-columns:1fr 1fr;gap:10px;margin-bottom:15px;">
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">開始日期</label>
                            <input type="date" id="customStartDate" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">結束日期</label>
                            <input type="date" id="customEndDate" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                    </div>
                    <div style="display:grid;grid-template-columns:1fr 1fr 1fr;gap:10px;margin-bottom:15px;">
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">檔案類型</label>
                            <select id="fileTypeSelect" multiple style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);height:80px;">
                                <option value=".csv">CSV 檔案</option>
                                <option value=".xlsx">Excel 檔案</option>
                                <option value=".txt">文字檔案</option>
                                <option value=".log">日誌檔案</option>
                                <option value=".zip">壓縮檔案</option>
                            </select>
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">最小大小 (MB)</label>
                            <input type="number" id="minSizeInput" placeholder="0" min="0" step="0.1" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                        <div>
                            <label style="display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;">最大大小 (MB)</label>
                            <input type="number" id="maxSizeInput" placeholder="無限制" min="0" step="0.1" style="width:100%;padding:10px;border:2px solid var(--border);border-radius:var(--radius);">
                        </div>
                    </div>
                    <div style="display:flex;gap:10px;align-items:center;margin-bottom:15px;">
                        <label style="display:flex;align-items:center;gap:5px;color:var(--secondary);">
                            <input type="checkbox" id="includeDirsCheckbox" checked>
                            包含目錄
                        </label>
                        <div style="flex:1;"></div>
                        <label style="color:var(--secondary);font-weight:500;">最大結果數:</label>
                        <input type="number" id="maxResultsInput" value="1000" min="1" max="10000" style="width:80px;padding:8px;border:2px solid var(--border);border-radius:var(--radius);">
                    </div>
                    <div style="text-align:center;">
                        <button class="btn btn-primary" onclick="performProductSearch()" id="productSearchBtn" style="padding:12px 30px;font-size:1.1rem;">
                            <i class="fas fa-search"></i> 開始產品搜尋
                        </button>
                        <button class="btn btn-warning" onclick="cancelProductSearch()" id="cancelSearchBtn" style="margin-left:10px;display:none;">
                            <i class="fas fa-stop"></i> 取消搜尋
                        </button>
                        <button class="btn btn-secondary" onclick="clearProductSearch()" style="margin-left:10px;">
                            <i class="fas fa-times"></i> 清除
                        </button>
                    </div>
                    <div id="productSearchStatus" style="margin-top:15px;"></div>
                </div>
                <div id="smartSearchPanel" style="margin-top:15px;padding:15px;background:linear-gradient(135deg, #667eea 0%, #764ba2 100%);border-radius:var(--radius);color:white;">
                    <h4 style="margin-bottom:15px;color:white;"><i class="fas fa-brain"></i> LLM 智慧搜尋</h4>
                    <div style="display:flex;gap:10px;margin-bottom:15px;">
                        <input type="text" id="smartSearchInput" placeholder="🧠 用自然語言描述您要找的檔案，例如：「搜尋產品 AAA 最近 6 個月的 Excel 檔案」" style="flex:1;padding:12px;border:none;border-radius:var(--radius);font-size:1rem;">
                        <button class="btn" onclick="performSmartSearch()" id="smartSearchBtn" style="background:white;color:var(--primary);font-weight:bold;padding:12px 20px;">
                            <i class="fas fa-brain"></i> 智慧搜尋
                        </button>
                    </div>
                    <div style="display:flex;gap:10px;align-items:center;margin-bottom:10px;">
                        <label style="color:white;font-weight:500;">最大結果數:</label>
                        <input type="number" id="smartMaxResultsInput" value="100" min="1" max="1000" style="width:80px;padding:8px;border:none;border-radius:var(--radius);">
                        <div style="flex:1;"></div>
                        <button class="btn" onclick="clearSmartSearch()" style="background:rgba(255,255,255,0.2);color:white;border:1px solid white;">
                            <i class="fas fa-times"></i> 清除
                        </button>
                    </div>
                    <div id="smartSearchStatus" style="margin-top:10px;"></div>
                    <div id="smartSearchResults" style="margin-top:15px;display:none;">
                        <div id="smartSearchInterpretation" style="background:rgba(255,255,255,0.1);padding:10px;border-radius:var(--radius);margin-bottom:10px;"></div>
                        <div id="smartSearchAnalysis" style="background:rgba(255,255,255,0.1);padding:10px;border-radius:var(--radius);margin-bottom:10px;"></div>
                        <div id="smartSearchSuggestions" style="background:rgba(255,255,255,0.1);padding:10px;border-radius:var(--radius);"></div>
                    </div>
                </div>
                <div id="filterStats" style="color:var(--muted);font-size:0.9rem;margin-top:5px;">載入中...</div>
            </div>
        </div>
        <div id="status"></div>

        <!-- 檔案處理工具 -->
        <div class="file-container" style="margin-bottom: var(--spacing);">
            <div class="file-header">
                <h3><i class="fas fa-tools"></i> 檔案處理工具</h3>
                <span id="selectedCount">未選擇檔案</span>
            </div>
            <div style="padding: var(--spacing);">
                <div style="display: flex; gap: 10px; margin-bottom: 15px; align-items: center;">
                    <button class="btn btn-secondary" onclick="selectAllFiles()" id="selectAllBtn">
                        <i class="fas fa-check-square"></i> 全選
                    </button>
                    <button class="btn btn-secondary" onclick="clearSelection()" id="clearSelectionBtn">
                        <i class="fas fa-square"></i> 清除選擇
                    </button>
                    <span style="color: var(--muted); margin-left: 20px;">選擇檔案或資料夾後，可以進行批量處理</span>
                </div>

                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                    <button class="btn btn-primary" onclick="processSelectedFiles('csv_summary')" id="csvSummaryBtn" disabled>
                        <i class="fas fa-chart-line"></i> CSV 摘要生成
                    </button>
                    <button class="btn btn-success" onclick="processSelectedFiles('code_comparison')" id="codeComparisonBtn" disabled>
                        <i class="fas fa-code-branch"></i> 程式碼比較
                    </button>
                </div>

                <div id="processingStatus" style="margin-top: 15px; display: none;">
                    <div style="background: var(--bg); padding: 15px; border-radius: var(--radius); border: 2px solid var(--primary);">
                        <h4 style="margin-bottom: 10px; color: var(--primary);">
                            <i class="fas fa-cog fa-spin"></i> 檔案處理進度
                        </h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                            <div>
                                <strong>處理工具:</strong> <span id="currentTool">-</span>
                            </div>
                            <div>
                                <strong>任務ID:</strong> <span id="currentTaskId">-</span>
                            </div>
                            <div>
                                <strong>開始時間:</strong> <span id="startTime">-</span>
                            </div>
                            <div>
                                <strong>處理進度:</strong> <span id="progressPercent">0%</span>
                            </div>
                        </div>
                        <div style="background: white; border-radius: var(--radius); padding: 10px; margin-bottom: 10px;">
                            <div id="progressBar" style="background: var(--primary); height: 8px; border-radius: 4px; width: 0%; transition: width 0.3s ease;"></div>
                        </div>
                        <div id="processingLogs" style="background: #f8f9fa; padding: 10px; border-radius: var(--radius); max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                            等待處理開始...
                        </div>
                        <div style="text-align: center; margin-top: 10px;">
                            <button class="btn btn-secondary" onclick="hideProcessingStatus()">
                                <i class="fas fa-times"></i> 關閉
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 檔案列表 -->
        <div class="file-container">
            <div class="file-header"><h3><i class="fas fa-list"></i> 檔案列表</h3><span id="fileCount">載入中...</span></div>
            <div id="fileList" class="file-list"><div class="loading"><i class="fas fa-spinner fa-spin"></i><p>正在載入...</p></div></div>
        </div>
    </div>
    <script type="module" src="/static/js/network_browser_script.js"></script>
</body>
</html>
