"""網路共享瀏覽器 - 工具函式"""

import os
import subprocess
from datetime import datetime
from pathlib import Path
from typing import List, Optional

from loguru import logger
try:
    from .network_models import NetworkFileInfo, NetworkCredentials
except ImportError:
    from network_models import NetworkFileInfo, NetworkCredentials


def list_smb_files(server: str, share: str, credentials: NetworkCredentials, subpath: str = "") -> List[NetworkFileInfo]:
    """列出SMB共享檔案 - 支援Windows和Linux"""
    try:
        import platform
        if platform.system() == "Windows":
            # Windows環境
            unc_path = f"\\\\{server}\\{share}"
            if subpath:
                unc_path = os.path.join(unc_path, subpath).replace('/', '\\')
            
            # 檢查是否使用當前Windows用戶
            if credentials.username == "current_windows_user":
                # 直接使用當前Windows用戶認證，不需要額外的net use命令
                logger.info(f"使用當前Windows用戶存取: {unc_path}")
            else:
                # 使用提供的認證資訊建立連接
                username = f"{credentials.domain}\\{credentials.username}" if credentials.domain else credentials.username
                try:
                    subprocess.run(["net", "use", f"\\\\{server}\\{share}", "/delete"], 
                                 capture_output=True, text=True, timeout=5)
                except:
                    pass
                
                connect_cmd = ["net", "use", f"\\\\{server}\\{share}", credentials.password, f"/user:{username}"]
                connect_result = subprocess.run(connect_cmd, capture_output=True, text=True, timeout=10)
                
                if connect_result.returncode != 0:
                    logger.error(f"SMB連接失敗: {connect_result.stderr}")
                    return []
            
            # 直接使用Python的os.listdir來列出檔案
            try:
                items = os.listdir(unc_path)
                files = []
                
                for item in items:
                    if item in ['.', '..']:
                        continue
                    
                    item_path = os.path.join(unc_path, item)
                    try:
                        stat_info = os.stat(item_path)
                        is_directory = os.path.isdir(item_path)
                        
                        if is_directory:
                            file_type = "目錄"
                            size = 0
                        else:
                            ext = Path(item).suffix.lower()
                            type_map = {
                                ('.txt', '.log', '.csv'): "文字檔案",
                                ('.xlsx', '.xls'): "Excel 檔案", 
                                ('.zip', '.7z', '.rar'): "壓縮檔案"
                            }
                            file_type = next((v for k, v in type_map.items() if ext in k), "其他檔案")
                            size = stat_info.st_size
                        
                        modified_time = datetime.fromtimestamp(stat_info.st_mtime).isoformat()
                        
                        file_info = NetworkFileInfo(
                            filename=item, 
                            size=size, 
                            size_mb=round(size / (1024 * 1024), 2),
                            modified_time=modified_time, 
                            file_type=file_type, 
                            is_directory=is_directory
                        )
                        files.append(file_info)
                        
                    except (OSError, PermissionError) as e:
                        logger.warning(f"無法存取項目 {item}: {e}")
                        continue
                        
            except (OSError, PermissionError) as e:
                logger.error(f"無法列出目錄 {unc_path}: {e}")
                return []
            
            logger.info(f"[OK] 成功取得 {len(files)} 個SMB檔案")
            return files
            
        else:
            # Linux環境：使用smbclient
            username = f"{credentials.domain}\\{credentials.username}" if credentials.domain else credentials.username
            smb_path = f"//{server}/{share}"
            if subpath:
                subpath_unix = subpath.replace('\\', '/')
                list_cmd = f"cd \"{subpath_unix}\"; ls"
            else:
                list_cmd = "ls"
            
            cmd = ["smbclient", smb_path, "-U", username, "-c", list_cmd]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15, input=credentials.password + "\n")
            
            if result.returncode != 0:
                logger.error(f"SMB列表失敗: {result.stderr}")
                return []
            
            if "NT_STATUS_OBJECT_NAME_NOT_FOUND" in result.stdout or "NT_STATUS_OBJECT_PATH_NOT_FOUND" in result.stdout:
                logger.warning(f"路徑不存在: {subpath}")
                return []
            
            files = []
            lines = result.stdout.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if not line or any(x in line for x in ['NT_STATUS', 'blocks available', 'smb:', 'Password for', 'cd ']):
                    continue
                
                parts = line.split()
                if len(parts) >= 3:
                    filename = parts[0]
                    if filename in ['.', '..']:
                        continue
                    
                    is_directory = len(parts) > 1 and 'D' in parts[1]
                    try:
                        size = int(parts[2]) if not is_directory and len(parts) > 2 else 0
                    except (ValueError, IndexError):
                        size = 0
                    
                    if is_directory:
                        file_type = "目錄"
                    else:
                        ext = Path(filename).suffix.lower()
                        type_map = {
                            ('.txt', '.log', '.csv'): "文字檔案",
                            ('.xlsx', '.xls'): "Excel 檔案", 
                            ('.zip', '.7z', '.rar'): "壓縮檔案"
                        }
                        file_type = next((v for k, v in type_map.items() if ext in k), "其他檔案")
                    
                    file_info = NetworkFileInfo(
                        filename=filename, size=size, size_mb=round(size / (1024 * 1024), 2),
                        modified_time=datetime.now().isoformat(), file_type=file_type, is_directory=is_directory
                    )
                    files.append(file_info)
            
            logger.info(f"[OK] 成功取得 {len(files)} 個SMB檔案")
            return files
        
    except Exception as e:
        logger.error(f"[ERROR] SMB檔案列表錯誤: {str(e)}")
        return []


def test_smb_connection(server: str, share: str, credentials: NetworkCredentials) -> tuple[bool, str]:
    """測試SMB連接 - Windows版本"""
    try:
        import platform
        if platform.system() == "Windows":
            unc_path = f"\\\\{server}\\{share}"
            
            # 檢查是否使用當前Windows用戶
            if credentials.username == "current_windows_user":
                # 直接測試UNC路徑是否可存取
                try:
                    import os
                    if os.path.exists(unc_path):
                        return True, "使用當前Windows用戶連接成功"
                    else:
                        return False, "無法存取網路路徑，請檢查網路連接或權限"
                except Exception as e:
                    return False, f"測試網路路徑時發生錯誤: {str(e)}"
            else:
                # 使用提供的認證資訊測試連接
                # 先嘗試斷開現有連接
                try:
                    subprocess.run(["net", "use", unc_path, "/delete"], 
                                 capture_output=True, text=True, timeout=5)
                except:
                    pass
                
                # 建立新連接
                username = f"{credentials.domain}\\{credentials.username}" if credentials.domain else credentials.username
                cmd = ["net", "use", unc_path, credentials.password, f"/user:{username}"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    # 測試是否能列出目錄
                    try:
                        test_result = subprocess.run(["dir", unc_path], 
                                                   capture_output=True, text=True, timeout=5, shell=True)
                        if test_result.returncode == 0:
                            return True, "連接成功"
                        else:
                            return False, "連接成功但無法存取目錄"
                    except:
                        return True, "連接成功"
                else:
                    error_msg = result.stderr.strip() or result.stdout.strip() or "未知錯誤"
                    if "密碼錯誤" in error_msg or "logon failure" in error_msg.lower():
                        return False, "帳號或密碼錯誤"
                    elif "找不到網路路徑" in error_msg or "network path" in error_msg.lower():
                        return False, "網路路徑不存在"
                    elif "拒絕存取" in error_msg or "access denied" in error_msg.lower():
                        return False, "存取被拒絕"
                    else:
                        return False, f"連接失敗: {error_msg}"
        else:
            # Linux環境：使用smbclient
            username = f"{credentials.domain}\\{credentials.username}" if credentials.domain else credentials.username
            cmd = ["smbclient", f"//{server}/{share}", "-U", username, "-c", "ls"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10, input=credentials.password + "\n")
            
            if result.returncode == 0:
                return True, "連接成功"
            else:
                error_msg = result.stderr.strip() or result.stdout.strip() or "未知錯誤"
                if "NT_STATUS_LOGON_FAILURE" in error_msg:
                    return False, "帳號或密碼錯誤"
                elif "NT_STATUS_BAD_NETWORK_NAME" in error_msg:
                    return False, "網路路徑不存在"
                elif "NT_STATUS_ACCESS_DENIED" in error_msg:
                    return False, "存取被拒絕"
                else:
                    return False, f"連接失敗: {error_msg}"
    except subprocess.TimeoutExpired:
        return False, "連接超時"
    except Exception as e:
        return False, f"測試錯誤: {str(e)}"


def convert_unc_to_linux_path(unc_path: str) -> List[str]:
    """轉換UNC路徑為Linux路徑"""
    if unc_path.startswith("\\\\"):
        path_parts = unc_path[2:].split("\\")
        if len(path_parts) >= 2:
            server, share = path_parts[0], path_parts[1]
            possible_paths = [f"/mnt/{x}" for x in ["z", "y", "x"]] + [
                f"/mnt/wsl/{server}/{share}", f"/mnt/{server.lower()}/{share}", f"/mnt/network/{server}_{share}"
            ]
            if len(path_parts) > 2:
                subpath = "/".join(path_parts[2:])
                possible_paths = [p + "/" + subpath for p in possible_paths]
            return possible_paths
    return [unc_path]


def validate_network_path(path: str) -> tuple[bool, str]:
    """驗證網路路徑是否有效且可存取"""
    try:
        for linux_path in convert_unc_to_linux_path(path):
            if os.path.exists(linux_path) or Path(linux_path).exists():
                return True, linux_path
        return False, ""
    except Exception:
        return False, ""


def get_file_info(file_path: str) -> Optional[NetworkFileInfo]:
    """取得檔案詳細資訊"""
    try:
        possible_paths = convert_unc_to_linux_path(file_path)
        
        linux_path = None
        for path in possible_paths:
            if os.path.exists(path):
                linux_path = path
                break
        
        if not linux_path:
            return None
        
        stat_info = os.stat(linux_path)
        filename = os.path.basename(linux_path)
        
        if os.path.isdir(linux_path):
            file_type = "目錄"
            is_directory = True
        else:
            file_extension = Path(filename).suffix.lower()
            type_map = {
                ('.csv', '.txt', '.log'): "文字檔案",
                ('.xlsx', '.xls'): "Excel 檔案",
                ('.zip', '.7z', '.rar'): "壓縮檔案"
            }
            file_type = next((v for k, v in type_map.items() if file_extension in k), "其他檔案")
            is_directory = False
        
        return NetworkFileInfo(
            filename=filename,
            size=stat_info.st_size,
            size_mb=round(stat_info.st_size / (1024 * 1024), 2),
            modified_time=datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
            file_type=file_type,
            is_directory=is_directory
        )
        
    except Exception as e:
        logger.error(f"[ERROR] 取得檔案資訊失敗: {file_path}, 錯誤: {str(e)}")
        return None