# Outlook Summary System - Virtual Environment Activation Script
# Usage: . .\activate_env.ps1 (note the dot and space before the script name)

# Set UTF-8 encoding for Windows PowerShell
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "Starting Outlook Summary System Development Environment..." -ForegroundColor Green

# Check if virtual environment exists
if (Test-Path ".\venv_win_3_11_12\Scripts\Activate.ps1") {
    Write-Host "Virtual environment found, activating..." -ForegroundColor Yellow
    
    # Activate virtual environment in current session
    . ".\venv_win_3_11_12\Scripts\Activate.ps1"
    
    Write-Host "Virtual environment activated successfully!" -ForegroundColor Green
    Write-Host "Current Python path: $(Get-Command python | Select-Object -ExpandProperty Source)" -ForegroundColor Cyan
    Write-Host "Virtual environment path: $env:VIRTUAL_ENV" -ForegroundColor Cyan
    
    # Show available commands
    Write-Host "`nAvailable development commands:" -ForegroundColor Magenta
    Write-Host "  make help          - Show all Makefile commands" -ForegroundColor White
    Write-Host "  python start_integrated_services.py - Start integrated services" -ForegroundColor White
    Write-Host "  pytest             - Run tests" -ForegroundColor White
    Write-Host "  make quality-check - Code quality check" -ForegroundColor White
    Write-Host "`nEnvironment is ready! You can now use the commands above." -ForegroundColor Green
    
} else {
    Write-Host "Virtual environment not found: .\venv_win_3_11_12\Scripts\Activate.ps1" -ForegroundColor Red
    Write-Host "Please check if the virtual environment path is correct" -ForegroundColor Yellow
}