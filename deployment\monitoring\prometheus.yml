# Prometheus監控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

  # 外部標籤
  external_labels:
    monitor: 'outlook-summary-monitor'
    environment: 'production'

# 規則文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身監控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Outlook Summary 應用監控
  - job_name: 'outlook-summary'
    static_configs:
      - targets: ['outlook-summary:8000']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    
    # 指標過濾
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'outlook_.*'
        action: keep
      
      # 添加自定義標籤
      - target_label: 'service'
        replacement: 'outlook-summary'
      - target_label: 'component'
        replacement: 'multiprocess-engine'

  # Redis監控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics

  # 系統監控 (Node Exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics
    
    # 重新標記
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: node-exporter:9100

  # 進程監控
  - job_name: 'process-exporter'
    static_configs:
      - targets: ['process-exporter:9256']
    scrape_interval: 15s

  # 健康檢查監控
  - job_name: 'outlook-health'
    static_configs:
      - targets: ['outlook-summary:8000']
    scrape_interval: 30s
    metrics_path: /health
    scrape_timeout: 10s
    
    # 健康檢查指標轉換
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'up'
        replacement: 'outlook_health_status'
        target_label: __name__

# 遠程寫入配置 (可選)
remote_write:
  - url: "http://remote-prometheus:9090/api/v1/write"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# 存儲配置
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB