#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的 Celery Redis 测试脚本 - 避免编码问题
"""

import os
import sys
import time

def test_redis():
    """测试 Redis 连接"""
    print("1. Testing Redis connection...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
        result = r.ping()
        print(f"   Redis PING: {result}")
        
        # 测试读写
        r.set('test_key', 'test_value', ex=10)
        value = r.get('test_key')
        print(f"   Redis Read/Write: {value.decode() if value else 'Failed'}")
        r.delete('test_key')
        
        return True
    except ImportError:
        print("   Error: redis package not installed")
        return False
    except Exception as e:
        print(f"   Error: {e}")
        return False

def test_celery_config():
    """测试 Celery 配置"""
    print("\n2. Testing Celery configuration...")
    
    # 检查环境变量
    use_memory = os.getenv('USE_MEMORY_BROKER', 'true').lower() == 'true'
    print(f"   USE_MEMORY_BROKER: {use_memory}")
    
    try:
        from src.tasks import celery_app
        print(f"   Broker URL: {celery_app.conf.broker_url}")
        print(f"   Result Backend: {celery_app.conf.result_backend}")
        print(f"   Task Always Eager: {celery_app.conf.task_always_eager}")
        
        # 检查 worker 状态
        i = celery_app.control.inspect()
        active_workers = i.active()
        print(f"   Active Workers: {len(active_workers) if active_workers else 0}")
        
        return True
    except Exception as e:
        print(f"   Error: {e}")
        return False

def test_simple_task():
    """测试简单任务"""
    print("\n3. Testing simple task...")
    
    try:
        from src.tasks import health_check_task
        
        print("   Submitting health check task...")
        result = health_check_task.delay()
        print(f"   Task ID: {result.id}")
        
        # 等待结果
        for i in range(10):
            time.sleep(1)
            print(f"   Waiting {i+1}/10 - State: {result.state}")
            
            if result.ready():
                if result.successful():
                    print(f"   Success: {result.result}")
                    return True
                else:
                    print(f"   Failed: {result.info}")
                    return False
        
        print("   Timeout")
        return False
        
    except Exception as e:
        print(f"   Error: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("Celery + Redis Diagnosis Test")
    print("=" * 50)
    
    # 执行测试
    redis_ok = test_redis()
    celery_ok = test_celery_config()
    task_ok = test_simple_task()
    
    # 结果总结
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print(f"Redis Connection: {'PASS' if redis_ok else 'FAIL'}")
    print(f"Celery Configuration: {'PASS' if celery_ok else 'FAIL'}")
    print(f"Task Execution: {'PASS' if task_ok else 'FAIL'}")
    print("=" * 50)
    
    if redis_ok and celery_ok and task_ok:
        print("All tests passed! System is working normally.")
        return 0
    else:
        print("Issues detected. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())