"""網路共享瀏覽器 API - 主檔案"""

import os
import subprocess
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import FileResponse, HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from loguru import logger

try:
    from .network_models import (
        NetworkFileListResponse, NetworkConnectRequest, NetworkConnectResponse,
        NetworkPathValidateRequest, NetworkPathValidateResponse, NetworkCredentials,
        ProductSearchRequest, ProductSearchResponse, ProductSearchFileInfo,
        SearchTaskResponse, TimeRangeType
    )
    from ...data_models.search_models import SmartSearchRequest, SmartSearchResponse
    from .network_utils import (
        list_smb_files, test_smb_connection, convert_unc_to_linux_path,
        validate_network_path, get_file_info
    )
    from ...services.product_search_service import ProductSearchService
    from ...services.llm_search_service import LLMSearchService
    from ...services.file_processing_service import get_file_processing_service
    from ...domain.entities.file_search import SearchFilters

    # 導入 Celery 相關工具和我們建立的任務
    from celery.result import AsyncResult
    try:
        from ...tasks import (
            search_product_task,
            health_check_task,
            run_csv_summary_task,
            run_code_comparison_task
        )
    except ImportError:
        # 處理直接執行時的導入問題
        search_product_task = None
        health_check_task = None
        run_csv_summary_task = None
        run_code_comparison_task = None

    # 導入 WebSocket 端點
    try:
        from .websocket_endpoints import websocket_router, start_background_tasks
    except ImportError:
        websocket_router = None
        start_background_tasks = None
except ImportError:
    # 當直接執行時使用絕對導入
    from network_models import (
        NetworkFileListResponse, NetworkConnectRequest, NetworkConnectResponse,
        NetworkPathValidateRequest, NetworkPathValidateResponse, NetworkCredentials,
        ProductSearchRequest, ProductSearchResponse, ProductSearchFileInfo,
        SearchTaskResponse, TimeRangeType
    )
    from network_utils import (
        list_smb_files, test_smb_connection, convert_unc_to_linux_path,
        validate_network_path, get_file_info
    )
    # 在直接執行時，這些導入可能會失敗，我們將在需要時處理
    ProductSearchService = None
    LLMSearchService = None
    SearchFilters = None

# 全域變數儲存活動連接
active_connections = {}

# 初始化產品搜尋服務
product_search_service = None
llm_search_service = None

if ProductSearchService is not None:
    product_search_service = ProductSearchService(max_workers=4, search_timeout=300)
    
    # 初始化 LLM 搜尋服務
    if LLMSearchService is not None:
        llm_search_service = LLMSearchService(product_search_service)

# 建立 FastAPI 應用程式實例
app = FastAPI(
    title="網路共享瀏覽器 API",
    description="提供網路共享資料夾的瀏覽、下載和管理功能",
    version="1.0.0"
)

# CORS 中介軟體設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 靜態檔案設定
static_path = Path(__file__).parent.parent / "web" / "static"
if static_path.exists():
    app.mount("/static", StaticFiles(directory=str(static_path)), name="static")

# 模板設定
templates_path = Path(__file__).parent.parent / "web" / "templates"
templates = Jinja2Templates(directory=str(templates_path))

# 添加 WebSocket 路由
if websocket_router:
    app.include_router(websocket_router, tags=["WebSocket"])
    logger.info("WebSocket 路由已添加")


def try_mount_network_share(server: str, share: str, mount_point: str, credentials: NetworkCredentials = None) -> bool:
    """嘗試掛載網路共享到指定掛載點，支援認證"""
    try:
        os.makedirs(mount_point, exist_ok=True)
        
        if credentials:
            success, message = test_smb_connection(server, share, credentials)
            if not success:
                logger.warning(f"[ERROR] SMB連接測試失敗，跳過掛載: {message}")
                return False
            
            creds_file = f"/tmp/.smbcreds_{os.getpid()}"
            try:
                with open(creds_file, 'w') as f:
                    f.write(f"username={credentials.username}\n")
                    f.write(f"password={credentials.password}\n")
                    if credentials.domain:
                        f.write(f"domain={credentials.domain}\n")
                os.chmod(creds_file, 0o600)
                
                mount_cmd1 = [
                    "mount", "-t", "cifs", f"//{server}/{share}", mount_point,
                    "-o", f"credentials={creds_file},uid=1000,gid=1000,iocharset=utf8"
                ]
                result = subprocess.run(mount_cmd1, capture_output=True, text=True, timeout=15)
                
                if result.returncode != 0:
                    mount_cmd2 = [
                        "sudo", "-n", "mount", "-t", "cifs", f"//{server}/{share}", mount_point,
                        "-o", f"credentials={creds_file},uid=1000,gid=1000,iocharset=utf8"
                    ]
                    result = subprocess.run(mount_cmd2, capture_output=True, text=True, timeout=15)
                
                if os.path.exists(creds_file):
                    os.remove(creds_file)
                
                if result.returncode == 0:
                    logger.info(f"[OK] 成功掛載網路共享(認證): //{server}/{share} -> {mount_point}")
                    return True
                else:
                    logger.warning(f"[ERROR] 認證掛載失敗: {result.stderr}")
                    return False
            finally:
                if os.path.exists(creds_file):
                    os.remove(creds_file)
        else:
            mount_cmd = [
                "sudo", "mount", "-t", "cifs", f"//{server}/{share}", mount_point,
                "-o", "guest,uid=1000,gid=1000,iocharset=utf8"
            ]
            result = subprocess.run(mount_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logger.info(f"[OK] 成功掛載網路共享(Guest): //{server}/{share} -> {mount_point}")
                return True
            else:
                logger.debug(f"Guest掛載失敗: {result.stderr}")
                return False
            
    except subprocess.TimeoutExpired:
        logger.warning(f"[ALARM_CLOCK] 掛載超時: //{server}/{share}")
        return False
    except Exception as e:
        logger.debug(f"掛載失敗: {str(e)}")
        return False


@app.get("/api/list", response_model=NetworkFileListResponse)
async def list_network_files(path: str = Query(..., description="網路資料夾路徑")):
    """列出網路共享資料夾中的檔案"""
    try:
        logger.info(f"[FILE_FOLDER] 取得網路檔案列表請求: {path}")
        
        if path.startswith("\\\\"):
            path_parts = path[2:].split("\\")
            if len(path_parts) >= 2:
                server = path_parts[0]
                share = path_parts[1]
                subpath = "/".join(path_parts[2:]) if len(path_parts) > 2 else ""
            else:
                return JSONResponse(status_code=400, content={"error": "無效的UNC路徑格式"})
        else:
            return JSONResponse(status_code=400, content={"error": "請使用UNC路徑格式"})
        
        connection_key = f"{server}_{share}"
        if connection_key not in active_connections:
            return JSONResponse(status_code=400, content={"error": "請先連接到網路共享"})
        
        credentials = active_connections[connection_key]
        files = list_smb_files(server, share, credentials, subpath)
        total_size = sum(f.size for f in files if not f.is_directory)
        files.sort(key=lambda x: (not x.is_directory, x.filename.lower()))
        
        logger.info(f"[OK] 成功取得 {len(files)} 個檔案/目錄")
        
        return NetworkFileListResponse(
            status="success", path=path, files=files, total_count=len(files),
            total_size_mb=round(total_size / (1024 * 1024), 2)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ERROR] 列出網路檔案失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"列出檔案時發生錯誤: {str(e)}"})


@app.get("/api/download")
async def download_network_file(path: str = Query(...), filename: str = Query(...)):
    """下載網路共享檔案"""
    try:
        linux_base_path = convert_unc_to_linux_path(path)
        linux_file_path = os.path.join(linux_base_path, filename)
        
        if not os.path.exists(linux_file_path):
            raise HTTPException(status_code=404, detail=f"檔案不存在: {filename}")
        if not os.path.isfile(linux_file_path):
            raise HTTPException(status_code=400, detail="指定路徑不是檔案")
        
        return FileResponse(path=linux_file_path, filename=filename, media_type='application/octet-stream')
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"檔案下載失敗: {str(e)}")


@app.get("/api/info")
async def get_network_file_info(path: str = Query(...), filename: str = Query(...)):
    """取得網路共享檔案的詳細資訊"""
    try:
        is_valid, linux_base_path = validate_network_path(path)
        if not is_valid:
            raise HTTPException(status_code=400, detail=f"無效的網路路徑: {path}")
        
        linux_file_path = os.path.join(linux_base_path, filename)
        file_info = get_file_info(linux_file_path)
        
        if not file_info:
            raise HTTPException(status_code=404, detail=f"檔案不存在: {filename}")
        
        return {
            "status": "success", "filename": file_info.filename, "size": file_info.size,
            "size_mb": file_info.size_mb, "modified_time": file_info.modified_time,
            "file_type": file_info.file_type, "is_directory": file_info.is_directory, "path": path
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取得檔案資訊失敗: {str(e)}")


@app.get("/api/credentials")
async def get_network_credentials():
    """獲取網路連接認證資訊"""
    try:
        # 從環境變數讀取認證資訊
        username = os.getenv('EMAIL_ADDRESS', 'telowyield1')
        password = os.getenv('EMAIL_PASSWORD', 'GMTgmt88TE')

        return {
            "status": "success",
            "credentials": {
                "username": username,
                "password": password,
                "domain": "gmt"
            }
        }
    except Exception as e:
        logger.error(f"[ERROR] 獲取認證資訊失敗: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"獲取認證資訊失敗: {str(e)}"}
        )


@app.get("/api/current-user")
async def get_current_user():
    """獲取當前 Windows 用戶資訊"""
    try:
        import platform
        import getpass

        if platform.system() == "Windows":
            # 獲取當前 Windows 用戶名稱
            current_user = getpass.getuser()
            computer_name = platform.node()

            return {
                "status": "success",
                "user_info": {
                    "username": current_user,
                    "computer_name": computer_name,
                    "domain": "gmt",
                    "system": platform.system(),
                    "full_name": f"{computer_name}\\{current_user}"
                }
            }
        else:
            return {
                "status": "success",
                "user_info": {
                    "username": getpass.getuser(),
                    "computer_name": platform.node(),
                    "domain": "unknown",
                    "system": platform.system(),
                    "full_name": getpass.getuser()
                }
            }
    except Exception as e:
        logger.error(f"[ERROR] 獲取當前用戶資訊失敗: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"獲取當前用戶資訊失敗: {str(e)}"}
        )


@app.post("/api/connect", response_model=NetworkConnectResponse)
async def connect_network_share(request: NetworkConnectRequest):
    """使用認證資訊連接網路共享"""
    try:
        if request.path.startswith("\\\\"):
            path_parts = request.path[2:].split("\\")
            if len(path_parts) >= 2:
                server = path_parts[0]
                share = path_parts[1]
            else:
                return NetworkConnectResponse(status="error", connected=False, message="無效的UNC路徑格式")
        else:
            return NetworkConnectResponse(status="error", connected=False, message="請使用UNC路徑格式 (\\\\server\\share)")
        
        # 檢查是否使用當前用戶認證
        if request.username == 'current_user':
            # 使用當前Windows用戶認證
            import platform
            if platform.system() == "Windows":
                # 在Windows上，直接測試UNC路徑是否可存取
                try:
                    import os
                    test_result = os.path.exists(request.path)
                    if test_result:
                        # 創建一個特殊的認證對象表示當前用戶
                        credentials = NetworkCredentials(
                            username="current_windows_user", 
                            password="", 
                            domain=request.domain,
                            server=server, 
                            share=share
                        )
                        
                        # 連接成功，儲存認證資訊
                        connection_key = f"{server}_{share}"
                        active_connections[connection_key] = credentials
                        logger.info(f"[OK] 使用當前Windows用戶連接成功: {connection_key}")
                        
                        return NetworkConnectResponse(
                            status="success", connected=True, 
                            message=f"使用當前Windows用戶成功連接到 {request.path}",
                            mount_point=f"smb://{connection_key}"
                        )
                    else:
                        return NetworkConnectResponse(
                            status="error", connected=False, 
                            message="無法存取網路路徑，請檢查網路連接或權限"
                        )
                except Exception as e:
                    return NetworkConnectResponse(
                        status="error", connected=False, 
                        message=f"測試網路路徑時發生錯誤: {str(e)}"
                    )
            else:
                return NetworkConnectResponse(
                    status="error", connected=False, 
                    message="當前用戶認證僅支援Windows環境"
                )
        else:
            # 使用提供的認證資訊
            credentials = NetworkCredentials(
                username=request.username, password=request.password, domain=request.domain,
                server=server, share=share
            )
            
            # 實際測試SMB連接
            logger.info(f"[LOCKED_WITH_KEY] 測試SMB連接: {server}/{share} (使用者: {request.username})")
            connection_success, connection_message = test_smb_connection(server, share, credentials)
            
            if not connection_success:
                logger.warning(f"[ERROR] SMB連接失敗: {connection_message}")
                return NetworkConnectResponse(
                    status="error", connected=False, 
                    message=f"連接失敗: {connection_message}"
                )
            
            # 連接成功，儲存認證資訊
            connection_key = f"{server}_{share}"
            active_connections[connection_key] = credentials
            logger.info(f"[OK] SMB連接成功，已儲存認證: {connection_key}")
            
            return NetworkConnectResponse(
                status="success", connected=True, 
                message=f"成功連接到 {request.path}",
                mount_point=f"smb://{connection_key}"
            )
        
    except Exception as e:
        return NetworkConnectResponse(status="error", connected=False, message=f"連接時發生錯誤: {str(e)}")


@app.post("/api/validate", response_model=NetworkPathValidateResponse)
async def validate_network_path_endpoint(request: NetworkPathValidateRequest):
    """驗證網路路徑是否有效且可存取"""
    try:
        is_valid, actual_path = validate_network_path(request.path)
        
        if is_valid:
            message = f"網路路徑有效且可存取: {actual_path}"
            status = "success"
        else:
            message = "網路路徑無效或無法存取"
            status = "error"
        
        return NetworkPathValidateResponse(
            status=status, valid=is_valid, path=request.path, message=message, accessible=is_valid
        )
        
    except Exception as e:
        return NetworkPathValidateResponse(
            status="error", valid=False, path=request.path,
            message=f"路徑驗證時發生錯誤: {str(e)}", accessible=False
        )


@app.get("/")
async def network_root():
    """網路瀏覽器根路徑，重定向到 UI 介面"""
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/network/ui", status_code=302)

@app.get("/ui")
async def get_network_browser_ui():
    """提供網路共享瀏覽器的 Web UI 介面 (新模組化版本)"""
    try:
        current_dir = Path(__file__).parent.parent
        template_file = current_dir / "web" / "templates" / "network_browser_new.html"

        if template_file.exists():
            # 讀取模板內容並進行路徑替換
            with open(template_file, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # 替換靜態文件路徑以適應整合服務
            html_content = html_content.replace('/static/js/', '/network/static/js/')
            html_content = html_content.replace('/static/css/', '/network/static/css/')

            return HTMLResponse(content=html_content)
        else:
            html_content = '''<!DOCTYPE html><html lang="zh-TW"><head><meta charset="UTF-8"><title>網路共享瀏覽器 (新版)</title></head><body><h1>[GLOBE_WITH_MERIDIANS] 網路共享瀏覽器 (新版)</h1><p>新版HTML模板檔案未找到，請檢查模板路徑。</p></body></html>'''
            return HTMLResponse(content=html_content)

    except Exception as e:
        return HTMLResponse(content="<h1>網路瀏覽器 UI (新版) 載入失敗</h1>", status_code=500)

@app.get("/ui_new")
async def get_network_browser_ui_new():
    """提供網路共享瀏覽器的 Web UI 介面 (新模組化版本)"""
    try:
        current_dir = Path(__file__).parent.parent
        template_file = current_dir / "web" / "templates" / "network_browser_new.html"

        if template_file.exists():
            # 讀取模板內容並進行路徑替換
            with open(template_file, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # 替換靜態文件路徑以適應整合服務
            html_content = html_content.replace('/static/js/', '/network/static/js/')
            html_content = html_content.replace('/static/css/', '/network/static/css/')

            return HTMLResponse(content=html_content)
        else:
            html_content = '''<!DOCTYPE html><html lang="zh-TW"><head><meta charset="UTF-8"><title>網路共享瀏覽器 (新版)</title></head><body><h1>[GLOBE_WITH_MERIDIANS] 網路共享瀏覽器 (新版)</h1><p>新版HTML模板檔案未找到，請檢查模板路徑。</p></body></html>'''
            return HTMLResponse(content=html_content)

    except Exception as e:
        return HTMLResponse(content="<h1>網路瀏覽器 UI (新版) 載入失敗</h1>", status_code=500)


@app.post("/api/test/simple")
async def test_simple():
    """簡單測試端點"""
    print("🧪 簡單測試端點被調用")
    return {"status": "success", "message": "測試端點正常"}

@app.get("/api/celery/health")
async def celery_health_check():
    """Celery 健康檢查端點"""
    try:
        logger.info("🏥 執行 Celery 健康檢查")

        if health_check_task is None:
            return {
                "status": "error",
                "celery_worker": "unavailable",
                "error": "Celery 任務未載入"
            }

        # 提交健康檢查任務
        task = health_check_task.delay()

        # 等待短時間看是否能完成
        import time
        start_time = time.time()
        timeout = 5.0  # 5秒超時

        while not task.ready() and (time.time() - start_time) < timeout:
            time.sleep(0.1)

        if task.ready():
            if task.successful():
                result = task.result
                logger.info("✅ Celery 健康檢查成功")
                return {
                    "status": "healthy",
                    "celery_worker": "running",
                    "task_id": task.id,
                    "result": result
                }
            else:
                logger.error("❌ Celery 健康檢查任務失敗")
                return {
                    "status": "unhealthy",
                    "celery_worker": "error",
                    "task_id": task.id,
                    "error": str(task.info)
                }
        else:
            logger.warning("⏳ Celery 健康檢查超時")
            return {
                "status": "timeout",
                "celery_worker": "slow_or_unavailable",
                "task_id": task.id,
                "message": "健康檢查任務超時"
            }

    except Exception as e:
        logger.error(f"❌ Celery 健康檢查異常: {str(e)}")
        return {
            "status": "error",
            "celery_worker": "unavailable",
            "error": str(e)
        }

@app.post("/api/search/product")
async def search_product_submit(request: ProductSearchRequest):
    """產品搜尋端點 - 提交一個背景搜尋任務"""
    try:
        logger.info(f"🔍 API 收到搜尋請求，準備提交至背景佇列: {request.product_name}")
        print(f"🔍 API 收到搜尋請求: {request.product_name}")

        if product_search_service is None:
            raise HTTPException(status_code=503, detail="產品搜尋服務未初始化")

        if search_product_task is None:
            raise HTTPException(status_code=503, detail="Celery 任務未載入，請檢查 Worker 是否正常")
        
        # 建立時間範圍和篩選條件 (這部分邏輯不變)
        logger.debug("📅 建立時間範圍...")
        time_range = product_search_service.create_time_range(
            request.time_range_type,
            request.custom_start_date,
            request.custom_end_date
        )
        logger.debug(f"✅ 時間範圍建立完成: {time_range}")

        # 將複雜物件轉換為可傳遞的字典
        time_range_dict = {
            'start_date': time_range.start_date.isoformat() if time_range.start_date else None,
            'end_date': time_range.end_date.isoformat() if time_range.end_date else None
        }

        # 手動轉換 SearchFilters 為字典（因為它是 dataclass）
        filters_dict = {
            'file_types': request.file_types,
            'min_size': int(request.min_size_mb * 1024 * 1024) if request.min_size_mb else None,
            'max_size': int(request.max_size_mb * 1024 * 1024) if request.max_size_mb else None,
            'include_directories': request.include_directories,
            'search_directory': request.search_directory  # 添加搜尋目錄參數
        }

        # 【關鍵修改】提交任務到 Celery，這是一個非阻塞操作
        logger.info(f"🚀 提交搜尋任務到 Celery 背景佇列: {request.product_name}")

        task = search_product_task.delay(
            request.product_name,
            str(request.base_path),
            time_range_dict,
            filters_dict
        )

        logger.info(f"✅ 任務已成功提交，Task ID: {task.id}")

        # 檢查是否是 eager 模式（任務立即完成）
        if task.ready():
            logger.info("🧪 Eager 模式：任務已立即完成，直接返回結果")
            if task.successful():
                result = task.result
                return {
                    "status": "completed",
                    "task_id": task.id,
                    "result": result,
                    "message": "搜尋已完成"
                }
            else:
                return {
                    "status": "failed",
                    "task_id": task.id,
                    "error": str(task.info),
                    "message": "搜尋失敗"
                }
        else:
            # 非 eager 模式，正常的異步處理
            return {
                "status": "processing",
                "task_id": task.id,
                "message": "搜尋任務已在背景開始執行"
            }

    except Exception as e:
        logger.error(f"❌ 提交產品搜尋任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交搜尋任務時發生錯誤: {str(e)}")


@app.get("/api/task/status/{task_id}")
async def get_task_status(task_id: str):
    """【關鍵修改】根據 Task ID 獲取 Celery 背景任務的狀態和結果"""
    try:
        logger.debug(f"🔍 查詢 Celery 任務狀態: {task_id}")

        # 使用 Celery 的 AsyncResult 來查詢任務
        task_result = AsyncResult(task_id)

        response = {
            "task_id": task_id,
            "status": task_result.status,
            "result": task_result.result if task_result.ready() else None,
            "info": task_result.info  # .info 包含 update_state 傳遞的 meta 資訊
        }

        if task_result.failed():
            # 如果任務失敗，將詳細的錯誤資訊也一併回傳
            response['error_message'] = str(task_result.info)
            logger.error(f"❌ 任務 {task_id} 失敗: {task_result.info}")

        return response

    except Exception as e:
        logger.error(f"❌ 查詢任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查詢任務狀態時發生錯誤: {str(e)}")


@app.get("/api/search/task/{task_id}", response_model=SearchTaskResponse)
async def get_search_task_status(task_id: str):
    """獲取搜尋任務狀態"""
    try:
        if product_search_service is None:
            raise HTTPException(status_code=500, detail="產品搜尋服務未初始化")
        
        task = product_search_service.get_task_status(task_id)
        if not task:
            raise HTTPException(status_code=404, detail=f"搜尋任務不存在: {task_id}")
        
        # 轉換任務狀態
        response = SearchTaskResponse(
            task_id=task.task_id,
            product_name=task.product_name,
            status=task.status.value,
            created_at=task.created_at.isoformat(),
            completed_at=datetime.now().isoformat() if task.is_completed() else None,
            result=None,  # 這裡可以根據需要添加結果轉換
            progress=100.0 if task.is_completed() else 50.0 if task.status.value == "in_progress" else 0.0
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ERROR] 獲取搜尋任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"獲取任務狀態時發生錯誤: {str(e)}")


@app.get("/api/smart-search")
async def smart_search(
    query: str = Query(..., description="自然語言搜尋查詢"),
    path: str = Query(default="\\\\************\\test_log", description="網路資料夾路徑"),
    max_results: int = Query(default=100, description="最大結果數量", ge=1, le=1000)
):
    """LLM 智慧搜尋端點 - 支援自然語言查詢"""
    try:
        if llm_search_service is None:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "service_unavailable",
                    "message": "LLM 智慧搜尋服務未初始化",
                    "query": query,
                    "fallback_endpoint": "/network/api/search/product",
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        logger.info(f"[BRAIN] 開始 LLM 智慧搜尋: {query}")
        
        # 執行智慧搜尋
        base_path = Path(path)
        result = await llm_search_service.smart_search(query, base_path, max_results)
        
        logger.info(f"[OK] LLM 智慧搜尋完成: {query}")
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"[ERROR] LLM 智慧搜尋失敗: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "query": query,
                "error_message": f"智慧搜尋時發生錯誤: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.post("/api/smart-search", response_model=SmartSearchResponse)
async def smart_search_post(request: SmartSearchRequest):
    """LLM 智慧搜尋端點 (POST) - 支援更複雜的搜尋請求"""
    try:
        if llm_search_service is None:
            return SmartSearchResponse(
                status="service_unavailable",
                query=request.query,
                error_message="LLM 智慧搜尋服務未初始化"
            )
        
        logger.info(f"[BRAIN] 開始 LLM 智慧搜尋 (POST): {request.query}")
        
        # 執行智慧搜尋
        base_path = Path(request.path)
        result = await llm_search_service.smart_search(request.query, base_path, request.max_results)
        
        # 轉換為回應模型
        response = SmartSearchResponse(
            status=result.get("status", "error"),
            query=result.get("query", request.query),
            interpretation=result.get("interpretation", {}),
            results=result.get("results", []),
            analysis=result.get("analysis", {}),
            suggestions=result.get("suggestions", []),
            search_duration=result.get("search_duration", 0.0),
            timestamp=result.get("timestamp", datetime.now().isoformat()),
            error_message=result.get("error_message")
        )
        
        logger.info(f"[OK] LLM 智慧搜尋完成 (POST): {request.query}")
        return response
        
    except Exception as e:
        logger.error(f"[ERROR] LLM 智慧搜尋失敗 (POST): {str(e)}")
        return SmartSearchResponse(
            status="error",
            query=request.query,
            error_message=f"智慧搜尋時發生錯誤: {str(e)}"
        )


@app.get("/test-smb")
async def test_smb_endpoint():
    """測試SMB連接函數"""
    from .network_utils import test_smb_connection
    from .network_models import NetworkCredentials
    
    creds = NetworkCredentials(
        username='wrong_user', password='wrong_pass', domain='gmt',
        server='************', share='test_log'
    )
    result = test_smb_connection('************', 'test_log', creds)
    return {"test_result": result, "function_working": True}

@app.post("/api/process/csv-summary")
async def process_csv_summary_submit(
    input_path: str = Query(..., description="輸入檔案或目錄路徑")
):
    """提交 CSV 摘要生成背景任務"""
    try:
        if run_csv_summary_task is None:
            raise HTTPException(status_code=503, detail="CSV 摘要任務未載入，請檢查 Worker 是否正常")

        logger.info(f"🚀 提交 CSV 摘要任務到背景佇列: {input_path}")

        # 提交任務到 Celery
        task = run_csv_summary_task.delay(input_path)

        logger.info(f"✅ CSV 摘要任務已成功提交，Task ID: {task.id}")

        # 立即回傳 Task ID 給前端
        return {
            "status": "processing",
            "task_id": task.id,
            "message": "CSV 摘要任務已在背景開始執行"
        }

    except Exception as e:
        logger.error(f"❌ 提交 CSV 摘要任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交 CSV 摘要任務時發生錯誤: {str(e)}")


@app.post("/api/process/code-comparison")
async def process_code_comparison_submit(
    input_path: str = Query(..., description="輸入檔案或目錄路徑")
):
    """提交程式碼比較背景任務"""
    try:
        if run_code_comparison_task is None:
            raise HTTPException(status_code=503, detail="程式碼比較任務未載入，請檢查 Worker 是否正常")

        logger.info(f"🚀 提交程式碼比較任務到背景佇列: {input_path}")

        # 提交任務到 Celery
        task = run_code_comparison_task.delay(input_path)

        logger.info(f"✅ 程式碼比較任務已成功提交，Task ID: {task.id}")

        # 立即回傳 Task ID 給前端
        return {
            "status": "processing",
            "task_id": task.id,
            "message": "程式碼比較任務已在背景開始執行"
        }

    except Exception as e:
        logger.error(f"❌ 提交程式碼比較任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交程式碼比較任務時發生錯誤: {str(e)}")


@app.get("/api/process/task/{task_id}")
async def get_processing_task_status(task_id: str):
    """獲取處理任務狀態"""
    try:
        file_processing_service = get_file_processing_service()
        
        # 獲取任務進度
        progress_info = await file_processing_service.get_task_progress(task_id)
        
        if "error" in progress_info:
            raise HTTPException(status_code=404, detail=progress_info["error"])
        
        return JSONResponse(content=progress_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ERROR] 獲取處理任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"獲取任務狀態時發生錯誤: {str(e)}")


@app.get("/api/process/tasks")
async def list_processing_tasks():
    """列出所有處理任務"""
    try:
        file_processing_service = get_file_processing_service()
        
        # 獲取任務列表
        tasks = file_processing_service.list_tasks()
        
        return JSONResponse(content={
            "status": "success",
            "tasks": tasks,
            "total_count": len(tasks),
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"[ERROR] 列出處理任務失敗: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error_message": f"列出任務時發生錯誤: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/health")
async def health_check():
    """健康檢查端點"""
    try:
        import platform
        if platform.system() == "Windows":
            result = subprocess.run(["ping", "-n", "1", "************"], capture_output=True, text=True, timeout=5)
        else:
            result = subprocess.run(["ping", "-c", "1", "************"], capture_output=True, text=True, timeout=5)
        network_status = "reachable" if result.returncode == 0 else "unreachable"
    except:
        network_status = "unreachable"
    
    # Windows環境下檢查UNC路徑可用性，Linux環境檢查掛載點
    if platform.system() == "Windows":
        # Windows: 檢查UNC路徑是否可存取
        unc_paths = ["\\\\************\\test_log", "\\\\************\\temp_7days"]
        available_paths = []
        for unc_path in unc_paths:
            try:
                if os.path.exists(unc_path):
                    available_paths.append(unc_path)
            except:
                pass
        mount_points = available_paths
    else:
        # Linux: 檢查掛載點
        possible_mounts = ["/mnt/wsl/************/temp_7days", "/mnt/************/temp_7days", "/mnt/network/************_temp_7days"]
        mount_points = [mount for mount in possible_mounts if os.path.exists(mount)]
    
    return {
        "status": "healthy", "service": "network_browser_api", "timestamp": datetime.now().isoformat(),
        "version": "1.0.0", "port": 8009,
        "network": {"target_host": "************", "connectivity": network_status, "available_mounts": mount_points, "mount_count": len(mount_points)}
    }


# 即時儀表板端點
@app.get("/dashboard")
async def get_realtime_dashboard():
    """即時監控儀表板"""
    from fastapi import Request
    
    # 創建一個模擬的 request 對象來與 Jinja2Templates 兼容
    class MockRequest:
        def __init__(self):
            self.url = "/dashboard"
    
    request = MockRequest()
    
    try:
        return templates.TemplateResponse("realtime_dashboard.html", {"request": request})
    except Exception as e:
        logger.error(f"儀表板模板錯誤: {e}")
        return HTMLResponse(
            content="<h1>即時監控儀表板</h1><p>模板載入失敗，請檢查模板文件</p>",
            status_code=200
        )


if __name__ == "__main__":
    import uvicorn
    
    # 啟動 WebSocket 背景任務
    if start_background_tasks:
        start_background_tasks()
        logger.info("WebSocket 背景任務已啟動")
    
    logger.info("[ROCKET] 啟動網路共享瀏覽器 API 服務 (端口: 8009)")
    logger.info("即時監控儀表板: http://localhost:8009/dashboard")
    uvicorn.run(app, host="0.0.0.0", port=8009)