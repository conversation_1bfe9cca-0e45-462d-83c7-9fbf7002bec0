{"tasks": [{"id": "a57fd053-c77c-48c6-a73d-315a7f2ade4a", "name": "創建統一服務配置管理模組", "description": "建立src/services/config_manager.py模組，整合所有服務的配置管理，包含端口配置、服務啟用狀態、日誌配置等，為統一架構提供配置基礎", "notes": "重用現有src/infrastructure/config/config_manager.py的設計模式，擴展支援多服務配置管理", "status": "pending", "dependencies": [], "createdAt": "2025-07-28T23:04:14.874Z", "updatedAt": "2025-07-28T23:04:14.874Z", "relatedFiles": [{"path": "src/services/config_manager.py", "type": "CREATE", "description": "統一服務配置管理模組"}, {"path": "src/infrastructure/config/config_manager.py", "type": "REFERENCE", "description": "現有配置管理器參考"}], "implementationGuide": "1. 創建UnifiedConfig類別管理所有服務配置 2. 定義服務端點配置(EMAIL_SERVICE, FT_EQC_SERVICE, SCHEDULER_SERVICE, NETWORK_SERVICE) 3. 實現配置驗證和預設值設定 4. 提供配置檔案載入和環境變數整合功能 5. 確保與現有config_manager.py相容", "verificationCriteria": "配置模組能正確載入所有服務配置，環境變數覆蓋機制正常運作，配置驗證功能完整", "analysisResult": "設計並實現統一企業級服務架構，將所有服務(Flask郵件收件夾、FastAPI FT-EQC處理、增強任務排程器、網路瀏覽器API)整合到單一5000端口，採用FastAPI作為主應用，使用WSGIMiddleware整合Flask，實現模組化設計、根目錄清理和統一路由管理"}, {"id": "b06db714-cf62-4e77-8606-e8390e644c28", "name": "實現服務整合器核心模組", "description": "建立src/services/service_integrator.py，負責將Flask郵件服務、FastAPI子服務整合到統一的FastAPI主應用中，使用WSGIMiddleware技術實現跨框架整合", "notes": "使用FastAPI的WSGIMiddleware包裝Flask應用，參考a2wsgi技術方案，確保性能最佳化", "status": "pending", "dependencies": [{"taskId": "a57fd053-c77c-48c6-a73d-315a7f2ade4a"}], "createdAt": "2025-07-28T23:04:14.874Z", "updatedAt": "2025-07-28T23:04:14.874Z", "relatedFiles": [{"path": "src/services/service_integrator.py", "type": "CREATE", "description": "服務整合器核心模組"}, {"path": "email_inbox_app.py", "type": "REFERENCE", "description": "現有Flask郵件應用"}, {"path": "src/presentation/api/ft_eqc_api.py", "type": "REFERENCE", "description": "FT-EQC FastAPI服務"}, {"path": "src/presentation/api/enhanced_scheduler_api.py", "type": "REFERENCE", "description": "增強排程器API"}, {"path": "src/presentation/api/network_browser_api.py", "type": "REFERENCE", "description": "網路瀏覽器API"}], "implementationGuide": "1. 創建ServiceIntegrator類別 2. 實現Flask應用包裝為WSGIMiddleware的功能 3. 整合ft_eqc_api.py、enhanced_scheduler_api.py、network_browser_api.py作為子路由 4. 實現統一的錯誤處理和中間件管理 5. 提供服務健康檢查端點", "verificationCriteria": "所有服務能成功整合到單一FastAPI應用，跨框架路由正常運作，中間件和錯誤處理統一有效", "analysisResult": "設計並實現統一企業級服務架構，將所有服務(Flask郵件收件夾、FastAPI FT-EQC處理、增強任務排程器、網路瀏覽器API)整合到單一5000端口，採用FastAPI作為主應用，使用WSGIMiddleware整合Flask，實現模組化設計、根目錄清理和統一路由管理"}, {"id": "14aed9ac-f8d9-4aed-b14f-a3a985bf7fb0", "name": "設計統一路由管理系統", "description": "建立src/services/route_manager.py，實現統一的路由規劃和管理，包含路由前綴分配(/inbox/, /ft-eqc/, /scheduler/, /network/)、路由衝突檢測、動態路由註冊功能", "notes": "確保所有路由都有明確的前綴分離，避免路由衝突，支援向下相容性", "status": "pending", "dependencies": [{"taskId": "b06db714-cf62-4e77-8606-e8390e644c28"}], "createdAt": "2025-07-28T23:04:14.874Z", "updatedAt": "2025-07-28T23:04:14.874Z", "relatedFiles": [{"path": "src/services/route_manager.py", "type": "CREATE", "description": "統一路由管理系統"}, {"path": "src/presentation/api/", "type": "REFERENCE", "description": "現有API路由結構參考"}], "implementationGuide": "1. 創建RouteManager類別 2. 定義路由前綴常數和規則 3. 實現路由衝突檢測邏輯 4. 提供動態路由註冊和移除功能 5. 實現路由重定向和反向代理邏輯 6. 創建統一的API文檔整合功能", "verificationCriteria": "路由前綴正確分配，無路由衝突，API文檔整合完整，向下相容性保持", "analysisResult": "設計並實現統一企業級服務架構，將所有服務(Flask郵件收件夾、FastAPI FT-EQC處理、增強任務排程器、網路瀏覽器API)整合到單一5000端口，採用FastAPI作為主應用，使用WSGIMiddleware整合Flask，實現模組化設計、根目錄清理和統一路由管理"}, {"id": "f70ffed8-5bff-42f8-8214-75d27b5ae20a", "name": "建立統一日誌和監控系統", "description": "建立src/services/unified_logger.py，整合所有服務的日誌管理，提供統一的日誌格式、等級控制、日誌輪轉，並實現服務監控和健康檢查功能", "notes": "重用現有src/infrastructure/logging/logger_manager.py，擴展支援多服務統一管理", "status": "pending", "dependencies": [{"taskId": "14aed9ac-f8d9-4aed-b14f-a3a985bf7fb0"}], "createdAt": "2025-07-28T23:04:14.874Z", "updatedAt": "2025-07-28T23:04:14.874Z", "relatedFiles": [{"path": "src/services/unified_logger.py", "type": "CREATE", "description": "統一日誌和監控系統"}, {"path": "src/infrastructure/logging/logger_manager.py", "type": "REFERENCE", "description": "現有日誌管理器"}, {"path": "src/infrastructure/adapters/notification/line_notification_service.py", "type": "REFERENCE", "description": "LINE通知服務"}], "implementationGuide": "1. 創建UnifiedLogger類別擴展現有LoggerManager 2. 實現服務級別的日誌隔離和聚合 3. 提供統一的日誌格式化和輸出控制 4. 實現服務健康監控和告警機制 5. 整合現有的LINE通知服務", "verificationCriteria": "所有服務日誌統一格式化，監控告警正常運作，日誌輪轉和存儲管理有效", "analysisResult": "設計並實現統一企業級服務架構，將所有服務(Flask郵件收件夾、FastAPI FT-EQC處理、增強任務排程器、網路瀏覽器API)整合到單一5000端口，採用FastAPI作為主應用，使用WSGIMiddleware整合Flask，實現模組化設計、根目錄清理和統一路由管理"}, {"id": "72891a27-a150-478c-8171-c575eff2aae0", "name": "重構主啟動程式", "description": "重構start_integrated_services.py為統一的企業級啟動程式，整合所有服務到單一5000端口，確保程式碼控制在500行內，實現優雅的啟動、停止和重啟機制", "notes": "保持向下相容性，支援現有的命令列參數，優化啟動時間和資源使用", "status": "pending", "dependencies": [{"taskId": "f70ffed8-5bff-42f8-8214-75d27b5ae20a"}], "createdAt": "2025-07-28T23:04:14.874Z", "updatedAt": "2025-07-28T23:04:14.874Z", "relatedFiles": [{"path": "start_integrated_services.py", "type": "TO_MODIFY", "description": "主啟動程式重構"}, {"path": "src/services/service_integrator.py", "type": "DEPENDENCY", "description": "服務整合器依賴"}, {"path": "src/services/route_manager.py", "type": "DEPENDENCY", "description": "路由管理器依賴"}], "implementationGuide": "1. 重構main()函數整合ServiceIntegrator 2. 實現命令列參數解析(--port, --host, --debug等) 3. 添加優雅的信號處理和資源清理 4. 實現服務預熱和健康檢查 5. 提供詳細的啟動資訊和狀態顯示 6. 確保程式碼行數控制在500行內", "verificationCriteria": "單一5000端口成功啟動所有服務，程式碼行數≤500行，優雅關閉機制正常，服務健康檢查有效", "analysisResult": "設計並實現統一企業級服務架構，將所有服務(Flask郵件收件夾、FastAPI FT-EQC處理、增強任務排程器、網路瀏覽器API)整合到單一5000端口，採用FastAPI作為主應用，使用WSGIMiddleware整合Flask，實現模組化設計、根目錄清理和統一路由管理"}, {"id": "69e916d9-049f-46d9-9e0c-91d3a38757f2", "name": "執行根目錄清理和結構最佳化", "description": "清理根目錄結構，保留必要檔案(csv_to_summary.py, code_comparison.py, batch_csv_to_excel_processor.py)，移除重複的啟動腳本和無用檔案，整理src/目錄結構", "notes": "保持重要功能檔案不變，確保現有功能不受影響，優化專案結構清晰度", "status": "pending", "dependencies": [{"taskId": "72891a27-a150-478c-8171-c575eff2aae0"}], "createdAt": "2025-07-28T23:04:14.874Z", "updatedAt": "2025-07-28T23:04:14.874Z", "relatedFiles": [{"path": "csv_to_summary.py", "type": "REFERENCE", "description": "保留的工具檔案"}, {"path": "code_comparison.py", "type": "REFERENCE", "description": "保留的工具檔案"}, {"path": "batch_csv_to_excel_processor.py", "type": "REFERENCE", "description": "保留的工具檔案"}, {"path": "src/", "type": "TO_MODIFY", "description": "整理目錄結構"}], "implementationGuide": "1. 保留指定的Python工具檔案在根目錄 2. 刪除所有.bat和.sh啟動腳本 3. 移除重複和無用的.md檔案 4. 清理test_*.py和simple_*.py等測試檔案 5. 整理src/目錄結構，確保模組導入路徑正確 6. 更新.gitignore和README.md", "verificationCriteria": "根目錄結構清晰，無重複檔案，所有模組導入正常，專案啟動無錯誤", "analysisResult": "設計並實現統一企業級服務架構，將所有服務(Flask郵件收件夾、FastAPI FT-EQC處理、增強任務排程器、網路瀏覽器API)整合到單一5000端口，採用FastAPI作為主應用，使用WSGIMiddleware整合Flask，實現模組化設計、根目錄清理和統一路由管理"}, {"id": "42b8cc27-04c0-4abb-b160-078ef06afd52", "name": "實施整合測試和驗證", "description": "建立完整的整合測試套件，驗證統一服務架構的所有功能，包含服務啟動測試、API端點測試、跨服務通信測試、負載測試和錯誤處理測試", "notes": "確保所有原有功能在新架構下正常運作，性能指標符合企業級要求", "status": "pending", "dependencies": [{"taskId": "69e916d9-049f-46d9-9e0c-91d3a38757f2"}], "createdAt": "2025-07-28T23:04:14.874Z", "updatedAt": "2025-07-28T23:04:14.874Z", "relatedFiles": [{"path": "integration_test.py", "type": "CREATE", "description": "整合測試腳本"}, {"path": "start_integrated_services.py", "type": "DEPENDENCY", "description": "測試目標主程式"}], "implementationGuide": "1. 創建integration_test.py測試腳本 2. 實現服務啟動和健康檢查測試 3. 測試所有API端點的可訪問性和回應正確性 4. 驗證跨服務功能整合(郵件處理->FT-EQC->排程器) 5. 執行負載測試確認性能指標 6. 測試優雅關閉和錯誤恢復機制", "verificationCriteria": "所有整合測試通過，服務啟動時間<30秒，API回應時間<2秒，記憶體使用量合理，錯誤處理機制有效", "analysisResult": "設計並實現統一企業級服務架構，將所有服務(Flask郵件收件夾、FastAPI FT-EQC處理、增強任務排程器、網路瀏覽器API)整合到單一5000端口，採用FastAPI作為主應用，使用WSGIMiddleware整合Flask，實現模組化設計、根目錄清理和統一路由管理"}]}