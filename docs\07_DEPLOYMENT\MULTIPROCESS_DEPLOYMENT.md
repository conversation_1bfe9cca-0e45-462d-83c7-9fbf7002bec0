# 多進程系統部署設置指南

## 🎯 部署概覽

本文檔提供 ProcessPoolExecutor 多核心處理系統的生產環境部署方案，確保充分利用多核心資源並實現高性能處理。

## 🏗️ 部署架構

```yaml
Production Environment:
  主應用: Python 3.11+ FastAPI/Flask
  進程池: ProcessPoolExecutor (CPU核心數)
  監控: 實時資源監控
  日誌: 分層日誌管理
  健康檢查: 自動故障恢復
  
Resource Allocation:
  CPU密集型進程池: 全部物理核心
  IO密集型線程池: 2x邏輯核心
  混合型進程池: 0.5x物理核心
```

## 📋 系統要求

### 硬體規格
```yaml
最低要求:
  CPU: 4核心8線程
  記憶體: 8GB
  磁碟: 50GB SSD

建議規格:
  CPU: 8核心16線程+
  記憶體: 16GB+
  磁碟: 100GB+ NVMe SSD
  
生產級規格:
  CPU: 16核心32線程+
  記憶體: 32GB+
  磁碟: 500GB+ NVMe SSD RAID
```

### 軟體環境
```yaml
作業系統: Linux (Ubuntu 20.04+ / CentOS 8+)
Python: 3.11+
進程管理: systemd / supervisord
監控: Prometheus + Grafana
代理: Nginx (可選)
```

## ⚙️ 部署配置

### 1. 進程池生產配置
```python
# config/production.py
import multiprocessing as mp
import os

class ProductionConfig:
    """生產環境配置"""
    
    # 進程池配置
    CPU_CORES = mp.cpu_count()
    PHYSICAL_CORES = CPU_CORES // 2  # 假設超線程
    
    PROCESS_POOL_CONFIG = {
        'cpu_intensive': {
            'max_workers': PHYSICAL_CORES,
            'max_tasks_per_child': 100,
            'timeout': 300
        },
        'io_intensive': {
            'max_workers': min(CPU_CORES * 2, 20),
            'max_tasks_per_child': 500,
            'timeout': 60
        },
        'mixed': {
            'max_workers': max(PHYSICAL_CORES // 2, 2),
            'max_tasks_per_child': 200,
            'timeout': 180
        }
    }
    
    # 資源限制
    MEMORY_LIMIT_MB = 512
    MAX_QUEUE_SIZE = 1000
    
    # 監控配置
    MONITORING_INTERVAL = 10
    HEALTH_CHECK_INTERVAL = 30
    
    # 日誌配置
    LOG_LEVEL = 'INFO'
    LOG_ROTATION = '100MB'
    LOG_RETENTION = '30 days'
```

### 2. 應用入口點
```python
# app/main.py
import asyncio
import signal
import sys
from contextlib import asynccontextmanager
from fastapi import FastAPI
from config.production import ProductionConfig
from core.multiprocess_manager import UnifiedProcessPoolManager
from core.monitoring import ProcessMonitor

class MultiProcessApp:
    """多進程應用管理器"""
    
    def __init__(self):
        self.config = ProductionConfig()
        self.process_manager = None
        self.monitor = None
        self.shutdown_event = asyncio.Event()
    
    async def startup(self):
        """應用啟動"""
        print("🚀 啟動多進程應用...")
        
        # 初始化進程管理器
        self.process_manager = UnifiedProcessPoolManager(self.config)
        await self.process_manager.initialize()
        
        # 初始化監控
        self.monitor = ProcessMonitor(self.process_manager)
        await self.monitor.start_monitoring()
        
        # 設置信號處理
        self._setup_signal_handlers()
        
        print(f"✅ 應用啟動完成 - CPU核心: {self.config.CPU_CORES}")
    
    async def shutdown(self):
        """應用關閉"""
        print("🛑 開始關閉應用...")
        
        self.shutdown_event.set()
        
        if self.monitor:
            await self.monitor.stop_monitoring()
        
        if self.process_manager:
            await self.process_manager.shutdown_all()
        
        print("✅ 應用已安全關閉")
    
    def _setup_signal_handlers(self):
        """設置信號處理器"""
        def signal_handler(signum, frame):
            print(f"收到信號 {signum}，準備關閉...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

# 全局應用實例
app_manager = MultiProcessApp()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI生命週期管理"""
    await app_manager.startup()
    yield
    await app_manager.shutdown()

# FastAPI應用
app = FastAPI(
    title="多進程郵件處理系統",
    version="1.0.0",
    lifespan=lifespan
)
```

### 3. Systemd 服務配置
```ini
# /etc/systemd/system/outlook-summary.service
[Unit]
Description=Outlook Summary Multi-Process Service
After=network.target
Requires=network.target

[Service]
Type=simple
User=outlook-user
Group=outlook-group
WorkingDirectory=/opt/outlook-summary
Environment=PYTHONPATH=/opt/outlook-summary
Environment=OUTLOOK_ENV=production
ExecStart=/opt/outlook-summary/venv/bin/python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 1
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=5

# 資源限制
LimitNOFILE=65536
LimitNPROC=32768
MemoryMax=4G
CPUQuota=800%

# 安全設置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/outlook-summary/logs /opt/outlook-summary/temp

[Install]
WantedBy=multi-user.target
```

### 4. Docker 部署配置
```dockerfile
# Dockerfile
FROM python:3.11-slim

# 系統更新和基礎套件
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libc6-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 創建應用用戶
RUN groupadd -r outlook && useradd -r -g outlook outlook

# 設置工作目錄
WORKDIR /app

# 複製依賴文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 複製應用代碼
COPY . .
RUN chown -R outlook:outlook /app

# 切換到應用用戶
USER outlook

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 啟動命令
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml  
version: '3.8'

services:
  outlook-summary:
    build: .
    container_name: outlook-summary
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - OUTLOOK_ENV=production
      - PYTHONUNBUFFERED=1
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
      - ./data:/app/data
    deploy:
      resources:
        limits:
          cpus: '8.0'
          memory: 4G
        reservations:
          cpus: '4.0'
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  monitoring:
    image: prom/prometheus:latest
    container_name: outlook-monitoring
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    depends_on:
      - outlook-summary
```

## 🔧 部署腳本

### 1. 自動化部署腳本
```bash
#!/bin/bash
# deploy.sh - 自動化部署腳本

set -e

# 配置變數
APP_NAME="outlook-summary"
APP_USER="outlook-user"
APP_DIR="/opt/$APP_NAME"
SERVICE_NAME="$APP_NAME.service"
PYTHON_VERSION="3.11"

echo "🚀 開始部署 $APP_NAME..."

# 檢查系統要求
check_requirements() {
    echo "🔍 檢查系統要求..."
    
    # 檢查CPU核心數
    CPU_CORES=$(nproc)
    if [ $CPU_CORES -lt 4 ]; then
        echo "❌ 警告: CPU核心數不足 ($CPU_CORES < 4)"
    else
        echo "✅ CPU核心數: $CPU_CORES"
    fi
    
    # 檢查記憶體
    MEMORY_GB=$(free -g | grep '^Mem:' | awk '{print $2}')
    if [ $MEMORY_GB -lt 8 ]; then
        echo "❌ 警告: 記憶體不足 ($MEMORY_GB GB < 8 GB)"
    else
        echo "✅ 記憶體: ${MEMORY_GB}GB"
    fi
    
    # 檢查Python版本
    if ! command -v python3.11 &> /dev/null; then
        echo "❌ 需要安裝 Python 3.11+"
        exit 1
    fi
    echo "✅ Python版本: $(python3.11 --version)"
}

# 創建用戶和目錄
setup_user_and_dirs() {
    echo "👤 設置用戶和目錄..."
    
    # 創建用戶
    if ! id "$APP_USER" &>/dev/null; then
        sudo useradd -r -s /bin/bash -d $APP_DIR $APP_USER
        echo "✅ 創建用戶: $APP_USER"
    fi
    
    # 創建目錄
    sudo mkdir -p $APP_DIR/{logs,temp,data,config}
    sudo chown -R $APP_USER:$APP_USER $APP_DIR
    echo "✅ 創建目錄結構"
}

# 安裝依賴
install_dependencies() {
    echo "📦 安裝依賴..."
    
    # 創建虛擬環境
    sudo -u $APP_USER python3.11 -m venv $APP_DIR/venv
    sudo -u $APP_USER $APP_DIR/venv/bin/pip install --upgrade pip
    
    # 安裝依賴
    sudo -u $APP_USER $APP_DIR/venv/bin/pip install -r requirements.txt
    echo "✅ 依賴安裝完成"
}

# 部署應用
deploy_application() {
    echo "🏗️ 部署應用..."
    
    # 複製代碼
    sudo cp -r src/ $APP_DIR/
    sudo cp -r config/ $APP_DIR/
    sudo cp app/ $APP_DIR/
    sudo chown -R $APP_USER:$APP_USER $APP_DIR
    
    # 配置權限
    sudo chmod +x $APP_DIR/src/main.py
    echo "✅ 應用部署完成"
}

# 配置服務
setup_service() {
    echo "⚙️ 配置系統服務..."
    
    # 複製服務文件
    sudo cp deployment/$SERVICE_NAME /etc/systemd/system/
    
    # 重載並啟用服務
    sudo systemctl daemon-reload
    sudo systemctl enable $SERVICE_NAME
    echo "✅ 服務配置完成"
}

# 執行部署步驟
check_requirements
setup_user_and_dirs
install_dependencies
deploy_application
setup_service

echo "🎉 部署完成！"
echo "啟動服務: sudo systemctl start $SERVICE_NAME"
echo "查看狀態: sudo systemctl status $SERVICE_NAME"
echo "查看日誌: sudo journalctl -u $SERVICE_NAME -f"
```

### 2. 性能調優腳本
```bash
#!/bin/bash
# optimize.sh - 系統性能調優

echo "🔧 開始系統性能調優..."

# CPU調優
optimize_cpu() {
    echo "⚡ CPU調優..."
    
    # 設置CPU頻率調節器
    echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
    
    # 設置CPU親和性
    echo "2-7" | sudo tee /sys/fs/cgroup/cpuset/outlook-summary/cpuset.cpus
    
    echo "✅ CPU調優完成"
}

# 記憶體調優
optimize_memory() {
    echo "💾 記憶體調優..."
    
    # 調整swap使用
    echo 10 | sudo tee /proc/sys/vm/swappiness
    
    # 調整記憶體over-commit
    echo 1 | sudo tee /proc/sys/vm/overcommit_memory
    
    echo "✅ 記憶體調優完成"
}

# 檔案系統調優
optimize_filesystem() {
    echo "📁 檔案系統調優..."
    
    # 增加檔案描述符限制
    echo "outlook-user soft nofile 65536" | sudo tee -a /etc/security/limits.conf
    echo "outlook-user hard nofile 65536" | sudo tee -a /etc/security/limits.conf
    
    # 調整檔案系統參數
    echo 65536 | sudo tee /proc/sys/fs/file-max
    
    echo "✅ 檔案系統調優完成"
}

optimize_cpu
optimize_memory
optimize_filesystem

echo "🎯 系統調優完成！重啟服務以生效。"
```

## 📊 監控和健康檢查

### 1. 健康檢查端點
```python
# app/health.py
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import psutil
import asyncio

router = APIRouter()

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """健康檢查端點"""
    try:
        # 檢查進程池狀態
        pool_status = app_manager.process_manager.get_pool_status()
        
        # 檢查系統資源
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # 檢查磁碟空間
        disk = psutil.disk_usage('/')
        
        status = {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent,
                "available_memory_gb": memory.available / (1024**3)
            },
            "process_pools": pool_status
        }
        
        # 健康檢查邏輯
        if cpu_percent > 95:
            status["status"] = "degraded"
        if memory.percent > 90:
            status["status"] = "degraded" 
        if disk.percent > 85:
            status["status"] = "degraded"
            
        return status
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"健康檢查失敗: {str(e)}")

@router.get("/metrics")
async def metrics() -> Dict[str, Any]:
    """Prometheus格式指標"""
    monitor = app_manager.monitor
    return await monitor.get_prometheus_metrics()
```

### 2. Prometheus監控配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'outlook-summary'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
```

## 🚀 部署命令

### 快速部署
```bash
# 1. 克隆代碼
git clone <repository-url> /opt/outlook-summary
cd /opt/outlook-summary

# 2. 執行部署腳本
chmod +x deployment/deploy.sh
sudo ./deployment/deploy.sh

# 3. 啟動服務
sudo systemctl start outlook-summary
sudo systemctl enable outlook-summary

# 4. 驗證部署
curl http://localhost:8000/health
sudo systemctl status outlook-summary
```

### Docker部署
```bash
# 1. 構建鏡像
docker-compose build

# 2. 啟動服務
docker-compose up -d

# 3. 查看狀態
docker-compose ps
docker-compose logs -f outlook-summary
```

## 🔍 故障排除

### 常見問題解決
```yaml
進程池啟動失敗:
  症狀: 服務無法啟動
  解決: 檢查記憶體和檔案描述符限制
  命令: ulimit -n; free -h

性能下降:
  症狀: 處理速度變慢
  解決: 檢查CPU和記憶體使用率
  命令: htop; iostat; iotop

記憶體洩漏:
  症狀: 記憶體使用持續增長
  解決: 重啟進程池或調整max_tasks_per_child
  命令: sudo systemctl restart outlook-summary
```

## 📈 性能優化

### 生產環境調優
```python
# 根據硬體調整配置
PRODUCTION_OPTIMIZATIONS = {
    "16核32線程": {
        "cpu_intensive_workers": 16,
        "io_intensive_workers": 32,
        "memory_per_worker": "256MB"
    },
    "32核64線程": {
        "cpu_intensive_workers": 32,
        "io_intensive_workers": 64,
        "memory_per_worker": "512MB"
    }
}
```

---

**任務完成！現在觸發輕量版文檔更新...**

這個部署設置方案提供了：
- 完整的生產環境配置
- 自動化部署腳本
- Docker和systemd支援
- 監控和健康檢查
- 性能調優指南
- 故障排除方案

系統將充分利用多核心資源，實現高性能的郵件和文件處理能力。
