# 異步架構設計規範 - Outlook Summary 系統

## 🎯 異步架構總體目標

### 核心問題分析
當前系統存在**假異步問題**：
```python
# 當前問題模式 - 假異步
async def fake_async_function():
    with ThreadPoolExecutor(max_workers=4) as executor:
        # GIL 限制導致真正並發度僅 4-8 個
        result = executor.submit(cpu_intensive_task)
        return result.result()  # 阻塞等待
```

### 目標架構模式
```python
# 目標真異步模式
async def true_async_function():
    # I/O 異步化 - 突破阻塞瓶頸
    async with aiohttp.ClientSession() as session:
        response = await session.get(url)
    
    # CPU 密集型使用 ProcessPoolExecutor - 突破 GIL
    loop = asyncio.get_event_loop()
    with ProcessPoolExecutor() as executor:
        result = await loop.run_in_executor(executor, cpu_intensive_task)
    
    return result
```

---

## 🏗️ 整體異步架構設計

### 架構層級圖
```mermaid
graph TD
    A[FastAPI 異步層] --> B[業務邏輯異步層]
    B --> C[I/O 異步適配層]
    B --> D[CPU 並行處理層]
    C --> E[數據庫異步連接]
    C --> F[文件異步讀寫]
    C --> G[HTTP 異步客戶端]
    D --> H[ProcessPoolExecutor]
    D --> I[Task 分割調度器]
```

### 1. FastAPI 異步層重構

#### 1.1 端點異步化改造
```python
# 改造前 - 阻塞式
@app.post("/api/process_email")
def process_email(email_data: dict):
    # 同步處理，阻塞其他請求
    result = email_processor.process_sync(email_data)
    return result

# 改造後 - 真異步
@app.post("/api/process_email")
async def process_email(email_data: dict):
    # 異步處理，不阻塞其他請求
    result = await email_processor.process_async(email_data)
    return result
```

#### 1.2 依賴注入異步化
```python
# 異步依賴注入模式
async def get_async_database():
    """異步數據庫連接依賴"""
    async with AsyncDatabase() as db:
        yield db

async def get_async_email_processor():
    """異步郵件處理器依賴"""
    return AsyncEmailProcessor()

@app.post("/api/process")
async def process_endpoint(
    data: dict,
    db: AsyncDatabase = Depends(get_async_database),
    processor: AsyncEmailProcessor = Depends(get_async_email_processor)
):
    async with db.transaction():
        result = await processor.process_async(data)
        await db.save_result(result)
    return result
```

### 2. 業務邏輯異步層設計

#### 2.1 核心服務異步化
```python
class AsyncEmailProcessor:
    """異步郵件處理器"""
    
    def __init__(self):
        self.db_pool = AsyncDatabasePool()
        self.http_session = aiohttp.ClientSession()
        self.semaphore = asyncio.Semaphore(50)  # 並發控制
    
    async def process_email_batch(self, emails: List[dict]) -> List[dict]:
        """批量異步處理郵件"""
        tasks = []
        async with self.semaphore:
            for email in emails:
                task = asyncio.create_task(self._process_single_email(email))
                tasks.append(task)
        
        # 並發執行所有任務
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return self._handle_batch_results(results)
    
    async def _process_single_email(self, email: dict) -> dict:
        """單個郵件異步處理"""
        try:
            # I/O 異步處理
            attachments = await self._process_attachments_async(email['attachments'])
            
            # CPU 密集型任務交給進程池
            analysis_result = await self._analyze_content_parallel(email['content'])
            
            # 異步保存結果
            async with self.db_pool.acquire() as db:
                await db.save_email_result({
                    'email_id': email['id'],
                    'attachments': attachments,
                    'analysis': analysis_result
                })
            
            return {'status': 'success', 'email_id': email['id']}
            
        except Exception as e:
            return {'status': 'error', 'email_id': email['id'], 'error': str(e)}
```

#### 2.2 異步任務編排
```python
class AsyncTaskOrchestrator:
    """異步任務編排器"""
    
    def __init__(self):
        self.task_queue = asyncio.Queue(maxsize=1000)
        self.result_store = AsyncResultStore()
        self.workers = []
    
    async def submit_task(self, task_type: str, task_data: dict) -> str:
        """提交異步任務"""
        task_id = str(uuid.uuid4())
        task = AsyncTask(
            task_id=task_id,
            task_type=task_type,
            data=task_data,
            created_at=datetime.now()
        )
        
        await self.task_queue.put(task)
        return task_id
    
    async def start_workers(self, worker_count: int = 10):
        """啟動異步工作器"""
        for i in range(worker_count):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
    
    async def _worker(self, worker_name: str):
        """異步工作器"""
        while True:
            try:
                task = await self.task_queue.get()
                result = await self._execute_task(task)
                await self.result_store.save_result(task.task_id, result)
                self.task_queue.task_done()
            except Exception as e:
                await self.result_store.save_error(task.task_id, str(e))
```

### 3. I/O 異步適配層實現

#### 3.1 數據庫異步化
```python
class AsyncDatabaseAdapter:
    """異步數據庫適配器"""
    
    def __init__(self, db_url: str, pool_size: int = 20):
        self.pool = None
        self.db_url = db_url
        self.pool_size = pool_size
    
    async def initialize(self):
        """初始化連接池"""
        import aiosqlite
        self.pool = aiosqlite.connect(
            self.db_url,
            isolation_level=None,  # 自動提交模式
            check_same_thread=False
        )
    
    async def execute_query(self, query: str, params: tuple = None) -> List[dict]:
        """執行異步查詢"""
        async with self.pool as db:
            db.row_factory = aiosqlite.Row
            async with db.execute(query, params or ()) as cursor:
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
    
    async def execute_batch(self, queries: List[tuple]) -> List[int]:
        """批量執行異步操作"""
        results = []
        async with self.pool as db:
            for query, params in queries:
                cursor = await db.execute(query, params)
                results.append(cursor.rowcount)
            await db.commit()
        return results
```

#### 3.2 文件異步處理
```python
import aiofiles
import aiofiles.os

class AsyncFileProcessor:
    """異步文件處理器"""
    
    async def read_file_async(self, file_path: str) -> str:
        """異步讀取文件"""
        async with aiofiles.open(file_path, mode='r', encoding='utf-8') as f:
            content = await f.read()
        return content
    
    async def write_file_async(self, file_path: str, content: str):
        """異步寫入文件"""
        async with aiofiles.open(file_path, mode='w', encoding='utf-8') as f:
            await f.write(content)
    
    async def process_large_file_stream(self, file_path: str) -> AsyncGenerator[str, None]:
        """大文件流式異步處理"""
        async with aiofiles.open(file_path, mode='r', encoding='utf-8') as f:
            async for line in f:
                yield line.strip()
    
    async def parallel_file_processing(self, file_paths: List[str]) -> List[dict]:
        """並行處理多個文件"""
        semaphore = asyncio.Semaphore(20)  # 限制並發文件數
        
        async def process_single_file(file_path: str) -> dict:
            async with semaphore:
                try:
                    content = await self.read_file_async(file_path)
                    # 處理文件內容
                    processed_content = await self._process_content_async(content)
                    return {
                        'file_path': file_path,
                        'status': 'success',
                        'content_length': len(processed_content)
                    }
                except Exception as e:
                    return {
                        'file_path': file_path,
                        'status': 'error',
                        'error': str(e)
                    }
        
        tasks = [process_single_file(fp) for fp in file_paths]
        results = await asyncio.gather(*tasks)
        return results
```

#### 3.3 HTTP 客戶端異步化
```python
class AsyncHTTPClient:
    """異步 HTTP 客戶端"""
    
    def __init__(self):
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=30)
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def send_notification(self, message: str, webhook_url: str) -> dict:
        """異步發送通知"""
        payload = {'message': message}
        
        try:
            async with self.session.post(webhook_url, json=payload) as response:
                result = await response.json()
                return {
                    'status': 'success',
                    'response': result,
                    'status_code': response.status
                }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def batch_http_requests(self, requests: List[dict]) -> List[dict]:
        """批量異步 HTTP 請求"""
        semaphore = asyncio.Semaphore(10)  # 限制並發請求數
        
        async def single_request(req: dict) -> dict:
            async with semaphore:
                try:
                    async with self.session.request(
                        method=req['method'],
                        url=req['url'],
                        json=req.get('json'),
                        params=req.get('params')
                    ) as response:
                        data = await response.json()
                        return {
                            'request_id': req.get('id'),
                            'status': 'success',
                            'status_code': response.status,
                            'data': data
                        }
                except Exception as e:
                    return {
                        'request_id': req.get('id'),
                        'status': 'error',
                        'error': str(e)
                    }
        
        tasks = [single_request(req) for req in requests]
        results = await asyncio.gather(*tasks)
        return results
```

---

## 🔄 從同步到異步的遷移策略

### 遷移階段規劃

#### Phase 1: 基礎設施異步化 (Day 1)
1. **數據庫層異步改造**
   - 安裝 `aiosqlite` 或遷移到 `asyncpg`
   - 重寫所有數據庫操作方法
   - 實施連接池管理

2. **HTTP 客戶端異步化**
   - 替換 `requests` 為 `aiohttp`
   - 重構通知服務
   - 實施請求超時和重試機制

#### Phase 2: 業務邏輯異步化 (Day 2)
1. **核心服務異步化**
   - `UnifiedEmailProcessor` 異步改造
   - `EQCProcessingService` 異步化
   - 文件處理服務異步化

2. **API 端點異步化**
   - 所有 FastAPI 端點添加 `async` 關鍵字
   - 依賴注入異步化
   - 中間件異步化

#### Phase 3: 性能優化 (Day 3-4)
1. **並發控制優化**
   - 實施 Semaphore 限制
   - 配置合適的並發數量
   - 添加背壓控制

2. **錯誤處理強化**
   - 異步異常處理
   - 任務超時機制
   - 斷路器模式

### 具體遷移步驟

#### 步驟 1: 數據庫異步化
```python
# 遷移前
class EmailDatabase:
    def save_email(self, email_data: dict):
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT INTO emails ...", (email_data,))
            conn.commit()

# 遷移後
class AsyncEmailDatabase:
    async def save_email(self, email_data: dict):
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("INSERT INTO emails ...", (email_data,))
            await db.commit()
```

#### 步驟 2: 服務類異步化
```python
# 遷移模板
class AsyncService:
    def __init__(self):
        self.db = AsyncDatabase()
        self.http_client = AsyncHTTPClient()
        self.file_processor = AsyncFileProcessor()
    
    async def process_async(self, data: dict) -> dict:
        # 並發執行多個異步操作
        db_task = self.db.save_data(data)
        http_task = self.http_client.send_notification(data)
        file_task = self.file_processor.process_file(data['file_path'])
        
        # 等待所有操作完成
        db_result, http_result, file_result = await asyncio.gather(
            db_task, http_task, file_task
        )
        
        return {
            'db_result': db_result,
            'http_result': http_result,
            'file_result': file_result
        }
```

---

## ⚡ 事件循環和協程管理

### 事件循環配置
```python
class AsyncAppManager:
    """異步應用管理器"""
    
    def __init__(self):
        self.loop = None
        self.tasks = []
    
    def setup_event_loop(self):
        """配置事件循環"""
        # 使用 uvloop 提高性能 (Linux)
        try:
            import uvloop
            asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
        except ImportError:
            pass  # Windows 使用默認循環
        
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        # 配置事件循環參數
        self.loop.set_debug(False)  # 生產環境關閉 debug
        self.loop.slow_callback_duration = 0.1  # 慢回調閾值
    
    async def start_background_tasks(self):
        """啟動背景任務"""
        # 任務清理器
        cleanup_task = asyncio.create_task(self._periodic_cleanup())
        
        # 健康檢查
        health_check_task = asyncio.create_task(self._health_check())
        
        # 統計收集
        stats_task = asyncio.create_task(self._collect_stats())
        
        self.tasks.extend([cleanup_task, health_check_task, stats_task])
    
    async def shutdown_gracefully(self):
        """優雅關閉"""
        # 取消所有背景任務
        for task in self.tasks:
            task.cancel()
        
        # 等待任務完成或超時
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # 關閉事件循環
        self.loop.close()
```

### 協程池管理
```python
class CoroutinePool:
    """協程池管理器"""
    
    def __init__(self, max_concurrent: int = 100):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_tasks = set()
    
    async def submit_coroutine(self, coro):
        """提交協程到池中執行"""
        async with self.semaphore:
            task = asyncio.create_task(coro)
            self.active_tasks.add(task)
            
            try:
                result = await task
                return result
            finally:
                self.active_tasks.discard(task)
    
    async def wait_all_complete(self):
        """等待所有活動任務完成"""
        if self.active_tasks:
            await asyncio.gather(*self.active_tasks, return_exceptions=True)
    
    def get_active_count(self) -> int:
        """獲取活動任務數量"""
        return len(self.active_tasks)
```

---

## 🛡️ 錯誤處理和超時機制

### 異步異常處理框架
```python
class AsyncErrorHandler:
    """異步錯誤處理器"""
    
    def __init__(self):
        self.error_stats = {}
        self.circuit_breakers = {}
    
    async def handle_with_retry(self, coro, max_retries: int = 3, backoff_factor: float = 1.5):
        """帶重試的異步執行"""
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return await coro
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    wait_time = backoff_factor ** attempt
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise last_exception
    
    async def handle_with_timeout(self, coro, timeout_seconds: int):
        """帶超時的異步執行"""
        try:
            return await asyncio.wait_for(coro, timeout=timeout_seconds)
        except asyncio.TimeoutError:
            raise TimeoutError(f"操作超時 ({timeout_seconds}秒)")
    
    async def handle_with_circuit_breaker(self, coro, service_name: str):
        """帶斷路器的異步執行"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker(
                failure_threshold=5,
                timeout=60
            )
        
        breaker = self.circuit_breakers[service_name]
        
        if breaker.is_open():
            raise Exception(f"服務 {service_name} 斷路器已開啟")
        
        try:
            result = await coro
            breaker.record_success()
            return result
        except Exception as e:
            breaker.record_failure()
            raise e

class CircuitBreaker:
    """斷路器實現"""
    
    def __init__(self, failure_threshold: int, timeout: int):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'closed'  # closed, open, half-open
    
    def is_open(self) -> bool:
        if self.state == 'open':
            if time.time() - self.last_failure_time > self.timeout:
                self.state = 'half-open'
                return False
            return True
        return False
    
    def record_success(self):
        self.failure_count = 0
        self.state = 'closed'
    
    def record_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'open'
```

### 統一超時管理
```python
class TimeoutManager:
    """超時管理器"""
    
    # 超時配置
    TIMEOUTS = {
        'database_query': 30,
        'http_request': 60,
        'file_processing': 300,
        'email_processing': 600,
        'batch_operation': 1800
    }
    
    @classmethod
    async def with_timeout(cls, operation_type: str, coro):
        """根據操作類型應用超時"""
        timeout = cls.TIMEOUTS.get(operation_type, 60)
        
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            raise TimeoutError(
                f"{operation_type} 操作超時 (超時限制: {timeout}秒)"
            )
    
    @classmethod
    def get_timeout(cls, operation_type: str) -> int:
        """獲取操作超時時間"""
        return cls.TIMEOUTS.get(operation_type, 60)
```

---

## 📊 性能監控和異步指標

### 異步性能監控
```python
class AsyncPerformanceMonitor:
    """異步性能監控器"""
    
    def __init__(self):
        self.metrics = {
            'active_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'average_task_time': 0,
            'concurrent_peak': 0
        }
        self.task_times = []
    
    async def monitor_task(self, coro, task_name: str):
        """監控任務執行"""
        start_time = time.time()
        self.metrics['active_tasks'] += 1
        
        # 更新並發峰值
        if self.metrics['active_tasks'] > self.metrics['concurrent_peak']:
            self.metrics['concurrent_peak'] = self.metrics['active_tasks']
        
        try:
            result = await coro
            self.metrics['completed_tasks'] += 1
            
            # 記錄執行時間
            execution_time = time.time() - start_time
            self.task_times.append(execution_time)
            
            # 更新平均時間
            self.metrics['average_task_time'] = sum(self.task_times) / len(self.task_times)
            
            return result
            
        except Exception as e:
            self.metrics['failed_tasks'] += 1
            raise e
        finally:
            self.metrics['active_tasks'] -= 1
    
    def get_stats(self) -> dict:
        """獲取性能統計"""
        return {
            **self.metrics,
            'success_rate': (
                self.metrics['completed_tasks'] / 
                (self.metrics['completed_tasks'] + self.metrics['failed_tasks'])
                * 100 if (self.metrics['completed_tasks'] + self.metrics['failed_tasks']) > 0 else 0
            ),
            'tasks_per_second': self.metrics['completed_tasks'] / max(sum(self.task_times), 1)
        }
    
    async def start_monitoring(self):
        """啟動監控任務"""
        while True:
            await asyncio.sleep(60)  # 每分鐘輸出統計
            stats = self.get_stats()
            logger.info(f"異步性能統計: {stats}")
```

---

## 🔧 配置管理和環境設置

### 異步配置管理
```python
class AsyncConfig:
    """異步配置管理"""
    
    # 異步運行時配置
    ASYNC_SETTINGS = {
        'max_concurrent_requests': 100,
        'database_pool_size': 20,
        'http_connection_pool_size': 50,
        'task_timeout_seconds': 300,
        'file_processing_chunk_size': 1024 * 1024,  # 1MB
        'semaphore_limits': {
            'database_operations': 20,
            'file_operations': 30,
            'http_requests': 50,
            'email_processing': 10
        }
    }
    
    @classmethod
    def get_semaphore(cls, operation_type: str) -> asyncio.Semaphore:
        """獲取操作特定的信號量"""
        limit = cls.ASYNC_SETTINGS['semaphore_limits'].get(operation_type, 10)
        return asyncio.Semaphore(limit)
    
    @classmethod
    def get_pool_size(cls, pool_type: str) -> int:
        """獲取連接池大小"""
        if pool_type == 'database':
            return cls.ASYNC_SETTINGS['database_pool_size']
        elif pool_type == 'http':
            return cls.ASYNC_SETTINGS['http_connection_pool_size']
        return 10
```

---

## 📈 預期性能改進

### 性能提升目標
```yaml
並發處理能力:
  當前: 4-8 個並發請求
  目標: 100+ 個並發請求
  提升倍數: 12.5x - 25x

響應時間:
  當前: 2-5 秒平均響應
  目標: <1 秒平均響應
  改善: 50-80% 響應時間減少

資源利用率:
  CPU 使用率: 25-50% → 80%+
  記憶體效率: 提升 30-50%
  I/O 吞吐量: 提升 300-500%

系統穩定性:
  錯誤率: 2-3% → <0.5%
  可用性: 95% → 99.9%+
  恢復時間: 30-60秒 → <10秒
```

### 關鍵性能指標 (KPI)
1. **並發處理量**: >= 100 個同時請求
2. **平均響應時間**: <= 1 秒
3. **CPU 利用率**: >= 70%
4. **錯誤率**: <= 0.5%
5. **記憶體使用穩定性**: 24小時無洩漏

---

*📅 文檔版本: v1.0*  
*👤 架構師: Backend-Architect Agent*  
*🔄 最後更新: 2025-07-30*  
*⏱️ 預估實施時間: 48 小時*