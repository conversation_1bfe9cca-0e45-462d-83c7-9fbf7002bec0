"""
郵件過濾器
將白名單功能整合到郵件處理流程中
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from src.data_models.email_models import EmailData
from .whitelist import EmailWhitelistManager
from .models import WhitelistCheckResult
from .exceptions import EmailWhitelistError
from src.infrastructure.logging.logger_manager import LoggerManager


class EmailFilter:
    """
    郵件過濾器
    
    負責在郵件處理前進行白名單檢查和過濾
    """
    
    def __init__(self, whitelist_manager: Optional[EmailWhitelistManager] = None):
        """
        初始化郵件過濾器
        
        Args:
            whitelist_manager: 白名單管理器，如果未提供則建立新實例
        """
        self.logger = LoggerManager().get_logger("EmailFilter")
        
        # 初始化白名單管理器
        if whitelist_manager:
            self.whitelist_manager = whitelist_manager
        else:
            try:
                self.whitelist_manager = EmailWhitelistManager()
            except Exception as e:
                self.logger.error(f"初始化白名單管理器失敗: {e}")
                self.whitelist_manager = None
        
        # 過濾統計
        self.filter_stats = {
            'total_filtered': 0,
            'allowed_count': 0,
            'blocked_count': 0,
            'error_count': 0,
            'last_filter_time': None
        }
        
        self.logger.info("郵件過濾器已初始化")
    
    def filter_email(self, email_data: EmailData) -> Dict[str, Any]:
        """
        過濾單封郵件
        
        Args:
            email_data: 郵件數據
            
        Returns:
            Dict[str, Any]: 過濾結果，包含：
                - is_allowed: 是否允許處理
                - check_result: 白名單檢查結果
                - reason: 過濾原因
                - action: 採取的動作
        """
        self.filter_stats['total_filtered'] += 1
        self.filter_stats['last_filter_time'] = datetime.now()
        
        try:
            # 如果白名單管理器不可用，預設允許
            if not self.whitelist_manager:
                self.logger.warning("白名單管理器不可用，預設允許郵件")
                self.filter_stats['allowed_count'] += 1
                return {
                    'is_allowed': True,
                    'check_result': None,
                    'reason': '白名單管理器不可用，預設允許',
                    'action': 'allow_by_default'
                }
            
            # 執行白名單檢查
            check_result = self.whitelist_manager.check_email(email_data.sender)
            
            if check_result.is_whitelisted:
                self.filter_stats['allowed_count'] += 1
                action = 'allow_whitelisted'
                self.logger.info(f"郵件已通過白名單檢查: {email_data.sender} -> {email_data.subject}")
            else:
                self.filter_stats['blocked_count'] += 1
                action = 'block_not_whitelisted'
                self.logger.warning(f"郵件被白名單過濾: {email_data.sender} -> {email_data.subject}")
            
            return {
                'is_allowed': check_result.is_whitelisted,
                'check_result': check_result,
                'reason': check_result.reason,
                'action': action
            }
            
        except EmailWhitelistError as e:
            self.filter_stats['error_count'] += 1
            self.logger.error(f"白名單檢查失敗: {e}")
            
            # 白名單檢查失敗時的預設行為（從環境變數讀取）
            import os
            default_on_error = os.getenv('EMAIL_WHITELIST_DEFAULT_ON_ERROR', 'allow').lower() == 'allow'
            
            if default_on_error:
                self.filter_stats['allowed_count'] += 1
                action = 'allow_on_error'
            else:
                self.filter_stats['blocked_count'] += 1
                action = 'block_on_error'
            
            return {
                'is_allowed': default_on_error,
                'check_result': None,
                'reason': f'白名單檢查失敗: {str(e)}',
                'action': action
            }
        
        except Exception as e:
            self.filter_stats['error_count'] += 1
            self.logger.error(f"郵件過濾發生未預期錯誤: {e}")
            
            # 發生未預期錯誤時預設允許
            self.filter_stats['allowed_count'] += 1
            return {
                'is_allowed': True,
                'check_result': None,
                'reason': f'過濾器錯誤，預設允許: {str(e)}',
                'action': 'allow_on_unexpected_error'
            }
    
    def filter_emails(self, emails: List[EmailData]) -> Dict[str, Any]:
        """
        批次過濾郵件
        
        Args:
            emails: 郵件列表
            
        Returns:
            Dict[str, Any]: 批次過濾結果，包含：
                - allowed_emails: 允許的郵件列表
                - blocked_emails: 被阻止的郵件列表
                - filter_results: 每封郵件的過濾結果
                - summary: 過濾摘要
        """
        start_time = datetime.now()
        self.logger.info(f"開始批次過濾 {len(emails)} 封郵件")
        
        allowed_emails = []
        blocked_emails = []
        filter_results = []
        
        for email_data in emails:
            try:
                filter_result = self.filter_email(email_data)
                
                # 記錄過濾結果
                filter_results.append({
                    'email_id': email_data.message_id,
                    'sender': email_data.sender,
                    'subject': email_data.subject,
                    'filter_result': filter_result
                })
                
                # 分類郵件
                if filter_result['is_allowed']:
                    allowed_emails.append(email_data)
                else:
                    blocked_emails.append(email_data)
                    
            except Exception as e:
                self.logger.error(f"過濾郵件時發生錯誤 {email_data.message_id}: {e}")
                # 發生錯誤時預設允許
                allowed_emails.append(email_data)
                filter_results.append({
                    'email_id': email_data.message_id,
                    'sender': email_data.sender,
                    'subject': email_data.subject,
                    'filter_result': {
                        'is_allowed': True,
                        'check_result': None,
                        'reason': f'過濾失敗，預設允許: {str(e)}',
                        'action': 'allow_on_filter_error'
                    }
                })
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        summary = {
            'total_emails': len(emails),
            'allowed_count': len(allowed_emails),
            'blocked_count': len(blocked_emails),
            'processing_time': processing_time,
            'filter_rate': len(blocked_emails) / len(emails) if emails else 0
        }
        
        self.logger.info(f"批次過濾完成: {summary['allowed_count']} 允許, "
                        f"{summary['blocked_count']} 阻止, 耗時 {processing_time:.2f}s")
        
        return {
            'allowed_emails': allowed_emails,
            'blocked_emails': blocked_emails,
            'filter_results': filter_results,
            'summary': summary
        }
    
    def get_filter_stats(self) -> Dict[str, Any]:
        """
        取得過濾器統計資訊
        
        Returns:
            Dict[str, Any]: 統計資訊
        """
        stats = self.filter_stats.copy()
        
        # 添加白名單管理器統計
        if self.whitelist_manager:
            whitelist_stats = self.whitelist_manager.get_stats()
            stats['whitelist_stats'] = whitelist_stats
        
        # 計算過濾率
        if stats['total_filtered'] > 0:
            stats['allow_rate'] = stats['allowed_count'] / stats['total_filtered']
            stats['block_rate'] = stats['blocked_count'] / stats['total_filtered']
            stats['error_rate'] = stats['error_count'] / stats['total_filtered']
        else:
            stats['allow_rate'] = 0
            stats['block_rate'] = 0
            stats['error_rate'] = 0
        
        return stats
    
    def reset_stats(self) -> None:
        """重置過濾統計"""
        self.filter_stats = {
            'total_filtered': 0,
            'allowed_count': 0,
            'blocked_count': 0,
            'error_count': 0,
            'last_filter_time': None
        }
        self.logger.info("過濾器統計已重置")
    
    def is_whitelist_enabled(self) -> bool:
        """
        檢查白名單功能是否啟用
        
        Returns:
            bool: 是否啟用
        """
        return self.whitelist_manager is not None and self.whitelist_manager.enabled
    
    def reload_whitelist(self) -> bool:
        """
        重新載入白名單
        
        Returns:
            bool: 是否成功
        """
        if not self.whitelist_manager:
            return False
        
        try:
            success = self.whitelist_manager.load_whitelist(force_reload=True)
            if success:
                self.logger.info("白名單已重新載入")
            return success
        except Exception as e:
            self.logger.error(f"重新載入白名單失敗: {e}")
            return False