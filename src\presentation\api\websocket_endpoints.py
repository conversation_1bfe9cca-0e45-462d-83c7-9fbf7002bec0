"""
WebSocket 端點實現
整合異步任務管理系統和即時狀態推送
"""

import json
import time
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import WebSocket, WebSocketDisconnect, HTTPException
from fastapi.routing import APIRouter
import logging

# 導入任務管理器
from src.services.concurrent_task_manager_enhanced import get_enhanced_task_manager
from src.infrastructure.logging.logger_manager import LoggerManager

# 導入即時任務處理器
try:
    from .realtime_task_handlers import register_realtime_handlers
except ImportError:
    register_realtime_handlers = None

# 路由器
websocket_router = APIRouter()
logger = LoggerManager().get_logger("WebSocketEndpoints")

class WebSocketConnectionManager:
    """WebSocket 連接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.client_subscriptions: Dict[str, List[str]] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str):
        """建立 WebSocket 連接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.client_subscriptions[client_id] = []
        self.connection_metadata[client_id] = {
            'connected_at': datetime.now(),
            'last_activity': datetime.now(),
            'message_count': 0
        }
        
        logger.info(f"WebSocket 連接已建立: {client_id}")
        
        # 發送歡迎消息
        await self.send_personal_message(client_id, {
            'type': 'welcome',
            'payload': {
                'client_id': client_id,
                'server_time': datetime.now().isoformat(),
                'available_subscriptions': [
                    'task_status',
                    'system_metrics',
                    'process_progress',
                    'alerts'
                ]
            }
        })
        
    def disconnect(self, client_id: str):
        """斷開 WebSocket 連接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.client_subscriptions:
            del self.client_subscriptions[client_id]
        if client_id in self.connection_metadata:
            del self.connection_metadata[client_id]
            
        logger.info(f"WebSocket 連接已斷開: {client_id}")
        
    async def send_personal_message(self, client_id: str, message: Dict[str, Any]):
        """發送個人消息"""
        if client_id not in self.active_connections:
            return False
            
        try:
            websocket = self.active_connections[client_id]
            message['timestamp'] = datetime.now().isoformat()
            await websocket.send_text(json.dumps(message))
            
            # 更新元數據
            if client_id in self.connection_metadata:
                self.connection_metadata[client_id]['last_activity'] = datetime.now()
                self.connection_metadata[client_id]['message_count'] += 1
                
            return True
        except Exception as e:
            logger.error(f"發送消息失敗 {client_id}: {e}")
            self.disconnect(client_id)
            return False
            
    async def broadcast_message(self, message: Dict[str, Any], subscription_type: str = None):
        """廣播消息"""
        if not self.active_connections:
            return
            
        message['timestamp'] = datetime.now().isoformat()
        message_text = json.dumps(message)
        
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            # 檢查訂閱
            if subscription_type and subscription_type not in self.client_subscriptions.get(client_id, []):
                continue
                
            try:
                await websocket.send_text(message_text)
                
                # 更新元數據
                if client_id in self.connection_metadata:
                    self.connection_metadata[client_id]['last_activity'] = datetime.now()
                    self.connection_metadata[client_id]['message_count'] += 1
                    
            except Exception as e:
                logger.error(f"廣播消息失敗 {client_id}: {e}")
                disconnected_clients.append(client_id)
                
        # 清理斷開的連接
        for client_id in disconnected_clients:
            self.disconnect(client_id)
            
    def subscribe_client(self, client_id: str, topics: List[str]):
        """訂閱主題"""
        if client_id not in self.client_subscriptions:
            return
            
        current_subscriptions = set(self.client_subscriptions[client_id])
        new_subscriptions = set(topics)
        self.client_subscriptions[client_id] = list(current_subscriptions.union(new_subscriptions))
        
        logger.info(f"客戶端 {client_id} 訂閱主題: {topics}")
        
    def unsubscribe_client(self, client_id: str, topics: List[str]):
        """取消訂閱主題"""
        if client_id not in self.client_subscriptions:
            return
            
        current_subscriptions = set(self.client_subscriptions[client_id])
        remove_subscriptions = set(topics)
        self.client_subscriptions[client_id] = list(current_subscriptions - remove_subscriptions)
        
        logger.info(f"客戶端 {client_id} 取消訂閱主題: {topics}")
        
    def get_connection_stats(self) -> Dict[str, Any]:
        """獲取連接統計"""
        return {
            'total_connections': len(self.active_connections),
            'connected_clients': list(self.active_connections.keys()),
            'connection_metadata': self.connection_metadata
        }

# 全局連接管理器實例
connection_manager = WebSocketConnectionManager()

@websocket_router.websocket("/ws/realtime/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket 主端點"""
    await connection_manager.connect(websocket, client_id)
    
    try:
        while True:
            # 接收客戶端消息
            data = await websocket.receive_text()
            await handle_websocket_message(client_id, data)
            
    except WebSocketDisconnect:
        connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket 錯誤 {client_id}: {e}")
        connection_manager.disconnect(client_id)

async def handle_websocket_message(client_id: str, message: str):
    """處理 WebSocket 消息"""
    try:
        data = json.loads(message)
        message_type = data.get('type')
        payload = data.get('payload', {})
        
        logger.debug(f"收到消息 {client_id}: {message_type}")
        
        if message_type == 'subscribe':
            await handle_subscribe(client_id, payload)
        elif message_type == 'unsubscribe':
            await handle_unsubscribe(client_id, payload)
        elif message_type == 'start_task':
            await handle_start_task(client_id, payload)
        elif message_type == 'cancel_task':
            await handle_cancel_task(client_id, payload)
        elif message_type == 'get_task_status':
            await handle_get_task_status(client_id, payload)
        elif message_type == 'ping':
            await handle_ping(client_id, payload)
        else:
            await connection_manager.send_personal_message(client_id, {
                'type': 'error',
                'payload': {
                    'message': f'未知消息類型: {message_type}'
                }
            })
            
    except json.JSONDecodeError:
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'payload': {
                'message': '無效的 JSON 格式'
            }
        })
    except Exception as e:
        logger.error(f"處理消息錯誤 {client_id}: {e}")
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'payload': {
                'message': f'服務器錯誤: {str(e)}'
            }
        })

async def handle_subscribe(client_id: str, payload: Dict[str, Any]):
    """處理訂閱請求"""
    topics = payload.get('topics', [])
    connection_manager.subscribe_client(client_id, topics)
    
    await connection_manager.send_personal_message(client_id, {
        'type': 'subscription_confirmed',
        'payload': {
            'subscribed_topics': topics,
            'current_subscriptions': connection_manager.client_subscriptions.get(client_id, [])
        }
    })

async def handle_unsubscribe(client_id: str, payload: Dict[str, Any]):
    """處理取消訂閱請求"""
    topics = payload.get('topics', [])
    connection_manager.unsubscribe_client(client_id, topics)
    
    await connection_manager.send_personal_message(client_id, {
        'type': 'unsubscription_confirmed',
        'payload': {
            'unsubscribed_topics': topics,
            'current_subscriptions': connection_manager.client_subscriptions.get(client_id, [])
        }
    })

async def handle_start_task(client_id: str, payload: Dict[str, Any]):
    """處理啟動任務請求"""
    task_type = payload.get('task_type')
    task_params = payload.get('task_params', {})
    
    if not task_type:
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'payload': {'message': '缺少任務類型'}
        })
        return
    
    try:
        # 獲取任務管理器
        task_manager = get_enhanced_task_manager()
        
        # 提交任務
        task_id = await task_manager.submit_async_task(
            task_type=task_type,
            task_params=task_params
        )
        
        # 發送任務啟動確認
        await connection_manager.send_personal_message(client_id, {
            'type': 'task_started',
            'payload': {
                'task_id': task_id,
                'task_type': task_type,
                'message': '任務已成功啟動'
            }
        })
        
        # 廣播任務狀態更新
        await connection_manager.broadcast_message({
            'type': 'task_status_update',
            'payload': {
                'task_id': task_id,
                'task_type': task_type,
                'status': 'PENDING',
                'progress': 0,
                'started_by': client_id
            }
        }, 'task_status')
        
    except Exception as e:
        logger.error(f"啟動任務失敗: {e}")
        await connection_manager.send_personal_message(client_id, {
            'type': 'task_start_failed',
            'payload': {
                'task_type': task_type,
                'error': str(e)
            }
        })

async def handle_cancel_task(client_id: str, payload: Dict[str, Any]):
    """處理取消任務請求"""
    task_id = payload.get('task_id')
    
    if not task_id:
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'payload': {'message': '缺少任務 ID'}
        })
        return
    
    try:
        # 獲取任務管理器
        task_manager = get_enhanced_task_manager()
        
        # 取消任務
        success = task_manager.cancel_task(task_id)
        
        if success:
            await connection_manager.send_personal_message(client_id, {
                'type': 'task_cancelled',
                'payload': {
                    'task_id': task_id,
                    'message': '任務已成功取消'
                }
            })
            
            # 廣播任務狀態更新
            await connection_manager.broadcast_message({
                'type': 'task_status_update',
                'payload': {
                    'task_id': task_id,
                    'status': 'CANCELLED',
                    'cancelled_by': client_id
                }
            }, 'task_status')
        else:
            await connection_manager.send_personal_message(client_id, {
                'type': 'task_cancel_failed',
                'payload': {
                    'task_id': task_id,
                    'message': '無法取消任務（可能已完成或不存在）'
                }
            })
            
    except Exception as e:
        logger.error(f"取消任務失敗: {e}")
        await connection_manager.send_personal_message(client_id, {
            'type': 'task_cancel_failed',
            'payload': {
                'task_id': task_id,
                'error': str(e)
            }
        })

async def handle_get_task_status(client_id: str, payload: Dict[str, Any]):
    """處理獲取任務狀態請求"""
    task_id = payload.get('task_id')
    
    if not task_id:
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'payload': {'message': '缺少任務 ID'}
        })
        return
    
    try:
        # 獲取任務管理器
        task_manager = get_enhanced_task_manager()
        
        # 獲取任務狀態
        task_status = task_manager.get_task_status(task_id)
        
        if task_status:
            await connection_manager.send_personal_message(client_id, {
                'type': 'task_status_response',
                'payload': {
                    'task_id': task_id,
                    'status': task_status
                }
            })
        else:
            await connection_manager.send_personal_message(client_id, {
                'type': 'task_not_found',
                'payload': {
                    'task_id': task_id,
                    'message': '找不到指定的任務'
                }
            })
            
    except Exception as e:
        logger.error(f"獲取任務狀態失敗: {e}")
        await connection_manager.send_personal_message(client_id, {
            'type': 'error',
            'payload': {
                'task_id': task_id,
                'error': str(e)
            }
        })

async def handle_ping(client_id: str, payload: Dict[str, Any]):
    """處理心跳請求"""
    client_timestamp = payload.get('timestamp')
    
    await connection_manager.send_personal_message(client_id, {
        'type': 'pong',
        'payload': {
            'client_timestamp': client_timestamp,
            'server_timestamp': int(time.time() * 1000),
            'connection_info': connection_manager.connection_metadata.get(client_id, {})
        }
    })

# 系統指標推送任務
async def system_metrics_broadcaster():
    """系統指標廣播任務"""
    import psutil
    
    while True:
        try:
            # 收集系統指標
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 獲取任務管理器統計
            task_manager = get_enhanced_task_manager()
            task_stats = task_manager.get_statistics()
            
            # 構造指標數據
            metrics = {
                'cpu_percent': round(cpu_percent, 1),
                'memory_percent': round(memory.percent, 1),
                'disk_percent': round((disk.used / disk.total) * 100, 1),
                'memory_available_mb': round(memory.available / (1024 * 1024), 1),
                'disk_free_gb': round(disk.free / (1024**3), 1),
                'active_tasks': task_stats.get('running_tasks', 0),
                'total_tasks': task_stats.get('total_tasks', 0),
                'completed_tasks': task_stats.get('completed_tasks', 0),
                'failed_tasks': task_stats.get('failed_tasks', 0),
                'success_rate': round(task_stats.get('success_rate', 0), 1),
                'websocket_connections': len(connection_manager.active_connections)
            }
            
            # 計算簡單的錯誤率和響應時間（模擬）
            metrics['error_rate'] = round(100 - metrics['success_rate'], 1)
            metrics['avg_response_time'] = round(50 + (cpu_percent * 2), 0)  # 模擬響應時間
            
            # 廣播系統指標
            await connection_manager.broadcast_message({
                'type': 'system_metrics_update',
                'payload': metrics
            }, 'system_metrics')
            
            # 等待30秒後再次更新
            await asyncio.sleep(30)
            
        except Exception as e:
            logger.error(f"系統指標廣播錯誤: {e}")
            await asyncio.sleep(30)

# WebSocket 連接統計端點
@websocket_router.get("/ws/stats")
async def get_websocket_stats():
    """獲取 WebSocket 連接統計"""
    return connection_manager.get_connection_stats()

# 手動觸發系統指標更新
@websocket_router.post("/ws/broadcast/system-metrics")
async def broadcast_system_metrics():
    """手動觸發系統指標廣播"""
    try:
        # 這裡可以觸發一次性的系統指標廣播
        await system_metrics_broadcaster.__wrapped__()  # 調用一次廣播函數
        return {"status": "success", "message": "系統指標已廣播"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 發送自定義告警
@websocket_router.post("/ws/broadcast/alert")
async def broadcast_alert(alert_data: Dict[str, Any]):
    """發送自定義告警"""
    try:
        await connection_manager.broadcast_message({
            'type': 'alert',
            'payload': alert_data
        }, 'alerts')
        return {"status": "success", "message": "告警已發送"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 啟動背景任務
def start_background_tasks():
    """啟動背景任務"""
    # 這個函數應該在應用啟動時調用
    asyncio.create_task(system_metrics_broadcaster())
    
    # 註冊即時任務處理器
    if register_realtime_handlers:
        register_realtime_handlers()
        logger.info("即時任務處理器已註冊")
    
    logger.info("WebSocket 背景任務已啟動")

# 任務狀態變更回調
def task_status_callback(task_id: str, status_data: Dict[str, Any]):
    """任務狀態變更回調函數"""
    # 這個函數可以被任務管理器調用來推送狀態更新
    asyncio.create_task(
        connection_manager.broadcast_message({
            'type': 'task_status_update',
            'payload': {
                'task_id': task_id,
                **status_data
            }
        }, 'task_status')
    )

# 進程進度更新回調
def process_progress_callback(process_id: str, progress_data: Dict[str, Any]):
    """進程進度更新回調函數"""
    asyncio.create_task(
        connection_manager.broadcast_message({
            'type': 'process_progress_update',
            'payload': {
                'process_id': process_id,
                **progress_data
            }
        }, 'process_progress')
    )