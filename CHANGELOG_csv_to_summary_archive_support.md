# CSV to Summary 壓縮檔支援功能更新

## 更新日期
2025-07-30

## 更新概述
為 `csv_to_summary.py` 添加了壓縮檔輸入支援功能，使其能夠直接處理壓縮檔案，與 `code_comparison.py` 的壓縮檔處理邏輯保持一致。

## 主要變更

### 1. 新增功能
- **壓縮檔自動偵測**：支援多種壓縮格式的自動識別
- **自動解壓縮**：自動解壓縮到同目錄下的同名資料夾
- **遞迴解壓縮**：掃描並解壓縮資料夾內的其他壓縮檔
- **SPD 檔案轉換**：自動將 .spd 檔案重命名為 .csv 格式
- **清理功能**：處理完成後自動刪除原始壓縮檔

### 2. 支援的壓縮格式
- **ZIP** (.zip) - 標準 ZIP 壓縮檔
- **7Z** (.7z) - 7-Zip 壓縮檔（需要 py7zr 套件）
- **RAR** (.rar) - WinRAR 壓縮檔（需要 rarfile 套件）
- **TAR** (.tar, .tar.gz, .tgz, .tar.bz2) - TAR 壓縮檔
- **GZIP** (.gz) - GZIP 壓縮檔
- **BZIP2** (.bz2) - BZIP2 壓縮檔

### 3. 程式碼變更

#### 新增導入
```python
# 新增 imports - 解壓縮功能
try:
    from src.infrastructure.adapters.excel.cta.cta_integrated_processor import extract_compressed_files
except ImportError as e:
    print(f"[WARNING] 警告: 無法匯入解壓縮模組: {e}")
    print("解壓縮功能將無法使用")
    extract_compressed_files = None

# 新增 imports - SPD 轉換功能
try:
    from src.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor import FileConverter
except ImportError as e:
    print(f"[WARNING] 警告: 無法匯入 SPD 轉換模組: {e}")
    print("SPD 轉換功能將無法使用")
    FileConverter = None
```

#### 新增函數
```python
def is_archive_file(path: str) -> bool:
    """判斷是否為支援的壓縮檔格式"""
    supported = ['.zip', '.7z', '.rar', '.tar', '.gz', '.bz2', '.tgz']
    path_obj = Path(path)
    
    # 檢查複合副檔名（如 .tar.gz, .tar.bz2）
    if path_obj.name.endswith('.tar.gz') or path_obj.name.endswith('.tar.bz2'):
        return True
    
    # 檢查單一副檔名
    return path_obj.suffix.lower() in supported
```

#### 主要邏輯修改
在 `main()` 函數中新增壓縮檔處理邏輯：
1. 判斷輸入是壓縮檔還是資料夾
2. 如果是壓縮檔，執行解壓縮流程
3. 遞迴掃描並解壓縮資料夾內的壓縮檔
4. **SPD 檔案轉換**：掃描並轉換所有 .spd 檔案為 .csv
5. 繼續原有的 CSV 處理流程

### 4. 使用方式更新

#### 新的使用範例
```bash
# 處理 ZIP 壓縮檔
python3 csv_to_summary.py data.zip --excel

# 處理 7Z 壓縮檔
python3 csv_to_summary.py archive.7z --verbose

# 處理 RAR 壓縮檔
python3 csv_to_summary.py files.rar --excel --verbose

# 處理 TAR 壓縮檔
python3 csv_to_summary.py backup.tar.gz
```

#### 處理流程
1. 自動偵測壓縮檔格式
2. 解壓縮到同目錄下的同名資料夾
3. 遞迴掃描並解壓縮資料夾內的其他壓縮檔
4. 刪除原始壓縮檔
5. **SPD 檔案轉換**：自動將 .spd 檔案重命名為 .csv
6. 執行 CSV 處理流程

### 5. SPD 檔案轉換功能

新增的 SPD 轉換功能具有以下特性：

- **自動偵測**：遞迴掃描所有子資料夾中的 .spd 檔案
- **不區分大小寫**：支援 .spd, .SPD, .Spd 等各種格式
- **保留檔名**：只改變副檔名，檔案內容和名稱保持不變
- **無縫整合**：在解壓縮完成後自動執行，無需額外操作
- **錯誤處理**：轉換失敗時顯示警告但不影響主流程

**轉換邏輯：**
```python
# === 新增：SPD 轉換 ===
if FileConverter is not None:
    print("\n[EDIT] 掃描並轉換 SPD 檔案為 CSV...")
    converted_count = FileConverter.convert_spd_files_to_csv(folder_path)
    if converted_count > 0:
        print(f"[OK] 成功轉換 {converted_count} 個 SPD 檔案")
    else:
        print("[INFO] 未發現需要轉換的 SPD 檔案")
```

### 6. 文檔更新
- 更新了 `MD/README_csv_to_summary.md`
- 新增壓縮檔支援說明
- 新增使用範例
- 更新參數說明

### 6. 相容性
- **向後相容**：原有的資料夾輸入方式完全不受影響
- **可選依賴**：壓縮檔功能為可選，缺少相關套件時會顯示警告但不影響基本功能
- **錯誤處理**：完善的錯誤處理機制，確保程式穩定性

### 7. 依賴套件
新增可選依賴套件：
```bash
pip install rarfile py7zr  # 支援 RAR 和 7Z 格式
```

## 測試驗證
- 通過了壓縮檔格式偵測測試
- 驗證了命令列參數解析
- 確認了向後相容性

## 注意事項
1. RAR 和 7Z 格式需要額外安裝對應的 Python 套件
2. 處理大型壓縮檔時可能需要較長時間
3. 確保有足夠的磁碟空間進行解壓縮操作
4. 原始壓縮檔會在處理完成後被自動刪除

## 相關檔案
- `csv_to_summary.py` - 主程式檔案
- `MD/README_csv_to_summary.md` - 使用說明文檔
- `src/infrastructure/adapters/excel/cta/cta_integrated_processor.py` - 解壓縮功能模組

---
**作者**: AI Assistant  
**版本**: v1.1.0  
**更新日期**: 2025-07-30
