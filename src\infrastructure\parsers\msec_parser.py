"""
MSEC (MSECZD) 廠商解析器實作
基於 VBA MSECZDInfoFromStrings 邏輯

VBA 邏輯參考：
- 識別條件：InStr(LCase(subject), "low yield hold") Or InStr(body, "微矽")
- 解析規則：正則匹配 (G|M|AT)\d 產品代碼，產品代碼後第1個詞為MO，第2個詞為LOT
- 範例：FW: Low yield hold lot G2566CQ71D-C(DD-CTAXP) PGM24100005 GHMT46.16
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


class MSECParser(VendorParser):
    """
    MSEC (MSECZD) 廠商郵件解析器
    
    識別條件：
    - 郵件主旨包含 "low yield hold"
    - 郵件內文包含 "微矽"
    
    解析機制：
    1. 使用正則表達式 \b(G|M|AT)\d 尋找產品代碼
    2. 產品代碼後的第一個詞作為 MO 號碼
    3. 產品代碼後的第二個詞作為 LOT 號碼
    """
    
    def __init__(self):
        """初始化 MSEC 解析器"""
        super().__init__()
        self._vendor_code = "MSEC"
        self._vendor_name = "MSEC"
        self._identification_patterns = [
            "low yield hold",    # 主旨關鍵字
            "微矽"              # 內文關鍵字
        ]
        self.set_confidence_threshold(0.8)
        
        # 初始化 logger
        self.logger = LoggerManager().get_logger("MSECParser")
        
        # MSEC 特有的正則模式
        self.product_pattern = r'\b(G|M|AT)\d+'  # 產品代碼：G/M/AT 開頭 + 數字

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        # VBA: InStr(1, LCase(subject), "low yield hold", vbTextCompare) > 0
        if "low yield hold" in subject.lower():
            matched_patterns.append("low yield hold")
            confidence_score += 0.8  # 提高分數以達到閾值

        # 檢查內文是否包含 "微矽"
        if "微矽" in body:
            matched_patterns.append("微矽")
            confidence_score += 0.8
        
        # 額外的信心分數計算
        # 檢查是否有產品代碼模式
        if re.search(self.product_pattern, subject, re.IGNORECASE):
            confidence_score += 0.2
            
        # 檢查是否有典型的 MSEC 格式（產品代碼 + 兩個後續詞）
        words = subject.split()
        for i, word in enumerate(words):
            if re.search(self.product_pattern, word, re.IGNORECASE):
                # 檢查後面是否有至少兩個詞（MO + LOT）
                if i + 2 < len(words):
                    confidence_score += 0.3
                    break
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="msec_keyword_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        if not email_data.subject:
            # 空主旨時嘗試從識別結果判斷
            identification = self.identify_vendor(email_data)
            if not identification.is_identified:
                raise ParsingError("Empty subject and cannot identify vendor", vendor_code=self.vendor_code)
        
        try:
            self.logger.info(f"開始 MSEC 解析...")
            self.logger.info(f"   主旨: {email_data.subject}")
            
            # 使用 MSEC 關鍵字解析機制
            msec_result = self.parse_msec_keywords(email_data.subject or "")
            
            self.logger.info(f"MSEC 解析結果:")
            self.logger.info(f"   產品代碼: {msec_result['product']}")
            self.logger.info(f"   MO編號: {msec_result['mo_number']}")
            self.logger.info(f"   LOT編號: {msec_result['lot_number']}")
            self.logger.info(f"   解析方法: {msec_result['method']}")
            
            # MO 編號保持原始格式
            mo_number = msec_result["mo_number"]
            if not mo_number or mo_number == "":
                mo_number = None
            
            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=msec_result["product"] if msec_result["product"] != "" else None,
                mo_number=mo_number,
                lot_number=msec_result["lot_number"] if msec_result["lot_number"] != "" else None,
                is_success=True,
                error_message=None,
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': msec_result["product"],
                    'lot_number': msec_result["lot_number"],
                    'mo_number': msec_result["mo_number"],
                    'parsing_method': msec_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'msec_word_position_parsing'
                }
            )
            
            self.logger.info(f"MSEC 解析成功")
            return result
            
        except Exception as e:
            self.logger.error(f"MSEC 解析失敗: {e}")
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=None,
                mo_number=None,
                lot_number=None,
                is_success=False,
                error_message=f"MSEC parsing failed: {str(e)}",
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_msec_keywords(self, subject: str) -> Dict[str, Any]:
        """
        解析 MSEC 關鍵字：產品代碼、MO、LOT
        支援多種格式：空格分隔、連續格式、混合格式
        
        支援格式：
        1. 標準格式：G2225RV1UP GM24120084 DN7K9.1C
        2. 連續格式：G2225RV1UPGM24120084DN7K9.1C
        3. 混合格式：G2225RV1UPGM24120084 DN7K9.1C
        """
        if not subject:
            return {
                "product": "",
                "mo_number": "",
                "lot_number": "",
                "method": "no_pattern"
            }
        
        self.logger.debug(f"開始解析 MSEC 主旨: {subject}")
        
        # 嘗試空格分隔格式（原有邏輯）
        space_result = self._parse_space_separated_format(subject)
        if space_result["product"] and space_result["mo_number"] and space_result["lot_number"]:
            self.logger.info(f"使用空格分隔格式成功解析")
            return space_result
        
        # 嘗試連續格式解析
        continuous_result = self._parse_continuous_format(subject)
        if continuous_result["product"] and continuous_result["mo_number"] and continuous_result["lot_number"]:
            self.logger.info(f"使用連續格式成功解析")
            return continuous_result
        
        # 嘗試混合格式解析
        mixed_result = self._parse_mixed_format(subject)
        if mixed_result["product"] and mixed_result["mo_number"] and mixed_result["lot_number"]:
            self.logger.info(f"使用混合格式成功解析")
            return mixed_result
        
        # 如果所有格式都失敗，返回最佳嘗試結果
        self.logger.warning(f"所有格式解析均未完全成功，返回最佳結果")
        return space_result if space_result["product"] else continuous_result

    def _parse_space_separated_format(self, subject: str) -> Dict[str, Any]:
        """解析空格分隔格式 (原有邏輯)"""
        product = ""
        mo_number = ""
        lot_number = ""
        method = "space_separated"
        
        # 分割主旨為單詞
        words = subject.split()
        
        # 1. 尋找產品代碼 (G/M/AT 開頭 + 數字)
        product_index = -1
        priority_prefixes = ['G', 'M', 'AT']
        
        for prefix in priority_prefixes:
            for i, word in enumerate(words):
                # 匹配產品代碼模式
                if re.search(rf'^{prefix}\d+', word, re.IGNORECASE):
                    # 處理複雜的產品代碼格式，只取第一個括號結束前的部分
                    if '(' in word and ')' in word:
                        first_close = word.find(')')
                        if first_close != -1:
                            product = word[:first_close + 1].upper()
                        else:
                            product = word.upper()
                    else:
                        product = word.upper()
                    
                    product_index = i
                    method = f"space_separated_{prefix.lower()}"
                    break
            
            if product_index != -1:
                break
        
        # 2. 提取 MO 和 LOT（產品代碼後的第1、2個詞）
        if product_index != -1 and product_index + 2 < len(words):
            mo_number = words[product_index + 1]
            lot_number = words[product_index + 2]
        
        self.logger.debug(f"空格分隔解析結果: product={product}, mo={mo_number}, lot={lot_number}")
        
        return {
            "product": product,
            "mo_number": mo_number,
            "lot_number": lot_number,
            "method": method
        }

    def _parse_continuous_format(self, subject: str) -> Dict[str, Any]:
        """解析連續格式，如 G2225RV1UPGM24120084DN7K9.1C"""
        product = ""
        mo_number = ""
        lot_number = ""
        method = "continuous"
        
        # 定義精確的正則表達式模式
        product_pattern = r'(G\w{8,10})'  # 產品代碼：G + 8-10個字母/數字
        mo_pattern = r'(GM\d{8})'         # MO編號：GM + 8位數字
        lot_pattern = r'([A-Z0-9\.]+)'    # LOT編號：大寫字母、數字、點
        
        # 嘗試匹配連續格式：產品代碼+MO編號+LOT編號
        continuous_pattern = f'{product_pattern}{mo_pattern}{lot_pattern}'
        match = re.search(continuous_pattern, subject, re.IGNORECASE)
        
        if match:
            product = match.group(1).upper()
            mo_number = match.group(2).upper()
            lot_number = match.group(3).upper()
            method = "continuous_full_match"
            self.logger.debug(f"連續格式完整匹配成功")
        else:
            # 嘗試部分匹配：尋找產品代碼+MO編號的組合
            partial_pattern = f'{product_pattern}{mo_pattern}'
            partial_match = re.search(partial_pattern, subject, re.IGNORECASE)
            
            if partial_match:
                product = partial_match.group(1).upper()
                mo_number = partial_match.group(2).upper()
                method = "continuous_partial_match"
                
                # 在剩餘文字中尋找LOT編號
                remaining_text = subject[partial_match.end():]
                lot_match = re.search(r'\b([A-Z0-9\.]+)', remaining_text, re.IGNORECASE)
                if lot_match:
                    lot_number = lot_match.group(1).upper()
                    
                self.logger.debug(f"連續格式部分匹配成功")
        
        self.logger.debug(f"連續格式解析結果: product={product}, mo={mo_number}, lot={lot_number}")
        
        return {
            "product": product,
            "mo_number": mo_number,
            "lot_number": lot_number,
            "method": method
        }

    def _parse_mixed_format(self, subject: str) -> Dict[str, Any]:
        """解析混合格式，如 G2225RV1UPGM24120084 DN7K9.1C"""
        product = ""
        mo_number = ""
        lot_number = ""
        method = "mixed"
        
        words = subject.split()
        
        # 尋找包含產品代碼+MO編號的複合詞
        product_mo_pattern = r'(G\w{8,10})(GM\d{8})'
        
        for i, word in enumerate(words):
            match = re.search(product_mo_pattern, word, re.IGNORECASE)
            if match:
                product = match.group(1).upper()
                mo_number = match.group(2).upper()
                method = "mixed_product_mo"
                
                # 在下一個詞中尋找LOT編號
                if i + 1 < len(words):
                    next_word = words[i + 1]
                    if re.match(r'^[A-Z0-9\.]+$', next_word, re.IGNORECASE):
                        lot_number = next_word.upper()
                        break
                
                # 如果下一個詞不是LOT編號，在當前詞的剩餘部分尋找
                remaining = word[match.end():]
                if remaining:
                    lot_match = re.search(r'^([A-Z0-9\.]+)', remaining, re.IGNORECASE)
                    if lot_match:
                        lot_number = lot_match.group(1).upper()
                break
        
        self.logger.debug(f"混合格式解析結果: product={product}, mo={mo_number}, lot={lot_number}")
        
        return {
            "product": product,
            "mo_number": mo_number,
            "lot_number": lot_number,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'G-prefixed product codes (G + digits)',
                'M-prefixed product codes (M + digits)', 
                'AT-prefixed product codes (AT + digits)',
                'Word position based MO/LOT extraction'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'regex_patterns': {
                'product': self.product_pattern
            },
            'based_on': 'VBA MSECZDInfoFromStrings',
            'extraction_capabilities': [
                'Product code via regex (G|M|AT)\\d+ pattern',
                'MO number from word after product code',
                'LOT number from second word after product code',
                'Case-insensitive pattern matching',
                'Low yield hold keyword identification'
            ],
            'special_features': [
                'Word position based extraction',
                'Multiple product code prefix support',
                'Case-insensitive matching',
                'FW: prefix tolerance'
            ]
        }
