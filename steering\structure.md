# 專案結構與組織

## 架構概述

本專案採用**六角架構**（Ports and Adapters），在六個主要層級間有清晰的關注點分離。系統支援 12 個半導體廠商，包含完整的郵件處理、檔案管理、AI 解析和監控功能。

## 原始碼結構 (`src/`)

### 核心架構層級

```
src/
├── domain/                 # 核心業務邏輯（內層）
│   ├── entities/          # 業務實體
│   ├── value_objects/     # 不可變值物件
│   ├── services/          # 領域服務
│   └── exceptions/        # 領域特定例外
│       └── base.py        # 基礎異常類別
│
├── application/           # 應用層
│   ├── use_cases/         # 業務使用案例
│   ├── interfaces/        # 埠定義
│   │   ├── email_reader.py
│   │   └── task_queue.py
│   └── services/          # 應用服務
│       └── unified_email_processor.py
│
├── infrastructure/        # 基礎設施層（外層）
│   ├── adapters/          # 外部系統適配器
│   ├── database/          # 資料庫實作
│   ├── parsers/           # 廠商特定解析器
│   ├── config/            # 配置管理
│   ├── logging/           # 日誌基礎設施
│   ├── llm/               # LLM 整合
│   └── auth/              # 身份驗證
│
├── presentation/          # 展示層
│   ├── api/               # REST API 端點
│   ├── web/               # 網頁介面
│   └── cli/               # 命令列介面
│
├── services/              # 應用服務
├── data_models/           # 資料傳輸物件
└── utils/                 # 共用工具
```

### 關鍵架構原則

1. **依賴方向**：依賴指向領域內部
2. **埠/適配器模式**：基礎設施適配領域介面
3. **單一職責**：每層都有清晰、專注的目的
4. **可測試性**：容易模擬外部依賴
5. **可擴展性**：新廠商和功能易於添加

## 基礎設施層詳細結構

### 適配器系統 (`src/infrastructure/adapters/`)

```
adapters/
├── analytics/             # 分析適配器
├── attachments/           # 附件管理
│   ├── attachment_manager.py
│   └── attachment_validator.py
├── cache/                 # 快取系統
├── database/              # 資料庫適配器
│   ├── email_database.py
│   └── models.py
├── email_inbox/           # 郵件收件箱
│   ├── email_sync_service.py
│   └── sync_attachment_handler.py
├── excel/                 # Excel 處理系統
│   ├── advanced_performance_manager.py
│   ├── csv_to_excel_converter.py
│   ├── cta/              # CTA 處理模組
│   ├── eqc/              # EQC 處理模組
│   └── 其他處理器...
├── file_handlers/         # 檔案處理器（12個廠商）
├── file_upload/           # 檔案上傳系統
├── filesystem/            # 檔案系統處理
├── llm/                   # LLM 整合
├── notification/          # 通知系統
├── outlook/               # Outlook 整合
├── pop3/                  # POP3 郵件系統
├── smtp/                  # SMTP 發送系統
└── web_api/               # Web API 適配器
```

### 解析器系統 (`src/infrastructure/parsers/`)

支援 12 個半導體廠商的專用解析器：

```
parsers/
├── base_parser.py         # 抽象基礎解析器
├── chuzhou_parser.py      # 滁州廠商解析器
├── etd_parser.py          # ETD 廠商解析器
├── gtk_parser.py          # GTK 廠商解析器
├── jcet_parser.py         # JCET 廠商解析器
├── lingsen_parser.py      # LINGSEN 廠商解析器
├── llm_parser.py          # AI LLM 解析器
├── msec_parser.py         # MSEC 廠商解析器
├── nanotech_parser.py     # NANOTECH 廠商解析器
├── nfme_parser.py         # NFME 廠商解析器
├── suqian_parser.py       # 宿遷廠商解析器
├── tsht_parser.py         # TSHT 廠商解析器
└── xaht_parser.py         # XAHT 廠商解析器
```

### LLM 整合系統 (`src/infrastructure/llm/`)

```
llm/
├── ollama_client.py       # Ollama 本地 LLM 客戶端
├── unified_llm_client.py  # 統一 LLM 客戶端
└── grok_*                 # Grok API 相關模組
```

## 展示層架構 (`src/presentation/`)

### API 系統

```
presentation/
├── api/                   # REST API 層
│   ├── ft_eqc_api.py     # FT-EQC 處理 API
│   ├── models.py         # API 資料模型
│   ├── network_browser_api.py
│   ├── network_models.py
│   ├── network_utils.py
│   ├── services/         # API 服務層
│   │   ├── api_utils.py
│   │   ├── cleanup_service.py
│   │   ├── eqc_processing_service.py
│   │   ├── file_cleanup_scheduler.py
│   │   └── file_management_service.py
│   └── utils/
├── cli/                   # 命令列介面
└── web/                   # 網頁介面
    ├── api/              # Web API 端點
    │   ├── attachment_api.py
    │   └── parser_api.py
    ├── frontend/         # Vue.js 前端（可選）
    ├── static/           # 靜態資源
    │   ├── css/         # 樣式表
    │   └── js/          # JavaScript 模組
    └── templates/        # HTML 模板
```

## 測試結構 (`tests/`)

```
tests/
├── unit/                  # 單元測試（隔離元件）
│   ├── test_project_structure.py
│   ├── test_site_column_finder.py
│   └── test_strategy_b_implementation.py
├── integration/           # 整合測試（多個元件）
│   ├── test_step5_integration.py
│   └── 其他整合測試...
├── e2e/                   # 端對端測試（完整工作流程）
│   ├── test_download_fix.py
│   └── test_step5_frontend.py
├── performance/           # 效能與負載測試
└── fixtures/              # 測試資料與固定裝置
    ├── expected_processing_result.md
    ├── sample_csv_data.csv
    ├── sample_folder_structure.py
    └── sample_gtk_email_result.json
```

## 配置與環境

### 環境配置

```
├── .env                   # 環境變數（不在 git 中）
├── .env.example           # 環境範本
├── .env.production        # 生產環境
├── pyproject.toml         # Python 專案配置
├── requirements.txt       # Python 依賴
├── Makefile              # 開發指令
└── uv.lock               # UV 包管理器鎖定檔案
```

### 專案配置檔案

```
├── project_info.json     # 專案元資訊
├── line_notifications.json # LINE 通知配置
└── email_config.py       # 郵件系統配置
```

## 文件結構

### 技術文件

```
├── README.md              # 專案概述與快速開始
├── CLAUDE.md              # AI 開發指導原則
├── CHANGELOG.md           # 版本變更記錄
├── DOC_SUMMARY.md         # 文件摘要
├── CROSS_PLATFORM_PATHS.md # 跨平台路徑處理
├── documentation/         # 技術文件
├── code_documentation/    # 自動生成程式碼文件
├── reports/               # 進度與分析報告
└── specs/                 # 規格文件
    ├── design.md
    ├── requirements.md
    └── tasks.md
```

### 指導文件

```
├── .kiro/steering/        # Kiro IDE 指導規則
├── steering/              # 統一指導文件（本目錄）
├── coordination/          # 多代理協調檔案
├── memory/                # AI 代理記憶
└── now/                   # 當前工作追蹤
```

## 資料與日誌

### 資料目錄

```
├── data/                  # 應用程式資料
│   ├── email_inbox.db     # SQLite 資料庫
│   └── tasks.json         # 任務佇列資料
├── attachments/           # 郵件附件（按 email_id 分組）
│   ├── email_3/
│   ├── email_4/
│   └── ...
├── extracted/             # 解壓縮檔案
├── tmp/                   # 暫存檔案
│   ├── extracted/
│   └── uploads/
└── logs/                  # 應用程式日誌
```

### 日誌系統

完整的日誌記錄包含：
- 應用程式日誌（按模組分類）
- 效能監控日誌
- 錯誤追蹤日誌
- 廠商特定處理日誌

## Docker 與部署

```
├── docker/                # Docker 配置
│   ├── grafana/          # 監控儀表板
│   ├── prometheus/       # 指標收集
│   ├── postgres/         # 資料庫
│   └── nginx/            # 反向代理
└── docker-compose.yml    # 服務編排
```

## 開發工具與快取

```
├── .vscode/              # VS Code 設定
├── .pytest_cache/        # Pytest 快取
├── htmlcov/              # 覆蓋率報告
├── .mypy_cache/          # MyPy 快取
├── __pycache__/          # Python 位元組碼
├── venv/                 # 標準虛擬環境
└── venv_win_3_11_12/     # UV 虛擬環境
```

## 命名慣例

### 檔案與目錄
- **Snake_case**：所有 Python 檔案與目錄
- **描述性名稱**：清楚表明目的
- **一致的後綴**：`_parser.py`、`_service.py`、`_test.py`、`_handler.py`

### 程式碼結構
- **類別**：PascalCase（`EmailProcessor`、`GTKParser`）
- **函數/變數**：snake_case（`process_email`、`parse_attachment`）
- **常數**：UPPER_SNAKE_CASE（`MAX_RETRY_COUNT`、`DEFAULT_TIMEOUT`）
- **私有成員**：前導底線（`_internal_method`、`_process_data`）

## 匯入組織

```python
# 標準函式庫匯入
import os
import logging
from typing import List, Optional, Dict, Any
from pathlib import Path

# 第三方函式庫匯入
import pandas as pd
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from loguru import logger

# 本地應用程式匯入
from src.domain.entities import Email
from src.infrastructure.parsers import BaseParser
from src.application.interfaces import EmailReader
```

## 檔案組織規則

1. **單一職責**：每個檔案一個主要類別（小型相關類別除外）
2. **清晰階層**：按功能邏輯分組
3. **最小依賴**：避免循環匯入
4. **介面隔離**：小型、專注的介面
5. **依賴注入**：基於建構子的依賴注入
6. **廠商擴展**：新廠商易於添加

## 特殊目錄

- **`.kiro/`**：Kiro IDE 配置與導向規則
- **`steering/`**：統一代理指導文件
- **`coordination/`**：多代理協調檔案
- **`memory/`**：AI 助理記憶與快取
- **`now/`**：當前工作狀態追蹤

## 語言考量

- **混合語言支援**：中文與英文文件
- **UTF-8 編碼**：所有檔案使用 UTF-8 編碼
- **Windows 相容性**：Windows 路徑與編碼的特殊處理
- **跨平台路徑**：統一的路徑處理機制

## 擴展性設計

### 新廠商添加流程
1. 建立新的 `{vendor}_parser.py` 解析器
2. 實作對應的 `{vendor}_file_handler.py` 檔案處理器
3. 更新廠商識別邏輯
4. 添加對應的測試案例
5. 更新文件和配置

### 新功能擴展
- 遵循六角架構原則
- 保持層級分離
- 添加適當的測試覆蓋
- 更新相關文件