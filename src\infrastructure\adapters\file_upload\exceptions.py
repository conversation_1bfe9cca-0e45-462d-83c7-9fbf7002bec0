"""
檔案上傳相關異常定義
"""


class FileUploadError(Exception):
    """檔案上傳基本異常類別"""
    pass


class FileSizeError(FileUploadError):
    """檔案大小超過限制異常"""
    
    def __init__(self, message: str = "檔案大小超過限制"):
        self.message = message
        super().__init__(self.message)


class FileFormatError(FileUploadError):
    """不支援的檔案格式異常"""
    
    def __init__(self, message: str = "不支援的檔案格式"):
        self.message = message
        super().__init__(self.message)


class ExtractionError(FileUploadError):
    """檔案解壓縮異常"""
    
    def __init__(self, message: str = "檔案解壓縮失敗"):
        self.message = message
        super().__init__(self.message)