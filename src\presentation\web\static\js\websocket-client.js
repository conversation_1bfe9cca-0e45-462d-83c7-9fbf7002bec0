/**
 * WebSocket 即時狀態更新客戶端
 * 整合異步任務管理系統和監控儀表板
 */

class WebSocketClient {
    constructor(baseUrl = window.location.host) {
        this.baseUrl = baseUrl;
        this.ws = null;
        this.clientId = this.generateClientId();
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 2000;
        
        // 事件監聽器
        this.eventHandlers = new Map();
        
        // 狀態緩存
        this.lastStatus = {};
        
        this.connect();
    }
    
    generateClientId() {
        return 'client_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
    }
    
    connect() {
        try {
            const wsUrl = `ws://${this.baseUrl}/ws/realtime/${this.clientId}`;
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = this.onOpen.bind(this);
            this.ws.onmessage = this.onMessage.bind(this);
            this.ws.onclose = this.onClose.bind(this);
            this.ws.onerror = this.onError.bind(this);
            
        } catch (error) {
            console.error('WebSocket connection failed:', error);
            this.scheduleReconnect();
        }
    }
    
    onOpen(event) {
        console.log('WebSocket connected:', this.clientId);
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 訂閱所有狀態更新
        this.subscribe([
            'task_status',
            'system_metrics', 
            'process_progress',
            'alerts'
        ]);
        
        this.emit('connected', { clientId: this.clientId });
    }
    
    onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }
    
    onClose(event) {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        
        if (event.code !== 1000) { // 非正常關閉
            this.scheduleReconnect();
        }
        
        this.emit('disconnected', { code: event.code, reason: event.reason });
    }
    
    onError(error) {
        console.error('WebSocket error:', error);
        this.emit('error', error);
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, delay);
        } else {
            console.error('Max reconnection attempts reached');
            this.emit('maxReconnectAttemptsReached');
        }
    }
    
    handleMessage(data) {
        const { type, payload, timestamp } = data;
        
        switch (type) {
            case 'task_status_update':
                this.handleTaskStatusUpdate(payload);
                break;
            case 'system_metrics_update':
                this.handleSystemMetricsUpdate(payload);
                break;
            case 'process_progress_update':
                this.handleProcessProgressUpdate(payload);
                break;
            case 'alert':
                this.handleAlert(payload);
                break;
            case 'pong':
                this.handlePong(payload);
                break;
            default:
                console.log('Unknown message type:', type, payload);
        }
        
        // 觸發通用事件
        this.emit('message', data);
        this.emit(type, payload);
    }
    
    handleTaskStatusUpdate(payload) {
        const { task_id, status, progress, result, error } = payload;
        
        // 更新任務狀態界面
        this.updateTaskStatus(task_id, {
            status,
            progress,
            result,
            error,
            updated_at: new Date()
        });
        
        this.emit('taskStatusUpdate', payload);
    }
    
    handleSystemMetricsUpdate(payload) {
        // 更新系統監控指標
        this.updateSystemMetrics(payload);
        this.emit('systemMetricsUpdate', payload);
    }
    
    handleProcessProgressUpdate(payload) {
        const { process_id, step, total_steps, message } = payload;
        
        // 更新進度條
        this.updateProcessProgress(process_id, {
            step,
            total_steps,
            progress: (step / total_steps) * 100,
            message
        });
        
        this.emit('processProgressUpdate', payload);
    }
    
    handleAlert(payload) {
        const { level, message, category } = payload;
        
        // 顯示告警
        this.showAlert(level, message, category);
        this.emit('alert', payload);
    }
    
    handlePong(payload) {
        // 心跳響應
        this.emit('pong', payload);
    }
    
    // UI 更新方法
    updateTaskStatus(taskId, statusData) {
        const taskElement = document.querySelector(`[data-task-id="${taskId}"]`);
        if (!taskElement) return;
        
        // 更新狀態標籤
        const statusElement = taskElement.querySelector('.task-status');
        if (statusElement) {
            statusElement.textContent = statusData.status;
            statusElement.className = `task-status status-${statusData.status.toLowerCase()}`;
        }
        
        // 更新進度條
        const progressElement = taskElement.querySelector('.task-progress');
        if (progressElement && statusData.progress !== undefined) {
            progressElement.style.width = `${statusData.progress}%`;
            progressElement.setAttribute('data-progress', statusData.progress);
        }
        
        // 更新進度文字
        const progressText = taskElement.querySelector('.progress-text');
        if (progressText) {
            progressText.textContent = `${statusData.progress || 0}%`;
        }
        
        // 更新結果或錯誤信息
        if (statusData.error) {
            const errorElement = taskElement.querySelector('.task-error');
            if (errorElement) {
                errorElement.textContent = statusData.error;
                errorElement.style.display = 'block';
            }
        }
        
        if (statusData.result) {
            const resultElement = taskElement.querySelector('.task-result');
            if (resultElement) {
                resultElement.textContent = JSON.stringify(statusData.result, null, 2);
            }
        }
    }
    
    updateSystemMetrics(metrics) {
        // 更新 CPU 使用率
        this.updateMetricElement('cpu-usage', metrics.cpu_percent, '%');
        
        // 更新記憶體使用率
        this.updateMetricElement('memory-usage', metrics.memory_percent, '%');
        
        // 更新活躍任務數
        this.updateMetricElement('active-tasks', metrics.active_tasks);
        
        // 更新 API 響應時間
        this.updateMetricElement('avg-response-time', metrics.avg_response_time, 'ms');
        
        // 更新錯誤率
        this.updateMetricElement('error-rate', metrics.error_rate, '%');
    }
    
    updateMetricElement(elementId, value, unit = '') {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = `${value}${unit}`;
            
            // 添加動畫效果
            element.classList.add('metric-updated');
            setTimeout(() => {
                element.classList.remove('metric-updated');
            }, 1000);
        }
    }
    
    updateProcessProgress(processId, progressData) {
        const progressElement = document.querySelector(`[data-process-id="${processId}"]`);
        if (!progressElement) return;
        
        const progressBar = progressElement.querySelector('.progress-bar-fill');
        const progressText = progressElement.querySelector('.progress-text');
        const stepText = progressElement.querySelector('.step-text');
        const messageText = progressElement.querySelector('.message-text');
        
        if (progressBar) {
            progressBar.style.width = `${progressData.progress}%`;
            progressBar.setAttribute('aria-valuenow', progressData.progress);
        }
        
        if (progressText) {
            progressText.textContent = `${Math.round(progressData.progress)}%`;
        }
        
        if (stepText) {
            stepText.textContent = `步驟 ${progressData.step}/${progressData.total_steps}`;
        }
        
        if (messageText && progressData.message) {
            messageText.textContent = progressData.message;
        }
    }
    
    showAlert(level, message, category = '') {
        const alertContainer = document.getElementById('alert-container') || this.createAlertContainer();
        
        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${level.toLowerCase()}`;
        alertElement.innerHTML = `
            <div class="alert-content">
                <span class="alert-icon">${this.getAlertIcon(level)}</span>
                <div class="alert-text">
                    ${category ? `<strong>${category}:</strong> ` : ''}
                    ${message}
                </div>
                <button class="alert-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        alertContainer.appendChild(alertElement);
        
        // 自動消失
        setTimeout(() => {
            if (alertElement.parentElement) {
                alertElement.remove();
            }
        }, 5000);
    }
    
    createAlertContainer() {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.className = 'alert-container';
        document.body.appendChild(container);
        return container;
    }
    
    getAlertIcon(level) {
        const icons = {
            'info': 'ℹ️',
            'warning': '⚠️',
            'error': '❌',
            'success': '✅'
        };
        return icons[level.toLowerCase()] || 'ℹ️';
    }
    
    // API 方法
    subscribe(topics) {
        if (!this.isConnected) return;
        
        this.send('subscribe', { topics });
    }
    
    unsubscribe(topics) {
        if (!this.isConnected) return;
        
        this.send('unsubscribe', { topics });
    }
    
    startTask(taskType, taskParams = {}) {
        if (!this.isConnected) return;
        
        this.send('start_task', {
            task_type: taskType,
            task_params: taskParams
        });
    }
    
    cancelTask(taskId) {
        if (!this.isConnected) return;
        
        this.send('cancel_task', { task_id: taskId });
    }
    
    getTaskStatus(taskId) {
        if (!this.isConnected) return;
        
        this.send('get_task_status', { task_id: taskId });
    }
    
    ping() {
        if (!this.isConnected) return;
        
        this.send('ping', { timestamp: Date.now() });
    }
    
    send(type, payload = {}) {
        if (!this.isConnected) {
            console.warn('WebSocket not connected, message queued:', type);
            return;
        }
        
        const message = {
            type,
            payload,
            timestamp: Date.now(),
            client_id: this.clientId
        };
        
        this.ws.send(JSON.stringify(message));
    }
    
    // 事件系統
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }
    
    off(event, handler) {
        if (!this.eventHandlers.has(event)) return;
        
        const handlers = this.eventHandlers.get(event);
        const index = handlers.indexOf(handler);
        if (index > -1) {
            handlers.splice(index, 1);
        }
    }
    
    emit(event, data = {}) {
        if (!this.eventHandlers.has(event)) return;
        
        const handlers = this.eventHandlers.get(event);
        handlers.forEach(handler => {
            try {
                handler(data);
            } catch (error) {
                console.error(`Error in event handler for ${event}:`, error);
            }
        });
    }
    
    // 清理方法
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
        }
    }
    
    destroy() {
        this.disconnect();
        this.eventHandlers.clear();
    }
}

// 全局實例
window.wsClient = null;

// 初始化函數
function initWebSocket() {
    if (window.wsClient) {
        window.wsClient.destroy();
    }
    
    window.wsClient = new WebSocketClient();
    
    // 設置全局事件監聽器
    window.wsClient.on('connected', () => {
        console.log('WebSocket connected successfully');
        updateConnectionStatus(true);
    });
    
    window.wsClient.on('disconnected', () => {
        console.log('WebSocket disconnected');
        updateConnectionStatus(false);
    });
    
    window.wsClient.on('error', (error) => {
        console.error('WebSocket error:', error);
        showErrorNotification('WebSocket 連接錯誤');
    });
    
    window.wsClient.on('maxReconnectAttemptsReached', () => {
        showErrorNotification('無法重新連接到服務器，請重新整理頁面');
    });
    
    return window.wsClient;
}

function updateConnectionStatus(isConnected) {
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
        statusElement.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
        statusElement.textContent = isConnected ? '已連接' : '未連接';
    }
}

function showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">❌</span>
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// 頁面載入時自動初始化
document.addEventListener('DOMContentLoaded', function() {
    initWebSocket();
});

// 頁面卸載時清理
window.addEventListener('beforeunload', function() {
    if (window.wsClient) {
        window.wsClient.destroy();
    }
});