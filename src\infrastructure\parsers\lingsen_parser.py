"""
LINGSEN 廠商解析器實作
基於 VBA LINGSENInfoFromStrings 邏輯

VBA 邏輯參考：
- 識別條件：InStr(UCase(body), "LINGSEN") Or InStr(LCase(senderAddress), "lingsen")
- 解析規則：正則匹配 (G|M|AT)\d 產品代碼，Run# 和 Lot# 提取，yield 百分比解析
- 範例：[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D LowYield = 95.09%
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult


class LINGSENParser(VendorParser):
    """
    LINGSEN 廠商郵件解析器
    
    識別條件：
    - 郵件正文包含 "LINGSEN"
    - 寄件者地址包含 "lingsen"
    
    解析機制：
    1. 使用正則表達式 \b(G|M|AT)\d 尋找產品代碼
    2. 尋找 Run# 後的數字作為 MO 號碼
    3. 尋找 Lot# 後的字串作為 Lot 號碼
    4. 使用正則表達式提取 yield 百分比
    """
    
    def __init__(self):
        """初始化 LINGSEN 解析器"""
        super().__init__()
        self._vendor_code = "LINGSEN"
        self._vendor_name = "LINGSEN"
        self._identification_patterns = [
            "LINGSEN",           # 內文關鍵字（大寫）
            "lingsen"            # 寄件者域名（小寫）
        ]
        self.set_confidence_threshold(0.8)
        
        # LINGSEN 特有的正則模式
        self.product_pattern = r'\b(G|M|AT)\d+'  # 產品代碼：G/M/AT 開頭 + 數字
        self.run_pattern = r'[Rr]un#(\d+)'       # Run# 後的數字
        self.lot_pattern = r'[Ll]ot#([^\s]+)'    # Lot# 後的字串
        self.yield_pattern = r'[Ll]ow[Yy]ield\s*=?\s*(\d+(?:\.\d+)?%)'  # yield 百分比

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        # VBA: InStr(1, UCase(body), "LINGSEN", vbTextCompare) > 0
        if "LINGSEN" in body.upper():
            matched_patterns.append("LINGSEN")
            confidence_score += 0.8  # 提高分數以達到閾值
            
        # 也檢查主旨中的 LINGSEN（增強識別能力）
        if "LINGSEN" in subject.upper():
            if "LINGSEN" not in matched_patterns:
                matched_patterns.append("LINGSEN")
            confidence_score += 0.3
            
        # VBA: InStr(1, LCase(senderAddress), "lingsen", vbTextCompare) > 0
        if "lingsen" in sender_lower:
            matched_patterns.append("lingsen")
            confidence_score += 0.6
        
        # 額外的信心分數計算
        # 檢查是否有產品代碼模式
        if re.search(self.product_pattern, subject, re.IGNORECASE):
            confidence_score += 0.2
            
        # 檢查是否有 Run# 或 Lot# 模式
        if re.search(self.run_pattern, subject, re.IGNORECASE) or re.search(self.lot_pattern, subject, re.IGNORECASE):
            confidence_score += 0.2
            
        # 檢查是否有 LowYield 關鍵字
        if "lowyield" in subject.lower() or "low yield" in subject.lower():
            confidence_score += 0.2
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        # 特殊處理：如果有明確的 LINGSEN 產品模式（G+數字），即使沒有 LINGSEN 關鍵字也識別
        # 放寬產品模式，只要是 G 開頭加數字即可
        has_g_product = re.search(r'G\d{3,}[A-Z0-9]*', subject, re.IGNORECASE)
        has_run = re.search(self.run_pattern, subject, re.IGNORECASE)
        has_lot = re.search(self.lot_pattern, subject, re.IGNORECASE)
        
        # 如果有 G 產品 + Run# + Lot#，這是典型的 LINGSEN 模式
        if has_g_product and has_run and has_lot:
            matched_patterns.append("lingsen_pattern")
            confidence_score = 0.85  # 直接設定高信心分數
        
        # 如果有 Lowyield + GMT + G產品，也是 LINGSEN
        elif "lowyield" in subject.lower() and "GMT" in subject and has_g_product:
            matched_patterns.append("lingsen_lowyield_pattern")
            confidence_score = 0.85
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="lingsen_keyword_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        if not email_data.subject:
            # 空主旨時嘗試從識別結果判斷
            identification = self.identify_vendor(email_data)
            if not identification.is_identified:
                raise ParsingError("Empty subject and cannot identify vendor", vendor_code=self.vendor_code)
        
        try:
            # 使用 LINGSEN 關鍵字解析機制
            lingsen_result = self.parse_lingsen_keywords(email_data.subject or "")
            
            # MO 編號保持原始格式，不添加前綴
            mo_number = lingsen_result["mo_number"]
            if not mo_number or mo_number == "":
                mo_number = None
            
            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=lingsen_result["product"] if lingsen_result["product"] != "?" else None,  # 🔧 添加產品代碼
                mo_number=mo_number,
                lot_number=lingsen_result["lot_number"] if lingsen_result["lot_number"] != "" else None,
                is_success=True,
                error_message=None,
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': lingsen_result["product"],
                    'lot_number': lingsen_result["lot_number"],
                    'mo_number': lingsen_result["mo_number"],  # 原始 MO 編號
                    'yield_rate': lingsen_result["yield_rate"],
                    'parsing_method': lingsen_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'lingsen_regex_parsing'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                is_success=False,
                error_message=f"LINGSEN parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_lingsen_keywords(self, subject: str) -> Dict[str, Any]:
        """
        解析 LINGSEN 關鍵字：產品代碼、Run#、Lot#、yield
        基於 VBA LINGSENInfoFromStrings 邏輯
        
        VBA 邏輯：
        - 使用正則表達式 \b(G|M|AT)\d 尋找產品代碼
        - 尋找 Run# 後的數字作為 MO 號碼
        - 尋找 Lot# 後的字串作為 Lot 號碼
        - 使用正則表達式提取 yield 百分比
        """
        if not subject:
            return {
                "product": "",
                "mo_number": "",
                "lot_number": "",
                "yield_rate": "",
                "method": "no_pattern"
            }
        
        product = ""
        mo_number = ""
        lot_number = ""
        yield_rate = ""
        method = "no_pattern"
        
        # 1. 尋找產品代碼 (G/M/AT 開頭 + 數字)
        # 使用正則表達式直接匹配，不依賴空格分割
        priority_prefixes = ['G', 'M', 'AT']  # G 優先級最高
        
        for prefix in priority_prefixes:
            # 匹配產品代碼，只取第一個完整的產品代碼（包含第一個括號）
            # 例如：G2518KK1U(CTAXP) 而不是 G2518KK1U(CTAXP)(G2518XXXXDB1KK1451)
            pattern = rf'\b({prefix}\d+[A-Z0-9]*(?:\([^)]+\))?)'
            match = re.search(pattern, subject, re.IGNORECASE)
            if match:
                # 只取第一個括號結束前的部分
                full_match = match.group(1).upper()
                # 如果有多個括號，只保留第一個
                if full_match.count('(') > 1:
                    first_close = full_match.find(')')
                    if first_close != -1:
                        product = full_match[:first_close + 1]
                    else:
                        product = full_match
                else:
                    product = full_match
                method = f"{prefix.lower()}_pattern"
                break
        
        # 2. 尋找 Run# 後的 MO 號碼
        run_matches = re.findall(self.run_pattern, subject, re.IGNORECASE)
        if run_matches:
            mo_number = run_matches[0]  # 取第一個匹配
        
        # 3. 尋找 Lot# 後的 Lot 號碼
        lot_matches = re.findall(self.lot_pattern, subject, re.IGNORECASE)
        if lot_matches:
            lot_number = lot_matches[0]  # 取第一個匹配
        
        # 4. 尋找 yield 百分比
        yield_matches = re.findall(self.yield_pattern, subject, re.IGNORECASE)
        if yield_matches:
            yield_rate = yield_matches[0]  # 取第一個匹配
        
        return {
            "product": product,
            "mo_number": mo_number,
            "lot_number": lot_number,
            "yield_rate": yield_rate,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'G-prefixed product codes (G + digits)',
                'M-prefixed product codes (M + digits)',
                'AT-prefixed product codes (AT + digits)',
                'Run# MO number extraction',
                'Lot# lot number extraction',
                'LowYield percentage extraction'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'regex_patterns': {
                'product': self.product_pattern,
                'run': self.run_pattern,
                'lot': self.lot_pattern,
                'yield': self.yield_pattern
            },
            'based_on': 'VBA LINGSENInfoFromStrings',
            'extraction_capabilities': [
                'Product code via regex (G|M|AT)\\d+ pattern',
                'MO number from Run# field',
                'LOT number from Lot# field',
                'Yield rate from LowYield = xx% pattern',
                'Case-insensitive pattern matching',
                'Email body and sender pattern identification'
            ],
            'special_features': [
                'Regex-based pattern extraction',
                'Multiple product code prefix support',
                'Yield percentage parsing',
                'Case-insensitive matching',
                'FW: prefix tolerance'
            ]
        }