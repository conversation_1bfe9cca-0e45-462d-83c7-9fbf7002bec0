#!/bin/bash
# Docker容器入口點腳本

set -e

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 環境變數設置
export PYTHONPATH="/app/src:$PYTHONPATH"
export OUTLOOK_ENV="${OUTLOOK_ENV:-production}"

log_info "🚀 啟動 Outlook Summary 多進程系統..."
log_info "Environment: $OUTLOOK_ENV"
log_info "Python Path: $PYTHONPATH"

# 等待依賴服務
wait_for_service() {
    local host=$1
    local port=$2
    local service=$3
    local timeout=${4:-30}
    
    log_info "等待 $service 服務 ($host:$port) 就緒..."
    
    for i in $(seq 1 $timeout); do
        if timeout 1 bash -c "echo >/dev/tcp/$host/$port"; then
            log_success "$service 服務已就緒"
            return 0
        fi
        
        if [ $i -eq $timeout ]; then
            log_error "$service 服務未就緒，超時退出"
            return 1
        fi
        
        sleep 1
    done
}

# 等待Redis服務 (如果配置了)
if [ -n "$REDIS_HOST" ]; then
    wait_for_service "${REDIS_HOST:-redis}" "${REDIS_PORT:-6379}" "Redis"
fi

# 健康檢查函數
health_check() {
    local max_attempts=5
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log_info "健康檢查嘗試 $attempt/$max_attempts..."
        
        if curl -f -s "http://localhost:8000/health" > /dev/null 2>&1; then
            log_success "應用健康檢查通過"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "健康檢查失敗"
            return 1
        fi
        
        attempt=$((attempt + 1))
        sleep 5
    done
}

# 創建必要目錄
create_directories() {
    log_info "創建必要目錄..."
    
    mkdir -p /app/logs/{app,worker,system}
    mkdir -p /app/temp/{uploads,processing,downloads}
    mkdir -p /app/data
    
    log_success "目錄創建完成"
}

# 系統信息檢查
check_system_info() {
    log_info "系統信息檢查..."
    
    # CPU信息
    cpu_cores=$(nproc)
    log_info "CPU核心數: $cpu_cores"
    
    # 記憶體信息
    memory_mb=$(free -m | grep '^Mem:' | awk '{print $2}')
    log_info "可用記憶體: ${memory_mb}MB"
    
    # 磁碟空間
    disk_usage=$(df -h /app | tail -1 | awk '{print $5}')
    log_info "磁碟使用率: $disk_usage"
    
    # Python版本
    python_version=$(python --version)
    log_info "Python版本: $python_version"
    
    # 設置進程池大小環境變數
    export CPU_CORES=$cpu_cores
    export MEMORY_MB=$memory_mb
}

# 優化設置
optimize_runtime() {
    log_info "運行時優化設置..."
    
    # 設置ulimit
    ulimit -n 65536 2>/dev/null || log_warning "無法設置檔案描述符限制"
    
    # Python優化
    export PYTHONOPTIMIZE=1
    export PYTHONHASHSEED=0
    
    log_success "運行時優化完成"
}

# 信號處理
setup_signal_handlers() {
    log_info "設置信號處理器..."
    
    # 優雅關閉處理
    trap 'log_info "收到SIGTERM信號，準備關閉..."; kill -TERM $APP_PID; wait $APP_PID; exit 0' TERM
    trap 'log_info "收到SIGINT信號，準備關閉..."; kill -INT $APP_PID; wait $APP_PID; exit 0' INT
    
    log_success "信號處理器設置完成"
}

# 啟動應用
start_application() {
    log_info "啟動多進程應用..."
    
    # 根據環境選擇啟動模式
    if [ "$OUTLOOK_ENV" = "development" ]; then
        log_info "開發模式啟動..."
        exec python -m uvicorn app.main:app \
            --host 0.0.0.0 \
            --port 8000 \
            --reload \
            --reload-dir /app/src \
            --log-level debug
    else
        log_info "生產模式啟動..."
        exec python -m uvicorn app.main:app \
            --host 0.0.0.0 \
            --port 8000 \
            --workers 1 \
            --loop asyncio \
            --log-level info \
            --access-log \
            --use-colors &
        
        APP_PID=$!
        log_success "應用已啟動 (PID: $APP_PID)"
        
        # 等待應用啟動
        sleep 10
        
        # 健康檢查
        if health_check; then
            log_success "應用啟動成功！"
        else
            log_error "應用啟動失敗"
            exit 1
        fi
        
        # 保持容器運行
        wait $APP_PID
    fi
}

# 錯誤處理
error_handler() {
    local exit_code=$?
    log_error "啟動過程中發生錯誤 (退出碼: $exit_code)"
    
    # 輸出相關日誌
    if [ -f /app/logs/app/error.log ]; then
        log_error "錯誤日誌:"
        tail -20 /app/logs/app/error.log
    fi
    
    exit $exit_code
}

# 設置錯誤處理
trap error_handler ERR

# 主執行流程
main() {
    create_directories
    check_system_info
    optimize_runtime
    setup_signal_handlers
    start_application
}

# 檢查是否為健康檢查請求
if [ "$1" = "health-check" ]; then
    log_info "執行健康檢查..."
    health_check
    exit $?
fi

# 檢查是否為Shell模式
if [ "$1" = "bash" ] || [ "$1" = "sh" ]; then
    log_info "啟動Shell模式..."
    exec "$@"
fi

# 執行主函數
main "$@"