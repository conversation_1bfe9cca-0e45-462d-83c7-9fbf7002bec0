"""
CHUZHOU (滁州) 廠商檔案處理器
對應 VBA 的 CopyFilesChuzhou 函數
"""

from pathlib import Path
from typing import List, Optional
from datetime import datetime

from .base_file_handler import BaseFileHandler


class ChuzhouFileHandler(BaseFileHandler):
    """
    CHUZHOU (滁州) 廠商檔案處理器

    VBA 邏輯：
    - 來源路徑：sourcePath & "\JCET\CHUZHOU\TO252\"
    - 搜尋模式：Dir(sourcePathChuzhou & lot & "*")
    - 檔案類型：壓縮檔 (.zip, .rar, .7z)
    - 特殊邏輯：選擇最新的壓縮檔
    """

    def __init__(self, source_base_path: str):
        """初始化 CHUZHOU 檔案處理器"""
        super().__init__(source_base_path, "CHUZHOU")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default",
                        mo: str = "") -> List[Path]:
        """
        CHUZHOU 的來源路徑
        
        VBA: sourcePathChuzhou = sourcePath & "\JCET\CHUZHOU\TO252\"
        """
        paths = []
        
        # CHUZHOU 固定路徑：\JCET\CHUZHOU\TO252\
        chuzhou_path = self.source_base_path / "JCET" / "CHUZHOU" / "TO252"
        paths.append(chuzhou_path)
        
        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        CHUZHOU 的檔案搜尋模式
        
        VBA: file = Dir(sourcePathChuzhou & lot & "*")
        注意：CHUZHOU 使用 LOT 搜尋，不是 MO
        """
        patterns = []
        
        if lot and lot != "default":
            patterns.append(f"{lot}*")  # VBA 模式：以 LOT 開頭
        elif mo:
            # 如果沒有 LOT，退回使用 MO
            patterns.append(f"*{mo}*")
            
        return patterns
        
    def _copy_by_lot(self, source_path: Path, dest_path: Path, lot: str) -> bool:
        """
        覆寫 LOT 複製邏輯，實作 CHUZHOU 特殊的最新壓縮檔選擇邏輯
        
        VBA 邏輯：
        - 搜尋以 LOT 開頭的檔案
        - 只考慮壓縮檔 (.zip, .rar, .7z)
        - 選擇最新的檔案
        """
        try:
            self.logger.info(f"      🔍 CHUZHOU 特殊邏輯: 搜尋以 LOT '{lot}' 開頭的最新壓縮檔")
            
            if not lot or lot == "default":
                self.logger.warning(f"      ❌ LOT 參數無效: '{lot}'")
                return False
            
            # 搜尋以 LOT 開頭的所有檔案
            pattern = f"{lot}*"
            all_files = list(source_path.glob(pattern))
            
            # 篩選出壓縮檔
            archive_files = [f for f in all_files 
                           if f.is_file() and f.suffix.lower() in self.archive_extensions]
            
            self.logger.info(f"      📁 找到 {len(all_files)} 個符合 '{pattern}' 的檔案")
            self.logger.info(f"      📦 其中 {len(archive_files)} 個是壓縮檔")
            
            if not archive_files:
                self.logger.warning(f"      ❌ 沒有找到以 '{lot}' 開頭的壓縮檔")
                return False
            
            # 找到最新的壓縮檔
            newest_file = self._find_newest_file_by_modification_time(archive_files)
            
            if newest_file:
                self.logger.info(f"      ✅ 選擇最新的壓縮檔: {newest_file.name}")
                return self._copy_file_with_check(newest_file, dest_path)
            else:
                self.logger.warning(f"      ❌ 無法確定最新的壓縮檔")
                return False
                
        except Exception as e:
            self.logger.error(f"CHUZHOU LOT 複製失敗: {e}")
            return False
    
    def _find_newest_file_by_modification_time(self, files: List[Path]) -> Optional[Path]:
        """
        從檔案列表中找到最新的檔案（根據修改時間）
        
        Args:
            files: 檔案列表
            
        Returns:
            最新的檔案，如果列表為空則返回 None
        """
        if not files:
            return None
            
        newest_file = None
        newest_time = datetime.min
        
        for file_path in files:
            try:
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time > newest_time:
                    newest_time = file_time
                    newest_file = file_path
            except Exception as e:
                self.logger.warning(f"無法讀取檔案時間 {file_path}: {e}")
                continue
        
        if newest_file:
            self.logger.info(f"      📅 最新檔案時間: {newest_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return newest_file
        
    def _supports_folder_copy(self) -> bool:
        """CHUZHOU 不支援資料夾複製"""
        return False