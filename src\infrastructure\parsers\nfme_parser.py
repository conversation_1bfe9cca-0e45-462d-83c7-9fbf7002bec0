"""
NFME 廠商解析器實作
基於 VBA NFMEInfoFromStrings 邏輯

VBA 邏輯參考：
- 識別條件：通過產品代碼格式 (G|M|AT)\d{1} 和 NFME 關鍵字
- 解析規則：尋找包含產品代碼的詞，使用 "-" 分隔符分離產品代碼和 MO 編號
- 範例：FW: G5619RZ1U-H4XU50.1F??OS超?
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


class NFMEParser(VendorParser):
    """
    NFME 廠商郵件解析器
    
    識別條件：
    - 郵件主旨或內文包含 "nfme" 關鍵字
    - 包含符合 (G|M|AT)\d 格式的產品代碼
    
    解析機制：
    1. 使用正則表達式 \b(G|M|AT)\d+ 尋找產品代碼
    2. 在找到產品代碼的詞中尋找 "-" 分隔符
    3. 分隔符前為產品代碼，後為 MO 編號（取前9個字符）
    """
    
    def __init__(self):
        """初始化 NFME 解析器"""
        super().__init__()
        self._vendor_code = "NFME"
        self._vendor_name = "NFME"
        self._identification_patterns = [
            "nfme",           # 主要識別關鍵字
            "os超",           # 常見關鍵字
            "os超?"          # 變體
        ]
        self.set_confidence_threshold(0.7)
        
        # 初始化 logger
        self.logger = LoggerManager().get_logger("NFMEParser")
        
        # NFME 特有的產品代碼模式
        self.product_pattern = r'\b(G|M|AT)\d+'  # 產品代碼：G/M/AT 開頭 + 數字

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        content = (subject + " " + body).lower()
        
        # 檢查 NFME 關鍵字
        if "nfme" in content:
            matched_patterns.append("nfme")
            confidence_score += 0.8
            
        # 檢查寄件者是否包含 NFME
        if "nfme" in sender_lower:
            if "nfme" not in matched_patterns:
                matched_patterns.append("nfme")
            confidence_score += 0.3
        
        # 檢查常見的 NFME 特徵詞
        if "os超" in content:
            matched_patterns.append("os超")
            confidence_score += 0.4
            
        # 檢查是否有產品代碼模式（NFME 特有格式）
        if re.search(self.product_pattern, subject, re.IGNORECASE):
            confidence_score += 0.3
            # 進一步檢查是否有 "-" 分隔的 MO 格式（包含空格）
            dash_pattern = r'\b((?:G|M|AT)\d+[A-Z0-9]*)\s*-\s*[A-Z0-9.]+'
            if re.search(dash_pattern, subject, re.IGNORECASE):
                confidence_score += 0.4
                matched_patterns.append("product_mo_pattern")
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="nfme_keyword_pattern_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        try:
            # 基於 VBA NFMEInfoFromStrings 邏輯進行解析
            subject = email_data.subject or ""
            body = email_data.body or ""
            
            # 首先嘗試從主旨解析
            nfme_result = self.parse_nfme_pattern(subject)
            
            # 如果主旨解析不完整，嘗試從郵件內文解析補充信息
            if not nfme_result["product"] or not nfme_result["mo_number"]:
                body_result = self.parse_nfme_pattern(body)
                
                # 合併解析結果
                if body_result["product"] and not nfme_result["product"]:
                    nfme_result["product"] = body_result["product"]
                if body_result["mo_number"] and not nfme_result["mo_number"]:
                    nfme_result["mo_number"] = body_result["mo_number"]
                if body_result["method"] != "no_pattern":
                    nfme_result["method"] = body_result["method"] + "_from_body"
            
            # 提取結果
            product_code = nfme_result["product"] if nfme_result["product"] else None
            mo_number = nfme_result["mo_number"] if nfme_result["mo_number"] else None
            
            # NFME 將 MO 編號視為 LOT 編號 (根據 NFME_REQUIRED_FIELDS=PD,LOT 配置)
            lot_number = mo_number
            
            # 檢查是否成功解析（根據 NFME_REQUIRED_FIELDS=PD,LOT）
            missing_fields = []
            if not product_code:
                missing_fields.append("產品代碼")
            if not lot_number:
                missing_fields.append("LOT編號")

            is_success = len(missing_fields) == 0
            error_message = None

            if not is_success:
                error_message = f"NFME 傳統解析失敗：缺少 {', '.join(missing_fields)}"
                self.logger.warning(f"NFME 傳統解析失敗詳情:")
                self.logger.warning(f"  主旨: {email_data.subject}")
                self.logger.warning(f"  解析方法: {nfme_result['method']}")
                self.logger.warning(f"  產品代碼: {product_code or '未找到'}")
                self.logger.warning(f"  MO編號: {mo_number or '未找到'}")
                self.logger.warning(f"  缺少欄位: {', '.join(missing_fields)}")
            else:
                self.logger.info(f"NFME 傳統解析成功:")
                self.logger.info(f"  產品代碼: {product_code}")
                self.logger.info(f"  MO編號: {mo_number}")
                self.logger.info(f"  解析方法: {nfme_result['method']}")

            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=product_code,
                mo_number=mo_number,
                lot_number=lot_number,
                is_success=is_success,
                error_message=error_message,
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': nfme_result["product"],
                    'mo_number': nfme_result["mo_number"],
                    'parsing_method': nfme_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'nfme_dash_pattern_parsing'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                extraction_method="traditional",
                is_success=False,
                error_message=f"NFME parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_nfme_pattern(self, text: str) -> Dict[str, Any]:
        """
        解析 NFME 模式：基於 VBA NFMEInfoFromStrings 邏輯
        
        VBA 邏輯：
        1. 使用正則表達式 \b(G|M|AT)\d{1} 尋找產品代碼
        2. 在找到的詞中尋找包含產品代碼的完整詞
        3. 如果詞包含 "-"，分離產品代碼（前半部）和 MO 編號（後半部，取前9字符）
        
        範例：G5619RZ1U-H4XU50.1F => 產品: G5619RZ1U, MO: H4XU50.1F
        修正：G5619RZ1U - HB6P24.1 => 產品: G5619RZ1U, MO: HB6P24.1 (處理空格情況)
        """
        if not text:
            return {
                "product": "",
                "mo_number": "",
                "method": "no_pattern"
            }
        
        product = ""
        mo_number = ""
        method = "no_pattern"
        
        # 首先嘗試更寬鬆的正則表達式來匹配產品代碼和 MO 編號的組合
        # 處理格式：產品代碼 - MO編號 (允許空格)
        # MO編號通常是 [字母][數字][.]格式，限制長度避免過度捕獲
        spaced_dash_pattern = r'\b((?:G|M|AT)\d+[A-Z0-9]*)\s*-\s*([A-Z0-9.]{1,10})'
        spaced_match = re.search(spaced_dash_pattern, text, re.IGNORECASE)
        
        if spaced_match:
            product = spaced_match.group(1)
            mo_part = spaced_match.group(2)
            
            # 對 MO 編號進行清理和驗證
            # 移除末尾不屬於 MO 格式的字符（如常見的英文單詞開頭）
            mo_cleaned = mo_part
            
            # 檢查是否以常見測試術語結尾（如 OS, TEST 等），如果是則移除
            common_suffixes = ['OS', 'TEST', 'FAIL', 'PASS', 'ERROR', 'ISSUE']
            for suffix in common_suffixes:
                if mo_cleaned.upper().endswith(suffix):
                    mo_cleaned = mo_cleaned[:-len(suffix)]
                    break
            
            # 最終清理並限制長度
            mo_clean = re.sub(r'[^\w.]', '', mo_cleaned)
            mo_number = mo_clean[:9] if len(mo_clean) > 9 else mo_clean
            method = "spaced_dash_pattern"
            
            return {
                "product": product,
                "mo_number": mo_number,
                "method": method
            }
        
        # VBA: 使用正則表達式尋找產品代碼模式
        # productString = "\b(G|M|AT)\d{1}"
        product_matches = re.findall(self.product_pattern, text, re.IGNORECASE)
        
        if product_matches:
            # VBA: 在詞陣列中尋找包含產品代碼的詞
            words = text.split()
            
            for word in words:
                # 檢查這個詞是否包含找到的產品代碼
                for product_match in product_matches:
                    if product_match.upper() in word.upper():
                        # VBA: 如果長度 > 0，分離產品代碼
                        # words2 = Split(product, "-")
                        if "-" in word:
                            parts = word.split("-")
                            if len(parts) >= 2:
                                # 清理產品代碼（移除括號等符號）
                                product_part = parts[0]
                                # 移除常見的標點符號
                                product = re.sub(r'[^\w]', '', product_part)
                                
                                # VBA: moString = Left(words2(1), 9)
                                mo_part = parts[1]
                                # 取前9個字符並清理
                                mo_clean = re.sub(r'[^\w.]', '', mo_part)
                                mo_number = mo_clean[:9] if len(mo_clean) > 9 else mo_clean
                                
                                method = "dash_separated_pattern"
                                break
                        else:
                            # 如果沒有 "-"，整個詞作為產品代碼
                            product = re.sub(r'[^\w]', '', word)
                            method = "single_word_pattern"
                            break
                
                if product:  # 如果已找到產品，跳出外層循環
                    break
        
        # 如果沒有找到標準模式，嘗試尋找其他 NFME 特徵
        if not product and not mo_number:
            # 尋找可能的產品-MO 組合模式
            combined_pattern = r'\b([GM]\d{4}[A-Z0-9]+)-([A-Z0-9.]+)\b'
            combined_match = re.search(combined_pattern, text, re.IGNORECASE)
            
            if combined_match:
                product = combined_match.group(1)
                mo_number = combined_match.group(2)[:9]  # 限制在9個字符
                method = "combined_pattern_regex"
        
        return {
            "product": product,
            "mo_number": mo_number,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'Product-MO dash separated format (G5619RZ1U-H4XU50.1F)',
                'Spaced dash separated format (G5619RZ1U - HB6P24.1)',
                'Single word product format',
                'Combined regex pattern matching'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'chinese_support': True,
            'based_on': 'VBA NFMEInfoFromStrings',
            'extraction_capabilities': [
                'Product code extraction using (G|M|AT)\\d+ pattern',
                'MO number extraction from dash-separated format (first 9 chars)',
                'Punctuation cleanup for cleaner extraction',
                'Multiple pattern fallback mechanisms'
            ],
            'special_features': [
                'Dash-separated parsing logic with space tolerance',
                'Length-limited MO extraction (9 chars max)',
                'Symbol cleanup for better matching',
                'Test keyword suffix removal (OS, TEST, FAIL, etc.)',
                'Multiple identification methods',
                'NFME_REQUIRED_FIELDS=PD,LOT compliance'
            ]
        }