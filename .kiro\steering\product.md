# 產品概述

## Outlook 摘要系統

這是一個**半導體測試資料郵件處理系統**，從 VBA Excel 遷移至 Python。系統自動化處理來自多個半導體測試廠商的郵件。

### 核心目的
- **自動化郵件處理**：監控並處理來自半導體測試廠商的郵件
- **資料萃取**：解析並提取郵件附件中的關鍵資訊（MO、LOT、良率等）
- **報表生成**：自動產生 Excel 摘要報表
- **檔案管理**：下載、解壓縮、轉換各種檔案格式
- **廠商整合**：支援多個廠商的不同資料格式

### 支援廠商
- **ETD**：透過 `anf` 關鍵字識別
- **GTK**：透過 `ft hold`、`ft lot` 關鍵字識別
- **JCET**：透過 `jcet` 關鍵字識別
- **LINGSEN**：透過 `lingsen` 關鍵字識別
- **LLM**：專用解析器
- **XAHT**：透過 `tianshui`、`西安` 關鍵字識別


### 主要功能
- 透過 POP3/Outlook 整合進行即時郵件監控
- 智慧廠商識別與路由
- 多格式檔案處理（CSV、Excel、壓縮檔案）
- 統計分析與趨勢監控
- 網頁管理介面
- RESTful API 整合

### 商業價值
- **自動化**：消除手動郵件處理工作流程
- **準確性**：減少資料萃取的人為錯誤
- **效率**：一致性處理多個廠商格式
- **監控**：提供測試作業的即時可視性
- **擴展性**：自動處理增加的郵件量


### 專案規模
- **程式碼行數**: 67,675 行 Python 代碼
- **支援廠商**: 6 個半導體測試廠商
- **開發歷程**: 112 次提交
- **測試覆蓋**: 2562 個測試檔案

### 目標使用者
- 半導體測試作業團隊
- 品質保證工程師
- 生產管理人員
- 資料分析師