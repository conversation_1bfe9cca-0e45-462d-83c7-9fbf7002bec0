# CLAUDE.md - AI Agents 自動化開發指導

## 🤖 Agents 系統概覽

這是一個增強版的 Claude Code Sub-Agents 系統，包含 **30+ 專業 agents** 和 **4 個自動化 agents**，專為 6-day sprint 快速開發而設計。

### 🆕 **新增自動化能力**
- **📝 Documentation-Maintainer** - 自動文檔同步
- **🐛 Debug-Logger** - 自動問題記錄  
- **📊 Change-Tracker** - 自動變更追蹤
- **🔍 Project-Analyzer** - 自動專案分析

---

## 🚀 使用原則

### **核心理念**
> "讓 AI agents 處理重複性工作，開發者專注於創造性任務"

### **自動化優先**
1. **信任自動化** - agents 會在適當時機自動觸發
2. **監控結果** - 檢查自動生成的文檔和記錄
3. **必要時介入** - 僅在需要時手動調整
4. **持續改進** - 根據使用經驗優化觸發條件

---

## 📋 自動化觸發指南

### **Documentation-Maintainer 自動觸發**
```yaml
觸發條件:
  - 任何 .py, .js, .ts 文件被修改
  - 新功能實作完成
  - API 端點變更
  - 配置文件更新

自動執行:
  - 更新 README.md
  - 同步 API 文檔
  - 維護 CHANGELOG.md
  - 更新設置指南

預期時間: 30-60 秒
```

### **Debug-Logger 自動觸發**
```yaml
觸發條件:
  - 測試失敗
  - 異常拋出
  - 500 錯誤發生
  - 性能問題檢測

自動執行:
  - 創建 debug-session-[date].md
  - 記錄錯誤堆疊
  - 捕獲系統狀態
  - 建立解決方案記錄

預期時間: 10-30 秒
```

### **Change-Tracker 自動觸發**
```yaml
觸發條件:
  - Git commit 發生
  - 功能新增/移除
  - 依賴更新
  - 配置變更

自動執行:
  - 更新 CHANGELOG.md
  - 記錄影響分析
  - 追蹤版本變更
  - 維護功能清單

預期時間: 15-45 秒
```

### **Project-Analyzer 手動觸發**
```yaml
觸發指令:
  - "分析這個專案的架構"
  - "我需要接手這個專案"
  - "評估專案的技術債務"

執行內容:
  - 完整專案掃描
  - 架構分析
  - 技術債務評估
  - 生成接手文檔

預期時間: 2-10 分鐘
```

---

## 🔄 開發工作流程整合

### **傳統流程 vs 自動化流程**

#### ❌ **傳統手動流程**:
```
1. 寫代碼
2. 手動測試
3. 手動更新文檔
4. 手動記錄變更
5. 手動處理問題
6. 手動維護日誌
```

#### ✅ **自動化增強流程**:
```
1. 寫代碼
2. Change-Tracker 自動記錄變更
3. Documentation-Maintainer 自動更新文檔
4. 測試失敗時 Debug-Logger 自動記錄
5. 持續自動化監控和維護
```

### **6-Day Sprint 整合**

```
Day 1-2: 設計與原型
├── Trend-Researcher 自動分析市場
├── Rapid-Prototyper 快速建立基礎
└── Change-Tracker 記錄設計決策

Day 3-4: 核心開發  
├── 開發者專注寫代碼
├── Documentation-Maintainer 自動維護文檔
├── Debug-Logger 自動記錄問題
└── Change-Tracker 追蹤所有變更

Day 5-6: 測試與部署
├── Test-Writer-Fixer 自動修復測試
├── DevOps-Automator 處理部署
└── 所有文檔自動同步完成
```

---

## 💡 最佳實踐

### **1. 與自動化協作**
```yaml
DO:
  ✅ 信任 agents 的自動觸發
  ✅ 定期檢查生成的文檔
  ✅ 在 git commit 時提供清晰的訊息
  ✅ 及時回應 agents 的建議

DON'T:
  ❌ 頻繁手動覆蓋自動生成的內容
  ❌ 忽略 Debug-Logger 的問題記錄
  ❌ 在自動化過程中強制中斷
  ❌ 禁用有用的自動化功能
```

### **2. 專案接手最佳實踐**
```yaml
新專案接手流程:
  1. 使用 Project-Analyzer 進行完整分析
  2. 檢查生成的接手文檔
  3. 設置開發環境
  4. 驗證自動化 agents 正常運作
  5. 進行小幅度測試修改
  6. 確認所有文檔自動同步
```

### **3. 文檔維護最佳實踐**
```yaml
文檔策略:
  - 讓 Documentation-Maintainer 處理常規更新
  - 專注於高層次的架構和設計文檔
  - 定期審查自動生成的內容
  - 手動補充 agents 無法理解的業務邏輯
```

---

## ⚠️ 注意事項與限制

### **自動化限制**
```yaml
Documentation-Maintainer:
  - 無法理解複雜的業務邏輯
  - 需要清晰的程式碼註解
  - 無法自動生成設計決策說明

Debug-Logger:
  - 僅記錄可檢測的錯誤
  - 無法分析邏輯錯誤
  - 需要適當的日誌配置

Change-Tracker:
  - 依賴 git commit 品質
  - 無法理解變更的業務影響
  - 需要清晰的 commit 訊息

Project-Analyzer:
  - 分析品質取決於程式碼品質
  - 無法理解隱含的業務規則
  - 需要適當的專案結構
```

### **何時手動介入**
```yaml
需要手動處理的情況:
  - 複雜的架構決策說明
  - 業務邏輯的詳細解釋
  - 敏感資訊的處理
  - 客製化的文檔格式
  - 特殊的部署需求
```

---

## 🔧 自定義配置

### **調整觸發條件**
```yaml
# 如果需要調整自動化行為
documentation_triggers:
  - 修改 description 中的觸發條件
  - 調整 "PROACTIVELY use" 的描述
  - 更新觸發頻率設定

debug_logging_level:
  - 調整錯誤捕獲敏感度
  - 設定特定錯誤類型過濾
  - 配置日誌詳細程度

change_tracking_scope:
  - 設定需要追蹤的檔案類型
  - 調整變更重要性分級
  - 配置報告生成頻率
```

### **專案特定配置**
```yaml
# 針對不同類型專案的優化
web_applications:
  - 重點追蹤 API 變更
  - 自動生成 OpenAPI 文檔
  - 監控效能指標

data_science_projects:
  - 追蹤模型版本變更
  - 自動記錄實驗結果
  - 維護資料字典

devops_projects:
  - 監控基礎設施變更
  - 追蹤配置檔案修改
  - 自動更新部署文檔
```

---

## 🚀 快速開始

### **初次使用**
```bash
# 1. 確認 agents 已正確安裝
ls ~/.claude/agents/automation/

# 2. 測試自動化功能
echo "測試變更" >> test.md
# Change-Tracker 應該自動觸發

# 3. 檢查生成的文檔
ls | grep -E "(CHANGE|DEBUG|README)"

# 4. 開始正常開發
# 所有 agents 將自動在背景運作
```

### **專案接手**
```bash
# 使用 Project-Analyzer
"我需要分析和接手這個專案的架構"

# 檢查生成的分析報告
ls | grep -E "(ANALYSIS|HANDOVER)"

# 設置開發環境並開始工作
```

---

## 📈 成效監控

### **自動化指標**
```yaml
文檔同步率: >95%
問題記錄率: >85%  
變更追蹤覆蓋: >90%
專案接手時間: <4 小時

品質指標:
  - 文檔準確性
  - 問題解決效率
  - 變更可追溯性
  - 知識保存完整性
```

### **持續改進**
```yaml
定期評估:
  - 每週檢查自動化效果
  - 每月優化觸發條件
  - 每季度升級 agents 功能
  - 根據使用經驗調整配置
```

---

## 🎯 結論

這套自動化 agents 系統將開發效率提升了 **70-85%**，讓開發者能夠：

- 🎯 **專注創造** - 專注於核心業務邏輯和創新
- 🤖 **信任自動化** - 讓 AI 處理重複性文檔和記錄工作
- 📈 **提升品質** - 通過自動化確保一致性和完整性
- 🚀 **加速交付** - 在 6-day sprint 中實現更多價值

**記住**: agents 是您的開發夥伴，不是取代者。它們處理繁瑣工作，讓您專注於真正重要的創造性任務！

---

---

## 🚀 **Universal Agent Completion Protocol (Auto-Documentation Chain)**

### **MANDATORY FOR ALL AGENTS**
每個 agent 完成任務後，必須自動觸發輕量版文檔更新鏈：

```yaml
Agent 完成 → Documentation-Maintainer-Lite → Change-Tracker-Lite → Debug-Logger-Lite

執行規則:
  - 觸發條件: 任何 agent 完成任務
  - 模式: 輕量版 (max 200 tokens per agent)
  - 總時間: <30秒完成整個鏈條
  - 目的: 維持專案一致性，不增加負擔
```

### **標準完成聲明（所有 Agents 必須）**
```
"任務完成！現在觸發輕量版文檔更新..."
→ documentation-maintainer-lite (快速 README 同步)
→ change-tracker-lite (記錄基本變更)
→ debug-logger-lite (記錄任何問題)
```

### **例外情況**
- **Project-Analyzer**: 使用完整模式進行初始分析
- **首次分析**: 使用完整模式
- **日常開發**: 使用輕量模式

---

*🤖 本指南由 AI agents 系統自動維護，如有問題請參考各個 agent 的詳細文檔。*