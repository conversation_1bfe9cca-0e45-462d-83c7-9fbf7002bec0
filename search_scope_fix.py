# Enhanced search scope implementation
def enhanced_search_directories(search_path, product_name, comprehensive_search=True):
    """Enhanced directory search with comprehensive coverage"""
    found_folders = []
    
    try:
        logger.info(f"🔍 開始增強搜尋產品: {product_name}")
        
        # 階段1: 搜尋根目錄
        logger.info("📁 階段1: 搜尋根目錄")
        try:
            items = list(search_path.iterdir())
            logger.info(f"📊 根目錄包含 {len(items)} 個項目")
            
            for item in items:
                if item.is_dir():
                    if is_product_match(product_name, item.name):
                        found_folders.append(item)
                        logger.info(f"✅ 在根目錄找到匹配: {item.name}")
        except Exception as e:
            logger.warning(f"❌ 搜尋根目錄失敗: {e}")
        
        # 階段2: 搜尋 ETD/FT 目錄
        logger.info("📁 階段2: 搜尋 ETD/FT 目錄")
        etd_path = search_path / "ETD" / "FT"
        try:
            if etd_path.exists():
                ft_items = list(etd_path.iterdir())
                logger.info(f"📊 ETD/FT 目錄包含 {len(ft_items)} 個項目")
                
                for item in ft_items:
                    if item.is_dir():
                        if is_product_match(product_name, item.name):
                            found_folders.append(item)
                            logger.info(f"✅ 在 ETD/FT 找到匹配: {item.name}")
        except Exception as e:
            logger.warning(f"❌ 搜尋 ETD/FT 目錄失敗: {e}")
        
        # 階段3: 搜尋所有重要目錄 (擴大範圍)
        logger.info("📁 階段3: 搜尋所有重要目錄")
        important_dirs = [
            "GTK", "JCAP", "JCET", "JSSI", "JCAP_JCET", 
            "AMAT", "ASML", "LAM", "TEL", "KLA",  # 設備廠商
            "EQC", "FT", "QC", "TEST", "DATA",    # 測試相關
            "PRODUCTION", "DEVELOPMENT", "ARCHIVE" # 其他可能目錄
        ]
        
        for dir_name in important_dirs:
            try:
                dir_path = search_path / dir_name
                if dir_path.exists():
                    # 搜尋第一層子目錄
                    sub_items = list(dir_path.iterdir())
                    logger.debug(f"📊 {dir_name} 目錄包含 {len(sub_items)} 個項目")
                    
                    for item in sub_items:
                        if item.is_dir():
                            if is_product_match(product_name, item.name):
                                found_folders.append(item)
                                logger.info(f"✅ 在 {dir_name} 找到匹配: {item.name}")
                            
                            # 搜尋第二層子目錄
                            if comprehensive_search:
                                try:
                                    for sub_item in item.iterdir():
                                        if sub_item.is_dir():
                                            if is_product_match(product_name, sub_item.name):
                                                found_folders.append(sub_item)
                                                logger.info(f"✅ 在 {dir_name}/{item.name} 找到匹配: {sub_item.name}")
                                except (PermissionError, OSError):
                                    continue
            except Exception as e:
                logger.warning(f"❌ 搜尋 {dir_name} 目錄失敗: {e}")
        
        # 階段4: 深度遞迴搜尋 (如果前面找到的結果不足)
        if comprehensive_search and len(found_folders) < 5:
            logger.info("📁 階段4: 深度遞迴搜尋所有目錄")
            try:
                max_depth = 3  # 限制搜尋深度避免過慢
                for root, dirs, files in os.walk(search_path):
                    current_depth = root.replace(str(search_path), '').count(os.sep)
                    if current_depth >= max_depth:
                        dirs.clear()  # 不再深入搜尋
                        continue
                    
                    for dirname in dirs:
                        if is_product_match(product_name, dirname):
                            dir_path = Path(root) / dirname
                            if dir_path not in found_folders:
                                found_folders.append(dir_path)
                                logger.info(f"✅ 深度搜尋找到: {dir_path}")
                        
                        # 限制結果數量避免過多
                        if len(found_folders) >= 20:
                            break
                    
                    if len(found_folders) >= 20:
                        break
            except Exception as e:
                logger.warning(f"❌ 深度搜尋失敗: {e}")
        
        logger.info(f"🎯 增強搜尋完成，總共找到 {len(found_folders)} 個匹配資料夾")
        return found_folders
        
    except Exception as e:
        logger.error(f"增強搜尋過程發生錯誤: {e}")
        return []

def is_product_match(product_name, folder_name, fuzzy_match=True):
    """Enhanced product matching with fuzzy matching support"""
    product_lower = product_name.lower().strip()
    folder_lower = folder_name.lower().strip()
    
    # 1. Exact match
    if product_lower == folder_lower:
        return True
    
    # 2. Simple substring match
    if product_lower in folder_lower:
        return True
    
    if not fuzzy_match:
        return False
    
    # 3. Fuzzy matching for common patterns
    
    # Remove common suffixes/prefixes
    clean_product = product_lower.rstrip('\\').rstrip('/').strip()
    clean_folder = folder_lower.rstrip('\\').rstrip('/').strip()
    
    # Match with cleaned names
    if clean_product in clean_folder:
        return True
    
    # 4. Handle special characters
    # Replace backslashes, underscores, dashes
    normalized_product = clean_product.replace('\\', '').replace('_', '').replace('-', '')
    normalized_folder = clean_folder.replace('\\', '').replace('_', '').replace('-', '')
    
    if normalized_product in normalized_folder:
        return True
    
    # 5. Partial matching for long product names
    if len(clean_product) >= 6:  # Only for reasonably long product names
        # Try matching first 6 characters
        if clean_folder.startswith(clean_product[:6]):
            return True
        
        # Try matching without last character (typos)
        if clean_product[:-1] in clean_folder:
            return True
    
    return False