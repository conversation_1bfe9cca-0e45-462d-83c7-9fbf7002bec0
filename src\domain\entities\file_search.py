"""檔案搜尋領域實體"""

from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional
from enum import Enum


class SearchStatus(Enum):
    """搜尋狀態枚舉"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class TimeRange:
    """時間範圍值物件"""
    start_date: datetime
    end_date: datetime
    
    @classmethod
    def last_months(cls, months: int) -> 'TimeRange':
        """建立最近N個月的時間範圍"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        return cls(start_date, end_date)
    
    @classmethod
    def last_days(cls, days: int) -> 'TimeRange':
        """建立最近N天的時間範圍"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        return cls(start_date, end_date)
    
    @classmethod
    def current_quarter(cls) -> 'TimeRange':
        """建立當前季度的時間範圍"""
        now = datetime.now()
        quarter = (now.month - 1) // 3 + 1
        start_month = (quarter - 1) * 3 + 1
        start_date = datetime(now.year, start_month, 1)
        
        if quarter == 4:
            end_date = datetime(now.year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(now.year, start_month + 3, 1) - timedelta(days=1)
        
        return cls(start_date, end_date)
    
    def contains(self, check_date: datetime) -> bool:
        """檢查日期是否在時間範圍內"""
        return self.start_date <= check_date <= self.end_date


@dataclass
class SearchFilters:
    """搜尋篩選條件"""
    time_range: Optional[TimeRange] = None
    file_types: Optional[List[str]] = None
    min_size: Optional[int] = None
    max_size: Optional[int] = None
    include_directories: bool = True
    search_directory: str = "auto"  # 指定搜尋目錄
    
    def matches_file(self, file_path: Path, file_size: int, modified_time: datetime) -> bool:
        """檢查檔案是否符合篩選條件"""
        # 時間範圍檢查
        if self.time_range and not self.time_range.contains(modified_time):
            return False
        
        # 檔案類型檢查
        if self.file_types:
            file_ext = file_path.suffix.lower()
            if file_ext not in self.file_types:
                return False
        
        # 檔案大小檢查
        if self.min_size is not None and file_size < self.min_size:
            return False
        
        if self.max_size is not None and file_size > self.max_size:
            return False
        
        return True


@dataclass
class FileInfo:
    """檔案資訊實體"""
    path: Path
    name: str
    size: int
    modified_time: datetime
    file_type: str
    is_directory: bool
    
    @property
    def size_mb(self) -> float:
        """檔案大小（MB）"""
        return round(self.size / (1024 * 1024), 2)


@dataclass
class ProductSearchResult:
    """產品搜尋結果實體"""
    product_name: str
    product_folder: Path
    matched_files: List[FileInfo]
    total_files: int
    total_files_in_directory: int = 0  # 目錄中的總檔案數（不含篩選條件）
    search_duration: float = 0.0
    filters_applied: Optional[SearchFilters] = None
    status: SearchStatus = SearchStatus.PENDING
    error_message: Optional[str] = None
    
    @property
    def total_size(self) -> int:
        """總檔案大小（位元組）"""
        return sum(f.size for f in self.matched_files if not f.is_directory)
    
    @property
    def total_size_mb(self) -> float:
        """總檔案大小（MB）"""
        return round(self.total_size / (1024 * 1024), 2)
    
    @property
    def success(self) -> bool:
        """搜尋是否成功"""
        return self.status == SearchStatus.COMPLETED and self.error_message is None


@dataclass
class SearchTask:
    """搜尋任務實體"""
    task_id: str
    product_name: str
    base_path: Path
    filters: SearchFilters
    created_at: datetime
    status: SearchStatus
    result: Optional[ProductSearchResult] = None
    
    def is_completed(self) -> bool:
        """檢查任務是否完成"""
        return self.status in [SearchStatus.COMPLETED, SearchStatus.FAILED]
    
    def is_successful(self) -> bool:
        """檢查任務是否成功完成"""
        return self.status == SearchStatus.COMPLETED and self.result is not None