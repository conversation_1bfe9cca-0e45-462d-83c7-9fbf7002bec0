# POP3 郵件伺服器設定
POP3_SERVER=pop.gmail.com
POP3_PORT=995
POP3_USERNAME=<EMAIL>
POP3_PASSWORD=your_app_password
POP3_USE_SSL=True

# 資料庫設定
DB_PATH=./emails.db

# 日誌設定
LOG_LEVEL=INFO
LOG_FILE=./logs/email_inbox.log

# 統一 LLM 配置
# LLM 提供者選擇: ollama 或 grok
LLM_PROVIDER=ollama

# Ollama 配置 (當 LLM_PROVIDER=ollama 時使用)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3:latest
LLM_TIMEOUT=30
LLM_MAX_RETRIES=3

# Grok 配置 (當 LLM_PROVIDER=grok 時使用)
GROK_API_KEY=your_grok_api_key_here
GROK_MODEL=grok-beta
GROK_BASE_URL=https://api.x.ai/v1

# 通用 LLM 配置
LLM_PARSING_ENABLED=true
LLM_PARSING_MODE=fallback
LLM_CONFIDENCE_THRESHOLD=0.7

# AI 模型配置
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EXPECTED_VECTOR_DIMENSION=384