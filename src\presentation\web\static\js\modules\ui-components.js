// UI 組件模組
// 處理狀態顯示、通知和 UI 互動元素

export class UIComponents {
    constructor() {
        this.statusIcons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            loading: 'spinner fa-spin'
        };
        
        this.smartSearchStatusColors = {
            success: 'rgba(40, 167, 69, 0.8)',
            error: 'rgba(231, 76, 60, 0.8)',
            loading: 'rgba(255, 255, 255, 0.8)'
        };
    }

    // 顯示主要狀態訊息
    showStatus(msg, type = 'loading') {
        const statusElement = document.getElementById('status');
        if (!statusElement) return;

        const icon = this.statusIcons[type] || this.statusIcons.loading;
        statusElement.innerHTML = `<div class="status ${type}"><i class="fas fa-${icon}"></i> ${msg}</div>`;
    }

    // 顯示產品搜尋狀態
    showProductSearchStatus(msg, type = 'loading') {
        const statusDiv = document.getElementById('productSearchStatus');
        if (!statusDiv) return;

        const icon = this.statusIcons[type] || this.statusIcons.loading;
        statusDiv.innerHTML = `<div class="status ${type}"><i class="fas fa-${icon}"></i> ${msg}</div>`;
    }

    // 顯示智慧搜尋狀態
    showSmartSearchStatus(msg, type = 'loading') {
        const statusDiv = document.getElementById('smartSearchStatus');
        if (!statusDiv) return;

        const icon = this.statusIcons[type] || this.statusIcons.loading;
        const color = this.smartSearchStatusColors[type] || this.smartSearchStatusColors.loading;
        
        statusDiv.innerHTML = `<div style="padding:10px;background:${color};border-radius:var(--radius);color:white;"><i class="fas fa-${icon}"></i> ${msg}</div>`;
    }

    // 顯示智慧搜尋查詢解析
    displaySmartSearchInterpretation(interpretation) {
        const div = document.getElementById('smartSearchInterpretation');
        if (!div) return;

        let html = '<h5 style="margin-bottom:10px;color:white;"><i class="fas fa-lightbulb"></i> 查詢解析</h5>';

        if (interpretation.product_names && interpretation.product_names.length > 0) {
            html += `<p><strong>識別的產品:</strong> ${interpretation.product_names.join(', ')}</p>`;
        }

        if (interpretation.time_range) {
            const timeRangeMap = {
                'last_week': '最近一週',
                'last_month': '最近一個月',
                'last_3_months': '最近三個月',
                'last_6_months': '最近六個月',
                'current_quarter': '本季',
                'custom': '自訂範圍'
            };
            html += `<p><strong>時間範圍:</strong> ${timeRangeMap[interpretation.time_range] || interpretation.time_range}</p>`;
        }

        if (interpretation.file_types && interpretation.file_types.length > 0) {
            html += `<p><strong>檔案類型:</strong> ${interpretation.file_types.join(', ')}</p>`;
        }

        if (interpretation.confidence) {
            const confidencePercent = (interpretation.confidence * 100).toFixed(0);
            html += `<p><strong>解析信心度:</strong> ${confidencePercent}%</p>`;
        }

        div.innerHTML = html;
    }

    // 顯示智慧搜尋分析結果
    displaySmartSearchAnalysis(analysis) {
        const div = document.getElementById('smartSearchAnalysis');
        if (!div) return;

        let html = '<h5 style="margin-bottom:10px;color:white;"><i class="fas fa-chart-bar"></i> 搜尋分析</h5>';
        html += `<p><strong>摘要:</strong> ${analysis.summary}</p>`;

        if (analysis.total_products > 0) {
            html += `<p><strong>產品數量:</strong> ${analysis.total_products}</p>`;
            html += `<p><strong>檔案數量:</strong> ${analysis.total_files}</p>`;
            html += `<p><strong>總大小:</strong> ${analysis.total_size_mb} MB</p>`;

            if (analysis.file_types) {
                const fileTypesList = Object.entries(analysis.file_types)
                    .map(([type, count]) => `${type} (${count})`)
                    .join(', ');
                html += `<p><strong>檔案類型分佈:</strong> ${fileTypesList}</p>`;
            }
        }

        if (analysis.llm_insights) {
            html += `<p><strong>AI 洞察:</strong> ${analysis.llm_insights.data_quality || '無額外洞察'}</p>`;
        }

        div.innerHTML = html;
    }

    // 顯示智慧搜尋建議
    displaySmartSearchSuggestions(suggestions) {
        const div = document.getElementById('smartSearchSuggestions');
        if (!div) return;

        if (!suggestions || suggestions.length === 0) {
            div.innerHTML = '<h5 style="margin-bottom:10px;color:white;"><i class="fas fa-lightbulb"></i> 建議</h5><p>暫無建議</p>';
            return;
        }

        let html = '<h5 style="margin-bottom:10px;color:white;"><i class="fas fa-lightbulb"></i> 建議</h5>';

        suggestions.forEach(suggestion => {
            const priorityIcon = suggestion.priority === 'high' ? '🔥' :
                               suggestion.priority === 'medium' ? '⚡' : '💡';

            html += `
                <div style="margin-bottom:8px;padding:8px;background:rgba(255,255,255,0.1);border-radius:4px;">
                    <strong>${priorityIcon} ${suggestion.title}</strong><br>
                    <small>${suggestion.description}</small>
                </div>
            `;
        });

        div.innerHTML = html;
    }

    // 顯示元素
    showElement(elementId, display = 'block') {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.display = display;
        }
    }

    // 隱藏元素
    hideElement(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.display = 'none';
        }
    }

    // 切換元素可見性
    toggleElement(elementId, display = 'block') {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.display = element.style.display === 'none' ? display : 'none';
        }
    }

    // 啟用/禁用按鈕
    setButtonState(buttonId, enabled) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = !enabled;
        }
    }

    // 設定文字內容
    setTextContent(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }

    // 設定 HTML 內容
    setHtmlContent(elementId, html) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = html;
        }
    }

    // 取得輸入值
    getInputValue(inputId) {
        const input = document.getElementById(inputId);
        return input ? input.value.trim() : '';
    }

    // 設定輸入值
    setInputValue(inputId, value) {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = value;
        }
    }

    // 清除輸入值
    clearInput(inputId) {
        this.setInputValue(inputId, '');
    }

    // 切換自訂日期範圍顯示
    toggleCustomDateRange() {
        const timeRange = this.getSelectValue('timeRangeSelect');
        const customDateRange = document.getElementById('customDateRange');

        if (customDateRange) {
            if (timeRange === 'custom') {
                customDateRange.style.display = 'grid';
            } else {
                customDateRange.style.display = 'none';
            }
        }
    }

    // 取得選擇框值
    getSelectValue(selectId) {
        const select = document.getElementById(selectId);
        return select ? select.value : '';
    }

    // 取得多選框選中的值
    getMultiSelectValues(selectId) {
        const select = document.getElementById(selectId);
        if (!select) return [];
        
        return Array.from(select.selectedOptions).map(option => option.value);
    }

    // 取得複選框狀態
    getCheckboxState(checkboxId) {
        const checkbox = document.getElementById(checkboxId);
        return checkbox ? checkbox.checked : false;
    }

    // 設定複選框狀態
    setCheckboxState(checkboxId, checked) {
        const checkbox = document.getElementById(checkboxId);
        if (checkbox) {
            checkbox.checked = checked;
        }
    }

    // 清除搜尋表單
    clearProductSearchForm() {
        this.clearInput('productNameInput');
        this.setSelectValue('searchDirectorySelect', 'auto');
        this.setSelectValue('timeRangeSelect', 'last_6_months');
        
        const fileTypeSelect = document.getElementById('fileTypeSelect');
        if (fileTypeSelect) {
            fileTypeSelect.selectedIndex = -1;
        }
        
        this.clearInput('minSizeInput');
        this.clearInput('maxSizeInput');
        this.setCheckboxState('includeDirsCheckbox', true);
        this.setInputValue('maxResultsInput', '1000');
        this.clearInput('customStartDate');
        this.clearInput('customEndDate');
        this.hideElement('customDateRange');
        
        // 清除搜尋狀態
        this.setHtmlContent('productSearchStatus', '');
    }

    // 清除智慧搜尋表單
    clearSmartSearchForm() {
        this.clearInput('smartSearchInput');
        this.setInputValue('smartMaxResultsInput', '100');
        
        // 清除搜尋狀態和結果
        this.setHtmlContent('smartSearchStatus', '');
        this.hideElement('smartSearchResults');
        this.setHtmlContent('smartSearchInterpretation', '');
        this.setHtmlContent('smartSearchAnalysis', '');
        this.setHtmlContent('smartSearchSuggestions', '');
    }

    // 設定選擇框值
    setSelectValue(selectId, value) {
        const select = document.getElementById(selectId);
        if (select) {
            select.value = value;
        }
    }

    // 調試日誌（保持與原始邏輯一致）
    debugLog(msg) {
        console.log('[DEBUG]', msg);
        this.showStatus(msg, 'loading');
    }

    // 登出功能
    logout() {
        location.reload();
    }

    // 觸發檔案下載
    triggerDownload(url, filename) {
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }
}