#!/usr/bin/env python3
"""
<PERSON>s 簡化版主要控制器
檔案大小檢查和拆分建議系統
"""

import sys
import os
import asyncio
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

class ClaudeHooksManager:
    """<PERSON> Hooks 簡化版管理器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any] = None):
        self.repo_root = Path(repo_root)
        self.claude_dir = self.repo_root / '.claude'
        self.config = config or self._get_default_config()
        
        # 設定日誌
        self._setup_logging()
        
        # 建立必要目錄
        self._ensure_directories()
    
    def _setup_logging(self):
        """設定日誌系統"""
        log_dir = self.claude_dir / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'claude_hooks.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('ClaudeHooks')
    
    def _ensure_directories(self):
        """確保必要的目錄存在"""
        directories = [
            self.claude_dir / 'logs',
            self.claude_dir / 'reports',
            self.claude_dir / 'reports' / 'analysis_reports',
            self.claude_dir / 'reports' / 'refactor_plans'
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取預設配置"""
        return {
            'file_size_limit': 500,
            'warning_threshold': 400,
            'languages': ['python'],
            'analysis': {
                'enabled': True,
                'check_file_size': True,
                'generate_suggestions': True
            }
        }
    
    async def analyze_project(self, changed_files: Optional[List[str]] = None) -> Dict[str, Any]:
        """分析專案，檢測需要優化的問題"""
        self.logger.info("🔍 開始專案分析...")
        
        analysis_results = {
            'timestamp': datetime.now().isoformat(),
            'file_size_analysis': {},
            'suggestions': []
        }
        
        # 檔案大小分析
        if self.config.get('analysis', {}).get('check_file_size', True):
            self.logger.info("📏 檢查檔案大小...")
            size_analysis = await self._analyze_file_sizes(changed_files)
            analysis_results['file_size_analysis'] = size_analysis
        
        # 生成建議
        if self.config.get('analysis', {}).get('generate_suggestions', True):
            suggestions = await self._generate_suggestions(analysis_results)
            analysis_results['suggestions'] = suggestions
        
        # 儲存分析報告
        await self._save_analysis_report(analysis_results)
        
        self.logger.info("✅ 專案分析完成")
        return analysis_results
    
    async def _analyze_file_sizes(self, changed_files: Optional[List[str]] = None) -> Dict[str, Any]:
        """分析檔案大小"""
        size_limit = self.config.get('file_size_limit', 500)
        warning_threshold = self.config.get('warning_threshold', 400)
        
        # 獲取要分析的檔案
        if changed_files:
            files_to_check = [Path(f) for f in changed_files if Path(f).exists()]
        else:
            # 分析所有 Python 檔案
            files_to_check = list(self.repo_root.rglob('*.py'))
            # 排除一些目錄
            files_to_check = [f for f in files_to_check 
                            if not any(part.startswith('.') for part in f.parts)]
        
        analysis = {
            'total_files_checked': len(files_to_check),
            'oversized_files': [],
            'warning_files': [],
            'normal_files': 0
        }
        
        for file_path in files_to_check:
            if not file_path.exists():
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    line_count = len(lines)
                
                file_info = {
                    'path': str(file_path.relative_to(self.repo_root)),
                    'absolute_path': str(file_path),
                    'lines': line_count,
                    'size_bytes': file_path.stat().st_size
                }
                
                if line_count > size_limit:
                    file_info['status'] = 'oversized'
                    file_info['excess_lines'] = line_count - size_limit
                    analysis['oversized_files'].append(file_info)
                    self.logger.warning(f"📏 超大檔案: {file_info['path']} ({line_count} 行)")
                elif line_count > warning_threshold:
                    file_info['status'] = 'warning'
                    analysis['warning_files'].append(file_info)
                    self.logger.info(f"⚠️ 接近限制: {file_info['path']} ({line_count} 行)")
                else:
                    analysis['normal_files'] += 1
                    
            except Exception as e:
                self.logger.error(f"無法分析檔案 {file_path}: {e}")
        
        return analysis
    
    async def _generate_suggestions(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成改進建議"""
        suggestions = []
        
        # 檔案大小建議
        oversized_files = analysis_results.get('file_size_analysis', {}).get('oversized_files', [])
        for file_info in oversized_files:
            suggestion = await self._generate_split_suggestion(file_info)
            suggestions.append(suggestion)
        
        return suggestions
    
    async def _generate_split_suggestion(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成檔案拆分建議"""
        file_path = Path(file_info['absolute_path'])
        
        suggestion = {
            'type': 'file_split',
            'file_path': file_info['path'],
            'current_lines': file_info['lines'],
            'priority': 'high' if file_info['lines'] > 1000 else 'medium',
            'reason': f"檔案有 {file_info['lines']} 行，超過建議的 {self.config['file_size_limit']} 行限制",
            'suggestions': []
        }
        
        try:
            # 分析檔案內容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 簡單的拆分建議
            suggestions_list = []
            
            # 檢查是否有多個類別
            class_count = content.count('class ')
            if class_count > 1:
                suggestions_list.append({
                    'strategy': 'class_split',
                    'description': f'將 {class_count} 個類別拆分到不同檔案中',
                    'estimated_files': class_count,
                    'complexity': 'medium'
                })
            
            # 檢查是否有很多函數
            function_count = content.count('def ')
            if function_count > 10:
                suggestions_list.append({
                    'strategy': 'function_split',
                    'description': f'將 {function_count} 個函數按功能分組到不同模組',
                    'estimated_files': max(2, function_count // 5),
                    'complexity': 'low'
                })
            
            # 檢查是否有很多導入
            import_lines = [line for line in content.split('\n') 
                          if line.strip().startswith(('import ', 'from '))]
            if len(import_lines) > 20:
                suggestions_list.append({
                    'strategy': 'dependency_split',
                    'description': f'檔案有 {len(import_lines)} 個導入，考慮拆分功能模組',
                    'estimated_files': 3,
                    'complexity': 'high'
                })
            
            suggestion['suggestions'] = suggestions_list
            
        except Exception as e:
            suggestion['error'] = f"無法分析檔案內容: {e}"
            suggestion['suggestions'] = [{
                'strategy': 'manual_review',
                'description': '請手動檢查檔案並考慮拆分',
                'complexity': 'manual'
            }]
        
        return suggestion
    
    async def _save_analysis_report(self, analysis_results: Dict[str, Any]):
        """儲存分析報告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.claude_dir / 'reports' / 'analysis_reports' / f'analysis_{timestamp}.json'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"📄 分析報告已儲存: {report_path}")
        
        # 也儲存一個易讀的版本
        readable_path = report_path.with_suffix('.md')
        await self._save_readable_report(analysis_results, readable_path)
    
    async def _save_readable_report(self, analysis_results: Dict[str, Any], report_path: Path):
        """儲存易讀的 Markdown 報告"""
        content = []
        content.append("# Claude Hooks 分析報告")
        content.append(f"\n**分析時間：** {analysis_results['timestamp']}")
        content.append("\n## 📊 檔案大小分析")
        
        file_analysis = analysis_results.get('file_size_analysis', {})
        total_files = file_analysis.get('total_files_checked', 0)
        oversized = len(file_analysis.get('oversized_files', []))
        warning = len(file_analysis.get('warning_files', []))
        normal = file_analysis.get('normal_files', 0)
        
        content.append(f"\n- **總檔案數：** {total_files}")
        content.append(f"- **超大檔案：** {oversized} 個")
        content.append(f"- **警告檔案：** {warning} 個")
        content.append(f"- **正常檔案：** {normal} 個")
        
        # 超大檔案詳情
        if oversized > 0:
            content.append("\n### ⚠️ 需要注意的超大檔案")
            for file_info in file_analysis.get('oversized_files', []):
                content.append(f"\n#### {file_info['path']}")
                content.append(f"- **行數：** {file_info['lines']}")
                content.append(f"- **檔案大小：** {file_info['size_bytes']} bytes")
                content.append(f"- **超出行數：** {file_info['excess_lines']}")
        
        # 改進建議
        suggestions = analysis_results.get('suggestions', [])
        if suggestions:
            content.append("\n## 💡 改進建議")
            for i, suggestion in enumerate(suggestions, 1):
                content.append(f"\n### 建議 {i}: {suggestion['file_path']}")
                content.append(f"- **優先級：** {suggestion['priority']}")
                content.append(f"- **原因：** {suggestion['reason']}")
                
                if suggestion.get('suggestions'):
                    content.append("- **拆分策略：**")
                    for strategy in suggestion['suggestions']:
                        content.append(f"  - **{strategy['strategy']}:** {strategy['description']}")
                        content.append(f"    - 預估檔案數: {strategy.get('estimated_files', 'N/A')}")
                        content.append(f"    - 複雜度: {strategy.get('complexity', 'N/A')}")
        
        content.append("\n---")
        content.append("*此報告由 Claude Code Hooks 自動生成*")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
        
        self.logger.info(f"📝 易讀報告已儲存: {report_path}")

async def main():
    """主函數"""
    repo_root = Path(__file__).parent.parent.parent
    manager = ClaudeHooksManager(repo_root)
    
    results = await manager.analyze_project()
    print("📊 分析完成！")
    
    # 顯示摘要
    file_analysis = results.get('file_size_analysis', {})
    oversized = len(file_analysis.get('oversized_files', []))
    if oversized > 0:
        print(f"⚠️ 發現 {oversized} 個檔案超過大小限制")
        for file_info in file_analysis.get('oversized_files', []):
            print(f"   - {file_info['path']} ({file_info['lines']} 行)")
    else:
        print("✅ 所有檔案大小都在合理範圍內")

if __name__ == '__main__':
    asyncio.run(main())
