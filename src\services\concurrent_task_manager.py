"""
企業級並發任務管理器 - 精簡版
符合 CLAUDE.md 500行限制要求
"""

import os
import sys
import json
import uuid
import time
import threading
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable
from concurrent.futures import Future

# 導入核心模組
from .concurrent_task_core import (
    TaskStatus, TaskPriority, TaskInfo, TaskExecutor, 
    TaskRegistry, TaskStatusTracker, DEFAULT_HANDLERS
)

# 動態添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from src.infrastructure.logging.logger_manager import LoggerManager
from src.infrastructure.adapters.notification.line_notification_service import LineNotificationService


class ConcurrentTaskManager:
    """
    企業級並發任務管理器 - 精簡版
    
    核心功能：
    - 非阻塞任務提交
    - 任務狀態追蹤
    - 企業級錯誤處理
    - 執行緒池管理
    """
    
    def __init__(self, 
                 max_workers: int = 4,
                 enable_notifications: bool = True,
                 logger=None):
        """初始化任務管理器"""
        self.logger = logger or LoggerManager().get_logger("ConcurrentTaskManager")
        
        # 核心組件
        self._executor = TaskExecutor(max_workers=max_workers, logger=self.logger)
        self._registry = TaskRegistry()
        self._tracker = TaskStatusTracker()
        
        # 通知服務
        self._notification_service = None
        if enable_notifications:
            try:
                self._notification_service = LineNotificationService()
            except Exception as e:
                self.logger.warning(f"[WARNING] 通知服務初始化失敗: {e}")
        
        # 任務執行追蹤
        self._futures: Dict[str, Future] = {}
        self._future_lock = threading.RLock()
        
        # 註冊預設處理器
        for task_type, handler in DEFAULT_HANDLERS.items():
            self._registry.register_handler(task_type, handler)
        
        self.logger.info(f"[OK] 並發任務管理器已初始化 (最大執行緒: {max_workers})")
    
    def register_handler(self, task_type: str, handler: Callable):
        """註冊任務處理器"""
        self._registry.register_handler(task_type, handler)
        self.logger.info(f"[OK] 已註冊任務處理器: {task_type}")
    
    def submit_task(self, 
                   task_type: str, 
                   task_params: Dict[str, Any] = None,
                   priority: TaskPriority = TaskPriority.NORMAL,
                   max_retries: int = 3) -> str:
        """
        提交任務到執行佇列（非阻塞）
        
        Returns:
            str: 任務ID
        """
        task_id = str(uuid.uuid4())
        task_params = task_params or {}
        
        # 檢查處理器
        handler = self._registry.get_handler(task_type)
        if not handler:
            raise ValueError(f"未找到任務類型處理器: {task_type}")
        
        # 創建任務資訊
        task_info = TaskInfo(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.PENDING,
            priority=priority,
            created_at=datetime.now(),
            input_params=task_params,
            max_retries=max_retries
        )
        
        # 添加到追蹤器
        self._tracker.add_task(task_info)
        
        # 提交到執行器
        future = self._executor.submit_task(self._execute_task_wrapper, task_id, task_type)
        
        with self._future_lock:
            self._futures[task_id] = future
        
        self.logger.info(f"[SUBMIT] 任務已提交: {task_id} ({task_type})")
        return task_id
    
    def _execute_task_wrapper(self, task_id: str, task_type: str):
        """任務執行包裝器"""
        try:
            # 更新狀態為執行中
            self._tracker.update_task(
                task_id,
                status=TaskStatus.RUNNING,
                started_at=datetime.now()
            )
            
            # 獲取任務資訊和處理器
            task_info = self._tracker.get_task(task_id)
            handler = self._registry.get_handler(task_type)
            
            start_time = time.time()
            
            # 執行任務
            result = handler(**task_info.input_params)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 更新為完成狀態
            self._tracker.update_task(
                task_id,
                status=TaskStatus.COMPLETED,
                completed_at=datetime.now(),
                result=result,
                actual_duration=duration,
                progress=100.0
            )
            
            # 發送成功通知
            self._send_notification(
                f"✅ 任務完成: {task_type}\n任務ID: {task_id}\n執行時間: {duration:.2f}秒"
            )
            
            self.logger.info(f"[COMPLETED] 任務完成: {task_id} (耗時: {duration:.2f}秒)")
            
        except Exception as e:
            # 處理錯誤
            self._handle_task_error(task_id, str(e))
        
        finally:
            # 清理 future 引用
            with self._future_lock:
                self._futures.pop(task_id, None)
    
    def _handle_task_error(self, task_id: str, error_message: str):
        """處理任務錯誤"""
        task_info = self._tracker.get_task(task_id)
        
        # 更新錯誤狀態
        self._tracker.update_task(
            task_id,
            status=TaskStatus.FAILED,
            completed_at=datetime.now(),
            error_message=error_message,
            retry_count=task_info.retry_count + 1 if task_info else 1
        )
        
        # 發送錯誤通知
        self._send_notification(
            f"❌ 任務失敗: {task_info.task_type if task_info else 'Unknown'}\n"
            f"任務ID: {task_id}\n錯誤: {error_message}"
        )
        
        self.logger.error(f"[FAILED] 任務失敗: {task_id} - {error_message}")
    
    def _send_notification(self, message: str):
        """發送通知"""
        if self._notification_service:
            try:
                self._notification_service.send_notification(message)
            except Exception as e:
                self.logger.warning(f"[WARNING] 通知發送失敗: {e}")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取任務狀態"""
        task_info = self._tracker.get_task(task_id)
        if not task_info:
            return None
        
        return {
            'task_id': task_info.task_id,
            'task_type': task_info.task_type,
            'status': task_info.status.value,
            'priority': task_info.priority.value,
            'created_at': task_info.created_at.isoformat(),
            'started_at': task_info.started_at.isoformat() if task_info.started_at else None,
            'completed_at': task_info.completed_at.isoformat() if task_info.completed_at else None,
            'progress': task_info.progress,
            'error_message': task_info.error_message,
            'actual_duration': task_info.actual_duration,
            'retry_count': task_info.retry_count
        }
    
    def list_tasks(self, status: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """列出任務"""
        status_filter = TaskStatus(status) if status else None
        tasks = self._tracker.list_tasks(status=status_filter)
        
        # 限制返回數量
        tasks = tasks[:limit]
        
        return [
            {
                'task_id': task.task_id,
                'task_type': task.task_type,
                'status': task.status.value,
                'created_at': task.created_at.isoformat(),
                'progress': task.progress,
                'error_message': task.error_message
            }
            for task in tasks
        ]
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        with self._future_lock:
            future = self._futures.get(task_id)
            if future and not future.done():
                cancelled = future.cancel()
                if cancelled:
                    self._tracker.update_task(
                        task_id,
                        status=TaskStatus.CANCELLED,
                        completed_at=datetime.now()
                    )
                    self.logger.info(f"[CANCELLED] 任務已取消: {task_id}")
                    return True
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計資訊"""
        all_tasks = self._tracker.list_tasks()
        
        stats = {
            'total_tasks': len(all_tasks),
            'completed_tasks': len([t for t in all_tasks if t.status == TaskStatus.COMPLETED]),
            'failed_tasks': len([t for t in all_tasks if t.status == TaskStatus.FAILED]),
            'running_tasks': len([t for t in all_tasks if t.status == TaskStatus.RUNNING]),
            'pending_tasks': len([t for t in all_tasks if t.status == TaskStatus.PENDING]),
            'cancelled_tasks': len([t for t in all_tasks if t.status == TaskStatus.CANCELLED])
        }
        
        # 計算成功率
        if stats['total_tasks'] > 0:
            stats['success_rate'] = (stats['completed_tasks'] / stats['total_tasks']) * 100
        else:
            stats['success_rate'] = 0
        
        return stats
    
    def shutdown(self, wait: bool = True):
        """關閉任務管理器"""
        self.logger.info("[SHUTDOWN] 正在關閉並發任務管理器...")
        
        if wait:
            # 等待所有任務完成
            with self._future_lock:
                futures = list(self._futures.values())
            
            for future in futures:
                try:
                    future.result(timeout=30)  # 最多等待30秒
                except Exception:
                    pass
        
        # 關閉執行器
        self._executor.shutdown(wait=wait)
        
        self.logger.info("[OK] 並發任務管理器已關閉")


# 全域實例（單例模式）
_task_manager_instance = None
_task_manager_lock = threading.Lock()

def get_task_manager(**kwargs) -> ConcurrentTaskManager:
    """獲取任務管理器單例實例"""
    global _task_manager_instance
    
    if _task_manager_instance is None:
        with _task_manager_lock:
            if _task_manager_instance is None:
                _task_manager_instance = ConcurrentTaskManager(**kwargs)
    
    return _task_manager_instance


# 便利函數
def submit_code_comparison_task(input_path: str, **kwargs) -> str:
    """提交程式碼對比任務的便利函數"""
    return get_task_manager().submit_task(
        'code_comparison',
        {'input_path': input_path, **kwargs}
    )


def submit_email_processing_task(email_data: Dict[str, Any], **kwargs) -> str:
    """提交郵件處理任務的便利函數"""
    return get_task_manager().submit_task(
        'email_processing',
        {'email_data': email_data, **kwargs}
    )