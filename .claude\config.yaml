# <PERSON> Hooks 配置檔案
# 這個檔案控制 Claude Hooks 系統的所有行為

# 全域配置
global:
  # 系統設定
  enabled: true
  log_level: "INFO"
  max_processing_time: 600  # 秒
  
  # 檔案過濾
  ignore_patterns:
    - "*.pyc"
    - "__pycache__"
    - ".git"
    - "*.log"
    - "*.tmp"
  
  # 專案類型自動檢測
  auto_detect_project_type: true

# 檔案大小分析配置
file_size_analysis:
  enabled: true
  max_lines: 500
  warning_threshold: 400
  
  # 語言特定設定
  language_settings:
    python:
      max_lines: 500
      warning_threshold: 400
      check_complexity: true
      max_function_lines: 50
      max_class_lines: 200
    
    javascript:
      max_lines: 300
      warning_threshold: 250
      check_complexity: true
      max_function_lines: 30
    
    typescript:
      max_lines: 400
      warning_threshold: 350
      check_complexity: true
      max_function_lines: 40
    
    markdown:
      max_lines: 1000
      warning_threshold: 800
      check_complexity: false

# 重複代碼檢測配置
duplicate_detection:
  enabled: true
  similarity_threshold: 0.8
  min_block_size: 5
  
  # 檢測類型
  detect_functions: true
  detect_classes: true
  detect_code_blocks: true
  
  # AST 比較設定
  normalize_variable_names: true
  ignore_comments: true
  ignore_whitespace: true
  
  # 排除模式
  exclude_patterns:
    - "test_*.py"
    - "*_test.py"
    - "tests/"

# Import 分析配置
import_analysis:
  enabled: true
  
  # 分析類型
  check_unused_imports: true
  check_circular_dependencies: true
  detect_optimization_opportunities: true
  
  # 建議設定
  suggest_stdlib_replacements: true
  suggest_local_module_usage: true
  
  # 排除模式
  exclude_modules:
    - "__future__"
    - "typing"

# 品質檢查配置
quality_checking:
  enabled: true
  
  # 複雜度檢查
  max_cyclomatic_complexity: 10
  max_nesting_depth: 4
  max_parameters: 5
  
  # 長度檢查
  max_function_lines: 50
  max_class_lines: 200
  max_line_length: 100
  
  # 風格檢查
  check_docstrings: true
  check_naming_conventions: true
  check_security_issues: true
  
  # 安全檢查模式
  security_patterns:
    - pattern: "eval\\s*\\("
      message: "使用 eval() 可能導致代碼注入"
      severity: "high"
    - pattern: "exec\\s*\\("
      message: "使用 exec() 可能導致代碼注入"
      severity: "high"
    - pattern: "subprocess\\.call\\([^)]*shell\\s*=\\s*True"
      message: "使用 shell=True 可能導致命令注入"
      severity: "high"

# 檔案拆分配置
file_splitting:
  enabled: true
  
  # 拆分策略
  strategies:
    - "class_split"     # 按類別拆分
    - "function_split"  # 按功能拆分
    - "mixed_split"     # 混合拆分
  
  # 拆分參數
  min_split_size: 50
  preserve_imports: true
  maintain_dependencies: true
  
  # 檔案命名
  naming_conventions:
    class_files: "{class_name_lower}.py"
    function_files: "{prefix}_functions.py"
    utility_files: "{original_name}_utils.py"

# 重複代碼清理配置
duplicate_cleaning:
  enabled: true
  preserve_newest: true
  backup_before_clean: true
  safe_mode: true
  similarity_threshold: 0.8
  
  # 清理策略
  strategies:
    - "remove_duplicates"    # 直接移除
    - "extract_functions"    # 提取共用函數
    - "merge_similar"        # 合併相似代碼

# Import 優化配置
import_optimization:
  enabled: true
  auto_apply_safe_changes: false
  preserve_existing_imports: true
  suggest_stdlib_replacements: true
  
  # 優化類型
  remove_unused_imports: true
  merge_duplicate_imports: true
  suggest_better_alternatives: true

# 驗證和回滾配置
validation:
  enabled: true
  
  # 驗證步驟
  run_syntax_check: true
  run_import_check: true
  run_tests: true
  check_code_quality: true
  
  # 測試配置
  test_command: "pytest tests/ -v"
  max_validation_time: 300
  
  # 快照設定
  auto_create_snapshots: true
  max_snapshots: 10
  
  # 回滾設定
  auto_rollback_on_failure: false
  require_confirmation: true

# 專案類型特定配置
project_types:
  django:
    file_size_analysis:
      max_lines: 600  # Django 檔案通常較大
    ignore_patterns:
      - "migrations/"
      - "settings/"
    special_files:
      - "models.py"
      - "views.py"
      - "admin.py"
  
  flask:
    file_size_analysis:
      max_lines: 400
    special_files:
      - "app.py"
      - "routes.py"
  
  fastapi:
    file_size_analysis:
      max_lines: 450
    special_files:
      - "main.py"
      - "routers/"
  
  data_science:
    file_size_analysis:
      max_lines: 800  # 筆記本轉換的檔案可能較大
    ignore_patterns:
      - "*.ipynb"
      - "data/"
      - "models/"
    special_files:
      - "analysis.py"
      - "preprocessing.py"

# 語言特定規則
language_rules:
  python:
    file_extensions: [".py"]
    
    # 分析規則
    analysis_rules:
      - rule: "function_length"
        max_lines: 50
        severity: "medium"
      - rule: "class_length"
        max_lines: 200
        severity: "medium"
      - rule: "complexity"
        max_complexity: 10
        severity: "high"
    
    # 重構規則
    refactoring_rules:
      - rule: "extract_long_function"
        threshold: 80
        strategy: "extract_helper_functions"
      - rule: "split_large_class"
        threshold: 300
        strategy: "split_by_responsibility"
    
    # 最佳實踐
    best_practices:
      - "use_type_hints"
      - "write_docstrings"
      - "follow_pep8"
      - "avoid_bare_except"
  
  javascript:
    file_extensions: [".js", ".jsx"]
    
    analysis_rules:
      - rule: "function_length"
        max_lines: 30
        severity: "medium"
      - rule: "complexity"
        max_complexity: 8
        severity: "high"
    
    best_practices:
      - "use_const_let"
      - "avoid_var"
      - "use_arrow_functions"
      - "handle_promises_properly"
  
  typescript:
    file_extensions: [".ts", ".tsx"]
    
    analysis_rules:
      - rule: "function_length"
        max_lines: 40
        severity: "medium"
      - rule: "interface_size"
        max_properties: 15
        severity: "medium"
    
    best_practices:
      - "use_strict_types"
      - "define_interfaces"
      - "use_generics"
      - "avoid_any_type"

# 通知和報告配置
notifications:
  enabled: true
  
  # 通知類型
  on_analysis_complete: true
  on_issues_found: true
  on_auto_fix_applied: true
  on_validation_failure: true
  
  # 報告格式
  report_format: "detailed"  # "summary", "detailed", "json"
  include_suggestions: true
  include_metrics: true

# 整合配置
integrations:
  # Git Hooks 整合
  git_hooks:
    enabled: true
    hook_types: ["pre-commit", "post-merge"]
    auto_install: false
  
  # IDE 整合
  ide_integration:
    vscode: true
    pycharm: false
  
  # CI/CD 整合
  ci_integration:
    github_actions: true
    gitlab_ci: false

# 性能配置
performance:
  # 並行處理
  max_concurrent_files: 4
  enable_caching: true
  cache_duration: 3600  # 秒
  
  # 記憶體限制
  max_memory_usage: "512MB"
  
  # 檔案大小限制
  max_file_size: "10MB"
  skip_large_files: true

# 實驗性功能
experimental:
  enabled: false
  
  features:
    - "ai_powered_refactoring"
    - "smart_variable_naming"
    - "auto_documentation_generation"
    - "performance_optimization_suggestions"

# 除錯配置
debug:
  enabled: false
  verbose_logging: false
  save_intermediate_results: false
  profile_performance: false