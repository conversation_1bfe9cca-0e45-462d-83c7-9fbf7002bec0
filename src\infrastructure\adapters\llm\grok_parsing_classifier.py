"""
Grok LLM 解析方式分類器
使用 Grok 智能分類所有廠商的解析方式
"""

import json
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .grok_client import GrokClient, GrokRequest
from src.infrastructure.logging.logger_manager import LoggerManager


class VendorType(Enum):
    """廠商類型"""
    JCET = "jcet"
    GTK = "gtk"
    ETD = "etd"
    LINGSEN = "lingsen"
    XAHT = "xaht"
    UNKNOWN = "unknown"


@dataclass
class ParsingMethod:
    """解析方式資料結構"""
    vendor: VendorType
    method_name: str
    method_type: str
    confidence: float
    extracted_data: Dict[str, Any]
    reasoning: str


@dataclass
class EmailAnalysis:
    """郵件分析結果"""
    vendor: VendorType
    vendor_confidence: float
    recommended_methods: List[ParsingMethod]
    extracted_product: Optional[str] = None
    extracted_mo: Optional[str] = None
    extracted_lot: Optional[str] = None
    extracted_yield: Optional[str] = None
    analysis_reasoning: str = ""


class GrokParsingClassifier:
    """
    Grok 解析方式分類器 - 單例模式
    智能分析郵件內容並推薦最適合的解析方式
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GrokParsingClassifier, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化分類器"""
        # 避免重複初始化
        if self._initialized:
            return
            
        self.logger = LoggerManager().get_logger("GrokParsingClassifier")
        self.grok_client = GrokClient()
        
        # 廠商解析方式知識庫
        self.vendor_knowledge = self._build_vendor_knowledge()
        
        self.logger.info("Grok 解析方式分類器已初始化")
        self._initialized = True
    
    def _build_vendor_knowledge(self) -> Dict[VendorType, Dict[str, Any]]:
        """構建廠商解析方式知識庫"""
        return {
            VendorType.JCET: {
                "methods": [
                    "kui_pattern_long", "gyc_pattern_long", "kui_pattern_short", 
                    "gyc_pattern_short", "lot_pattern", "test_batch_pattern", 
                    "product_pattern", "standard_mo_format", "jcet_specific_format"
                ],
                "identifiers": ["jcet", "jcetglobal.com", "致新", "chipson", "taiwan"],
                "patterns": {
                    "product": r"(G\d{4}[A-Z]\d{2}[A-Z])",
                    "mo_standard": r"([A-Z]\d{6})",
                    "mo_jcet": r"([A-Z]{2,4}\d{4,6}\.[A-Z]{1,2})",
                    "lot": r"lot[：:]\s*([A-Z0-9.]+)",
                    "test_batch": r"测试批号[：:]\s*([A-Z0-9.]+)"
                }
            },
            VendorType.GTK: {
                "methods": [
                    "keyword_extraction", "device_type_extraction", 
                    "bin1_extraction", "in_qty_extraction"
                ],
                "identifiers": ["ft hold", "ft lot", "gtk.com", "greatek.com.tw"],
                "patterns": {
                    "mo": r"mo\s*:\s*([^\s,~_]+)",
                    "lot": r"lot\s*(?:no\s*)?:\s*([^\s,~_]+)",
                    "yield": r"yield\s*:\s*([^\s,%~_]+)",
                    "product": r"product\s*:\s*([^\s,~_]+)",
                    "device_type": r"(?:device\s*)?type\s*:\s*([^\s,~_]+)"
                }
            },
            VendorType.ETD: {
                "methods": [
                    "anf_subject_parsing", "qty_extraction", 
                    "yield_extraction", "anomaly_detection"
                ],
                "identifiers": ["anf", "etrendtech"],
                "patterns": {
                    "anf_format": r"anf",
                    "qty": r"(?:input\s*)?(?:quantity|qty)\s*(?:units)?\s*:\s*(\d+)",
                    "yield": r"(?:yield|良率|pass\s*rate)\s*:\s*(\d+(?:\.\d+)?%?)",
                    "anomaly": r"異常"
                }
            },
            VendorType.LINGSEN: {
                "methods": [
                    "product_code_pattern", "run_extraction", 
                    "lot_extraction", "yield_extraction"
                ],
                "identifiers": ["LINGSEN", "lingsen"],
                "patterns": {
                    "product": r"\b(G|M|AT)\d+[A-Z0-9\(\)]*",
                    "run": r"[Rr]un#(\d+)",
                    "lot": r"[Ll]ot#([^\s]+)",
                    "yield": r"[Ll]ow[Yy]ield\s*=?\s*(\d+(?:\.\d+)?%)"
                }
            },
            VendorType.XAHT: {
                "methods": [
                    "wa_gyc_pattern", "underscore_format", "dual_method"
                ],
                "identifiers": ["tianshui", "西安"],
                "patterns": {
                    "wa_gyc": r"\b(wa|GYC)[A-Z0-9]+",
                    "underscore": r"_",
                    "product": r"[A-Z0-9]+",
                    "mo": r"[A-Z0-9]{11}"
                }
            }
        }
    
    def classify_email(self, subject: str, body: str, sender: str) -> EmailAnalysis:
        """
        分類郵件並推薦解析方式
        
        Args:
            subject: 郵件主題
            body: 郵件內容
            sender: 寄件者
            
        Returns:
            EmailAnalysis: 分析結果
        """
        if not self.grok_client.is_enabled():
            self.logger.warning("Grok 服務未啟用，使用傳統分類")
            return self._fallback_classify(subject, body, sender)
        
        try:
            # 構建分析提示
            prompt = self._build_classification_prompt(subject, body, sender)
            
            # 發送 Grok 請求
            request = GrokRequest(
                prompt=prompt,
                temperature=0.1,
                max_tokens=1500
            )
            
            response = self.grok_client.send_request(request)
            
            if response.success:
                return self._parse_grok_response(response.content, subject, body, sender)
            else:
                self.logger.error(f"Grok 分類失敗: {response.error}")
                return self._fallback_classify(subject, body, sender)
                
        except Exception as e:
            self.logger.error(f"分類過程發生錯誤: {str(e)}")
            return self._fallback_classify(subject, body, sender)
    
    def _build_classification_prompt(self, subject: str, body: str, sender: str) -> str:
        """構建分類提示"""
        prompt = f"""
你是一個專業的半導體郵件解析專家。請分析以下郵件內容，並判斷最適合的解析方式。

郵件資訊：
- 主題: {subject}
- 寄件者: {sender}
- 內容: {body[:1000]}...

支援的廠商和解析方式：

1. JCET (致新):
   - kui_pattern_long: KUI + 長度>4，取前15字符
   - gyc_pattern_long: GYC + 長度>4，取前15字符
   - kui_pattern_short: KUI + 長度≤4，位置解析
   - gyc_pattern_short: GYC + 長度≤4，位置解析
   - lot_pattern: "lot:" 關鍵字，正則匹配
   - test_batch_pattern: "测试批号:" 關鍵字
   - product_pattern: G+4數字+字母+2數字+字母格式
   - standard_mo_format: 標準MO格式 (1字母+6數字)
   - jcet_specific_format: JCET特有格式 (2-4字母+4-6數字+點+1-2字母)

2. GTK (Greatek):
   - keyword_extraction: 關鍵字提取 (mo, lot, yield, product)
   - device_type_extraction: 裝置類型提取
   - bin1_extraction: BIN1資料提取
   - in_qty_extraction: 入料數量提取

3. ETD (Etrend):
   - anf_subject_parsing: ANF主題格式解析
   - qty_extraction: 數量提取
   - yield_extraction: 良率提取
   - anomaly_detection: 異常資訊查找

4. LINGSEN:
   - product_code_pattern: 產品代碼模式 (G/M/AT開頭)
   - run_extraction: Run#提取
   - lot_extraction: Lot#提取
   - yield_extraction: LowYield提取

5. XAHT (西安華天):
   - wa_gyc_pattern: wa/GYC模式
   - underscore_format: 下劃線分隔格式
   - dual_method: 雙重解析邏輯

請以JSON格式回應，包含：
{{
  "vendor": "廠商名稱",
  "vendor_confidence": 0.95,
  "recommended_methods": [
    {{
      "method_name": "方法名稱",
      "method_type": "解析類型",
      "confidence": 0.9,
      "reasoning": "選擇原因"
    }}
  ],
  "extracted_product": "產品型號",
  "extracted_mo": "MO編號",
  "extracted_lot": "LOT編號",
  "extracted_yield": "良率",
  "analysis_reasoning": "整體分析推理"
}}
"""
        return prompt
    
    def _parse_grok_response(self, content: str, subject: str, body: str, sender: str) -> EmailAnalysis:
        """解析 Grok 回應"""
        try:
            # 嘗試提取JSON
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                data = json.loads(json_str)
                
                # 轉換為 EmailAnalysis
                vendor = VendorType(data.get('vendor', 'unknown').lower())
                
                methods = []
                for method_data in data.get('recommended_methods', []):
                    method = ParsingMethod(
                        vendor=vendor,
                        method_name=method_data.get('method_name', ''),
                        method_type=method_data.get('method_type', ''),
                        confidence=method_data.get('confidence', 0.0),
                        extracted_data={},
                        reasoning=method_data.get('reasoning', '')
                    )
                    methods.append(method)
                
                return EmailAnalysis(
                    vendor=vendor,
                    vendor_confidence=data.get('vendor_confidence', 0.0),
                    recommended_methods=methods,
                    extracted_product=data.get('extracted_product'),
                    extracted_mo=data.get('extracted_mo'),
                    extracted_lot=data.get('extracted_lot'),
                    extracted_yield=data.get('extracted_yield'),
                    analysis_reasoning=data.get('analysis_reasoning', '')
                )
            else:
                raise ValueError("無法提取JSON格式回應")
                
        except Exception as e:
            self.logger.error(f"解析 Grok 回應失敗: {str(e)}")
            return self._fallback_classify(subject, body, sender)
    
    def _fallback_classify(self, subject: str, body: str, sender: str) -> EmailAnalysis:
        """後備分類方法"""
        # 基本廠商識別
        vendor = self._identify_vendor_basic(subject, body, sender)
        
        # 基本資料提取
        product = self._extract_product_basic(subject, body, vendor)
        mo = self._extract_mo_basic(subject, body, vendor)
        lot = self._extract_lot_basic(subject, body, vendor)
        yield_rate = self._extract_yield_basic(subject, body, vendor)
        
        # 推薦方法
        methods = self._recommend_methods_basic(vendor, subject, body)
        
        return EmailAnalysis(
            vendor=vendor,
            vendor_confidence=0.8,
            recommended_methods=methods,
            extracted_product=product,
            extracted_mo=mo,
            extracted_lot=lot,
            extracted_yield=yield_rate,
            analysis_reasoning="使用傳統後備分類方法"
        )
    
    def _identify_vendor_basic(self, subject: str, body: str, sender: str) -> VendorType:
        """基本廠商識別"""
        text = f"{subject} {body} {sender}".lower()
        
        for vendor, info in self.vendor_knowledge.items():
            for identifier in info["identifiers"]:
                if identifier.lower() in text:
                    return vendor
        
        return VendorType.UNKNOWN
    
    def _extract_product_basic(self, subject: str, body: str, vendor: VendorType) -> Optional[str]:
        """基本產品型號提取"""
        text = f"{subject} {body}"
        
        if vendor in self.vendor_knowledge:
            patterns = self.vendor_knowledge[vendor]["patterns"]
            if "product" in patterns:
                match = re.search(patterns["product"], text, re.IGNORECASE)
                if match:
                    return match.group(1)
        
        return None
    
    def _extract_mo_basic(self, subject: str, body: str, vendor: VendorType) -> Optional[str]:
        """基本MO編號提取"""
        text = f"{subject} {body}"
        
        if vendor in self.vendor_knowledge:
            patterns = self.vendor_knowledge[vendor]["patterns"]
            
            # 嘗試不同的MO模式
            for pattern_key in ["mo", "mo_standard", "mo_jcet"]:
                if pattern_key in patterns:
                    match = re.search(patterns[pattern_key], text, re.IGNORECASE)
                    if match:
                        return match.group(1)
        
        return None
    
    def _extract_lot_basic(self, subject: str, body: str, vendor: VendorType) -> Optional[str]:
        """基本LOT編號提取"""
        text = f"{subject} {body}"
        
        if vendor in self.vendor_knowledge:
            patterns = self.vendor_knowledge[vendor]["patterns"]
            if "lot" in patterns:
                match = re.search(patterns["lot"], text, re.IGNORECASE)
                if match:
                    return match.group(1)
        
        return None
    
    def _extract_yield_basic(self, subject: str, body: str, vendor: VendorType) -> Optional[str]:
        """基本良率提取"""
        text = f"{subject} {body}"
        
        if vendor in self.vendor_knowledge:
            patterns = self.vendor_knowledge[vendor]["patterns"]
            if "yield" in patterns:
                match = re.search(patterns["yield"], text, re.IGNORECASE)
                if match:
                    return match.group(1)
        
        return None
    
    def _recommend_methods_basic(self, vendor: VendorType, subject: str, body: str) -> List[ParsingMethod]:
        """基本方法推薦"""
        methods = []
        
        if vendor in self.vendor_knowledge:
            vendor_methods = self.vendor_knowledge[vendor]["methods"]
            
            for method_name in vendor_methods:
                method = ParsingMethod(
                    vendor=vendor,
                    method_name=method_name,
                    method_type="basic",
                    confidence=0.7,
                    extracted_data={},
                    reasoning=f"基於廠商 {vendor.value} 的標準解析方法"
                )
                methods.append(method)
        
        return methods
    
    def get_vendor_knowledge(self) -> Dict[VendorType, Dict[str, Any]]:
        """獲取廠商知識庫"""
        return self.vendor_knowledge
    
    def add_vendor_pattern(self, vendor: VendorType, pattern_name: str, pattern: str):
        """添加廠商模式"""
        if vendor not in self.vendor_knowledge:
            self.vendor_knowledge[vendor] = {"methods": [], "identifiers": [], "patterns": {}}
        
        self.vendor_knowledge[vendor]["patterns"][pattern_name] = pattern
        self.logger.info(f"為廠商 {vendor.value} 添加模式: {pattern_name}")
    
    def test_classification(self, test_cases: List[Dict[str, str]]) -> List[EmailAnalysis]:
        """測試分類功能"""
        results = []
        
        for test_case in test_cases:
            subject = test_case.get('subject', '')
            body = test_case.get('body', '')
            sender = test_case.get('sender', '')
            
            analysis = self.classify_email(subject, body, sender)
            results.append(analysis)
            
            self.logger.info(f"測試案例分類結果: {analysis.vendor.value} (信心度: {analysis.vendor_confidence})")
        
        return results