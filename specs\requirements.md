# 專案需求規格：即時監控儀表板系統

## 專案概述

本專案為 Outlook Summary System 開發即時監控儀表板功能，該系統是一個企業級的半導體製造測試資料處理平台。監控儀表板將深度整合現有的監控資料來源，提供系統健康狀態、效能指標、錯誤追蹤和業務指標的即時視覺化展示。

### 系統現況

**完成進度：52.2%（24/46任務完成）**

**已完成核心架構：**
- 六角架構設計（領域層、應用層、基礎設施層、展示層）
- 三個API服務：Flask(5000)、FastAPI(8010)、網路瀏覽器API(8009)
- 5個廠商解析器（GTK、ETD、XAHT、JCET、LINGSEN）100%完成
- 兩階段EQC處理流程和8步驟處理系統
- Excel處理系統（8步驟流程、BIN1保護機制）
- LLM整合系統（UnifiedLLMClient支援Ollama和Grok）
- 210+個測試套件（TDD開發方法）

**現有監控基礎設施：**
- SyncMonitor：每60秒檢查同步狀態
- LoggerManager：結構化彩色日誌記錄系統
- AdvancedPerformanceManager：詳細的處理時間統計和瓶頸分析
- 現有API端點：/api/statistics、/api/sync/status、/api/connection/status、/api/health

## 功能需求

### 需求 FR-001：系統健康狀態監控

**使用者故事：** 身為系統管理員，我希望在集中式儀表板上檢視即時系統健康指標，以便快速識別並回應系統問題。

**優先級：** 高

**驗收標準：**
1. 當存取儀表板時，系統應顯示所有服務的目前狀態：
   - Flask 郵件收件夾服務（埠 5000）的連線狀態和回應時間
   - FastAPI FT-EQC 處理服務（埠 8010）的連線狀態和回應時間
   - 網路瀏覽器API服務（埠 8009）的連線狀態和回應時間
   - 郵件同步服務的執行狀態和最後同步時間
   - 資料庫連線狀態和查詢回應時間

2. 當服務變為不可用時，系統應在 30 秒內以紅色突出顯示受影響的服務，並顯示具體錯誤訊息

3. 當系統資源使用率超過 80% 時，系統應顯示警告指示器：
   - CPU 使用率超過 80%
   - 記憶體使用率超過 80%
   - 磁碟空間使用率超過 80%

4. 當儀表板載入時，系統應顯示每個服務元件的運行時間，精確到分鐘

5. 若發生任何嚴重錯誤，系統應立即顯示警報通知，包括錯誤時間、影響範圍和建議處理方式

### 需求 FR-002：郵件處理效能監控

**使用者故事：** 身為營運經理，我希望即時監控郵件處理效能，以確保製造測試資料的高效處理。

**優先級：** 高

**驗收標準：**
1. 當正在處理郵件時，系統應顯示目前處理速率：
   - 每分鐘處理的郵件數量
   - 每分鐘解析成功的郵件數量
   - 每分鐘處理的附件數量
   - 平均每封郵件的處理時間

2. 當同步服務執行時，系統應顯示詳細的會話資訊：
   - 目前會話開始時間
   - 已處理的郵件總數
   - 成功/失敗的郵件數量比例
   - 預估剩餘處理時間

3. 當解析失敗時，系統應提供詳細的失敗分析：
   - 按廠商分類的失敗計數（GTK、ETD、XAHT、JCET、LINGSEN）
   - 失敗原因分類統計
   - 最近失敗的郵件主題和寄件者

4. 當處理佇列累積時，系統應顯示佇列管理資訊：
   - 待處理郵件佇列長度
   - 按優先級分類的佇列狀態
   - 基於歷史資料的預估處理時間

5. 若處理速率低於預設閾值（每分鐘 5 封郵件），系統應顯示效能警告並提供可能原因

### 需求 FR-003：廠商解析器統計監控

**使用者故事：** 身為品質保證工程師，我希望追蹤特定廠商的處理統計資料，以識別不同半導體測試廠商的模式和問題。

**優先級：** 高

**驗收標準：**
1. 當處理廠商資料時，系統應按廠商顯示詳細統計：
   - GTK 廠商：識別條件 `ft hold`、`ft lot`，顯示解析成功率
   - ETD 廠商：識別條件 `anf`，顯示解析成功率
   - XAHT 廠商：識別條件 `tianshui`、`西安`，顯示解析成功率
   - JCET 廠商：識別條件 `jcet`、KUI 模式解析，顯示解析成功率
   - LINGSEN 廠商：識別條件 `lingsen`，顯示解析成功率

2. 當發生解析錯誤時，系統應提供詳細的錯誤分析：
   - 按廠商分類的 parse_status 統計（pending、parsed、failed）
   - 按 extraction_method 分類統計（traditional、llm、hybrid、fallback）
   - 顯示 parse_error 欄位中的常見錯誤模式

3. 當儀表板重新整理時，系統應顯示廠商處理趨勢：
   - 過去 24 小時內每個廠商的郵件接收量
   - 每個廠商的平均良率（yield_value）趨勢
   - MO（製造訂單）和 LOT（批次號）處理統計

4. 當廠商顯示效能下降時，系統應以警告指示器突出顯示：
   - 解析成功率低於 85% 時顯示黃色警告
   - 解析成功率低於 70% 時顯示紅色警報

5. 若廠商在 2 小時內成功處理數為零，系統應顯示嚴重警報並建議檢查對應的解析器

### 需求 FR-004：錯誤日誌和事件監控

**使用者故事：** 身為系統管理員，我希望檢視詳細的錯誤日誌和系統事件，以便有效地排除故障。

**優先級：** 中

**驗收標準：**
1. 當發生錯誤時，系統應顯示最近 50 條帶有時間戳記的錯誤訊息：
   - LoggerManager 記錄的結構化日誌
   - 郵件解析錯誤（parse_error 欄位內容）
   - 服務連線錯誤和逾時
   - 資料庫操作錯誤

2. 當點擊錯誤時，系統應顯示詳細的錯誤資訊：
   - 完整的堆疊追蹤資訊
   - 相關的郵件 ID 和廠商資訊
   - 錯誤發生時的系統狀態
   - 建議的修復步驟

3. 當套用篩選時，系統應允許多維度篩選：
   - 按錯誤等級篩選（ERROR、WARNING、INFO、DEBUG）
   - 按服務類型篩選（Flask、FastAPI、EmailSync、Database）
   - 按廠商篩選（GTK、ETD、XAHT、JCET、LINGSEN）
   - 按時間範圍篩選（最近 1 小時、6 小時、24 小時）

4. 當錯誤日誌更新時，系統應在無使用者介入的情況下自動重新整理顯示，並突出顯示新增的錯誤

5. 若每小時嚴重錯誤超過 10 個，系統應顯示升級警報並自動通知相關人員

### 需求 FR-005：處理統計和趨勢分析

**使用者故事：** 身為業務分析師，我希望檢視處理統計資料和趨勢，以分析系統效能和容量規劃。

**優先級：** 中

**驗收標準：**
1. 當儀表板載入時，系統應顯示基於 EmailDB 表的統計資料：
   - 今日、本週和本月處理的郵件總數（基於 received_time 欄位）
   - 已讀/未讀郵件比例（基於 is_read 欄位）
   - 已處理/未處理郵件比例（基於 is_processed 欄位）
   - 帶附件郵件統計（基於 has_attachments 和 attachment_count 欄位）

2. 當資料可用時，系統應顯示處理效能趨勢：
   - 基於 parsed_at 欄位的解析時間趨勢
   - 各廠商的平均良率趨勢（基於 yield_value 欄位）
   - 不同 extraction_method 的處理時間比較
   - EmailProcessStatusDB 表中各步驟的完成時間統計

3. 當時間週期變更時，系統應動態查詢資料庫並更新圖表：
   - 支援最近 1 小時、6 小時、24 小時、7 天、30 天的時間範圍
   - 根據選擇的時間範圍調整資料聚合粒度

4. 當滑鼠懸停在圖表元素上時，系統應顯示詳細的工具提示：
   - 具體的數值和百分比
   - 相關的廠商或處理步驟資訊
   - 與前一週期的比較資料

5. 若處理量超過歷史平均值 50%，系統應基於 SenderDB 表的統計資料建議容量擴展

### 需求 FR-006：即時通知和警報系統

**使用者故事：** 身為系統操作員，我希望接收嚴重事件的即時通知，以便立即回應系統問題。

**優先級：** 高

**驗收標準：**
1. 當發生嚴重錯誤時，系統應根據錯誤類型顯示通知：
   - 廠商解析器失敗（parse_status = 'failed'）時顯示瀏覽器通知
   - Flask 或 FastAPI 服務無回應時顯示緊急通知
   - 資料庫連線中斷時顯示系統級警報
   - 郵件同步服務停止時顯示服務警報

2. 當服務離線時，系統應顯示顯著的警報橫幅：
   - 埠 5000（Flask）無法連線時顯示「郵件服務離線」警報
   - 埠 8010（FastAPI）無法連線時顯示「處理服務離線」警報
   - 埠 8009（網路API）無法連線時顯示「網路API服務離線」警報
   - SyncMonitor 檢測到同步失敗時顯示「同步服務異常」警報

3. 當點擊通知時，系統應導航到儀表板的相關部分：
   - 廠商錯誤通知導航到廠商統計區域
   - 服務錯誤通知導航到系統健康狀態區域
   - 處理錯誤通知導航到錯誤日誌區域

4. 當存在多個警報時，系統應按嚴重程度優先順序顯示：
   - 嚴重（Critical）：服務完全離線、資料庫無法連線
   - 警告（Warning）：效能下降、解析失敗率高
   - 資訊（Info）：一般狀態更新、統計資料變化

5. 若使用者關閉警報，系統在 30 分鐘內不應再次顯示相同類型的警報，但應在日誌中保留記錄

### 需求 FR-007：儀表板配置管理

**使用者故事：** 身為系統管理員，我希望配置儀表板重新整理速率和警報閾值，以根據營運需求自訂監控。

**優先級：** 低

**驗收標準：**
1. 當存取設定時，系統應允許配置多種參數：
   - 儀表板重新整理間隔（5-300 秒，預設 30 秒）
   - SyncMonitor 檢查間隔（目前固定 60 秒，應可調整為 30-300 秒）
   - 資料庫查詢逾時設定（目前固定 5 秒，應可調整為 3-30 秒）

2. 當設定閾值時，系統應接受各種警報的自訂值：
   - CPU、記憶體、磁碟使用率警報閾值（預設 80%，可調整為 50-95%）
   - 郵件處理速率警報閾值（預設每分鐘 5 封，可調整為 1-50 封）
   - 廠商解析成功率警報閾值（預設 85% 警告、70% 嚴重，可調整）
   - 每小時錯誤數量警報閾值（預設 10 個，可調整為 5-100 個）

3. 當儲存配置變更時，系統應立即套用新設定：
   - 更新 SyncMonitor 的檢查間隔而無需重新啟動服務
   - 動態調整前端儀表板的重新整理頻率
   - 即時生效警報閾值變更

4. 當輸入無效值時，系統應顯示詳細的驗證錯誤：
   - 數值範圍驗證（如重新整理間隔必須在 5-300 秒之間）
   - 邏輯驗證（如警告閾值必須高於嚴重閾值）
   - 格式驗證（如必須為正整數）

5. 若設定損壞或遺失，系統應恢復為預設配置值並記錄恢復事件到日誌中

### 需求 FR-008：LLM和檔案處理監控

**使用者故事：** 身為系統管理員，我希望監控 LLM 服務和檔案處理系統的狀態，以確保 AI 輔助解析和檔案處理功能正常運作。

**優先級：** 中

**驗收標準：**
1. 當監控 LLM 服務時，系統應顯示詳細的服務狀態：
   - UnifiedLLMClient 的可用性狀態（Ollama/Grok）
   - LLM 服務的回應時間和成功率
   - 基於 llm_analysis_timestamp 的最近分析活動
   - 基於 llm_service_used 的服務使用統計（grok vs ollama）
   - confidence_score 的平均值和分佈統計

2. 當監控檔案處理系統時，系統應顯示處理狀態：
   - FileHandlerFactory 各廠商檔案處理器的狀態
   - SyncAttachmentHandler 附件處理成功率
   - Excel 處理系統（CTA、EQC、FT）的處理統計
   - 檔案下載、解壓、轉換各步驟的成功率

3. 當監控 EmailProcessStatusDB 時，系統應顯示具體處理步驟狀態：
   - eqctotaldata 步驟的完成率和平均處理時間
   - code_detection 步驟的成功率和錯誤統計
   - dual_search 步驟的處理效能
   - report_generation 步驟的完成狀態

4. 當 LLM 服務出現問題時，系統應顯示具體警報：
   - LLM 服務連線失敗時顯示「AI 解析服務離線」警報
   - confidence_score 平均值低於 0.7 時顯示「AI 解析品質下降」警告
   - LLM 回應時間超過 30 秒時顯示「AI 服務回應緩慢」警告

5. 若檔案處理失敗率超過 20%，系統應顯示「檔案處理系統異常」警報並提供詳細的失敗原因分析

### 需求 FR-009：良率數據和異常檢測

**使用者故事：** 身為品質保證工程師，我希望監控良率數據和異常檢測功能，以確保製造測試數據的準確性和及時發現異常。

**優先級：** 中

**驗收標準：**
1. 當監控良率數據時，系統應顯示詳細的良率統計：
   - 基於 yield_value 欄位的即時良率分佈圖
   - 各廠商的良率趨勢對比（過去 24 小時、7 天、30 天）
   - 良率異常檢測結果（低於歷史平均值 2 個標準差的案例）
   - 高良率和低良率的郵件數量統計

2. 當檢測到良率異常時，系統應提供詳細分析：
   - 異常良率的具體數值和偏差程度
   - 相關的廠商、產品代碼（pd）、批次號（lot）資訊
   - 異常發生的時間趨勢和頻率統計
   - 與同期其他廠商良率的對比分析

3. 當監控產品和批次數據時，系統應顯示：
   - 基於 pd 欄位的產品代碼分佈統計
   - 基於 lot 和 mo 欄位的批次處理統計
   - 重複批次號的檢測和警報
   - 批次處理時間的趨勢分析

4. 當良率數據出現異常模式時，系統應自動警報：
   - 單一廠商良率連續 3 次低於 90% 時顯示「廠商良率異常」警報
   - 整體良率低於歷史平均值 10% 時顯示「系統良率下降」警告
   - 檢測到可能的數據錯誤（如良率超過 100% 或為負值）時顯示「數據異常」警報

5. 若 24 小時內未收到任何良率數據，系統應顯示「良率數據中斷」嚴重警報

### 需求 FR-010：網路連線和編碼處理監控

**使用者故事：** 身為系統管理員，我希望監控系統的網路連線品質和編碼處理狀況，以確保郵件接收和數據處理的穩定性。

**優先級：** 低

**驗收標準：**
1. 當監控網路連線時，系統應顯示詳細的連線狀態：
   - POP3/Outlook 郵件服務器的連線品質和延遲
   - 基於 EmailSyncService.get_connection_status() 的連線統計
   - 網路逾時和重連次數統計
   - 郵件接收速度和穩定性指標

2. 當監控編碼處理時，系統應顯示 Unicode 處理狀況：
   - unicode_fix_global 模組的運作狀態
   - 編碼錯誤的發生頻率和類型統計
   - 中英文混合內容的處理成功率
   - MIME 編碼解析的成功率統計

3. 當監控自動化流程時，系統應顯示：
   - AutoEmailProcessor 的自動解析執行狀態
   - 自動同步服務的運行狀態和間隔設定
   - 背景任務的執行佇列和完成狀態
   - 定時任務的執行記錄和成功率

4. 當網路或編碼出現問題時，系統應顯示相應警報：
   - 郵件服務器連線失敗超過 5 分鐘時顯示「郵件服務器離線」嚴重警報
   - Unicode 編碼錯誤率超過 5% 時顯示「編碼處理異常」警告
   - 自動同步服務停止超過 10 分鐘時顯示「自動同步中斷」警報

5. 若系統檢測到持續的網路不穩定（連線成功率低於 90%），系統應建議檢查網路設定和防火牆配置

## 非功能需求

### 需求 NFR-001：效能需求

**說明：** 監控系統必須具備高效能，不能影響現有系統的處理效能。

**具體要求：**
- 監控資料收集對系統效能的影響應低於 5%
- 儀表板頁面載入時間應在 3 秒內
- API 回應時間應在 1 秒內
- 即時資料更新延遲應在 30 秒內
- 支援同時 50 個使用者存取

### 需求 NFR-002：可用性需求

**說明：** 監控系統應具備高可用性，能夠獨立運行。

**具體要求：**
- 系統可用性應達到 99.5%
- 監控服務應能夠獨立於業務系統運行
- 支援優雅的故障處理和恢復機制
- 具備自我監控和健康檢查能力

### 需求 NFR-003：擴展性需求

**說明：** 監控系統應具備良好的擴展性，支援未來功能擴展。

**具體要求：**
- 支援新增監控指標和廠商
- 支援水平擴展和負載均衡
- 模組化設計，支援插件架構
- 支援多種資料來源整合

### 需求 NFR-004：安全性需求

**說明：** 監控系統應具備適當的安全性保護。

**具體要求：**
- 實施基於角色的存取控制（RBAC）
- API 請求需要適當的身份驗證
- 敏感資料需要進行脫敏處理
- 記錄所有存取和操作日誌

### 需求 NFR-005：相容性需求

**說明：** 監控系統應與現有系統保持良好相容性。

**具體要求：**
- 完全相容現有的六角架構設計
- 不能修改現有的資料庫結構
- 支援現有的彩色日誌系統
- 與現有的三個API服務無縫整合

## 技術約束

### 約束 TC-001：架構約束

- 必須遵循現有的六角架構設計原則
- 不能破壞領域邊界和依賴反轉原則
- 必須支援現有的測試驅動開發（TDD）方法

### 約束 TC-002：技術棧約束

- 後端必須使用 Python 3.11+
- 前端建議使用 Vue.js 3 + ECharts
- 資料庫必須使用現有的 SQLite
- 必須整合現有的 Flask 和 FastAPI 服務

### 約束 TC-003：資料約束

- 不能修改現有的核心資料表結構
- 新增的監控表必須使用獨立的命名空間
- 必須支援現有的資料格式和編碼方式

## 驗收測試策略

### 測試類型

1. **單元測試**：確保每個監控元件功能正確
2. **整合測試**：驗證與現有系統的整合效果
3. **效能測試**：確保監控系統不影響主系統效能
4. **使用者驗收測試**：驗證使用者體驗和功能完整性

### 測試環境

- 開發環境：本地開發測試
- 測試環境：完整功能測試
- 預生產環境：效能和壓力測試
- 生產環境：生產部署驗證

### 成功標準

- 所有功能需求100%實現
- 所有非功能需求達標
- 通過所有安全性和相容性測試
- 使用者滿意度評分達到4.0/5.0以上

---

**文件版本：** 1.0  
**最後更新：** 2025-07-26  
**核准狀態：** 待核准