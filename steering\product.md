# 產品概述

## Outlook 摘要系統

這是一個**半導體測試資料郵件處理系統**，從 VBA Excel 遷移至 Python。系統自動化處理來自多個半導體測試廠商的郵件，執行資料萃取、檔案管理和報表生成。

### 核心目的
- **自動化郵件處理**：監控並處理來自半導體測試廠商的郵件
- **資料萃取**：解析並提取郵件附件中的關鍵資訊（MO、LOT、良率等）
- **報表生成**：自動產生 Excel 摘要報表和統計分析
- **檔案管理**：下載、解壓縮、轉換各種檔案格式
- **廠商整合**：支援多個廠商的不同資料格式和協議
- **系統監控**：提供即時狀態監控和效能分析

### 支援廠商
系統目前支援 **12個** 半導體測試廠商：

#### 主要廠商
- **ETD**：透過 `anf` 關鍵字識別
- **GTK**：透過 `ft hold`、`ft lot` 關鍵字識別
- **JCET**：透過 `jcet` 關鍵字識別
- **LINGSEN**：透過 `lingsen` 關鍵字識別
- **XAHT**：透過 `tianshui`、`西安` 關鍵字識別

#### 新增廠商
- **MSEC**：專用解析器和檔案處理器
- **NANOTECH**：納米技術廠商支援
- **NFME**：專用檔案格式處理
- **TSHT**：天水半導體廠商
- **Chuzhou**：滁州廠商解析
- **Suqian**：宿遷廠商支援
- **LLM**：使用 AI 智慧解析器

### 主要功能

#### 郵件處理系統
- **POP3/Outlook 整合**：即時郵件監控和同步
- **智慧廠商識別**：自動路由到對應解析器
- **附件管理**：下載、解壓、檔案格式轉換
- **Email 資料庫**：完整的郵件記錄和狀態追蹤

#### 資料處理能力
- **多格式支援**：CSV、Excel、TXT、壓縮檔案、MDB/ACCDB
- **智慧解析**：廠商特定格式自動識別和處理
- **統計分析**：良率分析、趨勢監控、異常檢測
- **CTA 處理**：完整的 CSV 到 Excel 轉換系統

#### API 和介面
- **RESTful API**：完整的 API 端點支援
- **網頁管理介面**：郵件管理、檔案處理、狀態監控
- **CLI 工具**：命令列操作介面
- **FastAPI 文檔**：自動生成的 API 文檔

#### 進階功能
- **AI 整合**：Ollama/Grok LLM 智慧解析
- **效能監控**：Prometheus + Grafana 監控
- **並行處理**：多核心處理最佳化
- **錯誤處理**：完整的異常處理和恢復機制

### 商業價值
- **自動化**：消除手動郵件處理工作流程
- **準確性**：減少資料萃取的人為錯誤
- **效率**：一致性處理多個廠商格式
- **監控**：提供測試作業的即時可視性
- **擴展性**：自動處理增加的郵件量
- **智慧化**：AI 輔助的複雜格式解析
- **可靠性**：完整的錯誤處理和恢復機制

### 專案規模
- **程式碼行數**: 67,675+ 行 Python 代碼
- **支援廠商**: 12 個半導體測試廠商
- **開發歷程**: 112+ 次提交
- **測試覆蓋**: 2562+ 個測試檔案
- **架構模組**: 六角架構設計，多層級結構
- **API 端點**: 30+ 個 REST API 端點
- **檔案處理器**: 12 個專用廠商處理器
- **解析器**: 12 個智慧解析器

### 目標使用者
- **半導體測試作業團隊**：日常郵件處理和資料萃取
- **品質保證工程師**：良率分析和趨勢監控
- **生產管理人員**：即時狀態監控和報表生成
- **資料分析師**：統計分析和數據挖掘
- **系統管理員**：系統監控和維護管理
- **開發團隊**：API 整合和系統擴展

### 系統特色
- **現代化架構**：六角架構設計，清晰的關注點分離
- **AI 驅動**：智慧解析和異常檢測
- **高度可測試**：90%+ 測試覆蓋率，TDD 開發
- **跨平台支援**：Windows/Linux 環境相容
- **容器化部署**：Docker + Docker Compose 支援
- **監控完整**：日誌記錄、效能監控、告警系統