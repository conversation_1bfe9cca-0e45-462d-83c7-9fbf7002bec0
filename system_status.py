# -*- coding: utf-8 -*-
"""
Claude Code Hooks 狀態檢查和總結
"""

import os
import sys
from pathlib import Path

def check_system_status():
    print("=" * 60)
    print("Claude Code Hooks 系統狀態檢查")
    print("=" * 60)
    
    project_root = Path(r"D:\project\python\outlook_summary")
    
    print(f"[INFO] 專案目錄: {project_root}")
    print(f"[INFO] 目錄存在: {project_root.exists()}")
    
    # 檢查關鍵檔案
    key_files = [
        ".claude/settings.json",
        ".claude/run_hooks.py", 
        ".claude/hooks/main_simple.py",
        ".claude/hooks/update_docs_safe.py"
    ]
    
    print("\n[CHECK] 檢查關鍵檔案:")
    all_files_exist = True
    for file_path in key_files:
        full_path = project_root / file_path
        exists = full_path.exists()
        status = "EXISTS" if exists else "MISSING"
        print(f"   {status:8} {file_path}")
        if not exists:
            all_files_exist = False
    
    # 檢查生成的檔案
    print("\n[CHECK] 檢查生成檔案:")
    generated_files = [
        "project_info.json",
        "DOC_SUMMARY.md", 
        "CHANGELOG.md"
    ]
    
    for file_path in generated_files:
        full_path = project_root / file_path
        exists = full_path.exists()
        status = "EXISTS" if exists else "MISSING"
        size = ""
        if exists:
            size = f"({full_path.stat().st_size} bytes)"
        print(f"   {status:8} {file_path} {size}")
    
    # 檢查日誌目錄
    print("\n[CHECK] 檢查日誌目錄:")
    log_dirs = [
        ".claude/logs",
        ".claude/reports/analysis_reports"
    ]
    
    for dir_path in log_dirs:
        full_path = project_root / dir_path
        exists = full_path.exists()
        status = "EXISTS" if exists else "MISSING"
        count = ""
        if exists and full_path.is_dir():
            file_count = len(list(full_path.iterdir()))
            count = f"({file_count} files)"
        print(f"   {status:8} {dir_path} {count}")
    
    # 系統狀態總結
    print("\n" + "=" * 60)
    print("系統狀態總結")
    print("=" * 60)
    
    if all_files_exist:
        print("[SUCCESS] 所有關鍵檔案都存在")
    else:
        print("[WARNING] 部分關鍵檔案缺失")
    
    print("\n[FEATURES] 已修復的功能:")
    print("   ✅ Windows 編碼問題 (cp950 -> UTF-8)")
    print("   ✅ Unicode/Emoji 輸出問題")
    print("   ✅ 路徑相容性問題")
    print("   ✅ 虛擬環境檔案掃描問題")
    print("   ✅ readable_path 變數錯誤")
    
    print("\n[WORKFLOW] 工作流程:")
    print("   1. Claude Code 編輯檔案 → PostToolUse Hook")
    print("   2. 執行檔案大小分析 → 生成拆分建議")  
    print("   3. 任務完成 → Stop Hook")
    print("   4. 更新專案文檔 → 統計資料同步")
    
    print("\n[USAGE] 使用方式:")
    print("   • 在 Claude Code 中正常編輯 Python 檔案")
    print("   • 系統會自動檢查檔案大小 (>500行 = 建議拆分)")
    print("   • 任務完成時自動更新 README.md 等文檔")
    print("   • 查看 .claude/reports/ 目錄中的分析報告")
    
    print("\n[TEST] 測試指令:")
    print("   python test_fixed.py           # 測試修復版本")
    print("   python test_encoding_fix.bat   # Windows 編碼測試")
    
    print("\n[NEXT] 下一步:")
    print("   1. 在 Claude Code 中編輯任何 Python 檔案測試")
    print("   2. 檢查 .claude/logs/ 中的執行日誌")
    print("   3. 確認 README.md 是否自動更新")
    
    print("\n" + "=" * 60)
    print("檢查完成！")
    print("=" * 60)

if __name__ == "__main__":
    check_system_status()
