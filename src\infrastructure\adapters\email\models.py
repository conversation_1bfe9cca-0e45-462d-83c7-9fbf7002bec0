"""
郵件白名單相關數據模型
定義白名單條目和檢查結果的數據結構
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List
from enum import Enum


class WhitelistEntryType(Enum):
    """白名單條目類型"""
    EMAIL = "email"      # 完整郵件地址匹配
    DOMAIN = "domain"    # 網域匹配 (@domain.com)
    WILDCARD = "wildcard"  # 通配符匹配


@dataclass
class EmailWhitelistEntry:
    """
    郵件白名單條目
    
    Attributes:
        pattern: 匹配模式 (例如: <EMAIL> 或 @domain.com)
        entry_type: 條目類型 (EMAIL, DOMAIN, WILDCARD)
        description: 條目描述
        is_active: 是否啟用
        created_at: 建立時間
        updated_at: 更新時間
    """
    pattern: str
    entry_type: WhitelistEntryType
    description: Optional[str] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化後處理"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def matches(self, email_address: str) -> bool:
        """
        檢查郵件地址是否與此條目匹配
        
        Args:
            email_address: 要檢查的郵件地址
            
        Returns:
            bool: 是否匹配
        """
        if not self.is_active:
            return False
            
        email_address = email_address.lower().strip()
        pattern = self.pattern.lower().strip()
        
        if self.entry_type == WhitelistEntryType.EMAIL:
            return email_address == pattern
        elif self.entry_type == WhitelistEntryType.DOMAIN:
            # 網域匹配，pattern 格式為 @domain.com
            if pattern.startswith('@'):
                domain = pattern[1:]
                return email_address.endswith('@' + domain)
            else:
                # 兼容性：如果沒有 @ 前綴，自動加上
                return email_address.endswith('@' + pattern)
        elif self.entry_type == WhitelistEntryType.WILDCARD:
            # 通配符匹配（未來實作）
            import fnmatch
            return fnmatch.fnmatch(email_address, pattern)
        
        return False
    
    def to_dict(self) -> dict:
        """轉換為字典格式"""
        return {
            'pattern': self.pattern,
            'entry_type': self.entry_type.value,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'EmailWhitelistEntry':
        """從字典建立實例"""
        return cls(
            pattern=data['pattern'],
            entry_type=WhitelistEntryType(data['entry_type']),
            description=data.get('description'),
            is_active=data.get('is_active', True),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
            updated_at=datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else None
        )


@dataclass
class WhitelistCheckResult:
    """
    白名單檢查結果
    
    Attributes:
        is_whitelisted: 是否在白名單中
        matched_entry: 匹配的白名單條目
        email_address: 被檢查的郵件地址
        check_time: 檢查時間
        reason: 檢查結果說明
    """
    is_whitelisted: bool
    email_address: str
    check_time: datetime
    matched_entry: Optional[EmailWhitelistEntry] = None
    reason: Optional[str] = None
    
    def __post_init__(self):
        """初始化後處理"""
        if self.check_time is None:
            self.check_time = datetime.now()
    
    def to_dict(self) -> dict:
        """轉換為字典格式"""
        return {
            'is_whitelisted': self.is_whitelisted,
            'email_address': self.email_address,
            'check_time': self.check_time.isoformat(),
            'matched_entry': self.matched_entry.to_dict() if self.matched_entry else None,
            'reason': self.reason
        }