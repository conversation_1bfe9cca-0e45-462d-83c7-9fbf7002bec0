/**
 * 郵件解析 UI 增強
 * 處理解析結果的顯示和互動
 */

class EmailParserUI {
    constructor() {
        this.initialized = false;
    }
    
    /**
     * 初始化解析 UI
     */
    initialize() {
        if (this.initialized) return;
        
        // 添加解析相關的樣式
        this.injectStyles();
        
        // 修改郵件列表顯示
        this.enhanceEmailList();
        
        this.initialized = true;
    }
    
    /**
     * 注入解析相關的樣式
     */
    injectStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .email-parse-info {
                display: flex;
                gap: 10px;
                align-items: center;
                font-size: 0.85em;
                color: #666;
            }
            
            .parse-tag {
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.8em;
                white-space: nowrap;
            }
            
            .parse-tag.vendor {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            
            .parse-tag.lot {
                background-color: #f3e5f5;
                color: #7b1fa2;
            }
            
            .parse-tag.yield {
                background-color: #e8f5e9;
                color: #388e3c;
            }
            
            .parse-tag.pd {
                background-color: #fff3e0;
                color: #f57c00;
            }
            
            .parse-tag.mo {
                background-color: #e1f5fe;
                color: #0277bd;
            }
            
            .parse-tag.traditional {
                background-color: #e8eaf6;
                color: #3f51b5;
            }
            
            .parse-tag.llm {
                background-color: #f1f8e9;
                color: #689f38;
            }
            
            .parse-tag.hybrid {
                background-color: #fce4ec;
                color: #ad1457;
            }
            
            .parse-tag.fallback {
                background-color: #fff8e1;
                color: #f9a825;
            }
            
            .parse-status {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: inline-block;
                margin-right: 5px;
            }
            
            .parse-status.parsed {
                background-color: #4caf50;
            }
            
            .parse-status.failed {
                background-color: #f44336;
            }
            
            .parse-status.pending {
                background-color: #ff9800;
            }
            
            .email-item.has-parse-data {
                background-color: #f5f5f5;
            }
            
            .parse-actions {
                display: flex;
                gap: 5px;
            }
            
            .parse-btn {
                padding: 4px 8px;
                font-size: 0.8em;
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .parse-btn:hover {
                background-color: #1976d2;
            }
            
            .manual-btn {
                padding: 4px 8px;
                font-size: 0.8em;
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-left: 5px;
            }
            
            .manual-btn:hover {
                background-color: #45a049;
            }
            
            .manual-btn-failed {
                background-color: #ff9800;
                animation: pulse 2s infinite;
            }
            
            .manual-btn-failed:hover {
                background-color: #f57c00;
            }
            
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }
            
            /* 批次解析對話框樣式 */
            .batch-parse-options {
                margin: 20px 0;
            }
            
            .radio-option {
                display: block;
                margin: 10px 0;
                padding: 10px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            
            .radio-option:hover {
                background-color: #f5f5f5;
            }
            
            .radio-option input[type="radio"] {
                margin-right: 10px;
            }
            
            .parse-count {
                color: #666;
                font-size: 0.9em;
            }
            
            .batch-parse-info {
                margin-top: 20px;
                padding: 10px;
                background-color: #e3f2fd;
                border-radius: 4px;
            }
            
            .info-text {
                margin: 0;
                color: #1976d2;
                font-size: 0.9em;
            }
            
            /* 手動輸入對話框樣式 */
            .manual-input-dialog {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }
            
            .manual-input-content {
                background: white;
                padding: 20px;
                border-radius: 8px;
                width: 90%;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
            }
            
            .manual-input-form {
                display: grid;
                gap: 15px;
            }
            
            .form-group {
                display: flex;
                flex-direction: column;
            }
            
            .form-group label {
                font-weight: bold;
                margin-bottom: 5px;
                color: #333;
            }
            
            .form-group input, .form-group select {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
            
            .form-group input:focus, .form-group select:focus {
                outline: none;
                border-color: #2196f3;
                box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
            }
            
            .form-actions {
                display: flex;
                gap: 10px;
                justify-content: flex-end;
                margin-top: 20px;
            }
            
            .btn-save {
                background-color: #4caf50;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .btn-save:hover {
                background-color: #45a049;
            }
            
            .btn-cancel {
                background-color: #f44336;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .btn-cancel:hover {
                background-color: #da190b;
            }
            
            .hidden {
                display: none !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 增強郵件列表顯示
     */
    enhanceEmailList() {
        // 覆寫原有的 createEmailItem 方法
        if (window.emailInbox && window.emailInbox.listManager) {
            const originalCreateEmailItem = window.emailInbox.listManager.createEmailItem.bind(window.emailInbox.listManager);
            
            window.emailInbox.listManager.createEmailItem = (email) => {
                return this.enhanceEmailItem(originalCreateEmailItem(email), email);
            };
        }
    }
    
    /**
     * 增強單個郵件項目
     */
    enhanceEmailItem(baseHtml, email) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = baseHtml;
        const emailItem = tempDiv.firstElementChild;
        
        // 添加解析狀態類別
        if (email.parse_status === 'parsed') {
            emailItem.classList.add('has-parse-data');
        }
        
        // 在主題後添加解析資訊
        const subjectDiv = emailItem.querySelector('.email-subject');
        if (subjectDiv) {
            const parseInfo = this.createParseInfo(email);
            subjectDiv.insertAdjacentHTML('afterend', parseInfo);
        }
        
        // 在操作按鈕中添加解析按鈕（所有郵件都顯示）
        const actionsDiv = emailItem.querySelector('.email-actions');
        if (actionsDiv) {
            // 根據解析狀態顯示不同的按鈕文字
            const btnText = email.parse_status === 'parsed' ? '重新解析' : '解析郵件';
            const btnIcon = email.parse_status === 'parsed' ? '🔄' : '🔍';
            
            const parseBtn = `
                <button class="parse-btn" onclick="window.emailParserUI.parseEmail(${email.id})" title="${btnText}">
                    ${btnIcon} ${btnText}
                </button>
            `;
            
            // 手動輸入按鈕（解析失敗時特別突出）
            const manualBtnClass = email.parse_status === 'failed' ? 'manual-btn manual-btn-failed' : 'manual-btn';
            const manualBtn = `
                <button class="${manualBtnClass}" onclick="window.emailParserUI.showManualInputDialog(${email.id})" title="手動輸入解析資料">
                    ✏️ 手動輸入
                </button>
            `;
            
            actionsDiv.insertAdjacentHTML('afterbegin', parseBtn + manualBtn);
        }
        
        return emailItem.outerHTML;
    }
    
    /**
     * 創建解析資訊顯示
     */
    createParseInfo(email) {
        let infoHtml = '<div class="email-parse-info">';
        
        // 只顯示解析狀態指示器（彩色圓點）
        let statusTitle = this.getParseStatusText(email.parse_status);
        
        // 為已解析的郵件添加詳細資訊到 tooltip
        if (email.parse_status === 'parsed') {
            let details = [];
            if (email.vendor_code) details.push(`廠商: ${email.vendor_code}`);
            if (email.pd) details.push(`產品: ${email.pd}`);
            if (email.lot) details.push(`批號: ${email.lot}`);
            if (email.yield_value) details.push(`良率: ${email.yield_value}`);
            if (email.extraction_method) {
                const methodText = this.getExtractionMethodText(email.extraction_method);
                details.push(`解析方法: ${methodText}`);
            }
            
            if (details.length > 0) {
                statusTitle += ' - ' + details.join(', ');
            }
        } else if (email.parse_status === 'failed' && email.parse_error) {
            statusTitle += ` - 錯誤: ${email.parse_error}`;
        }
        
        infoHtml += `<span class="parse-status ${email.parse_status}" title="${statusTitle}"></span>`;
        
        infoHtml += '</div>';
        return infoHtml;
    }
    
    /**
     * 取得解析狀態文字
     */
    getParseStatusText(status) {
        const statusMap = {
            'parsed': '已解析',
            'pending': '待解析',
            'failed': '解析失敗'
        };
        return statusMap[status] || status;
    }
    
    /**
     * 取得解析方法文字
     */
    getExtractionMethodText(method) {
        const methodMap = {
            'traditional': '傳統',
            'llm': 'LLM',
            'hybrid': '混合',
            'fallback': '備援'
        };
        return methodMap[method] || method;
    }
    
    /**
     * 取得解析方法CSS類別
     */
    getExtractionMethodClass(method) {
        const classMap = {
            'traditional': 'traditional',
            'llm': 'llm', 
            'hybrid': 'hybrid',
            'fallback': 'fallback'
        };
        return classMap[method] || 'traditional';
    }
    
    /**
     * 截斷文字
     */
    truncate(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    /**
     * 解析單個郵件
     */
    async parseEmail(emailId) {
        try {
            const response = await fetch(`/api/parser/emails/${emailId}/reparse`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('郵件解析成功', 'success');
                
                // 解析成功後自動執行處理功能
                try {
                    console.log('解析成功，自動執行檔案處理...');
                    await this.autoProcessAfterParsing(emailId);
                } catch (processError) {
                    console.error('自動處理失敗:', processError);
                    this.showNotification('解析成功但自動處理失敗', 'warning');
                }
                
                // 重新載入郵件列表
                if (window.emailInbox && window.emailInbox.listManager) {
                    window.emailInbox.listManager.loadEmails();
                }
            } else {
                this.showNotification('郵件解析失敗: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('解析郵件失敗:', error);
            this.showNotification('解析郵件失敗', 'error');
        }
    }
    
    /**
     * 解析成功後自動處理單個郵件的檔案
     */
    async autoProcessAfterParsing(emailId) {
        try {
            console.log(`自動處理郵件 ${emailId} 的檔案...`);
            
            // 調用批次處理 API（它會處理所有已解析的郵件，包括剛解析的這封）
            const response = await fetch('/api/parser/emails/batch-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                if (result.processed_count > 0) {
                    console.log(`自動處理完成: 處理了 ${result.processed_count} 封郵件`);
                    this.showNotification(
                        `自動處理完成: 處理 ${result.processed_count} 封郵件，成功 ${result.success_count}`, 
                        'success'
                    );
                } else {
                    console.log('沒有需要處理的檔案');
                }
            } else {
                console.error('自動處理失敗:', result.error);
                this.showNotification('自動處理失敗: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('自動處理請求失敗:', error);
            throw error;
        }
    }
    
    /**
     * 顯示批次解析對話框
     */
    async showBatchParseDialog() {
        const dialog = document.getElementById('batch-parse-dialog');
        if (!dialog) return;
        
        // 先獲取統計資訊
        try {
            const response = await fetch('/api/parser/statistics');
            const result = await response.json();
            
            if (result.success && result.data.database_stats) {
                const stats = result.data.database_stats;
                const pendingCount = document.getElementById('pending-count');
                const failedCount = document.getElementById('failed-count');
                
                if (pendingCount) {
                    pendingCount.textContent = `(${stats.pending_emails || 0} 封)`;
                }
                if (failedCount) {
                    failedCount.textContent = `(${stats.failed_emails || 0} 封)`;
                }
            }
        } catch (error) {
            console.error('獲取解析統計失敗:', error);
        }
        
        dialog.classList.remove('hidden');
    }
    
    /**
     * 隱藏批次解析對話框
     */
    hideBatchParseDialog() {
        const dialog = document.getElementById('batch-parse-dialog');
        if (dialog) {
            dialog.classList.add('hidden');
        }
    }
    
    /**
     * 執行批次解析
     */
    async executeBatchParse() {
        const selectedType = document.querySelector('input[name="parse-type"]:checked');
        if (!selectedType) {
            this.showNotification('請選擇解析類型', 'warning');
            return;
        }
        
        this.hideBatchParseDialog();
        
        // 根據選擇的類型調用不同的解析方法
        switch (selectedType.value) {
            case 'pending':
                await this.batchParseEmails(false);
                break;
            case 'failed':
                await this.batchParseEmails(true);
                break;
            case 'all':
                // 先解析待解析的，再解析失敗的
                await this.batchParseEmails(false);
                await this.batchParseEmails(true);
                break;
        }
    }
    
    /**
     * 自動解析所有未解析郵件
     */
    async autoParseAllPending() {
        try {
            // 直接執行批次解析，不顯示對話框
            await this.batchParseEmails(false);
        } catch (error) {
            console.error('自動批次解析失敗:', error);
        }
    }
    
    /**
     * 批次處理已解析郵件的檔案
     */
    async batchProcessFiles() {
        try {
            // 顯示處理中提示
            this.showNotification('正在批次處理已解析郵件的檔案...', 'info');
            
            const response = await fetch('/api/parser/emails/batch-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                if (result.processed_count === 0) {
                    this.showNotification('沒有已解析的郵件需要處理', 'info');
                } else {
                    this.showNotification(
                        `批次處理完成: 處理 ${result.processed_count} 封郵件，成功 ${result.success_count}，失敗 ${result.failed_count}`, 
                        'success'
                    );
                }
                
                // 重新載入郵件列表
                if (window.emailInbox && window.emailInbox.listManager) {
                    window.emailInbox.listManager.loadEmails();
                }
            } else {
                this.showNotification('批次處理失敗: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('批次處理失敗:', error);
            this.showNotification('批次處理失敗', 'error');
        }
    }
    
    /**
     * 批次解析郵件
     */
    async batchParseEmails(onlyFailed = false) {
        try {
            // 顯示進度提示
            this.showNotification(
                `正在批次解析${onlyFailed ? '失敗' : '待解析'}郵件...`, 
                'info'
            );
            
            const response = await fetch('/api/parser/emails/batch-parse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    failed_only: onlyFailed,
                    limit: 50
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                if (result.parsed_count === 0) {
                    this.showNotification(
                        `沒有${onlyFailed ? '失敗' : '待解析'}的郵件需要處理`, 
                        'info'
                    );
                } else {
                    this.showNotification(
                        `批次解析完成: 處理 ${result.parsed_count} 封，成功 ${result.success_count}，失敗 ${result.failed_count}`, 
                        'success'
                    );
                }
                
                // 重新載入郵件列表
                if (window.emailInbox && window.emailInbox.listManager) {
                    window.emailInbox.listManager.loadEmails();
                }
            } else {
                this.showNotification('批次解析失敗: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('批次解析失敗:', error);
            this.showNotification('批次解析失敗', 'error');
        }
    }
    
    /**
     * 顯示手動輸入對話框
     */
    async showManualInputDialog(emailId) {
        try {
            // 先獲取郵件資料
            const response = await fetch(`/api/emails/${emailId}`);
            const emailData = await response.json();
            
            if (!emailData.success) {
                this.showNotification('獲取郵件資料失敗', 'error');
                return;
            }
            
            const email = emailData.data;
            
            // 創建對話框 HTML
            const dialogHtml = `
                <div id="manual-input-dialog" class="manual-input-dialog">
                    <div class="manual-input-content">
                        <h3>手動輸入解析資料</h3>
                        <p><strong>郵件主題:</strong> ${email.subject}</p>
                        <p><strong>寄件者:</strong> ${email.sender}</p>
                        
                        <form class="manual-input-form" id="manual-input-form">
                            <div class="form-group">
                                <label for="vendor">廠家 (Vendor) *</label>
                                <select id="vendor" name="vendor" required>
                                    <option value="">請選擇廠家</option>
                                    <option value="JCET">JCET (致新)</option>
                                    <option value="GTK">GTK</option>
                                    <option value="ETD">ETD (Etrend)</option>
                                    <option value="LINGSEN">LINGSEN (凌昇)</option>
                                    <option value="XAHT">XAHT</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="pd">產品編號 (PD) *</label>
                                <input type="text" id="pd" name="pd" placeholder="例如: G2892K21D(CA)" value="${email.pd || ''}" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="lot">批次編號 (LOT) *</label>
                                <input type="text" id="lot" name="lot" placeholder="例如: YHW0049.Y" value="${email.lot || ''}" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="mo">製造訂單 (MO)</label>
                                <input type="text" id="mo" name="mo" placeholder="例如: TS25063596.1" value="">
                            </div>
                            
                            <div class="form-group">
                                <label for="yield">良率 (YIELD)</label>
                                <input type="number" id="yield" name="yield" step="0.01" min="0" max="100" placeholder="例如: 97.38" value="${email.yield_value || ''}">
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn-cancel" onclick="window.emailParserUI.hideManualInputDialog()">取消</button>
                                <button type="submit" class="btn-save">儲存</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            
            // 添加到頁面
            document.body.insertAdjacentHTML('beforeend', dialogHtml);
            
            // 設定廠家預設值
            if (email.vendor_code) {
                document.getElementById('vendor').value = email.vendor_code;
            }
            
            // 綁定表單提交事件
            document.getElementById('manual-input-form').addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveManualInput(emailId);
            });
            
        } catch (error) {
            console.error('顯示手動輸入對話框失敗:', error);
            this.showNotification('顯示輸入對話框失敗', 'error');
        }
    }
    
    /**
     * 隱藏手動輸入對話框
     */
    hideManualInputDialog() {
        const dialog = document.getElementById('manual-input-dialog');
        if (dialog) {
            dialog.remove();
        }
    }
    
    /**
     * 儲存手動輸入的資料
     */
    async saveManualInput(emailId) {
        try {
            const form = document.getElementById('manual-input-form');
            const formData = new FormData(form);
            
            const data = {
                vendor_code: formData.get('vendor'),
                pd: formData.get('pd'),
                lot: formData.get('lot'),
                mo: formData.get('mo'),
                yield_value: formData.get('yield') ? parseFloat(formData.get('yield')) : null
            };
            
            // 驗證必填欄位
            if (!data.vendor_code || !data.pd || !data.lot) {
                this.showNotification('請填寫所有必填欄位（廠家、產品編號、批次編號）', 'warning');
                return;
            }
            
            const response = await fetch(`/api/parser/emails/${emailId}/manual-input`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('手動輸入資料已儲存', 'success');
                this.hideManualInputDialog();
                
                // 重新載入郵件列表
                if (window.emailInbox && window.emailInbox.listManager) {
                    window.emailInbox.listManager.loadEmails();
                }
            } else {
                this.showNotification('儲存失敗: ' + result.error, 'error');
            }
            
        } catch (error) {
            console.error('儲存手動輸入失敗:', error);
            this.showNotification('儲存失敗', 'error');
        }
    }
    
    /**
     * 顯示通知
     */
    showNotification(message, type = 'info') {
        if (window.EmailUIUtils) {
            const elements = window.emailInbox ? window.emailInbox.elements : {};
            window.EmailUIUtils.showNotification(elements, message, type);
        } else {
            alert(message);
        }
    }
}

// 創建全域實例
window.emailParserUI = new EmailParserUI();

// 在 DOM 載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待郵件收件夾初始化完成
    setTimeout(() => {
        window.emailParserUI.initialize();
        
        // 自動執行批次解析（解析所有未解析郵件）
        setTimeout(() => {
            console.log('自動執行批次解析所有未解析郵件...');
            window.emailParserUI.autoParseAllPending();
        }, 500);
    }, 1000);
});