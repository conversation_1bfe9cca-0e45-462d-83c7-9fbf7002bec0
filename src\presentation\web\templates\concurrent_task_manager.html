<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>並發任務管理器 - 企業級任務監控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .status-section {
            padding: 30px;
        }

        .status-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-4px);
        }

        .status-card h3 {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }

        .status-card .value {
            font-size: 2.5em;
            font-weight: 700;
            color: #007bff;
        }

        .tasks-section {
            margin-top: 30px;
        }

        .tasks-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .tasks-header h2 {
            color: #495057;
        }

        .task-tabs {
            display: flex;
            gap: 10px;
        }

        .tab-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .task-list {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .task-item {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-item:hover {
            background-color: #f8f9fa;
        }

        .task-info {
            flex: 1;
        }

        .task-id {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .task-details {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .task-type {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .task-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-running { background: #d4edda; color: #155724; }
        .status-completed { background: #d1ecf1; color: #0c5460; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-cancelled { background: #e2e3e5; color: #383d41; }

        .task-progress {
            margin-top: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            transition: width 0.3s ease;
        }

        .task-actions {
            display: flex;
            gap: 10px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 6px;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .empty-state svg {
            width: 64px;
            height: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* 核取方塊優化樣式 */
        .options-card:hover {
            border-color: #007bff !important;
            background: #f0f8ff !important;
        }

        .checkbox-label:hover {
            color: #007bff !important;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .options-card {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 12px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>並發任務管理器</h1>
            <p>企業級程式碼對比任務處理平台</p>
        </div>

        <!-- 任務提交表單 -->
        <div class="controls">
            <h2 style="margin-bottom: 20px; color: #495057;">提交新任務</h2>
            <form id="taskForm">
                <div class="form-group">
                    <label for="inputPath">輸入路徑</label>
                    <input type="text" id="inputPath" name="input_path" placeholder="資料夾路徑或壓縮檔路徑" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="codeRegion">程式碼區間 (可選)</label>
                        <input type="text" id="codeRegion" name="code_region" placeholder="例：298,335,1565,1600">
                    </div>
                    <div class="form-group">
                        <label for="priority">優先級</label>
                        <select id="priority" name="priority">
                            <option value="low">低</option>
                            <option value="normal" selected>普通</option>
                            <option value="high">高</option>
                            <option value="critical">緊急</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label style="font-weight: 600; color: #495057; margin-bottom: 12px; display: block;">執行選項</label>
                    <div class="options-card" style="
                        background: #f8f9fa; 
                        border: 1px solid #dee2e6; 
                        border-radius: 8px; 
                        padding: 16px; 
                        display: flex; 
                        gap: 20px;
                        transition: border-color 0.3s ease;
                    ">
                        <label class="checkbox-label" style="display: flex; align-items: center; gap: 8px; cursor: pointer; font-size: 14px; font-weight: 500; color: #495057;">
                            <input type="checkbox" id="withExcel" name="with_excel" style="margin: 0; width: 16px; height: 16px; accent-color: #007bff;">
                            <span>生成 Excel 檔案</span>
                        </label>
                        <label class="checkbox-label" style="display: flex; align-items: center; gap: 8px; cursor: pointer; font-size: 14px; font-weight: 500; color: #495057;">
                            <input type="checkbox" id="verbose" name="verbose" style="margin: 0; width: 16px; height: 16px; accent-color: #007bff;">
                            <span>詳細模式</span>
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <span>🚀</span> 提交任務
                </button>
            </form>
        </div>

        <!-- 系統狀態 -->
        <div class="status-section">
            <div class="status-cards">
                <div class="status-card">
                    <h3>活躍任務</h3>
                    <div class="value" id="activeTaskCount">-</div>
                </div>
                <div class="status-card">
                    <h3>總任務數</h3>
                    <div class="value" id="totalTaskCount">-</div>
                </div>
                <div class="status-card">
                    <h3>工作執行緒</h3>
                    <div class="value" id="workerThreads">-</div>
                </div>
                <div class="status-card">
                    <h3>最大並發</h3>
                    <div class="value" id="maxConcurrent">-</div>
                </div>
            </div>

            <!-- 任務列表 -->
            <div class="tasks-section">
                <div class="tasks-header">
                    <h2>任務監控</h2>
                    <div class="task-tabs">
                        <button class="tab-btn active" data-tab="active">活躍任務</button>
                        <button class="tab-btn" data-tab="completed">已完成</button>
                    </div>
                    <button class="btn btn-secondary" onclick="refreshTasks()">
                        <span>🔄</span> 重新整理
                    </button>
                </div>

                <div class="task-list" id="taskList">
                    <div class="empty-state">
                        <div class="loading"></div>
                        <p>載入任務中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全域變數
        let currentTab = 'active';
        let refreshInterval;

        // API 基本 URL
        const API_BASE = '/api';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 設置表單提交處理
            document.getElementById('taskForm').addEventListener('submit', handleTaskSubmit);
            
            // 設置頁籤切換
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    switchTab(this.dataset.tab);
                });
            });

            // 初始載入
            loadSystemStatus();
            loadTasks();
            
            // 設置自動重新整理
            startAutoRefresh();
        });

        // 提交任務
        async function handleTaskSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const submitBtn = e.target.querySelector('button[type="submit"]');
            
            // 禁用提交按鈕
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<div class="loading"></div> 提交中...';
            
            try {
                const response = await fetch(`${API_BASE}/tasks/code_comparison`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('任務提交成功！任務ID: ' + result.task_id, 'success');
                    e.target.reset();
                    loadTasks();
                    loadSystemStatus();
                } else {
                    showAlert('任務提交失敗: ' + (result.detail || '未知錯誤'), 'danger');
                }
            } catch (error) {
                showAlert('提交任務時發生錯誤: ' + error.message, 'danger');
            } finally {
                // 恢復提交按鈕
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<span>🚀</span> 提交任務';
            }
        }

        // 載入系統狀態
        async function loadSystemStatus() {
            try {
                const response = await fetch(`${API_BASE}/tasks/system/status`);
                const result = await response.json();
                
                if (response.ok && result.data) {
                    const data = result.data;
                    document.getElementById('activeTaskCount').textContent = data.active_tasks || 0;
                    document.getElementById('totalTaskCount').textContent = data.total_tasks || 0;
                    document.getElementById('workerThreads').textContent = data.worker_threads || 0;
                    document.getElementById('maxConcurrent').textContent = data.max_concurrent_tasks || 0;
                }
            } catch (error) {
                console.error('載入系統狀態失敗:', error);
            }
        }

        // 載入任務列表
        async function loadTasks() {
            const taskList = document.getElementById('taskList');
            taskList.innerHTML = '<div class="empty-state"><div class="loading"></div><p>載入任務中...</p></div>';
            
            try {
                let endpoint;
                if (currentTab === 'active') {
                    endpoint = `${API_BASE}/tasks/active`;
                } else {
                    endpoint = `${API_BASE}/tasks/completed?limit=20`;
                }
                
                const response = await fetch(endpoint);
                const result = await response.json();
                
                if (response.ok && result.data) {
                    renderTasks(result.data);
                } else {
                    throw new Error(result.detail || '載入失敗');
                }
            } catch (error) {
                taskList.innerHTML = `<div class="empty-state"><p>載入任務失敗: ${error.message}</p></div>`;
            }
        }

        // 渲染任務列表
        function renderTasks(tasks) {
            const taskList = document.getElementById('taskList');
            
            if (tasks.length === 0) {
                taskList.innerHTML = '<div class="empty-state"><p>沒有任務</p></div>';
                return;
            }
            
            const tasksHTML = tasks.map(task => {
                const statusClass = `status-${task.status}`;
                const progressWidth = task.progress || 0;
                
                return `
                    <div class="task-item">
                        <div class="task-info">
                            <div class="task-id">${task.task_id}</div>
                            <div class="task-details">
                                <span class="task-type">${task.task_type}</span>
                                <span class="task-status ${statusClass}">${getStatusText(task.status)}</span>
                                <span>建立時間: ${formatDateTime(task.created_at)}</span>
                                ${task.actual_duration ? `<span>執行時間: ${task.actual_duration}秒</span>` : ''}
                            </div>
                            ${task.status === 'running' ? `
                                <div class="task-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: ${progressWidth}%"></div>
                                    </div>
                                </div>
                            ` : ''}
                            ${task.error_message ? `<div style="color: #dc3545; margin-top: 10px; font-size: 0.9em;">${task.error_message}</div>` : ''}
                        </div>
                        <div class="task-actions">
                            <button class="btn btn-secondary" onclick="showTaskDetails('${task.task_id}')">詳情</button>
                            ${task.status === 'pending' || task.status === 'running' ? 
                                `<button class="btn btn-danger" onclick="cancelTask('${task.task_id}')">取消</button>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
            
            taskList.innerHTML = tasksHTML;
        }

        // 取消任務
        async function cancelTask(taskId) {
            if (!confirm('確定要取消這個任務嗎？')) return;
            
            try {
                const response = await fetch(`${API_BASE}/tasks/${taskId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showAlert('任務已取消', 'success');
                    loadTasks();
                    loadSystemStatus();
                } else {
                    showAlert('取消任務失敗: ' + (result.detail || '未知錯誤'), 'danger');
                }
            } catch (error) {
                showAlert('取消任務時發生錯誤: ' + error.message, 'danger');
            }
        }

        // 顯示任務詳情
        async function showTaskDetails(taskId) {
            try {
                const response = await fetch(`${API_BASE}/tasks/${taskId}/status`);
                const result = await response.json();
                
                if (response.ok && result.data) {
                    const task = result.data;
                    alert(`任務詳情:\n\n任務ID: ${task.task_id}\n類型: ${task.task_type}\n狀態: ${getStatusText(task.status)}\n建立時間: ${formatDateTime(task.created_at)}\n${task.started_at ? `開始時間: ${formatDateTime(task.started_at)}\n` : ''}${task.completed_at ? `完成時間: ${formatDateTime(task.completed_at)}\n` : ''}進度: ${task.progress}%\n${task.error_message ? `錯誤: ${task.error_message}\n` : ''}${task.actual_duration ? `執行時間: ${task.actual_duration}秒` : ''}`);
                } else {
                    showAlert('載入任務詳情失敗', 'danger');
                }
            } catch (error) {
                showAlert('載入任務詳情時發生錯誤: ' + error.message, 'danger');
            }
        }

        // 切換頁籤
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新按鈕狀態
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.tab === tab);
            });
            
            // 載入對應任務
            loadTasks();
        }

        // 重新整理任務
        function refreshTasks() {
            loadTasks();
            loadSystemStatus();
        }

        // 開始自動重新整理
        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                loadTasks();
                loadSystemStatus();
            }, 5000); // 每5秒重新整理
        }

        // 停止自動重新整理
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // 工具函數
        function getStatusText(status) {
            const statusMap = {
                'pending': '等待中',
                'running': '執行中',
                'completed': '已完成',
                'failed': '失敗',
                'cancelled': '已取消',
                'timeout': '超時'
            };
            return statusMap[status] || status;
        }

        function formatDateTime(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleString('zh-TW');
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            const controls = document.querySelector('.controls');
            controls.insertBefore(alert, controls.firstChild);
            
            // 3秒後自動移除
            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // 頁面離開時停止自動重新整理
        window.addEventListener('beforeunload', stopAutoRefresh);
    </script>
</body>
</html>