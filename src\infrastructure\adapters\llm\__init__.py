"""
Grok LLM 解析器模組
提供基於 Grok 的智能郵件解析功能
"""

from .grok_client import GrokClient, GrokRequest, GrokResponse
from .grok_parsing_classifier import (
    GrokParsingClassifier, 
    VendorType, 
    ParsingMethod, 
    EmailAnalysis
)
from .grok_smart_parser import GrokSmartParser, SmartParsingResult
from .grok_parser_factory import GrokParserFactory

__all__ = [
    # 核心客戶端
    'GrokClient',
    'GrokRequest', 
    'GrokResponse',
    
    # 分類器
    'GrokParsingClassifier',
    'VendorType',
    'ParsingMethod',
    'EmailAnalysis',
    
    # 智能解析器
    'GrokSmartParser',
    'SmartParsingResult',
    
    # 工廠類
    'GrokParserFactory'
]

# 版本資訊
__version__ = "1.0.0"
__author__ = "Email Parser Team"
__description__ = "Grok LLM智能郵件解析器，支援所有廠商的解析方式"

# 模組級別的便捷函數
def create_grok_parser():
    """
    創建 Grok 解析器實例
    
    Returns:
        GrokParserFactory: Grok解析器工廠實例
    """
    return GrokParserFactory()

def test_grok_connection():
    """
    測試 Grok 連接
    
    Returns:
        dict: 連接測試結果
    """
    factory = GrokParserFactory()
    return factory.test_grok_connection()

def get_supported_vendors():
    """
    獲取支援的廠商列表
    
    Returns:
        list: 支援的廠商列表
    """
    return ["JCET", "GTK", "ETD", "LINGSEN", "XAHT"]

def get_parsing_methods():
    """
    獲取所有解析方法
    
    Returns:
        dict: 解析方法資訊
    """
    return {
        "JCET": [
            "kui_pattern_long", "gyc_pattern_long", "kui_pattern_short", 
            "gyc_pattern_short", "lot_pattern", "test_batch_pattern", 
            "product_pattern", "standard_mo_format", "jcet_specific_format"
        ],
        "GTK": [
            "keyword_extraction", "device_type_extraction", 
            "bin1_extraction", "in_qty_extraction"
        ],
        "ETD": [
            "anf_subject_parsing", "qty_extraction", 
            "yield_extraction", "anomaly_detection"
        ],
        "LINGSEN": [
            "product_code_pattern", "run_extraction", 
            "lot_extraction", "yield_extraction"
        ],
        "XAHT": [
            "wa_gyc_pattern", "underscore_format", "dual_method"
        ]
    }

# 模組初始化日誌
import logging
logger = logging.getLogger(__name__)
logger.info(f"Grok LLM 解析器模組已載入 - 版本: {__version__}")