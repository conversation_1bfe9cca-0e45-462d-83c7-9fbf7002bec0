#!/usr/bin/env python3
"""
測試驗證和回滾機制
確保所有變更都經過驗證，並提供回滾功能
"""

import ast
import os
import shutil
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
import json

class ValidationEngine:
    """驗證引擎"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('ValidationEngine')
        
        # 驗證配置
        self.run_syntax_check = config.get('run_syntax_check', True)
        self.run_import_check = config.get('run_import_check', True)
        self.run_tests = config.get('run_tests', True)
        self.test_command = config.get('test_command', 'pytest tests/ -v')
        self.max_validation_time = config.get('max_validation_time', 300)  # 5分鐘
        
        # 回滾配置
        self.auto_create_snapshots = config.get('auto_create_snapshots', True)
        self.max_snapshots = config.get('max_snapshots', 10)
        self.snapshot_dir = self.repo_root / '.claude' / 'snapshots'
        self.snapshot_dir.mkdir(parents=True, exist_ok=True)
    
    async def validate_changes(self, changes: List[Dict[str, Any]], 
                             operation_type: str = 'unknown') -> Dict[str, Any]:
        """驗證變更"""
        self.logger.info(f"🔍 開始驗證變更: {operation_type}")
        
        # 創建快照
        snapshot_info = None
        if self.auto_create_snapshots:
            snapshot_info = await self._create_snapshot(operation_type)
        
        validation_results = {
            'status': 'unknown',
            'operation_type': operation_type,
            'snapshot_info': snapshot_info,
            'validation_steps': [],
            'errors': [],
            'warnings': [],
            'recommendations': []
        }
        
        try:
            # 步驟1: 語法檢查
            if self.run_syntax_check:
                syntax_result = await self._validate_syntax(changes)
                validation_results['validation_steps'].append(syntax_result)
                
                if not syntax_result['passed']:
                    validation_results['status'] = 'syntax_error'
                    validation_results['errors'].extend(syntax_result['errors'])
                    return validation_results
            
            # 步驟2: 導入檢查
            if self.run_import_check:
                import_result = await self._validate_imports(changes)
                validation_results['validation_steps'].append(import_result)
                
                if not import_result['passed']:
                    validation_results['status'] = 'import_error'
                    validation_results['errors'].extend(import_result['errors'])
                    validation_results['warnings'].extend(import_result.get('warnings', []))
            
            # 步驟3: 測試執行
            if self.run_tests:
                test_result = await self._run_tests()
                validation_results['validation_steps'].append(test_result)
                
                if not test_result['passed']:
                    validation_results['status'] = 'test_failure'
                    validation_results['errors'].extend(test_result['errors'])
                    
                    # 如果測試失敗，建議回滾
                    if snapshot_info:
                        validation_results['recommendations'].append({
                            'type': 'rollback_suggestion',
                            'message': '測試失敗，建議回滾到變更前狀態',
                            'snapshot_id': snapshot_info['snapshot_id']
                        })
            
            # 步驟4: 代碼品質檢查
            quality_result = await self._validate_code_quality(changes)
            validation_results['validation_steps'].append(quality_result)
            validation_results['warnings'].extend(quality_result.get('warnings', []))
            
            # 確定最終狀態
            if validation_results['status'] == 'unknown':
                if validation_results['errors']:
                    validation_results['status'] = 'failed'
                elif validation_results['warnings']:
                    validation_results['status'] = 'passed_with_warnings'
                else:
                    validation_results['status'] = 'passed'
            
            self.logger.info(f"✅ 驗證完成: {validation_results['status']}")
            return validation_results
            
        except Exception as e:
            self.logger.error(f"驗證過程中發生錯誤: {e}")
            validation_results['status'] = 'validation_error'
            validation_results['errors'].append(f'驗證系統錯誤: {str(e)}')
            return validation_results
    
    async def _create_snapshot(self, operation_type: str) -> Dict[str, Any]:
        """創建代碼快照"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        snapshot_id = f"{operation_type}_{timestamp}"
        snapshot_path = self.snapshot_dir / snapshot_id
        
        try:
            snapshot_path.mkdir(exist_ok=True)
            
            # 快照所有 Python 檔案
            files_backed_up = []
            total_size = 0
            
            for py_file in self.repo_root.rglob('*.py'):
                if any(ignore_dir in py_file.parts for ignore_dir in ['.git', '__pycache__', '.claude']):
                    continue
                
                relative_path = py_file.relative_to(self.repo_root)
                backup_file = snapshot_path / relative_path
                backup_file.parent.mkdir(parents=True, exist_ok=True)
                
                shutil.copy2(py_file, backup_file)
                files_backed_up.append(str(relative_path))
                total_size += py_file.stat().st_size
            
            # 保存快照元數據
            metadata = {
                'snapshot_id': snapshot_id,
                'timestamp': timestamp,
                'operation_type': operation_type,
                'files_count': len(files_backed_up),
                'total_size_bytes': total_size,
                'files': files_backed_up
            }
            
            metadata_file = snapshot_path / 'metadata.json'
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            # 清理舊快照
            await self._cleanup_old_snapshots()
            
            self.logger.info(f"📸 快照創建: {snapshot_id} ({len(files_backed_up)} 個檔案)")
            return metadata
            
        except Exception as e:
            self.logger.error(f"創建快照失敗: {e}")
            return {
                'snapshot_id': snapshot_id,
                'error': str(e),
                'success': False
            }
    
    async def _validate_syntax(self, changes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """驗證 Python 語法"""
        result = {
            'step': 'syntax_validation',
            'passed': True,
            'errors': [],
            'files_checked': []
        }
        
        # 收集需要檢查的檔案
        files_to_check = set()
        
        for change in changes:
            if 'file_path' in change:
                files_to_check.add(change['file_path'])
            if 'new_files' in change:
                for new_file in change['new_files']:
                    files_to_check.add(new_file.get('path', ''))
        
        # 檢查每個檔案的語法
        for file_path in files_to_check:
            if not file_path or not file_path.endswith('.py'):
                continue
            
            full_path = self.repo_root / file_path
            if not full_path.exists():
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 嘗試解析 AST
                ast.parse(content)
                result['files_checked'].append(file_path)
                
            except SyntaxError as e:
                result['passed'] = False
                result['errors'].append({
                    'file': file_path,
                    'line': e.lineno,
                    'column': e.offset,
                    'message': e.msg,
                    'type': 'syntax_error'
                })
            except Exception as e:
                result['passed'] = False
                result['errors'].append({
                    'file': file_path,
                    'message': str(e),
                    'type': 'parse_error'
                })
        
        return result
    
    async def _validate_imports(self, changes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """驗證導入語句"""
        result = {
            'step': 'import_validation',
            'passed': True,
            'errors': [],
            'warnings': [],
            'files_checked': []
        }
        
        # 收集需要檢查的檔案
        files_to_check = set()
        
        for change in changes:
            if 'file_path' in change:
                files_to_check.add(change['file_path'])
            if 'new_files' in change:
                for new_file in change['new_files']:
                    files_to_check.add(new_file.get('path', ''))
        
        # 檢查每個檔案的導入
        for file_path in files_to_check:
            if not file_path or not file_path.endswith('.py'):
                continue
            
            full_path = self.repo_root / file_path
            if not full_path.exists():
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                import_errors = await self._check_file_imports(tree, file_path)
                
                if import_errors['errors']:
                    result['passed'] = False
                    result['errors'].extend(import_errors['errors'])
                
                result['warnings'].extend(import_errors['warnings'])
                result['files_checked'].append(file_path)
                
            except Exception as e:
                result['warnings'].append({
                    'file': file_path,
                    'message': f'無法檢查導入: {str(e)}',
                    'type': 'import_check_failed'
                })
        
        return result
    
    async def _check_file_imports(self, tree: ast.AST, file_path: str) -> Dict[str, List]:
        """檢查單一檔案的導入"""
        errors = []
        warnings = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    try:
                        __import__(alias.name)
                    except ImportError:
                        # 檢查是否是本地模組
                        if not self._is_local_module(alias.name):
                            errors.append({
                                'file': file_path,
                                'line': node.lineno,
                                'message': f'無法導入模組: {alias.name}',
                                'type': 'import_error'
                            })
                    except Exception as e:
                        warnings.append({
                            'file': file_path,
                            'line': node.lineno,
                            'message': f'導入檢查警告 {alias.name}: {str(e)}',
                            'type': 'import_warning'
                        })
            
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    try:
                        __import__(node.module)
                    except ImportError:
                        if not self._is_local_module(node.module):
                            errors.append({
                                'file': file_path,
                                'line': node.lineno,
                                'message': f'無法導入模組: {node.module}',
                                'type': 'import_error'
                            })
                    except Exception as e:
                        warnings.append({
                            'file': file_path,
                            'line': node.lineno,
                            'message': f'導入檢查警告 {node.module}: {str(e)}',
                            'type': 'import_warning'
                        })
        
        return {'errors': errors, 'warnings': warnings}
    
    def _is_local_module(self, module_name: str) -> bool:
        """檢查是否是本地模組"""
        # 檢查相對路徑
        module_path = self.repo_root / f"{module_name.replace('.', '/')}.py"
        if module_path.exists():
            return True
        
        # 檢查包
        package_path = self.repo_root / module_name.replace('.', '/') / "__init__.py"
        if package_path.exists():
            return True
        
        return False
    
    async def _run_tests(self) -> Dict[str, Any]:
        """執行測試套件"""
        result = {
            'step': 'test_execution',
            'passed': True,
            'errors': [],
            'output': '',
            'duration': 0
        }
        
        if not self.test_command:
            result['errors'].append('未配置測試命令')
            result['passed'] = False
            return result
        
        try:
            start_time = datetime.now()
            
            # 執行測試命令
            process = subprocess.run(
                self.test_command.split(),
                cwd=self.repo_root,
                capture_output=True,
                text=True,
                timeout=self.max_validation_time
            )
            
            end_time = datetime.now()
            result['duration'] = (end_time - start_time).total_seconds()
            result['output'] = process.stdout + process.stderr
            result['return_code'] = process.returncode
            
            if process.returncode != 0:
                result['passed'] = False
                result['errors'].append({
                    'message': '測試執行失敗',
                    'return_code': process.returncode,
                    'output': result['output']
                })
            
        except subprocess.TimeoutExpired:
            result['passed'] = False
            result['errors'].append({
                'message': f'測試執行超時 (>{self.max_validation_time}秒)',
                'type': 'timeout'
            })
        except FileNotFoundError:
            result['passed'] = False
            result['errors'].append({
                'message': f'找不到測試命令: {self.test_command}',
                'type': 'command_not_found'
            })
        except Exception as e:
            result['passed'] = False
            result['errors'].append({
                'message': f'測試執行錯誤: {str(e)}',
                'type': 'execution_error'
            })
        
        return result
    
    async def _validate_code_quality(self, changes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """驗證代碼品質"""
        result = {
            'step': 'quality_validation',
            'passed': True,
            'warnings': [],
            'metrics': {}
        }
        
        # 收集需要檢查的檔案
        files_to_check = set()
        
        for change in changes:
            if 'file_path' in change:
                files_to_check.add(change['file_path'])
            if 'new_files' in change:
                for new_file in change['new_files']:
                    files_to_check.add(new_file.get('path', ''))
        
        # 品質檢查指標
        total_files = 0
        total_lines = 0
        long_files = 0
        complex_functions = 0
        
        for file_path in files_to_check:
            if not file_path or not file_path.endswith('.py'):
                continue
            
            full_path = self.repo_root / file_path
            if not full_path.exists():
                continue
            
            try:
                with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                
                total_files += 1
                file_lines = len(lines)
                total_lines += file_lines
                
                # 檢查檔案長度
                if file_lines > 500:
                    long_files += 1
                    result['warnings'].append({
                        'file': file_path,
                        'message': f'檔案過長 ({file_lines} 行)',
                        'type': 'long_file',
                        'lines': file_lines
                    })
                
                # 檢查函數複雜度
                content = ''.join(lines)
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        complexity = self._calculate_complexity(node)
                        if complexity > 10:
                            complex_functions += 1
                            result['warnings'].append({
                                'file': file_path,
                                'line': node.lineno,
                                'function': node.name,
                                'message': f'函數複雜度過高 ({complexity})',
                                'type': 'high_complexity',
                                'complexity': complexity
                            })
                
            except Exception as e:
                result['warnings'].append({
                    'file': file_path,
                    'message': f'品質檢查失敗: {str(e)}',
                    'type': 'quality_check_failed'
                })
        
        # 設定指標
        result['metrics'] = {
            'total_files': total_files,
            'total_lines': total_lines,
            'long_files': long_files,
            'complex_functions': complex_functions,
            'avg_lines_per_file': total_lines / total_files if total_files > 0 else 0
        }
        
        return result
    
    def _calculate_complexity(self, func_node: ast.FunctionDef) -> int:
        """計算函數複雜度"""
        complexity = 1
        
        for node in ast.walk(func_node):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, ast.With):
                complexity += 1
        
        return complexity
    
    async def _cleanup_old_snapshots(self):
        """清理舊快照"""
        try:
            snapshots = []
            for snapshot_dir in self.snapshot_dir.iterdir():
                if snapshot_dir.is_dir():
                    metadata_file = snapshot_dir / 'metadata.json'
                    if metadata_file.exists():
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        snapshots.append((snapshot_dir, metadata.get('timestamp', '')))
            
            # 按時間排序，保留最新的
            snapshots.sort(key=lambda x: x[1], reverse=True)
            
            # 刪除多餘的快照
            for snapshot_dir, _ in snapshots[self.max_snapshots:]:
                shutil.rmtree(snapshot_dir)
                self.logger.debug(f"刪除舊快照: {snapshot_dir.name}")
                
        except Exception as e:
            self.logger.warning(f"清理快照失敗: {e}")

class RollbackManager:
    """回滾管理器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('RollbackManager')
        self.snapshot_dir = self.repo_root / '.claude' / 'snapshots'
    
    async def list_snapshots(self) -> List[Dict[str, Any]]:
        """列出所有可用快照"""
        snapshots = []
        
        try:
            for snapshot_dir in self.snapshot_dir.iterdir():
                if snapshot_dir.is_dir():
                    metadata_file = snapshot_dir / 'metadata.json'
                    if metadata_file.exists():
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        
                        metadata['snapshot_path'] = str(snapshot_dir)
                        snapshots.append(metadata)
            
            # 按時間排序
            snapshots.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            return snapshots
            
        except Exception as e:
            self.logger.error(f"列出快照失敗: {e}")
            return []
    
    async def rollback_to_snapshot(self, snapshot_id: str) -> Dict[str, Any]:
        """回滾到指定快照"""
        self.logger.info(f"🔄 開始回滾到快照: {snapshot_id}")
        
        try:
            snapshot_path = self.snapshot_dir / snapshot_id
            if not snapshot_path.exists():
                return {
                    'status': 'error',
                    'message': f'快照不存在: {snapshot_id}'
                }
            
            # 讀取快照元數據
            metadata_file = snapshot_path / 'metadata.json'
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 創建當前狀態的備份
            current_backup = await self._create_current_backup()
            
            restored_files = []
            errors = []
            
            # 恢復檔案
            for file_path in metadata.get('files', []):
                try:
                    source_file = snapshot_path / file_path
                    target_file = self.repo_root / file_path
                    
                    if source_file.exists():
                        # 確保目錄存在
                        target_file.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 複製檔案
                        shutil.copy2(source_file, target_file)
                        restored_files.append(file_path)
                    else:
                        errors.append(f'快照中缺少檔案: {file_path}')
                        
                except Exception as e:
                    errors.append(f'恢復檔案 {file_path} 失敗: {str(e)}')
            
            result = {
                'status': 'success' if not errors else 'partial_success',
                'snapshot_id': snapshot_id,
                'restored_files': len(restored_files),
                'total_files': len(metadata.get('files', [])),
                'current_backup': current_backup,
                'errors': errors
            }
            
            if errors:
                result['message'] = f'部分檔案恢復失敗 ({len(errors)} 個錯誤)'
            else:
                result['message'] = f'成功恢復 {len(restored_files)} 個檔案'
            
            self.logger.info(f"✅ 回滾完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"回滾失敗: {e}")
            return {
                'status': 'error',
                'message': f'回滾失敗: {str(e)}'
            }
    
    async def _create_current_backup(self) -> Dict[str, Any]:
        """創建當前狀態的備份"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_id = f"pre_rollback_{timestamp}"
        backup_path = self.snapshot_dir / backup_id
        
        try:
            backup_path.mkdir(exist_ok=True)
            
            files_backed_up = []
            for py_file in self.repo_root.rglob('*.py'):
                if any(ignore_dir in py_file.parts for ignore_dir in ['.git', '__pycache__', '.claude']):
                    continue
                
                relative_path = py_file.relative_to(self.repo_root)
                backup_file = backup_path / relative_path
                backup_file.parent.mkdir(parents=True, exist_ok=True)
                
                shutil.copy2(py_file, backup_file)
                files_backed_up.append(str(relative_path))
            
            metadata = {
                'snapshot_id': backup_id,
                'timestamp': timestamp,
                'operation_type': 'pre_rollback_backup',
                'files_count': len(files_backed_up),
                'files': files_backed_up
            }
            
            metadata_file = backup_path / 'metadata.json'
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            return {
                'backup_id': backup_id,
                'files_count': len(files_backed_up)
            }
            
        except Exception as e:
            self.logger.warning(f"創建回滾前備份失敗: {e}")
            return {'error': str(e)}