# FT Summary UI 布局更新說明

## 更新概述

已將 `http://localhost:5555/ft-eqc/ft-summary-ui` 的布局修改為與 `http://localhost:5555/ft-eqc/ui` 完全一致的橫向布局設計。

## 主要變更

### 1. 布局結構調整

**修改前：**
- 垂直布局
- 輸入方式選擇（單選按鈕）
- 條件顯示路徑輸入或文件上傳

**修改後：**
- 橫向布局（與 ft-eqc/ui 一致）
- 左側：主要路徑輸入區域（folder-input-card）
- 右側：文件上傳區域（side-card）
- 並行操作，無需切換

### 2. HTML 結構變更

```html
<!-- 新的橫向布局 -->
<div class="controls-container">
    <!-- 左側主要區域 -->
    <div class="folder-input-card">
        <div class="folder-icon">
            <i class="fas fa-folder-open"></i>
        </div>
        <h3>📁 資料夾輸入</h3>
        <form id="ftSummaryForm">
            <input id="folderPath" class="folder-input" />
            <button class="primary-btn">開始批量處理</button>
        </form>
    </div>
    
    <!-- 右側文件上傳 -->
    <div class="side-card">
        <h4><i class="fas fa-upload"></i> 檔案上傳</h4>
        <div class="upload-zone" id="uploadZone">
            <i class="fas fa-cloud-upload-alt"></i>
            <div>拖放檔案或點擊上傳</div>
            <input type="file" id="fileInput" />
        </div>
        <div id="uploadStatus"></div>
    </div>
</div>
```

### 3. CSS 樣式更新

**引用 ft-eqc CSS：**
```html
<link rel="stylesheet" href="/ft-eqc/static/css/variables.css">
<link rel="stylesheet" href="/ft-eqc/static/css/base.css">
<link rel="stylesheet" href="/ft-eqc/static/css/components.css">
<link rel="stylesheet" href="/ft-eqc/static/css/layout.css">
<link rel="stylesheet" href="/ft-eqc/static/css/special-components.css">
<link rel="stylesheet" href="/ft-eqc/static/css/responsive.css">
```

**新增樣式：**
- `.controls-container`: 橫向布局容器
- `.folder-input-card`: 左側主要輸入區域
- `.side-card`: 右側文件上傳區域
- `.folder-input`: 路徑輸入框樣式
- `.primary-btn`: 主要按鈕樣式

### 4. JavaScript 邏輯調整

**移除功能：**
- 輸入方式切換邏輯
- `handleInputMethodChange()` 方法
- 條件顯示/隱藏邏輯

**保留功能：**
- 文件上傳處理
- 路徑驗證
- 雙重輸入支援（路徑 + 上傳）

**更新邏輯：**
```javascript
validateInput() {
    // 檢查路徑輸入或文件上傳
    const folderPath = document.getElementById('folderPath').value.trim();
    const hasPath = folderPath.length > 0;
    const hasUpload = this.uploadedFilePath !== null;
    
    // 任一方式有效即可
    const isValid = hasPath || hasUpload;
    // ...
}

startProcessing() {
    // 優先使用上傳的文件，其次使用路徑輸入
    let folderPath = '';
    
    if (this.uploadedFilePath) {
        folderPath = this.uploadedFilePath;
    } else {
        folderPath = document.getElementById('folderPath').value.trim();
    }
    // ...
}
```

## 功能特點

### 1. 並行操作
- 用戶可以同時看到路徑輸入和文件上傳選項
- 無需切換輸入方式
- 優先使用上傳文件，備用路徑輸入

### 2. 一致的視覺體驗
- 與 ft-eqc/ui 完全相同的布局
- 相同的 side-card 樣式
- 相同的文件上傳區域設計

### 3. 響應式設計
- 支援不同螢幕尺寸
- 橫向布局在大螢幕上效果更佳
- 保持良好的可用性

## 文件上傳區域規格

**完全匹配 ft-eqc/ui：**
```html
<div class="side-card">
    <h4><i class="fas fa-upload" aria-hidden="true"></i> 檔案上傳</h4>
    <div class="upload-zone" id="uploadZone" style="min-height: 80px; display: flex; flex-direction: column; justify-content: center;">
        <i class="fas fa-cloud-upload-alt" aria-hidden="true" style="font-size: 1.5em; color: var(--primary-color); margin-bottom: 8px;"></i>
        <div style="font-size: 12px; text-align: center;">
            拖放檔案或點擊上傳<br>
            <small style="color: #666;">支援 ZIP, 7Z, RAR<br>(最大 <span id="maxSizeDisplay">1000</span>MB)</small>
        </div>
        <input type="file" id="fileInput" accept=".zip,.7z,.rar,.tar,.gz" style="display: none;">
    </div>
    <div id="uploadStatus" style="margin-top: 8px; font-size: 11px; display: none;"></div>
</div>
```

## 使用方式

### 方式一：路徑輸入
1. 在左側輸入框輸入路徑
2. 支援本地路徑：`D:\folder`
3. 支援遠端路徑：`\\server\share\folder`
4. 點擊「開始批量處理」

### 方式二：文件上傳
1. 在右側上傳區域拖放或點擊上傳
2. 支援 ZIP, 7Z, RAR 格式
3. 最大 1000MB
4. 上傳完成後自動啟用處理按鈕

### 方式三：混合使用
1. 可以先輸入路徑作為備用
2. 再上傳文件（優先使用）
3. 系統會自動選擇最佳輸入源

## 相容性

- ✅ 保持所有原有功能
- ✅ 支援雙反斜線遠端路徑
- ✅ 支援文件上傳和自動解壓縮
- ✅ 支援處理完成後壓縮下載
- ✅ 向下相容現有 API

## 技術細節

**CSS 變數支援：**
- `var(--primary-color)`: 主要顏色
- `var(--secondary-color)`: 次要顏色
- 自動適配主題色彩

**響應式斷點：**
- 大螢幕：橫向布局
- 小螢幕：自動調整（通過 responsive.css）

**文件引用順序：**
1. ft-eqc CSS 文件（基礎樣式）
2. 自定義樣式（覆蓋和擴展）
3. ft-eqc JavaScript 工具
4. 文件上傳組件
5. 主處理器

## 測試建議

1. **布局測試**：檢查橫向布局是否正確顯示
2. **功能測試**：驗證路徑輸入和文件上傳都能正常工作
3. **響應式測試**：在不同螢幕尺寸下測試
4. **整合測試**：確保與後端 API 正常通信
5. **視覺一致性**：與 ft-eqc/ui 對比確認樣式一致

現在 `ft-summary-ui` 已經完全採用與 `ft-eqc/ui` 相同的布局設計！
