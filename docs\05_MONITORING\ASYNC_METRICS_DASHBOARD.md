# 異步指標儀表板設計與實現

## 📊 儀表板總覽

### 設計目標
- **即時性能展示**: 以直覺的視覺化方式展示異步系統各項性能指標
- **分級監控視圖**: 支援從系統整體到單個組件的層級化檢視
- **歷史趨勢分析**: 提供長期性能走勢和模式識別
- **互動式操作**: 支援鼽取細節、過濾和實時調整

### 儀表板架構
```mermaid
graph TB
    A[總覽儀表板] --> B[異步任務監控]
    A --> C[API性能監控]
    A --> D[資源使用監控]
    A --> E[業務指標監控]
    
    B --> B1[協程狀態圖表]
    B --> B2[任務佇列監控]
    B --> B3[事件循環性能]
    
    C --> C1[響應時間分布]
    C --> C2[吞吐量統計]
    C --> C3[錯誤率追蹤]
    
    D --> D1[記憶體使用量]
    D --> D2[CPU利用率]
    D --> D3[連接池狀態]
    
    E --> E1[郵件處理統計]
    E --> E2[文件處理速度]
    E --> E3[業務效率指標]
```

---

## 📊 儀表板組件設計

### 1. 總覽儀表板 (Overview Dashboard)

#### 1.1 關鍵指標卡片 (KPI Cards)
```html
<!-- 總覽儀表板核心結構 -->
<div class="dashboard-overview">
    <!-- 系統健康狀態 -->
    <div class="kpi-card system-health" id="system-health">
        <div class="card-header">
            <h3>系統健康狀態</h3>
            <div class="health-indicator" id="health-status"></div>
        </div>
        <div class="card-content">
            <div class="metric-row">
                <span class="label">異步任務</span>
                <span class="value" id="active-tasks">--</span>
            </div>
            <div class="metric-row">
                <span class="label">活躍連接</span>
                <span class="value" id="active-connections">--</span>
            </div>
            <div class="metric-row">
                <span class="label">當前負載</span>
                <span class="value" id="current-load">--</span>
            </div>
        </div>
    </div>
    
    <!-- API性能指標 -->
    <div class="kpi-card api-performance" id="api-performance">
        <div class="card-header">
            <h3>API性能</h3>
            <div class="trend-indicator" id="api-trend"></div>
        </div>
        <div class="card-content">
            <div class="metric-row">
                <span class="label">平均響應時間</span>
                <span class="value" id="avg-response-time">-- ms</span>
            </div>
            <div class="metric-row">
                <span class="label">每秒請求數</span>
                <span class="value" id="requests-per-second">--</span>
            </div>
            <div class="metric-row">
                <span class="label">成功率</span>
                <span class="value" id="success-rate">--%</span>
            </div>
        </div>
    </div>
    
    <!-- 資源使用情況 -->
    <div class="kpi-card resource-usage" id="resource-usage">
        <div class="card-header">
            <h3>資源使用</h3>
            <div class="alert-indicator" id="resource-alerts"></div>
        </div>
        <div class="card-content">
            <div class="metric-row">
                <span class="label">CPU使用率</span>
                <span class="value" id="cpu-usage">--%</span>
                <div class="progress-bar" id="cpu-progress"></div>
            </div>
            <div class="metric-row">
                <span class="label">記憶體使用</span>
                <span class="value" id="memory-usage">--%</span>
                <div class="progress-bar" id="memory-progress"></div>
            </div>
            <div class="metric-row">
                <span class="label">磁碟使用</span>
                <span class="value" id="disk-usage">--%</span>
                <div class="progress-bar" id="disk-progress"></div>
            </div>
        </div>
    </div>
    
    <!-- 業務指標 -->
    <div class="kpi-card business-metrics" id="business-metrics">
        <div class="card-header">
            <h3>業務指標</h3>
            <div class="performance-badge" id="business-performance"></div>
        </div>
        <div class="card-content">
            <div class="metric-row">
                <span class="label">郵件處理率</span>
                <span class="value" id="email-processing-rate">-- /分</span>
            </div>
            <div class="metric-row">
                <span class="label">文件處理速度</span>
                <span class="value" id="file-processing-speed">-- MB/s</span>
            </div>
            <div class="metric-row">
                <span class="label">處理成功率</span>
                <span class="value" id="processing-success-rate">--%</span>
            </div>
        </div>
    </div>
</div>
```

#### 1.2 即時趨勢圖表
```html
<!-- 即時性能趨勢圖 -->
<div class="realtime-charts">
    <!-- 異步任務即時狀態 -->
    <div class="chart-container" id="async-tasks-chart">
        <div class="chart-header">
            <h4>異步任務即時狀態</h4>
            <div class="chart-controls">
                <button class="time-range-btn active" data-range="5m">5分鐘</button>
                <button class="time-range-btn" data-range="30m">30分鐘</button>
                <button class="time-range-btn" data-range="1h">1小時</button>
                <button class="time-range-btn" data-range="6h">6小時</button>
            </div>
        </div>
        <canvas id="asyncTasksCanvas" width="800" height="300"></canvas>
        <div class="chart-legend">
            <div class="legend-item">
                <span class="color-indicator active-tasks"></span>
                <span>活躍任務</span>
            </div>
            <div class="legend-item">
                <span class="color-indicator pending-tasks"></span>
                <span>等待任務</span>
            </div>
            <div class="legend-item">
                <span class="color-indicator completed-tasks"></span>
                <span>已完成任務 (每分鐘)</span>
            </div>
        </div>
    </div>
    
    <!-- API響應時間分布 -->
    <div class="chart-container" id="response-time-chart">
        <div class="chart-header">
            <h4>API響應時間分布</h4>
            <div class="metric-summary">
                <span class="summary-item">P50: <span id="p50-response">--</span>ms</span>
                <span class="summary-item">P95: <span id="p95-response">--</span>ms</span>
                <span class="summary-item">P99: <span id="p99-response">--</span>ms</span>
            </div>
        </div>
        <canvas id="responseTimeCanvas" width="800" height="300"></canvas>
    </div>
    
    <!-- 資源使用趨勢 -->
    <div class="chart-container" id="resource-trends-chart">
        <div class="chart-header">
            <h4>系統資源使用趨勢</h4>
            <div class="resource-toggles">
                <label><input type="checkbox" checked data-metric="cpu"> CPU</label>
                <label><input type="checkbox" checked data-metric="memory"> 記憶體</label>
                <label><input type="checkbox" checked data-metric="disk"> 磁碟</label>
                <label><input type="checkbox" checked data-metric="network"> 網路</label>
            </div>
        </div>
        <canvas id="resourceTrendsCanvas" width="800" height="300"></canvas>
    </div>
</div>
```

### 2. 異步任務監控儀表板

#### 2.1 任務執行概況
```html
<div class="async-tasks-dashboard">
    <!-- 任務統計概覽 -->
    <div class="task-statistics">
        <div class="stat-card">
            <div class="stat-value" id="total-tasks">--</div>
            <div class="stat-label">總任務數</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="active-tasks-count">--</div>
            <div class="stat-label">活躍任務</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="completed-tasks-count">--</div>
            <div class="stat-label">已完成</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="failed-tasks-count">--</div>
            <div class="stat-label">失敗任務</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="cancelled-tasks-count">--</div>
            <div class="stat-label">已取消</div>
        </div>
    </div>
    
    <!-- 任務列表視圖 -->
    <div class="task-list-view">
        <div class="view-header">
            <h4>即時任務狀態</h4>
            <div class="filter-controls">
                <select id="task-status-filter">
                    <option value="all">所有狀態</option>
                    <option value="running">運行中</option>
                    <option value="pending">等待中</option>
                    <option value="completed">已完成</option>
                    <option value="failed">已失敗</option>
                </select>
                <input type="text" id="task-search" placeholder="搜尋任務...">
            </div>
        </div>
        
        <div class="task-list" id="task-list">
            <!-- 動態生成的任務項目 -->
        </div>
    </div>
    
    <!-- 任務性能分析 -->
    <div class="task-performance-analysis">
        <div class="performance-metrics">
            <div class="metric-box">
                <div class="metric-title">平均執行時間</div>
                <div class="metric-value" id="avg-execution-time">-- ms</div>
                <div class="metric-trend" id="execution-time-trend"></div>
            </div>
            
            <div class="metric-box">
                <div class="metric-title">任務完成率</div>
                <div class="metric-value" id="task-completion-rate">-- /s</div>
                <div class="metric-trend" id="completion-rate-trend"></div>
            </div>
            
            <div class="metric-box">
                <div class="metric-title">錯誤率</div>
                <div class="metric-value" id="task-error-rate">--%</div>
                <div class="metric-trend" id="error-rate-trend"></div>
            </div>
        </div>
        
        <!-- 任務類型分布圖 -->
        <div class="task-type-distribution">
            <h5>任務類型分布</h5>
            <canvas id="taskTypeChart" width="400" height="300"></canvas>
        </div>
    </div>
</div>
```

#### 2.2 事件循環性能監控
```html
<div class="event-loop-monitoring">
    <!-- 事件循環健康指標 -->
    <div class="loop-health-indicators">
        <div class="health-metric">
            <div class="metric-name">循環延遲</div>
            <div class="metric-value" id="loop-lag">-- ms</div>
            <div class="status-indicator" id="lag-status"></div>
        </div>
        
        <div class="health-metric">
            <div class="metric-name">慢回調數量</div>
            <div class="metric-value" id="slow-callbacks">--</div>
            <div class="status-indicator" id="callbacks-status"></div>
        </div>
        
        <div class="health-metric">
            <div class="metric-name">事件佇列大小</div>
            <div class="metric-value" id="event-queue-size">--</div>
            <div class="status-indicator" id="queue-status"></div>
        </div>
    </div>
    
    <!-- 循環性能詳細圖表 -->
    <div class="loop-performance-chart">
        <div class="chart-header">
            <h4>事件循環性能詳細追蹤</h4>
        </div>
        <canvas id="eventLoopChart" width="800" height="400"></canvas>
    </div>
</div>
```

### 3. API性能儀表板

#### 3.1 端點性能分析
```html
<div class="api-performance-dashboard">
    <!-- API端點概覽 -->
    <div class="endpoints-overview">
        <div class="overview-header">
            <h4>API端點性能概覽</h4>
            <div class="time-selector">
                <select id="api-time-range">
                    <option value="1h">過去 1 小時</option>
                    <option value="6h">過去 6 小時</option>
                    <option value="24h">過去 24 小時</option>
                    <option value="7d">過去 7 天</option>
                </select>
            </div>
        </div>
        
        <div class="endpoints-table">
            <table id="api-endpoints-table">
                <thead>
                    <tr>
                        <th>端點</th>
                        <th>請求數</th>
                        <th>平均響應時間</th>
                        <th>P95響應時間</th>
                        <th>成功率</th>
                        <th>錯誤率</th>
                        <th>路徑</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 動態生成的端點數據 -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 響應時間热力圖 -->
    <div class="response-time-heatmap">
        <div class="heatmap-header">
            <h4>API響應時間热力圖</h4>
            <div class="heatmap-legend">
                <span class="legend-label">快</span>
                <div class="color-scale">
                    <div class="color-box fast"></div>
                    <div class="color-box medium"></div>
                    <div class="color-box slow"></div>
                    <div class="color-box critical"></div>
                </div>
                <span class="legend-label">慢</span>
            </div>
        </div>
        <div class="heatmap-container">
            <canvas id="responseHeatmapCanvas" width="800" height="400"></canvas>
        </div>
    </div>
    
    <!-- 錯誤率追蹤 -->
    <div class="error-tracking">
        <div class="error-overview">
            <div class="error-stat">
                <div class="stat-number" id="total-errors">--</div>
                <div class="stat-description">總錯誤數</div>
            </div>
            <div class="error-stat">
                <div class="stat-number" id="error-rate-percent">--%</div>
                <div class="stat-description">錯誤率</div>
            </div>
            <div class="error-stat">
                <div class="stat-number" id="critical-errors">--</div>
                <div class="stat-description">致命錯誤</div>
            </div>
        </div>
        
        <div class="error-details">
            <h5>最近錯誤詳情</h5>
            <div class="error-list" id="recent-errors">
                <!-- 動態生成的錯誤記錄 -->
            </div>
        </div>
    </div>
</div>
```

### 4. 資源使用儀表板

#### 4.1 系統資源監控
```html
<div class="resource-monitoring-dashboard">
    <!-- 資源使用總覽 -->
    <div class="resource-overview">
        <div class="resource-gauge-container">
            <!-- CPU使用率圖表 -->
            <div class="gauge-item">
                <div class="gauge-title">CPU使用率</div>
                <canvas id="cpuGauge" width="200" height="200"></canvas>
                <div class="gauge-details">
                    <div class="detail-item">
                        <span class="label">當前:</span>
                        <span class="value" id="current-cpu">--%</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">平均:</span>
                        <span class="value" id="avg-cpu">--%</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">峰值:</span>
                        <span class="value" id="peak-cpu">--%</span>
                    </div>
                </div>
            </div>
            
            <!-- 記憶體使用率圖表 -->
            <div class="gauge-item">
                <div class="gauge-title">記憶體使用</div>
                <canvas id="memoryGauge" width="200" height="200"></canvas>
                <div class="gauge-details">
                    <div class="detail-item">
                        <span class="label">使用:</span>
                        <span class="value" id="used-memory">-- MB</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">可用:</span>
                        <span class="value" id="available-memory">-- MB</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">總計:</span>
                        <span class="value" id="total-memory">-- MB</span>
                    </div>
                </div>
            </div>
            
            <!-- 磁碟使用率圖表 -->
            <div class="gauge-item">
                <div class="gauge-title">磁碟使用</div>
                <canvas id="diskGauge" width="200" height="200"></canvas>
                <div class="gauge-details">
                    <div class="detail-item">
                        <span class="label">使用:</span>
                        <span class="value" id="used-disk">-- GB</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">可用:</span>
                        <span class="value" id="free-disk">-- GB</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">總計:</span>
                        <span class="value" id="total-disk">-- GB</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 連接池狀態監控 -->
    <div class="connection-pools-monitoring">
        <div class="pools-header">
            <h4>連接池狀態監控</h4>
        </div>
        
        <div class="pools-grid">
            <!-- 數據庫連接池 -->
            <div class="pool-card">
                <div class="pool-title">數據庫連接池</div>
                <div class="pool-stats">
                    <div class="stat-row">
                        <span class="stat-label">活躍連接:</span>
                        <span class="stat-value" id="db-active-connections">--</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">空閒連接:</span>
                        <span class="stat-value" id="db-idle-connections">--</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">等待請求:</span>
                        <span class="stat-value" id="db-pending-requests">--</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">最大連接:</span>
                        <span class="stat-value" id="db-max-connections">--</span>
                    </div>
                </div>
                <div class="pool-utilization">
                    <div class="utilization-bar">
                        <div class="utilization-fill" id="db-utilization-fill"></div>
                    </div>
                    <div class="utilization-text" id="db-utilization-text">--%</div>
                </div>
            </div>
            
            <!-- HTTP連接池 -->
            <div class="pool-card">
                <div class="pool-title">HTTP連接池</div>
                <div class="pool-stats">
                    <div class="stat-row">
                        <span class="stat-label">活躍連接:</span>
                        <span class="stat-value" id="http-active-connections">--</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">空閒連接:</span>
                        <span class="stat-value" id="http-idle-connections">--</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">總請求數:</span>
                        <span class="stat-value" id="http-total-requests">--</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">最大連接:</span>
                        <span class="stat-value" id="http-max-connections">--</span>
                    </div>
                </div>
                <div class="pool-utilization">
                    <div class="utilization-bar">
                        <div class="utilization-fill" id="http-utilization-fill"></div>
                    </div>
                    <div class="utilization-text" id="http-utilization-text">--%</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- I/O操作監控 -->
    <div class="io-operations-monitoring">
        <div class="io-header">
            <h4>I/O操作監控</h4>
        </div>
        
        <div class="io-metrics">
            <div class="io-category">
                <h5>文件I/O</h5>
                <div class="io-stats">
                    <div class="io-stat">
                        <span class="label">讀取速度:</span>
                        <span class="value" id="file-read-speed">-- MB/s</span>
                    </div>
                    <div class="io-stat">
                        <span class="label">寫入速度:</span>
                        <span class="value" id="file-write-speed">-- MB/s</span>
                    </div>
                    <div class="io-stat">
                        <span class="label">活躍操作:</span>
                        <span class="value" id="active-file-ops">--</span>
                    </div>
                </div>
            </div>
            
            <div class="io-category">
                <h5>網路I/O</h5>
                <div class="io-stats">
                    <div class="io-stat">
                        <span class="label">上傳速度:</span>
                        <span class="value" id="network-upload-speed">-- KB/s</span>
                    </div>
                    <div class="io-stat">
                        <span class="label">下載速度:</span>
                        <span class="value" id="network-download-speed">-- KB/s</span>
                    </div>
                    <div class="io-stat">
                        <span class="label">連接數:</span>
                        <span class="value" id="network-connections">--</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- I/O趨勢圖表 -->
        <div class="io-trends-chart">
            <canvas id="ioTrendsChart" width="800" height="300"></canvas>
        </div>
    </div>
</div>
```

---

## 💻 儀表板互動功能實現

### 1. 即時數據更新系統

#### 1.1 WebSocket連接管理
```javascript
// 即時數據接收系統
class RealtimeMetricsConnector {
    constructor(wsUrl) {
        this.wsUrl = wsUrl;
        this.ws = null;
        this.reconnectInterval = 5000;
        this.maxReconnectAttempts = 10;
        this.reconnectAttempts = 0;
        this.isConnected = false;
        this.messageHandlers = new Map();
        this.metricsBuffer = new Map();
        
        this.connect();
    }
    
    connect() {
        try {
            this.ws = new WebSocket(this.wsUrl);
            
            this.ws.onopen = () => {
                console.log('即時數據連接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.onConnected();
            };
            
            this.ws.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };
            
            this.ws.onclose = () => {
                console.log('即時數據連接已斷開');
                this.isConnected = false;
                this.attemptReconnect();
            };
            
            this.ws.onerror = (error) => {
                console.error('即時數據連接錯誤:', error);
            };
            
        } catch (error) {
            console.error('建立連接失敗:', error);
            this.attemptReconnect();
        }
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`嘗試重連 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectInterval);
        } else {
            console.error('重連失敗，已達到最大嘗試次數');
            this.showConnectionError();
        }
    }
    
    handleMessage(data) {
        const { type, payload } = data;
        
        // 緩存最新指標數據
        if (type === 'metrics_update') {
            this.updateMetricsBuffer(payload);
        }
        
        // 觸發相應的數據處理程序
        if (this.messageHandlers.has(type)) {
            this.messageHandlers.get(type)(payload);
        }
    }
    
    updateMetricsBuffer(metrics) {
        const timestamp = Date.now();
        
        for (const [metricName, value] of Object.entries(metrics)) {
            if (!this.metricsBuffer.has(metricName)) {
                this.metricsBuffer.set(metricName, []);
            }
            
            const buffer = this.metricsBuffer.get(metricName);
            buffer.push({ timestamp, value });
            
            // 保持緩存大小 (最多1000個數據點)
            if (buffer.length > 1000) {
                buffer.shift();
            }
        }
    }
    
    onMessageType(type, handler) {
        this.messageHandlers.set(type, handler);
    }
    
    getMetricHistory(metricName, timeRangeMs = 300000) { // 預設取5分鐘
        const cutoffTime = Date.now() - timeRangeMs;
        const buffer = this.metricsBuffer.get(metricName) || [];
        
        return buffer.filter(point => point.timestamp >= cutoffTime);
    }
    
    getCurrentMetric(metricName) {
        const buffer = this.metricsBuffer.get(metricName) || [];
        return buffer.length > 0 ? buffer[buffer.length - 1] : null;
    }
    
    onConnected() {
        // 訂閱所需的指標類型
        this.subscribe([
            'async_tasks',
            'api_performance', 
            'system_resources',
            'business_metrics'
        ]);
    }
    
    subscribe(metricTypes) {
        if (this.isConnected) {
            this.ws.send(JSON.stringify({
                type: 'subscribe',
                payload: { metrics: metricTypes }
            }));
        }
    }
    
    showConnectionError() {
        // 顯示連接錯誤提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'connection-error';
        errorDiv.innerHTML = `
            <div class="error-message">
                <i class="icon-warning"></i>
                即時數據連接中斷，正在嘗試重連...
                <button onclick="location.reload()">重新整理</button>
            </div>
        `;
        document.body.appendChild(errorDiv);
    }
}
```

#### 1.2 圖表更新管理
```javascript
// 圖表管理器
class DashboardChartManager {
    constructor(metricsConnector) {
        this.connector = metricsConnector;
        this.charts = new Map();
        this.updateInterval = 1000; // 1秒更新一次
        this.isUpdating = false;
        
        this.initializeCharts();
        this.startUpdating();
    }
    
    initializeCharts() {
        // 初始化異步任務圖表
        this.charts.set('asyncTasks', this.createAsyncTasksChart());
        
        // 初始化API性能圖表
        this.charts.set('apiPerformance', this.createAPIPerformanceChart());
        
        // 初始化資源使用圖表
        this.charts.set('resourceUsage', this.createResourceUsageChart());
        
        // 初始化業務指標圖表
        this.charts.set('businessMetrics', this.createBusinessMetricsChart());
    }
    
    createAsyncTasksChart() {
        const canvas = document.getElementById('asyncTasksCanvas');
        const ctx = canvas.getContext('2d');
        
        return new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '活躍任務',
                        data: [],
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.1
                    },
                    {
                        label: '等待任務',
                        data: [],
                        borderColor: '#FF9800',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        tension: 0.1
                    },
                    {
                        label: '完成任務/分',
                        data: [],
                        borderColor: '#2196F3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '任務數量'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '完成率 (/分鐘)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: '時間'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.parsed.y;
                                return label;
                            }
                        }
                    }
                }
            }
        });
    }
    
    createAPIPerformanceChart() {
        const canvas = document.getElementById('responseTimeCanvas');
        const ctx = canvas.getContext('2d');
        
        return new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '平均響應時間',
                        data: [],
                        borderColor: '#2196F3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.1
                    },
                    {
                        label: 'P95響應時間',
                        data: [],
                        borderColor: '#FF9800',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        tension: 0.1
                    },
                    {
                        label: 'P99響應時間',
                        data: [],
                        borderColor: '#F44336',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '響應時間 (ms)'
                        },
                        beginAtZero: true
                    },
                    x: {
                        title: {
                            display: true,
                            text: '時間'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });
    }
    
    createResourceUsageChart() {
        const canvas = document.getElementById('resourceTrendsCanvas');
        const ctx = canvas.getContext('2d');
        
        return new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'CPU使用率 (%)',
                        data: [],
                        borderColor: '#E91E63',
                        backgroundColor: 'rgba(233, 30, 99, 0.1)',
                        hidden: false
                    },
                    {
                        label: '記憶體使用率 (%)',
                        data: [],
                        borderColor: '#9C27B0',
                        backgroundColor: 'rgba(156, 39, 176, 0.1)',
                        hidden: false
                    },
                    {
                        label: '磁碟使用率 (%)',
                        data: [],
                        borderColor: '#673AB7',
                        backgroundColor: 'rgba(103, 58, 183, 0.1)',
                        hidden: false
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '使用率 (%)'
                        },
                        min: 0,
                        max: 100
                    },
                    x: {
                        title: {
                            display: true,
                            text: '時間'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        onClick: (e, legendItem) => {
                            // 自定義圖例點擊事件
                            const index = legendItem.datasetIndex;
                            const chart = this.charts.get('resourceUsage');
                            const meta = chart.getDatasetMeta(index);
                            
                            meta.hidden = meta.hidden === null ? !chart.data.datasets[index].hidden : null;
                            chart.update();
                        }
                    }
                }
            }
        });
    }
    
    createBusinessMetricsChart() {
        // 簡化的業務指標圖表實現
        const canvas = document.getElementById('businessMetricsCanvas');
        if (!canvas) return null;
        
        const ctx = canvas.getContext('2d');
        
        return new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['郵件處理', '文件上傳', 'EQC處理', 'API請求'],
                datasets: [{
                    label: '每分鐘處理量',
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(33, 150, 243, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(156, 39, 176, 0.8)'
                    ],
                    borderColor: [
                        '#4CAF50',
                        '#2196F3',
                        '#FF9800', 
                        '#9C27B0'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '處理量 (/分鐘)'
                        }
                    }
                }
            }
        });
    }
    
    startUpdating() {
        this.isUpdating = true;
        this.updateLoop();
    }
    
    stopUpdating() {
        this.isUpdating = false;
    }
    
    async updateLoop() {
        while (this.isUpdating) {
            try {
                await this.updateAllCharts();
                await new Promise(resolve => setTimeout(resolve, this.updateInterval));
            } catch (error) {
                console.error('更新圖表錯誤:', error);
                await new Promise(resolve => setTimeout(resolve, 5000)); // 錯誤時等待5秒
            }
        }
    }
    
    async updateAllCharts() {
        // 更新異步任務圖表
        this.updateAsyncTasksChart();
        
        // 更新API性能圖表
        this.updateAPIPerformanceChart();
        
        // 更新資源使用圖表
        this.updateResourceUsageChart();
        
        // 更新業務指標圖表
        this.updateBusinessMetricsChart();
        
        // 更新KPI卡片
        this.updateKPICards();
    }
    
    updateAsyncTasksChart() {
        const chart = this.charts.get('asyncTasks');
        if (!chart) return;
        
        const timeRange = this.getSelectedTimeRange();
        const activeTasks = this.connector.getMetricHistory('async.active_tasks', timeRange);
        const pendingTasks = this.connector.getMetricHistory('async.pending_tasks', timeRange);
        const completedTasks = this.connector.getMetricHistory('async.completed_tasks_per_minute', timeRange);
        
        // 生成時間標籤
        const labels = activeTasks.map(point => this.formatTimestamp(point.timestamp));
        
        chart.data.labels = labels;
        chart.data.datasets[0].data = activeTasks.map(point => point.value);
        chart.data.datasets[1].data = pendingTasks.map(point => point.value);
        chart.data.datasets[2].data = completedTasks.map(point => point.value);
        
        chart.update('none'); // 不使用動畫以提高性能
    }
    
    updateAPIPerformanceChart() {
        const chart = this.charts.get('apiPerformance');
        if (!chart) return;
        
        const timeRange = this.getSelectedTimeRange();
        const avgResponseTime = this.connector.getMetricHistory('api.avg_response_time', timeRange);
        const p95ResponseTime = this.connector.getMetricHistory('api.p95_response_time', timeRange);
        const p99ResponseTime = this.connector.getMetricHistory('api.p99_response_time', timeRange);
        
        const labels = avgResponseTime.map(point => this.formatTimestamp(point.timestamp));
        
        chart.data.labels = labels;
        chart.data.datasets[0].data = avgResponseTime.map(point => point.value);
        chart.data.datasets[1].data = p95ResponseTime.map(point => point.value);
        chart.data.datasets[2].data = p99ResponseTime.map(point => point.value);
        
        chart.update('none');
    }
    
    updateResourceUsageChart() {
        const chart = this.charts.get('resourceUsage');
        if (!chart) return;
        
        const timeRange = this.getSelectedTimeRange();
        const cpuUsage = this.connector.getMetricHistory('system.cpu_percent', timeRange);
        const memoryUsage = this.connector.getMetricHistory('system.memory_percent', timeRange);
        const diskUsage = this.connector.getMetricHistory('system.disk_usage_percent', timeRange);
        
        const labels = cpuUsage.map(point => this.formatTimestamp(point.timestamp));
        
        chart.data.labels = labels;
        chart.data.datasets[0].data = cpuUsage.map(point => point.value);
        chart.data.datasets[1].data = memoryUsage.map(point => point.value);
        chart.data.datasets[2].data = diskUsage.map(point => point.value);
        
        chart.update('none');
    }
    
    updateBusinessMetricsChart() {
        const chart = this.charts.get('businessMetrics');
        if (!chart) return;
        
        // 獲取最新的業務指標
        const emailProcessingRate = this.connector.getCurrentMetric('business.email_processing_rate');
        const fileUploadRate = this.connector.getCurrentMetric('business.file_upload_rate');
        const eqcProcessingRate = this.connector.getCurrentMetric('business.eqc_processing_rate');
        const apiRequestRate = this.connector.getCurrentMetric('api.requests_per_minute');
        
        chart.data.datasets[0].data = [
            emailProcessingRate?.value || 0,
            fileUploadRate?.value || 0,
            eqcProcessingRate?.value || 0,
            apiRequestRate?.value || 0
        ];
        
        chart.update('none');
    }
    
    updateKPICards() {
        // 更新系統健康狀態卡片
        const activeTasks = this.connector.getCurrentMetric('async.active_tasks');
        const activeConnections = this.connector.getCurrentMetric('db_pool.active_connections');
        const currentLoad = this.connector.getCurrentMetric('system.cpu_percent');
        
        this.updateElementText('active-tasks', activeTasks?.value || '--');
        this.updateElementText('active-connections', activeConnections?.value || '--');
        this.updateElementText('current-load', currentLoad ? `${currentLoad.value.toFixed(1)}%` : '--');
        
        // 更新API性能卡片
        const avgResponseTime = this.connector.getCurrentMetric('api.avg_response_time');
        const requestsPerSecond = this.connector.getCurrentMetric('api.requests_per_second');
        const successRate = this.connector.getCurrentMetric('api.success_rate');
        
        this.updateElementText('avg-response-time', avgResponseTime ? `${avgResponseTime.value.toFixed(0)} ms` : '-- ms');
        this.updateElementText('requests-per-second', requestsPerSecond?.value || '--');
        this.updateElementText('success-rate', successRate ? `${successRate.value.toFixed(1)}%` : '--%');
        
        // 更新資源使用卡片
        const cpuUsage = this.connector.getCurrentMetric('system.cpu_percent');
        const memoryUsage = this.connector.getCurrentMetric('system.memory_percent');
        const diskUsage = this.connector.getCurrentMetric('system.disk_usage_percent');
        
        this.updateElementText('cpu-usage', cpuUsage ? `${cpuUsage.value.toFixed(1)}%` : '--%');
        this.updateElementText('memory-usage', memoryUsage ? `${memoryUsage.value.toFixed(1)}%` : '--%');
        this.updateElementText('disk-usage', diskUsage ? `${diskUsage.value.toFixed(1)}%` : '--%');
        
        // 更新進度條
        this.updateProgressBar('cpu-progress', cpuUsage?.value || 0);
        this.updateProgressBar('memory-progress', memoryUsage?.value || 0);
        this.updateProgressBar('disk-progress', diskUsage?.value || 0);
        
        // 更新業務指標卡片
        const emailProcessingRate = this.connector.getCurrentMetric('business.email_processing_rate');
        const fileProcessingSpeed = this.connector.getCurrentMetric('business.file_processing_speed');
        const processingSuccessRate = this.connector.getCurrentMetric('business.processing_success_rate');
        
        this.updateElementText('email-processing-rate', emailProcessingRate ? `${emailProcessingRate.value.toFixed(0)} /分` : '-- /分');
        this.updateElementText('file-processing-speed', fileProcessingSpeed ? `${fileProcessingSpeed.value.toFixed(1)} MB/s` : '-- MB/s');
        this.updateElementText('processing-success-rate', processingSuccessRate ? `${processingSuccessRate.value.toFixed(1)}%` : '--%');
    }
    
    updateElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }
    
    updateProgressBar(elementId, percentage) {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.width = `${Math.min(percentage, 100)}%`;
            
            // 根據百分比設置顏色
            if (percentage < 50) {
                element.className = 'progress-fill normal';
            } else if (percentage < 80) {
                element.className = 'progress-fill warning';
            } else {
                element.className = 'progress-fill critical';
            }
        }
    }
    
    getSelectedTimeRange() {
        // 獲取當前選擇的時間範圍
        const selector = document.querySelector('.time-range-btn.active');
        const range = selector ? selector.dataset.range : '5m';
        
        const timeRanges = {
            '5m': 5 * 60 * 1000,    // 5分鐘
            '30m': 30 * 60 * 1000,  // 30分鐘
            '1h': 60 * 60 * 1000,   // 1小時
            '6h': 6 * 60 * 60 * 1000 // 6小時
        };
        
        return timeRanges[range] || timeRanges['5m'];
    }
    
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('zh-TW', { 
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}
```

### 2. 使用者互動功能

#### 2.1 過濾和搜尋
```javascript
// 過濾和搜尋功能
class DashboardInteractionManager {
    constructor() {
        this.setupTimeRangeControls();
        this.setupFilterControls();
        this.setupSearchFunctionality();
        this.setupExportFunctionality();
    }
    
    setupTimeRangeControls() {
        // 時間範圍控制
        document.querySelectorAll('.time-range-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 移除所有按鈕的active狀態
                document.querySelectorAll('.time-range-btn').forEach(b => b.classList.remove('active'));
                
                // 添加當前按鈕的active狀態
                e.target.classList.add('active');
                
                // 觸發圖表更新
                this.onTimeRangeChanged(e.target.dataset.range);
            });
        });
    }
    
    setupFilterControls() {
        // 異步任務過濾
        const taskStatusFilter = document.getElementById('task-status-filter');
        if (taskStatusFilter) {
            taskStatusFilter.addEventListener('change', (e) => {
                this.filterTasks(e.target.value);
            });
        }
        
        // API端點過濾
        const apiTimeRange = document.getElementById('api-time-range');
        if (apiTimeRange) {
            apiTimeRange.addEventListener('change', (e) => {
                this.filterAPIEndpoints(e.target.value);
            });
        }
        
        // 資源監控切換
        document.querySelectorAll('.resource-toggles input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.toggleResourceMetric(e.target.dataset.metric, e.target.checked);
            });
        });
    }
    
    setupSearchFunctionality() {
        // 任務搜尋
        const taskSearch = document.getElementById('task-search');
        if (taskSearch) {
            taskSearch.addEventListener('input', this.debounce((e) => {
                this.searchTasks(e.target.value);
            }, 300));
        }
        
        // API端點搜尋
        const apiSearch = document.getElementById('api-search');
        if (apiSearch) {
            apiSearch.addEventListener('input', this.debounce((e) => {
                this.searchAPIEndpoints(e.target.value);
            }, 300));
        }
    }
    
    setupExportFunctionality() {
        // 數據匯出功能
        const exportBtn = document.getElementById('export-data-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportDashboardData();
            });
        }
        
        // 圖表截圖功能
        const screenshotBtn = document.getElementById('screenshot-btn');
        if (screenshotBtn) {
            screenshotBtn.addEventListener('click', () => {
                this.takeScreenshot();
            });
        }
    }
    
    onTimeRangeChanged(range) {
        // 通知圖表管理器更新時間範圍
        if (window.chartManager) {
            window.chartManager.updateAllCharts();
        }
        
        console.log(`時間範圍已更改為: ${range}`);
    }
    
    filterTasks(status) {
        const taskList = document.getElementById('task-list');
        if (!taskList) return;
        
        const taskItems = taskList.querySelectorAll('.task-item');
        
        taskItems.forEach(item => {
            const taskStatus = item.dataset.status;
            
            if (status === 'all' || taskStatus === status) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
        
        console.log(`任務過濾器已設置為: ${status}`);
    }
    
    searchTasks(query) {
        const taskList = document.getElementById('task-list');
        if (!taskList) return;
        
        const taskItems = taskList.querySelectorAll('.task-item');
        const searchTerm = query.toLowerCase();
        
        taskItems.forEach(item => {
            const taskName = item.querySelector('.task-name').textContent.toLowerCase();
            const taskId = item.dataset.taskId || '';
            
            if (taskName.includes(searchTerm) || taskId.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
        
        console.log(`任務搜尋: ${query}`);
    }
    
    filterAPIEndpoints(timeRange) {
        // 更新API端點表格数據
        this.loadAPIEndpointsData(timeRange);
        console.log(`API端點時間範圍已更改為: ${timeRange}`);
    }
    
    searchAPIEndpoints(query) {
        const apiTable = document.getElementById('api-endpoints-table');
        if (!apiTable) return;
        
        const rows = apiTable.querySelectorAll('tbody tr');
        const searchTerm = query.toLowerCase();
        
        rows.forEach(row => {
            const endpoint = row.cells[0].textContent.toLowerCase();
            const path = row.cells[6].textContent.toLowerCase();
            
            if (endpoint.includes(searchTerm) || path.includes(searchTerm)) {
                row.style.display = 'table-row';
            } else {
                row.style.display = 'none';
            }
        });
        
        console.log(`API端點搜尋: ${query}`);
    }
    
    toggleResourceMetric(metric, enabled) {
        const chart = window.chartManager?.charts.get('resourceUsage');
        if (!chart) return;
        
        const metricIndex = {
            'cpu': 0,
            'memory': 1,
            'disk': 2,
            'network': 3
        }[metric];
        
        if (metricIndex !== undefined) {
            const meta = chart.getDatasetMeta(metricIndex);
            meta.hidden = !enabled;
            chart.update();
        }
        
        console.log(`資源指標 ${metric} ${enabled ? '已顯示' : '已隱藏'}`);
    }
    
    async loadAPIEndpointsData(timeRange) {
        try {
            const response = await fetch(`/monitoring/api-endpoints?range=${timeRange}`);
            const data = await response.json();
            
            this.updateAPIEndpointsTable(data.endpoints);
        } catch (error) {
            console.error('載入API端點數據失敗:', error);
        }
    }
    
    updateAPIEndpointsTable(endpoints) {
        const tableBody = document.querySelector('#api-endpoints-table tbody');
        if (!tableBody) return;
        
        tableBody.innerHTML = '';
        
        endpoints.forEach(endpoint => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${endpoint.name}</td>
                <td>${endpoint.request_count}</td>
                <td>${endpoint.avg_response_time.toFixed(0)}ms</td>
                <td>${endpoint.p95_response_time.toFixed(0)}ms</td>
                <td class="success-rate">${endpoint.success_rate.toFixed(1)}%</td>
                <td class="error-rate">${endpoint.error_rate.toFixed(2)}%</td>
                <td class="endpoint-path">${endpoint.path}</td>
            `;
            
            // 根據成功率設置顏色
            const successCell = row.querySelector('.success-rate');
            if (endpoint.success_rate < 95) {
                successCell.classList.add('low-success');
            } else if (endpoint.success_rate < 99) {
                successCell.classList.add('medium-success');
            } else {
                successCell.classList.add('high-success');
            }
            
            // 根據錯誤率設置顏色
            const errorCell = row.querySelector('.error-rate');
            if (endpoint.error_rate > 5) {
                errorCell.classList.add('high-error');
            } else if (endpoint.error_rate > 1) {
                errorCell.classList.add('medium-error');
            } else {
                errorCell.classList.add('low-error');
            }
            
            tableBody.appendChild(row);
        });
    }
    
    async exportDashboardData() {
        try {
            const exportData = {
                timestamp: new Date().toISOString(),
                timeRange: this.getSelectedTimeRange(),
                metrics: {
                    asyncTasks: window.metricsConnector?.getMetricHistory('async.active_tasks') || [],
                    apiPerformance: window.metricsConnector?.getMetricHistory('api.avg_response_time') || [],
                    systemResources: {
                        cpu: window.metricsConnector?.getMetricHistory('system.cpu_percent') || [],
                        memory: window.metricsConnector?.getMetricHistory('system.memory_percent') || [],
                        disk: window.metricsConnector?.getMetricHistory('system.disk_usage_percent') || []
                    }
                }
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `dashboard-metrics-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log('儀表板數據已匯出');
        } catch (error) {
            console.error('匯出数據失敗:', error);
        }
    }
    
    async takeScreenshot() {
        try {
            // 使用 html2canvas 庫截取儀表板截圖
            if (typeof html2canvas !== 'undefined') {
                const canvas = await html2canvas(document.querySelector('.dashboard-container'));
                
                const link = document.createElement('a');
                link.download = `dashboard-screenshot-${new Date().toISOString().split('T')[0]}.png`;
                link.href = canvas.toDataURL();
                link.click();
                
                console.log('儀表板截圖已保存');
            } else {
                console.warn('截圖功能需要 html2canvas 庫');
            }
        } catch (error) {
            console.error('截圖失敗:', error);
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    getSelectedTimeRange() {
        const selector = document.querySelector('.time-range-btn.active');
        return selector ? selector.dataset.range : '5m';
    }
}
```

---

## 🎨 樣式設計 (CSS)

### 1. 响應式布局
```css
/* 儀表板基本樣式 */
.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f7fa;
}

/* KPI卡片樣式 */
.dashboard-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.kpi-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

/* 健康狀態指示器 */
.health-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #e74c3c;
    animation: pulse 2s infinite;
}

.health-indicator.healthy {
    background-color: #27ae60;
}

.health-indicator.warning {
    background-color: #f39c12;
}

.health-indicator.critical {
    background-color: #e74c3c;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 指標行樣式 */
.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-row .label {
    color: #7f8c8d;
    font-size: 14px;
}

.metric-row .value {
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
}

/* 進度條樣式 */
.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #ecf0f1;
    border-radius: 2px;
    margin-top: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 2px;
}

.progress-fill.normal {
    background-color: #27ae60;
}

.progress-fill.warning {
    background-color: #f39c12;
}

.progress-fill.critical {
    background-color: #e74c3c;
}

/* 圖表容器樣式 */
.realtime-charts {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h4 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
}

/* 時間範圍按鈕組 */
.chart-controls {
    display: flex;
    gap: 8px;
}

.time-range-btn {
    padding: 6px 12px;
    border: 1px solid #bdc3c7;
    background: white;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.time-range-btn:hover {
    background-color: #ecf0f1;
}

.time-range-btn.active {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

/* 圖例樣式 */
.chart-legend {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-top: 16px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #7f8c8d;
}

.color-indicator {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.color-indicator.active-tasks {
    background-color: #4CAF50;
}

.color-indicator.pending-tasks {
    background-color: #FF9800;
}

.color-indicator.completed-tasks {
    background-color: #2196F3;
}

/* 異步任務儀表板樣式 */
.async-tasks-dashboard {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

.task-statistics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 500;
}

/* 任務列表樣式 */
.task-list-view {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.filter-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-controls select {
    padding: 8px 12px;
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    font-size: 14px;
}

.filter-controls input[type="text"] {
    padding: 8px 12px;
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
}

/* 任務項目樣式 */
.task-list {
    max-height: 400px;
    overflow-y: auto;
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: background-color 0.2s ease;
}

.task-item:hover {
    background-color: #f8f9fa;
}

.task-name {
    font-weight: 500;
    color: #2c3e50;
}

.task-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.task-status.running {
    background-color: #d4edda;
    color: #155724;
}

.task-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.task-status.completed {
    background-color: #d1ecf1;
    color: #0c5460;
}

.task-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* 資源監控樣式 */
.resource-gauge-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

.gauge-item {
    background: white;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.gauge-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16px;
}

.gauge-details {
    margin-top: 16px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    border-bottom: 1px solid #ecf0f1;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item .label {
    color: #7f8c8d;
    font-size: 14px;
}

.detail-item .value {
    font-weight: 600;
    color: #2c3e50;
}

/* 連接池狀態樣式 */
.pools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.pool-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pool-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16px;
}

.pool-stats {
    margin-bottom: 16px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
}

.stat-label {
    color: #7f8c8d;
    font-size: 14px;
}

.stat-value {
    font-weight: 600;
    color: #2c3e50;
}

.pool-utilization {
    display: flex;
    align-items: center;
    gap: 12px;
}

.utilization-bar {
    flex: 1;
    height: 8px;
    background-color: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
}

.utilization-fill {
    height: 100%;
    background-color: #27ae60;
    transition: width 0.3s ease;
}

.utilization-text {
    font-weight: 600;
    color: #2c3e50;
    min-width: 40px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 10px;
    }
    
    .dashboard-overview {
        grid-template-columns: 1fr;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
    
    .chart-controls {
        width: 100%;
        justify-content: center;
    }
    
    .filter-controls {
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }
    
    .filter-controls input[type="text"] {
        width: 100%;
    }
    
    .resource-gauge-container {
        grid-template-columns: 1fr;
    }
    
    .pools-grid {
        grid-template-columns: 1fr;
    }
}

/* 暗黑主題支援 */
@media (prefers-color-scheme: dark) {
    .dashboard-container {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .kpi-card,
    .chart-container,
    .task-list-view,
    .gauge-item,
    .pool-card {
        background: #2d2d2d;
        color: #e0e0e0;
    }
    
    .card-header h3,
    .chart-header h4,
    .gauge-title,
    .pool-title {
        color: #f0f0f0;
    }
    
    .metric-row {
        border-bottom-color: #404040;
    }
    
    .time-range-btn {
        background: #404040;
        border-color: #606060;
        color: #e0e0e0;
    }
    
    .time-range-btn:hover {
        background-color: #505050;
    }
}

/* 列印樣式 */
@media print {
    .dashboard-container {
        background: white;
        box-shadow: none;
    }
    
    .kpi-card,
    .chart-container {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .chart-controls,
    .filter-controls {
        display: none;
    }
}
```

---

## 🚀 部署與測試

### 1. 儀表板初始化和啟動
```javascript
// 儀表板初始化程序
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('初始化監控儀表板...');
        
        // 建立即時數據連接
        const wsUrl = `ws://${window.location.host}/ws/metrics`;
        window.metricsConnector = new RealtimeMetricsConnector(wsUrl);
        
        // 初始化圖表管理器
        window.chartManager = new DashboardChartManager(window.metricsConnector);
        
        // 初始化互動功能
        window.interactionManager = new DashboardInteractionManager();
        
        // 設置錯誤處理
        window.addEventListener('error', (event) => {
            console.error('儀表板錯誤:', event.error);
            showErrorNotification('儀表板發生錯誤，正在嘗試恢復...');
        });
        
        // 設置未捕獲的Promise拒絕處理
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未捕獲的Promise錯誤:', event.reason);
            event.preventDefault();
        });
        
        console.log('監控儀表板初始化完成');
        
    } catch (error) {
        console.error('初始化儀表板失敗:', error);
        showErrorNotification('無法初始化儀表板，請重整理頁面');
    }
});

// 錯誤通知顯示函數
function showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="icon-error"></i>
            <span>${message}</span>
            <button class="close-btn" onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 5秒後自動移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
```

### 2. 港試和驗證檢查清單
```yaml
功能測試:
  基本功能:
    - [ ] 儀表板正常載入和顯示
    - [ ] KPI卡片數據正確更新
    - [ ] 即時圖表正常繪製
    - [ ] WebSocket連接穩定
    
  互動功能:
    - [ ] 時間範圍切換功能正常
    - [ ] 過濾和搜尋功能正常
    - [ ] 圖表互動操作正常
    - [ ] 資料匯出功能正常
    
  性能測試:
    - [ ] 1000個數據點更新流暢
    - [ ] 多圖表同時更新性能
    - [ ] 長時間運行穩定性
    - [ ] 記憶體使用控制
    
  相容性測試:
    - [ ] Chrome/Edge/Firefox 支援
    - [ ] 行動裝置響應式設計
    - [ ] 不同螢幕尺寸適配
    - [ ] 暗黑主題支援測試

用戶體驗測試:
  易用性:
    - [ ] 新用戶能快速理解儀表板
    - [ ] 關鍵指標易於找到和理解
    - [ ] 錯誤狀態清晰顯示
    - [ ] 幫助說明和提示完善
    
  可訪問性:
    - [ ] 鍵盤導航支援
    - [ ] 螢幕閱讀器相容
    - [ ] 高對比度模式支援
    - [ ] 字體大小調整支援
```

---

## 📚 維護和優化

### 1. 持續優化計劃
- **性能優化**: 減少不必要的DOM更新，實現虛擬化渲染
- **數據壓縮**: 實現指標數據壓縮傳輸，減少頁寬使用
- **緩存優化**: 實現智能緩存策略，提高響應速度
- **自適應的更新頻率**: 根據網路情況自動調整更新間隔

### 2. 新功能規劃
- **自定義儀表板**: 允許用戶自由組合儀表板組件
- **告警規則編輯器**: 圖形化的告警規則編輯界面
- **數據關聯分析**: 提供指標間的關聯性分析
- **預測性分析**: 基於歷史數據的趨勢預測
- **団隊協作功能**: 支援多用戶共享和討論儀表板

---

*本文檔提供了完整的異步指標儀表板設計與實現方案，確保系統管理員能夠即時掌握異步系統的各項性能指標，實現高效的系統監控和管理。*