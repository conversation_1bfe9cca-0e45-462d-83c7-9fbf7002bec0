"""
郵件適配器模組
提供各種郵件處理相關的適配器功能
"""

from .models import EmailWhitelistEntry, WhitelistCheckResult, WhitelistEntryType
from .whitelist import EmailWhitelistManager
from .emaillist_parser import EmaillistParser
from .email_filter import EmailFilter
from .exceptions import (
    EmailWhitelistError,
    EmaillistFileError,
    EmaillistParseError,
    EmaillistValidationError,
    WhitelistManagerError,
    EmailAddressValidationError
)

__all__ = [
    'EmailWhitelistManager',
    'EmailWhitelistEntry', 
    'WhitelistCheckResult',
    'WhitelistEntryType',
    'EmaillistParser',
    'EmailFilter',
    'EmailWhitelistError',
    'EmaillistFileError',
    'EmaillistParseError',
    'EmaillistValidationError',
    'WhitelistManagerError',
    'EmailAddressValidationError'
]