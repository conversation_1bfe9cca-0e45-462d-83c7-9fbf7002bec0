#!/usr/bin/env python3
"""
<PERSON>s 主要控制器
智能代碼管理系統的入口點，整合檔案分析、重構建議和自動優化
"""

import sys
import os
import asyncio
import json
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加項目路徑到系統路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from .analyzers.file_size_analyzer import FileSizeAnalyzer
from .analyzers.duplicate_detector import DuplicateDetector
from .analyzers.import_analyzer import ImportAnalyzer
from .analyzers.quality_checker import QualityChecker
from .refactors.file_splitter import FileSplitter
from .refactors.duplicate_cleaner import DuplicateCleaner
from .refactors.import_optimizer import ImportOptimizer
from .validators.test_runner import TestRunner
from .validators.syntax_validator import SyntaxValidator

class ClaudeHooksManager:
    """<PERSON> Hooks 主要管理器"""
    
    def __init__(self, repo_root: Path, config_path: Optional[Path] = None):
        self.repo_root = Path(repo_root)
        self.claude_dir = self.repo_root / '.claude'
        self.config_path = config_path or self.claude_dir / 'config' / 'settings.yaml'
        
        # 設定日誌
        self._setup_logging()
        
        # 載入配置
        self.config = self._load_config()
        
        # 初始化分析器
        self.analyzers = {
            'file_size': FileSizeAnalyzer(self.repo_root, self.config),
            'duplicate': DuplicateDetector(self.repo_root, self.config),
            'import': ImportAnalyzer(self.repo_root, self.config),
            'quality': QualityChecker(self.repo_root, self.config)
        }
        
        # 初始化重構器
        self.refactors = {
            'file_splitter': FileSplitter(self.repo_root, self.config),
            'duplicate_cleaner': DuplicateCleaner(self.repo_root, self.config),
            'import_optimizer': ImportOptimizer(self.repo_root, self.config)
        }
        
        # 初始化驗證器
        self.validators = {
            'test_runner': TestRunner(self.repo_root, self.config),
            'syntax_validator': SyntaxValidator(self.repo_root, self.config)
        }
    
    def _setup_logging(self):
        """設定日誌系統"""
        log_dir = self.claude_dir / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'claude_hooks.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('ClaudeHooks')
    
    def _load_config(self) -> Dict[str, Any]:
        """載入配置檔案"""
        try:
            if self.config_path.exists():
                import yaml
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
        except Exception as e:
            self.logger.warning(f"載入配置失敗，使用預設配置: {e}")
        
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取預設配置"""
        return {
            'file_size_limit': 500,
            'languages': ['python', 'javascript', 'typescript'],
            'analysis': {
                'enabled': True,
                'check_duplicates': True,
                'optimize_imports': True,
                'quality_check': True
            },
            'refactor': {
                'auto_split_files': False,  # 預設不自動拆分，先提供建議
                'auto_clean_duplicates': False,
                'create_refactor_branch': True,
                'backup_before_refactor': True
            },
            'validation': {
                'run_tests': True,
                'syntax_check': True,
                'performance_check': False
            }
        }
    
    async def analyze_project(self, changed_files: Optional[List[str]] = None) -> Dict[str, Any]:
        """分析專案，檢測需要優化的問題"""
        self.logger.info("🔍 開始專案分析...")
        
        analysis_results = {}
        
        # 檔案大小分析
        if self.config.get('analysis', {}).get('enabled', True):
            self.logger.info("📏 檢查檔案大小...")
            size_analysis = await self.analyzers['file_size'].analyze(changed_files)
            analysis_results['file_size'] = size_analysis
        
        # 重複代碼檢測
        if self.config.get('analysis', {}).get('check_duplicates', True):
            self.logger.info("🔍 檢測重複代碼...")
            duplicate_analysis = await self.analyzers['duplicate'].analyze(changed_files)
            analysis_results['duplicates'] = duplicate_analysis
        
        # Import 依賴分析
        if self.config.get('analysis', {}).get('optimize_imports', True):
            self.logger.info("📦 分析 Import 依賴...")
            import_analysis = await self.analyzers['import'].analyze(changed_files)
            analysis_results['imports'] = import_analysis
        
        # 代碼品質檢查
        if self.config.get('analysis', {}).get('quality_check', True):
            self.logger.info("✨ 檢查代碼品質...")
            quality_analysis = await self.analyzers['quality'].analyze(changed_files)
            analysis_results['quality'] = quality_analysis
        
        # 儲存分析報告
        await self._save_analysis_report(analysis_results)
        
        self.logger.info("✅ 專案分析完成")
        return analysis_results
    
    async def generate_refactor_plan(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """根據分析結果生成重構計劃"""
        self.logger.info("📋 生成重構計劃...")
        
        refactor_plan = {
            'timestamp': datetime.now().isoformat(),
            'file_splits': [],
            'duplicate_cleanups': [],
            'import_optimizations': [],
            'quality_improvements': []
        }
        
        # 檔案拆分計劃
        file_size_issues = analysis_results.get('file_size', {}).get('oversized_files', [])
        for file_info in file_size_issues:
            split_suggestion = await self._generate_split_suggestion(file_info)
            refactor_plan['file_splits'].append(split_suggestion)
        
        # 重複代碼清理計劃
        duplicates = analysis_results.get('duplicates', {}).get('duplicate_groups', [])
        for duplicate_group in duplicates:
            cleanup_suggestion = await self._generate_cleanup_suggestion(duplicate_group)
            refactor_plan['duplicate_cleanups'].append(cleanup_suggestion)
        
        # Import 優化計劃
        import_issues = analysis_results.get('imports', {}).get('optimization_opportunities', [])
        for import_issue in import_issues:
            optimization_suggestion = await self._generate_import_suggestion(import_issue)
            refactor_plan['import_optimizations'].append(optimization_suggestion)
        
        # 儲存重構計劃
        await self._save_refactor_plan(refactor_plan)
        
        return refactor_plan
    
    async def execute_refactor_plan(self, refactor_plan: Dict[str, Any], auto_execute: bool = False) -> Dict[str, Any]:
        """執行重構計劃"""
        if not auto_execute:
            self.logger.info("📋 重構計劃已準備，請手動確認後執行")
            return {"status": "plan_ready", "plan": refactor_plan}
        
        self.logger.info("🔧 開始執行重構計劃...")
        
        execution_results = {
            'status': 'success',
            'completed_tasks': [],
            'failed_tasks': [],
            'rollback_info': None
        }
        
        try:
            # 建立備份
            if self.config.get('refactor', {}).get('backup_before_refactor', True):
                backup_info = await self._create_backup()
                execution_results['rollback_info'] = backup_info
            
            # 建立重構分支
            if self.config.get('refactor', {}).get('create_refactor_branch', True):
                branch_name = await self._create_refactor_branch()
                execution_results['refactor_branch'] = branch_name
            
            # 執行檔案拆分
            for split_task in refactor_plan.get('file_splits', []):
                try:
                    result = await self.refactors['file_splitter'].execute(split_task)
                    execution_results['completed_tasks'].append(('file_split', split_task, result))
                except Exception as e:
                    execution_results['failed_tasks'].append(('file_split', split_task, str(e)))
            
            # 執行重複代碼清理
            for cleanup_task in refactor_plan.get('duplicate_cleanups', []):
                try:
                    result = await self.refactors['duplicate_cleaner'].execute(cleanup_task)
                    execution_results['completed_tasks'].append(('duplicate_cleanup', cleanup_task, result))
                except Exception as e:
                    execution_results['failed_tasks'].append(('duplicate_cleanup', cleanup_task, str(e)))
            
            # 執行驗證
            validation_result = await self._validate_refactor()
            execution_results['validation'] = validation_result
            
        except Exception as e:
            execution_results['status'] = 'failed'
            execution_results['error'] = str(e)
            self.logger.error(f"重構執行失敗: {e}")
        
        return execution_results
    
    async def _create_backup(self) -> Dict[str, Any]:
        """建立重構前備份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = self.claude_dir / 'backup' / 'pre_refactor_snapshots' / timestamp
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 備份重要檔案和目錄
        important_paths = [
            'src',
            'tests',
            'pyproject.toml',
            'requirements.txt'
        ]
        
        backup_info = {
            'timestamp': timestamp,
            'backup_path': str(backup_dir),
            'backed_up_files': []
        }
        
        for path_str in important_paths:
            source_path = self.repo_root / path_str
            if source_path.exists():
                if source_path.is_file():
                    import shutil
                    shutil.copy2(source_path, backup_dir / path_str)
                elif source_path.is_dir():
                    import shutil
                    shutil.copytree(source_path, backup_dir / path_str, dirs_exist_ok=True)
                backup_info['backed_up_files'].append(path_str)
        
        return backup_info
    
    async def _create_refactor_branch(self) -> str:
        """建立重構專用分支"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        branch_name = f"refactor/claude-optimization-{timestamp}"
        
        try:
            subprocess.run(['git', 'checkout', '-b', branch_name], 
                          cwd=self.repo_root, check=True, capture_output=True)
            self.logger.info(f"✅ 已建立重構分支: {branch_name}")
            return branch_name
        except subprocess.CalledProcessError as e:
            self.logger.error(f"建立分支失敗: {e}")
            raise
    
    async def _validate_refactor(self) -> Dict[str, Any]:
        """驗證重構結果"""
        validation_results = {}
        
        # 語法驗證
        if self.config.get('validation', {}).get('syntax_check', True):
            syntax_result = await self.validators['syntax_validator'].validate()
            validation_results['syntax'] = syntax_result
        
        # 測試驗證
        if self.config.get('validation', {}).get('run_tests', True):
            test_result = await self.validators['test_runner'].run()
            validation_results['tests'] = test_result
        
        return validation_results
    
    async def _save_analysis_report(self, analysis_results: Dict[str, Any]):
        """儲存分析報告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.claude_dir / 'reports' / 'analysis_reports' / f'analysis_{timestamp}.json'
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"📄 分析報告已儲存: {report_path}")
    
    async def _save_refactor_plan(self, refactor_plan: Dict[str, Any]):
        """儲存重構計劃"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plan_path = self.claude_dir / 'reports' / 'refactor_plans' / f'plan_{timestamp}.json'
        plan_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(plan_path, 'w', encoding='utf-8') as f:
            json.dump(refactor_plan, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"📋 重構計劃已儲存: {plan_path}")
    
    async def _generate_split_suggestion(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成檔案拆分建議"""
        # 這裡可以整合 AI 模型來生成更智能的拆分建議
        return {
            'file_path': file_info['path'],
            'current_lines': file_info['lines'],
            'suggested_splits': file_info.get('split_suggestions', []),
            'strategy': 'class_based',  # 或 'function_based', 'module_based'
            'priority': 'high' if file_info['lines'] > 1000 else 'medium'
        }
    
    async def _generate_cleanup_suggestion(self, duplicate_group: Dict[str, Any]) -> Dict[str, Any]:
        """生成重複代碼清理建議"""
        return {
            'duplicate_id': duplicate_group.get('id'),
            'files_involved': duplicate_group.get('files', []),
            'similarity_score': duplicate_group.get('similarity', 0),
            'suggested_action': 'extract_to_shared_module',
            'keep_file': duplicate_group.get('newest_file')
        }
    
    async def _generate_import_suggestion(self, import_issue: Dict[str, Any]) -> Dict[str, Any]:
        """生成 Import 優化建議"""
        return {
            'file_path': import_issue.get('file'),
            'issue_type': import_issue.get('type'),
            'current_imports': import_issue.get('current_imports', []),
            'suggested_imports': import_issue.get('suggested_imports', []),
            'potential_savings': import_issue.get('savings', {})
        }

async def main():
    """主函數 - 可作為獨立腳本執行"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Claude Hooks 智能代碼管理系統')
    parser.add_argument('--analyze', action='store_true', help='執行專案分析')
    parser.add_argument('--plan', action='store_true', help='生成重構計劃')
    parser.add_argument('--execute', action='store_true', help='執行重構計劃')
    parser.add_argument('--files', nargs='*', help='指定要分析的檔案')
    parser.add_argument('--config', help='配置檔案路徑')
    
    args = parser.parse_args()
    
    # 取得專案根目錄
    repo_root = Path(__file__).parent.parent.parent
    
    # 建立管理器
    config_path = Path(args.config) if args.config else None
    manager = ClaudeHooksManager(repo_root, config_path)
    
    if args.analyze:
        results = await manager.analyze_project(args.files)
        print("📊 分析完成，結果已儲存到報告中")
        
        if args.plan:
            plan = await manager.generate_refactor_plan(results)
            print("📋 重構計劃已生成")
            
            if args.execute:
                execution = await manager.execute_refactor_plan(plan, auto_execute=True)
                print(f"🔧 重構執行完成，狀態: {execution['status']}")

if __name__ == '__main__':
    asyncio.run(main())