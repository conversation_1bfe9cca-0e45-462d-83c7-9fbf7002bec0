"""路由管理器
負責 URL 路由管理、衝突檢測和動態路由配置
"""

import re
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from urllib.parse import urlparse, urljoin

from fastapi import APIRouter, HTTPException
from loguru import logger

from .config_manager import get_config_manager, ServiceConfig


class RouteConflictType(Enum):
    """路由衝突類型"""
    EXACT_MATCH = "exact_match"        # 完全相同
    PREFIX_OVERLAP = "prefix_overlap"  # 前綴重疊
    PATTERN_CONFLICT = "pattern_conflict"  # 模式衝突


@dataclass
class RouteInfo:
    """路由資訊"""
    service_name: str
    path: str
    method: str
    handler: str
    priority: int = 0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class RouteConflict:
    """路由衝突資訊"""
    conflict_type: RouteConflictType
    route1: RouteInfo
    route2: RouteInfo
    description: str


class RouteManager:
    """路由管理器
    
    管理所有服務的 URL 路由，檢測衝突和優化路由匹配
    """
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.routes: Dict[str, List[RouteInfo]] = {}  # service_name -> routes
        self.route_patterns: Dict[str, re.Pattern] = {}  # 編譯後的正則表達式
        self.conflicts: List[RouteConflict] = []
    
    def register_service_routes(self, service_name: str, router: APIRouter) -> bool:
        """註冊服務路由"""
        try:
            service_config = self.config_manager.get_service_config(service_name)
            if not service_config:
                logger.error(f"服務 {service_name} 配置不存在")
                return False
            
            routes = []
            
            # 提取 router 中的所有路由
            for route in router.routes:
                if hasattr(route, 'path') and hasattr(route, 'methods'):
                    for method in route.methods:
                        route_path = self._normalize_path(
                            service_config.path_prefix, 
                            route.path
                        )
                        
                        route_info = RouteInfo(
                            service_name=service_name,
                            path=route_path,
                            method=method.upper(),
                            handler=getattr(route, 'name', 'unknown'),
                            priority=service_config.metadata.get('priority', 0),
                            metadata={
                                'service_type': service_config.service_type.value,
                                'original_path': route.path,
                                'prefix': service_config.path_prefix
                            }
                        )
                        
                        routes.append(route_info)
            
            self.routes[service_name] = routes
            
            # 編譯路由模式
            self._compile_route_patterns(service_name)
            
            logger.info(f"服務 {service_name} 注冊了 {len(routes)} 個路由")
            return True
            
        except Exception as e:
            logger.error(f"註冊服務 {service_name} 路由失敗: {e}")
            return False
    
    def register_flask_routes(self, service_name: str, flask_app) -> bool:
        """註冊 Flask 應用的路由"""
        try:
            service_config = self.config_manager.get_service_config(service_name)
            if not service_config:
                logger.error(f"服務 {service_name} 配置不存在")
                return False
            
            routes = []
            
            # 提取 Flask 路由
            for rule in flask_app.url_map.iter_rules():
                route_path = self._normalize_path(
                    service_config.path_prefix,
                    rule.rule
                )
                
                for method in rule.methods:
                    if method not in ['HEAD', 'OPTIONS']:  # 過濾不必要的方法
                        route_info = RouteInfo(
                            service_name=service_name,
                            path=route_path,
                            method=method.upper(),
                            handler=rule.endpoint,
                            priority=service_config.metadata.get('priority', 0),
                            metadata={
                                'service_type': 'flask',
                                'original_path': rule.rule,
                                'prefix': service_config.path_prefix,
                                'flask_endpoint': rule.endpoint
                            }
                        )
                        
                        routes.append(route_info)
            
            self.routes[service_name] = routes
            self._compile_route_patterns(service_name)
            
            logger.info(f"Flask 服務 {service_name} 注冊了 {len(routes)} 個路由")
            return True
            
        except Exception as e:
            logger.error(f"註冊 Flask 服務 {service_name} 路由失敗: {e}")
            return False
    
    def _normalize_path(self, prefix: str, path: str) -> str:
        """標準化路徑"""
        # 移除前後的斜線
        prefix = prefix.strip('/')
        path = path.strip('/')
        
        # 組合路徑
        if path:
            normalized = f"/{prefix}/{path}"
        else:
            normalized = f"/{prefix}"
        
        # 清理多餘的斜線
        normalized = re.sub(r'/+', '/', normalized)
        
        return normalized
    
    def _compile_route_patterns(self, service_name: str):
        """編譯路由模式為正則表達式"""
        patterns = {}
        
        for route in self.routes.get(service_name, []):
            # 將 FastAPI/Flask 參數模式轉換為正則表達式
            pattern = route.path
            
            # FastAPI 參數模式: {param} -> (?P<param>[^/]+)
            pattern = re.sub(r'\{([^}]+)\}', r'(?P<\1>[^/]+)', pattern)
            
            # Flask 參數模式: <param> -> (?P<param>[^/]+)
            pattern = re.sub(r'<([^>]+)>', r'(?P<\1>[^/]+)', pattern)
            
            # 處理類型指定: <int:param> -> (?P<param>\d+)
            pattern = re.sub(r'<int:([^>]+)>', r'(?P<\1>\\d+)', pattern)
            pattern = re.sub(r'<float:([^>]+)>', r'(?P<\1>[0-9.]+)', pattern)
            
            try:
                compiled_pattern = re.compile(f"^{pattern}$")
                patterns[f"{route.method}:{route.path}"] = compiled_pattern
            except re.error as e:
                logger.warning(f"無法編譯路由模式 {route.path}: {e}")
        
        self.route_patterns[service_name] = patterns
    
    def detect_conflicts(self) -> List[RouteConflict]:
        """檢測路由衝突"""
        conflicts = []
        all_routes = []
        
        # 收集所有路由
        for service_routes in self.routes.values():
            all_routes.extend(service_routes)
        
        # 檢查每對路由
        for i, route1 in enumerate(all_routes):
            for route2 in all_routes[i + 1:]:
                conflict = self._check_route_conflict(route1, route2)
                if conflict:
                    conflicts.append(conflict)
        
        self.conflicts = conflicts
        return conflicts
    
    def _check_route_conflict(self, route1: RouteInfo, route2: RouteInfo) -> Optional[RouteConflict]:
        """檢查兩個路由是否衝突"""
        # 不同 HTTP 方法不算衝突
        if route1.method != route2.method:
            return None
        
        # 相同服務不算衝突
        if route1.service_name == route2.service_name:
            return None
        
        # 完全相同的路徑
        if route1.path == route2.path:
            return RouteConflict(
                conflict_type=RouteConflictType.EXACT_MATCH,
                route1=route1,
                route2=route2,
                description=f"完全相同的路徑: {route1.path}"
            )
        
        # 前綴重疊
        if route1.path.startswith(route2.path + '/') or route2.path.startswith(route1.path + '/'):
            return RouteConflict(
                conflict_type=RouteConflictType.PREFIX_OVERLAP,
                route1=route1,
                route2=route2,
                description=f"前綴重疊: {route1.path} 和 {route2.path}"
            )
        
        # 檢查正則模式衝突
        pattern1_key = f"{route1.method}:{route1.path}"
        pattern2_key = f"{route2.method}:{route2.path}"
        
        pattern1 = self.route_patterns.get(route1.service_name, {}).get(pattern1_key)
        pattern2 = self.route_patterns.get(route2.service_name, {}).get(pattern2_key)
        
        if pattern1 and pattern2:
            # 簡單檢查：看是否能互相匹配
            test_paths = ["/test/123", "/api/user/456", "/data/file.txt"]
            
            for test_path in test_paths:
                match1 = pattern1.match(test_path)
                match2 = pattern2.match(test_path)
                
                if match1 and match2:
                    return RouteConflict(
                        conflict_type=RouteConflictType.PATTERN_CONFLICT,
                        route1=route1,
                        route2=route2,
                        description=f"模式衝突: 都能匹配 {test_path}"
                    )
        
        return None
    
    def get_route_mapping(self) -> Dict[str, Any]:
        """獲取路由映射表"""
        mapping = {
            "services": {},
            "conflicts": [],
            "statistics": {
                "total_routes": 0,
                "total_services": len(self.routes),
                "total_conflicts": len(self.conflicts)
            }
        }
        
        # 服務路由
        for service_name, routes in self.routes.items():
            service_config = self.config_manager.get_service_config(service_name)
            
            mapping["services"][service_name] = {
                "config": {
                    "name": service_config.name if service_config else service_name,
                    "prefix": service_config.path_prefix if service_config else "/",
                    "type": service_config.service_type.value if service_config else "unknown"
                },
                "routes": [
                    {
                        "path": route.path,
                        "method": route.method,
                        "handler": route.handler,
                        "priority": route.priority
                    }
                    for route in routes
                ],
                "route_count": len(routes)
            }
            
            mapping["statistics"]["total_routes"] += len(routes)
        
        # 衝突資訊
        for conflict in self.conflicts:
            mapping["conflicts"].append({
                "type": conflict.conflict_type.value,
                "description": conflict.description,
                "route1": {
                    "service": conflict.route1.service_name,
                    "path": conflict.route1.path,
                    "method": conflict.route1.method
                },
                "route2": {
                    "service": conflict.route2.service_name,
                    "path": conflict.route2.path,
                    "method": conflict.route2.method
                }
            })
        
        return mapping
    
    def resolve_route(self, path: str, method: str) -> Optional[RouteInfo]:
        """解析路由到對應的服務"""
        best_match = None
        best_priority = -1
        
        # 按優先級尋找匹配的路由
        for service_name, routes in self.routes.items():
            for route in routes:
                if route.method != method.upper():
                    continue
                
                # 嘗試精確匹配
                if route.path == path:
                    if route.priority > best_priority:
                        best_match = route
                        best_priority = route.priority
                    continue
                
                # 嘗試模式匹配
                pattern_key = f"{route.method}:{route.path}"
                pattern = self.route_patterns.get(service_name, {}).get(pattern_key)
                
                if pattern and pattern.match(path):
                    if route.priority > best_priority:
                        best_match = route
                        best_priority = route.priority
        
        return best_match
    
    def get_service_routes(self, service_name: str) -> List[RouteInfo]:
        """獲取指定服務的所有路由"""
        return self.routes.get(service_name, [])
    
    def clear_service_routes(self, service_name: str):
        """清除指定服務的路由"""
        if service_name in self.routes:
            del self.routes[service_name]
        
        if service_name in self.route_patterns:
            del self.route_patterns[service_name]
        
        logger.info(f"已清除服務 {service_name} 的路由")
    
    def refresh_routes(self):
        """重新整理所有路由"""
        # 重新編譯所有模式
        for service_name in self.routes.keys():
            self._compile_route_patterns(service_name)
        
        # 重新檢測衝突
        self.detect_conflicts()
        
        logger.info("路由管理器已重新整理")


# 全域路由管理器實例
_route_manager: Optional[RouteManager] = None


def get_route_manager() -> RouteManager:
    """獲取全域路由管理器實例"""
    global _route_manager
    
    if _route_manager is None:
        _route_manager = RouteManager()
    
    return _route_manager
