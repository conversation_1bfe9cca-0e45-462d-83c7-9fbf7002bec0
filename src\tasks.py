"""
Celery 背景任務定義
提供異步處理耗時操作，解決事件迴圈阻塞問題
"""

import asyncio
import os
from pathlib import Path
from typing import Dict, Any
from celery import Celery
from loguru import logger

# 導入現有的服務和資料模型
try:
    from .services.product_search_service import ProductSearchService
    from .domain.entities.file_search import SearchFilters, TimeRange
    from .data_models.search_models import TimeRangeType
except ImportError:
    # 處理直接執行時的導入問題
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from src.services.product_search_service import ProductSearchService
    from src.domain.entities.file_search import SearchFilters, TimeRange
    from src.data_models.search_models import TimeRangeType

# --- Celery 應用程式設定 ---
# 建立一個 Celery 應用程式物件
celery_app = Celery('outlook_summary_tasks')

# 【關鍵修改】告訴 Celery 從 'celeryconfig' 這個模組載入設定
# Celery 會自動尋找 celeryconfig.py 檔案
celery_app.config_from_object('celeryconfig')

logger.info("✅ Celery 應用程式已初始化，配置從 celeryconfig.py 載入")

# 配置已從 celeryconfig.py 載入，無需手動設定

# --- 在 Worker 啟動時初始化服務 ---
# 這些物件將存在於獨立的 Celery Worker 程序中，與 FastAPI 完全隔離
product_search_service = None

def get_product_search_service():
    """獲取產品搜尋服務實例（延遲初始化）"""
    global product_search_service
    if product_search_service is None:
        product_search_service = ProductSearchService(max_workers=2, search_timeout=300)
        logger.info("✅ Celery Worker 中的產品搜尋服務已初始化")
    return product_search_service

# --- 定義背景任務 ---

@celery_app.task(
    bind=True,
    name="tasks.search_product",
    autoretry_for=(Exception,),  # 自動重試的異常類型
    retry_kwargs={'max_retries': 3, 'countdown': 60},  # 最多重試3次，每次間隔60秒
    retry_backoff=True,  # 指數退避
    retry_jitter=True    # 添加隨機抖動避免雷群效應
)
def search_product_task(self, product_name: str, base_path_str: str, time_range_dict: Dict[str, Any], filters_dict: Dict[str, Any]):
    """
    背景執行產品搜尋的任務
    
    Args:
        product_name: 產品名稱
        base_path_str: 基礎搜尋路徑字串
        time_range_dict: 時間範圍字典
        filters_dict: 搜尋篩選條件字典
        
    Returns:
        Dict: 搜尋結果字典
    """
    task_id = self.request.id
    logger.info(f"🔍 Celery Worker 收到搜尋任務 [{task_id}]: '{product_name}'")
    
    try:
        # 更新任務狀態為開始執行
        self.update_state(state='PROGRESS', meta={'status': '正在初始化搜尋...'})
        
        # 從傳入的字典重建物件
        base_path = Path(base_path_str)
        
        # 重建時間範圍（將 ISO 字串轉換為 datetime 物件）
        from datetime import datetime

        start_date_str = time_range_dict['start_date']
        end_date_str = time_range_dict['end_date']

        # 解析 ISO 格式的日期字串
        start_date = datetime.fromisoformat(start_date_str.replace('Z', '+00:00')) if start_date_str else None
        end_date = datetime.fromisoformat(end_date_str.replace('Z', '+00:00')) if end_date_str else None

        time_range = TimeRange(
            start_date=start_date,
            end_date=end_date
        )
        
        # 重建搜尋篩選條件
        filters = SearchFilters(
            time_range=time_range,
            file_types=filters_dict.get('file_types'),
            min_size=filters_dict.get('min_size'),
            max_size=filters_dict.get('max_size'),
            include_directories=filters_dict.get('include_directories', True),
            search_directory=filters_dict.get('search_directory', 'auto')
        )
        
        logger.info(f"📁 開始搜尋產品: {product_name} 在路徑: {base_path}")
        
        # 更新任務狀態
        self.update_state(state='PROGRESS', meta={'status': f'正在搜尋產品資料夾: {product_name}'})
        
        # 獲取服務實例並執行搜尋
        service = get_product_search_service()
        
        # 在同步的 Celery 任務中執行異步的服務函式
        # 檢查是否已經在事件循環中
        try:
            loop = asyncio.get_running_loop()
            # 如果已經在事件循環中，使用線程池執行
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, service.search_product_folder(
                    product_name,
                    base_path,
                    time_range,
                    filters
                ))
                result = future.result()
        except RuntimeError:
            # 沒有運行的事件循環，可以安全使用 asyncio.run
            result = asyncio.run(service.search_product_folder(
                product_name,
                base_path,
                time_range,
                filters
            ))
        
        logger.success(f"✅ 任務 [{task_id}] 完成，找到 {len(result.matched_files)} 個檔案")

        # 【關鍵修改】使用更簡潔的序列化方式
        # 檢查是否有 dict() 方法（Pydantic 模型）
        if hasattr(result, 'dict'):
            return result.dict()
        else:
            # 手動序列化 dataclass
            return {
                'status': result.status.value if hasattr(result.status, 'value') else str(result.status),
                'product_name': result.product_name,
                'product_folder': str(result.product_folder),
                'matched_files': [
                    {
                        'name': file_info.name,
                        'path': str(file_info.path),
                        'size': file_info.size,
                        'modified_time': file_info.modified_time.isoformat() if file_info.modified_time else None,
                        'is_directory': file_info.is_directory
                    }
                    for file_info in result.matched_files
                ],
                'total_files': result.total_files,
                'total_files_in_directory': getattr(result, 'total_files_in_directory', 0),
                'total_size_mb': result.total_size_mb,
                'search_duration': result.search_duration,
                'error_message': result.error_message
            }
        
    except Exception as e:
        error_msg = f"搜尋任務執行失敗: {str(e)}"
        retry_count = self.request.retries
        max_retries = self.max_retries

        logger.error(f"❌ 任務 [{task_id}] 失敗 (重試 {retry_count}/{max_retries}): {error_msg}")
        logger.error(f"🔍 錯誤詳情: {type(e).__name__}: {str(e)}")

        # 如果還有重試機會，記錄重試狀態
        if retry_count < max_retries:
            logger.warning(f"🔄 任務 [{task_id}] 將在 60 秒後重試...")
            self.update_state(
                state='RETRY',
                meta={
                    'error': error_msg,
                    'product_name': product_name,
                    'task_id': task_id,
                    'retry_count': retry_count,
                    'max_retries': max_retries,
                    'next_retry_in': 60,
                    'status': f'重試 {retry_count + 1}/{max_retries} 中...'
                }
            )
        else:
            # 最終失敗
            logger.error(f"💀 任務 [{task_id}] 最終失敗，已達最大重試次數")
            self.update_state(
                state='FAILURE',
                meta={
                    'error': error_msg,
                    'product_name': product_name,
                    'task_id': task_id,
                    'retry_count': retry_count,
                    'final_failure': True,
                    'exc_type': type(e).__name__,
                    'exc_message': str(e)
                }
            )

        # 重新引發異常，讓 Celery 處理重試邏輯
        raise

@celery_app.task(
    bind=True,
    name="tasks.run_csv_summary",
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 30},
    retry_backoff=True,
    retry_jitter=True
)
def run_csv_summary_task(self, input_path: str):
    """背景執行 CSV 摘要生成的任務"""
    task_id = self.request.id
    logger.info(f"📊 Celery Worker 收到 CSV 摘要任務 [{task_id}]: {input_path}")

    try:
        # 更新任務狀態為開始執行
        self.update_state(state='PROGRESS', meta={'status': '正在初始化 CSV 摘要處理...'})

        # 導入檔案處理服務
        try:
            from .services.file_processing_service import get_file_processing_service
            file_processing_service = get_file_processing_service()
        except ImportError:
            # 處理直接執行時的導入問題
            import sys
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from src.services.file_processing_service import get_file_processing_service
            file_processing_service = get_file_processing_service()

        self.update_state(state='PROGRESS', meta={'status': f'正在處理 CSV 檔案: {input_path}'})

        # 執行 CSV 摘要處理
        result = asyncio.run(file_processing_service.execute_csv_summary(input_path))

        logger.success(f"✅ CSV 摘要任務 [{task_id}] 完成")

        # 回傳結果
        return {
            'status': 'completed',
            'input_path': input_path,
            'result': result.dict() if hasattr(result, 'dict') else result,
            'task_id': task_id
        }

    except Exception as e:
        error_msg = f"CSV 摘要任務執行失敗: {str(e)}"
        logger.error(f"❌ 任務 [{task_id}] 失敗: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise

@celery_app.task(
    bind=True,
    name="tasks.run_code_comparison",
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 2, 'countdown': 30},
    retry_backoff=True,
    retry_jitter=True
)
def run_code_comparison_task(self, input_path: str):
    """背景執行程式碼比較的任務"""
    task_id = self.request.id
    logger.info(f"🔍 Celery Worker 收到程式碼比較任務 [{task_id}]: {input_path}")

    try:
        # 更新任務狀態為開始執行
        self.update_state(state='PROGRESS', meta={'status': '正在初始化程式碼比較處理...'})

        # 導入檔案處理服務
        try:
            from .services.file_processing_service import get_file_processing_service
            file_processing_service = get_file_processing_service()
        except ImportError:
            # 處理直接執行時的導入問題
            import sys
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from src.services.file_processing_service import get_file_processing_service
            file_processing_service = get_file_processing_service()

        self.update_state(state='PROGRESS', meta={'status': f'正在比較程式碼: {input_path}'})

        # 執行程式碼比較處理
        result = asyncio.run(file_processing_service.execute_code_comparison(input_path))

        logger.success(f"✅ 程式碼比較任務 [{task_id}] 完成")

        # 回傳結果
        return {
            'status': 'completed',
            'input_path': input_path,
            'result': result.dict() if hasattr(result, 'dict') else result,
            'task_id': task_id
        }

    except Exception as e:
        error_msg = f"程式碼比較任務執行失敗: {str(e)}"
        logger.error(f"❌ 任務 [{task_id}] 失敗: {error_msg}")
        self.update_state(state='FAILURE', meta={'error': error_msg})
        raise

@celery_app.task(bind=True, name="tasks.health_check")
def health_check_task(self):
    """健康檢查任務"""
    task_id = self.request.id
    logger.info(f"🏥 Celery Worker 健康檢查 [{task_id}]")

    try:
        # 測試服務初始化
        service = get_product_search_service()

        return {
            'status': 'healthy',
            'worker_id': task_id,
            'service_initialized': service is not None,
            'timestamp': asyncio.run(asyncio.sleep(0.1)) or 'test_completed'
        }

    except Exception as e:
        logger.error(f"❌ 健康檢查失敗 [{task_id}]: {e}")
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise

# --- Celery Worker 事件處理 ---

@celery_app.task(bind=True)
def debug_task(self):
    """調試任務"""
    print(f'Request: {self.request!r}')
    return {'message': 'Debug task completed', 'request_id': self.request.id}

# --- Worker 啟動時的初始化 ---
if __name__ == '__main__':
    # 當直接執行此文件時，啟動 Celery Worker
    celery_app.start()
