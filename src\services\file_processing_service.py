"""檔案處理服務
整合 csv_to_summary.py 和 code_comparison.py 為可呼叫的服務函式
"""

import os
import sys
import asyncio
import subprocess
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from loguru import logger


class ProcessingStatus(str, Enum):
    """處理狀態"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class ProcessingTool(str, Enum):
    """處理工具類型"""
    CSV_SUMMARY = "csv_summary"
    CODE_COMPARISON = "code_comparison"


@dataclass
class ProcessingTask:
    """處理任務"""
    task_id: str
    tool: ProcessingTool
    input_path: str
    status: ProcessingStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    output_files: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    progress: float = 0.0


@dataclass
class ProcessingResult:
    """處理結果"""
    success: bool
    task_id: str
    output_files: List[str]
    processing_time: float
    tool_used: str
    error_message: Optional[str] = None
    logs: List[str] = field(default_factory=list)


class FileProcessingService:
    """檔案處理服務
    
    整合現有的處理工具為可呼叫的服務函式
    """
    
    def __init__(self):
        self.tasks: Dict[str, ProcessingTask] = {}
        self.project_root = Path(__file__).parent.parent.parent
        self.csv_tool_path = self.project_root / "csv_to_summary.py"
        self.code_tool_path = self.project_root / "code_comparison.py"
        
        # 驗證工具檔案存在
        if not self.csv_tool_path.exists():
            logger.warning(f"CSV 工具檔案不存在: {self.csv_tool_path}")
        if not self.code_tool_path.exists():
            logger.warning(f"程式碼比較工具檔案不存在: {self.code_tool_path}")
    
    def create_task(self, tool: ProcessingTool, input_path: str) -> str:
        """建立處理任務"""
        task_id = str(uuid.uuid4())
        task = ProcessingTask(
            task_id=task_id,
            tool=tool,
            input_path=input_path,
            status=ProcessingStatus.PENDING,
            created_at=datetime.now()
        )
        self.tasks[task_id] = task
        logger.info(f"建立處理任務: {task_id}, 工具: {tool}, 輸入: {input_path}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[ProcessingTask]:
        """取得任務狀態"""
        return self.tasks.get(task_id)
    
    async def execute_csv_summary(
        self, 
        input_path: str, 
        task_id: Optional[str] = None
    ) -> ProcessingResult:
        """執行 CSV 摘要生成"""
        if not task_id:
            task_id = self.create_task(ProcessingTool.CSV_SUMMARY, input_path)
        
        task = self.tasks[task_id]
        task.status = ProcessingStatus.IN_PROGRESS
        task.started_at = datetime.now()
        
        start_time = datetime.now()
        
        try:
            # 驗證輸入路徑
            input_path_obj = Path(input_path)
            if not input_path_obj.exists():
                raise FileNotFoundError(f"輸入路徑不存在: {input_path}")
            
            # 準備執行命令
            cmd = [
                sys.executable,
                str(self.csv_tool_path),
                str(input_path_obj)
            ]
            
            logger.info(f"執行 CSV 摘要命令: {' '.join(cmd)}")
            
            # 執行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            stdout, stderr = await process.communicate()
            
            # 處理結果
            processing_time = (datetime.now() - start_time).total_seconds()
            
            if process.returncode == 0:
                # 成功
                task.status = ProcessingStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100.0
                
                # 尋找輸出檔案
                output_files = self._find_output_files(input_path_obj, "csv_summary")
                task.output_files = [str(f) for f in output_files]
                
                result = ProcessingResult(
                    success=True,
                    task_id=task_id,
                    output_files=task.output_files,
                    processing_time=processing_time,
                    tool_used="csv_to_summary.py",
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.info(f"CSV 摘要處理成功: {task_id}")
                
            else:
                # 失敗
                error_msg = stderr.decode('utf-8', errors='ignore')
                task.status = ProcessingStatus.FAILED
                task.error_message = error_msg
                task.completed_at = datetime.now()
                
                result = ProcessingResult(
                    success=False,
                    task_id=task_id,
                    output_files=[],
                    processing_time=processing_time,
                    tool_used="csv_to_summary.py",
                    error_message=error_msg,
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.error(f"CSV 摘要處理失敗: {task_id}, 錯誤: {error_msg}")
            
            return result
            
        except Exception as e:
            # 例外處理
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            task.status = ProcessingStatus.FAILED
            task.error_message = error_msg
            task.completed_at = datetime.now()
            
            result = ProcessingResult(
                success=False,
                task_id=task_id,
                output_files=[],
                processing_time=processing_time,
                tool_used="csv_to_summary.py",
                error_message=error_msg
            )
            
            logger.error(f"CSV 摘要處理例外: {task_id}, 錯誤: {error_msg}")
            return result
    
    async def execute_code_comparison(
        self, 
        input_path: str, 
        task_id: Optional[str] = None
    ) -> ProcessingResult:
        """執行程式碼比較"""
        if not task_id:
            task_id = self.create_task(ProcessingTool.CODE_COMPARISON, input_path)
        
        task = self.tasks[task_id]
        task.status = ProcessingStatus.IN_PROGRESS
        task.started_at = datetime.now()
        
        start_time = datetime.now()
        
        try:
            # 驗證輸入路徑
            input_path_obj = Path(input_path)
            if not input_path_obj.exists():
                raise FileNotFoundError(f"輸入路徑不存在: {input_path}")
            
            # 準備執行命令
            cmd = [
                sys.executable,
                str(self.code_tool_path),
                str(input_path_obj)
            ]
            
            logger.info(f"執行程式碼比較命令: {' '.join(cmd)}")
            
            # 執行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.project_root)
            )
            
            stdout, stderr = await process.communicate()
            
            # 處理結果
            processing_time = (datetime.now() - start_time).total_seconds()
            
            if process.returncode == 0:
                # 成功
                task.status = ProcessingStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100.0
                
                # 尋找輸出檔案
                output_files = self._find_output_files(input_path_obj, "code_comparison")
                task.output_files = [str(f) for f in output_files]
                
                result = ProcessingResult(
                    success=True,
                    task_id=task_id,
                    output_files=task.output_files,
                    processing_time=processing_time,
                    tool_used="code_comparison.py",
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.info(f"程式碼比較處理成功: {task_id}")
                
            else:
                # 失敗
                error_msg = stderr.decode('utf-8', errors='ignore')
                task.status = ProcessingStatus.FAILED
                task.error_message = error_msg
                task.completed_at = datetime.now()
                
                result = ProcessingResult(
                    success=False,
                    task_id=task_id,
                    output_files=[],
                    processing_time=processing_time,
                    tool_used="code_comparison.py",
                    error_message=error_msg,
                    logs=[stdout.decode('utf-8', errors='ignore')]
                )
                
                logger.error(f"程式碼比較處理失敗: {task_id}, 錯誤: {error_msg}")
            
            return result
            
        except Exception as e:
            # 例外處理
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)
            
            task.status = ProcessingStatus.FAILED
            task.error_message = error_msg
            task.completed_at = datetime.now()
            
            result = ProcessingResult(
                success=False,
                task_id=task_id,
                output_files=[],
                processing_time=processing_time,
                tool_used="code_comparison.py",
                error_message=error_msg
            )
            
            logger.error(f"程式碼比較處理例外: {task_id}, 錯誤: {error_msg}")
            return result
    
    def _find_output_files(self, input_path: Path, tool_type: str) -> List[Path]:
        """尋找輸出檔案"""
        output_files = []
        
        try:
            # 根據工具類型尋找可能的輸出檔案
            if tool_type == "csv_summary":
                # CSV 摘要工具通常會產生 Excel 檔案
                patterns = ["*summary*.xlsx", "*摘要*.xlsx", "*_processed.xlsx"]
            elif tool_type == "code_comparison":
                # 程式碼比較工具通常會產生比較報告
                patterns = ["*comparison*.xlsx", "*比較*.xlsx", "*_diff.xlsx"]
            else:
                patterns = ["*.xlsx", "*.csv"]
            
            # 在輸入目錄及其父目錄中搜尋
            search_dirs = [input_path]
            if input_path.is_file():
                search_dirs.append(input_path.parent)
            
            for search_dir in search_dirs:
                if search_dir.is_dir():
                    for pattern in patterns:
                        output_files.extend(search_dir.glob(pattern))
            
            # 移除重複並按修改時間排序
            output_files = list(set(output_files))
            output_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
        except Exception as e:
            logger.warning(f"尋找輸出檔案時發生錯誤: {e}")
        
        return output_files
    
    async def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """取得任務進度"""
        task = self.tasks.get(task_id)
        if not task:
            return {"error": "任務不存在"}
        
        return {
            "task_id": task.task_id,
            "status": task.status.value,
            "progress": task.progress,
            "tool": task.tool.value,
            "input_path": task.input_path,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "output_files": task.output_files,
            "error_message": task.error_message
        }
    
    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任務"""
        return [
            {
                "task_id": task.task_id,
                "status": task.status.value,
                "tool": task.tool.value,
                "input_path": task.input_path,
                "created_at": task.created_at.isoformat(),
                "progress": task.progress
            }
            for task in self.tasks.values()
        ]
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的舊任務"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        tasks_to_remove = [
            task_id for task_id, task in self.tasks.items()
            if task.status in [ProcessingStatus.COMPLETED, ProcessingStatus.FAILED]
            and task.completed_at
            and task.completed_at < cutoff_time
        ]
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
            logger.info(f"清理舊任務: {task_id}")


# 全域服務實例
_file_processing_service = None


def get_file_processing_service() -> FileProcessingService:
    """取得檔案處理服務實例（單例模式）"""
    global _file_processing_service
    if _file_processing_service is None:
        _file_processing_service = FileProcessingService()
    return _file_processing_service