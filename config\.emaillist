# .emaillist 檔案格式範例
# 此檔案定義郵件白名單，用於過濾允許處理的郵件寄件者
# 
# 支援的格式：
# 1. 完整郵件地址: <EMAIL>
# 2. 網域匹配: @example.com (匹配該網域下的所有郵件)
# 3. 註解: 以 # 開頭的行或行末註解
#
# 範例:

# ===== 管理員和系統帳號 =====
<EMAIL>              # 系統管理員


# ===== 信任的供應商網域 =====
@gmt.com.tw                    # 

# 注意事項：
# - 模式匹配不區分大小寫
# - 重複的條目會被警告但不會阻止載入
# - 空行會被忽略
# - 無效的格式會被記錄為錯誤
# - 建議定期檢查和更新白名單
# - 避免使用過於寬泛的網域匹配 (如 @gmail.com)

# 使用方法：
# 1. 複製此檔案為 .emaillist (去掉 .example 後綴)
# 2. 根據實際需求修改白名單條目
# 3. 重新啟動郵件處理服務或手動重新載入
# 4. 檢查日誌確認白名單載入成功

# 環境變數配置：
# EMAIL_WHITELIST_ENABLED=true          # 啟用/停用白名單功能
# EMAIL_WHITELIST_DEFAULT_ACTION=deny    # 不在白名單時的預設行為
# EMAIL_WHITELIST_AUTO_RELOAD=true       # 自動重新載入白名單檔案
# EMAIL_CONFIG_DIR=./config                    # 配置檔案目錄


#1. EMAIL_WHITELIST_DEFAULT_ACTION=deny (預設值，建議)
# EMAIL_WHITELIST_DEFAULT_ACTION=deny
# - 行為： 預設拒絕所有郵件，只允許白名單中的寄件者
# - 安全性： 🔒 高安全性 - 白名單模式
# - 使用場景： 生產環境、高安全性需求
# - 註解： 最安全的選項，明確控制允許的寄件者
#
# 2. EMAIL_WHITELIST_DEFAULT_ACTION=allow
# EMAIL_WHITELIST_DEFAULT_ACTION=allow
# - 行為： 預設允許所有郵件，白名單失效時不影響正常處理
# - 安全性： ⚠️ 低安全性 - 降級模式
# - 使用場景： 開發環境、測試階段
# - 註解： 白名單檔案問題時的備援選項
#
# 3. EMAIL_WHITELIST_DEFAULT_ACTION=log_only
# EMAIL_WHITELIST_DEFAULT_ACTION=log_only
# - 行為： 記錄所有郵件但不過濾，用於監控和分析
# - 安全性： ℹ️ 監控模式 - 無過濾效果
# - 使用場景： 收集數據、分析寄件者模式
# - 註解： 部署前的觀察期使用
