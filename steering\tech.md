# 技術堆疊與建置系統

## 核心技術

### 後端框架
- **Python >=3.9**：主要開發語言，支援現代 Python 特性
- **Flask 2.3.3**：郵件收件夾管理服務（埠號 5000）
- **FastAPI 0.104.1**：FT-EQC 處理服務（埠號 8010），包含自動 API 文檔
- **SQLAlchemy 2.0.23**：資料庫 ORM 操作，支援 PostgreSQL 和 SQLite
- **Pydantic 2.5.0**：資料驗證與序列化，型別安全保證
- **Uvicorn 0.24.0**：ASGI 伺服器，支援高效能異步處理

### 資料處理
- **Pandas 2.1.3**：資料操作與分析，支援大型資料集處理
- **OpenPyXL 3.1.2**：Excel 檔案讀寫處理
- **XlsxWriter 3.1.9**：高效能 Excel 報表生成
- **NumPy 1.24.3**：數值計算和陣列操作
- **CSV 處理**：原生支援 + 自訂解析器

### 資料庫
- **SQLite**：開發與測試環境，內建支援
- **PostgreSQL**：生產環境（透過 Docker），高效能和可擴展性
- **資料庫遷移**：SQLAlchemy Alembic 支援

### 測試與品質
- **Pytest**：測試框架，要求 90%+ 覆蓋率
- **Pytest-asyncio**：異步測試支援
- **Pytest-cov**：測試覆蓋率報告
- **Playwright 1.52.0**：端對端測試和瀏覽器自動化
- **Black**：程式碼格式化，確保一致性
- **Flake8**：程式碼檢查和風格指南
- **MyPy**：靜態型別檢查，100% 型別註解要求
- **Coverage**：詳細的測試覆蓋率報告

### 基礎設施
- **Docker & Docker Compose**：容器化部署和服務編排
- **Prometheus**：指標收集和監控
- **Grafana**：視覺化監控儀表板
- **Loguru 0.7.2**：結構化日誌記錄
- **Nginx**：反向代理和負載平衡

### AI/LLM 整合
- **Ollama**：本地 LLM 提供者（預設）
- **Grok API**：替代 LLM 提供者，雲端服務
- **Sentence Transformers**：文字嵌入和語義搜尋
- **智慧解析**：AI 驅動的複雜格式解析

### 郵件處理
- **POP3 客戶端**：原生 Python POP3 實作
- **Outlook 整合**：COM 介面和 API 整合
- **SMTP 發送**：郵件通知和報告發送
- **MIME 處理**：附件解析和處理

### 檔案處理
- **壓縮檔案**：7zip、RAR、ZIP 格式支援
- **Office 文件**：Excel、Access (MDB/ACCDB) 處理
- **文字處理**：多編碼支援（UTF-8、GBK、BIG5）
- **路徑處理**：跨平台路徑管理

## 建置系統

### 環境設定

#### 標準虛擬環境
```bash
# 建立虛擬環境（必要）
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# 安裝相依套件
pip install -r requirements.txt
```

#### UV 虛擬環境（推薦）
```bash
# 使用 UV 建立的高效能虛擬環境
venv_win_3_11_12\Scripts\activate    # Windows
./venv_win_3_11_12/Scripts/python.exe script.py  # 直接執行

# 批處理檔案
activate_venv_3_11_12.bat        # 啟動虛擬環境
run_with_venv_3_11_12.bat script.py  # 直接執行腳本
```

### 開發指令

#### 基本開發流程
```bash
# 程式碼品質檢查
black src/ tests/                # 格式化程式碼
flake8 src/ tests/               # 程式碼風格檢查
mypy src/                        # 型別檢查

# 執行測試並產生覆蓋率報告
pytest --cov=src --cov-report=html
pytest --cov=src --cov-report=term-missing

# 分類測試執行
pytest tests/unit/              # 單元測試
pytest tests/integration/       # 整合測試
pytest tests/e2e/              # 端對端測試
```

#### 效能測試
```bash
# 執行效能測試
pytest tests/performance/ -v

# 並行測試（多核心）
pytest -n auto tests/

# 特定廠商測試
pytest -k "gtk" tests/          # GTK 廠商測試
pytest -k "etd" tests/          # ETD 廠商測試
```

### Makefile 指令
```bash
make help              # 顯示可用指令
make dev-setup         # 設定開發環境
make test              # 執行所有測試
make test-unit         # 僅執行單元測試
make test-integration  # 僅執行整合測試
make quality-check     # 執行所有程式碼品質檢查
make tdd-red          # TDD 紅燈階段
make tdd-green        # TDD 綠燈階段
make clean            # 清理暫存檔案
make docs             # 生成文檔
```

### 服務管理

#### 整合服務啟動
```bash
# 啟動所有整合服務
python start_integrated_services.py

# 使用 UV 環境啟動
venv_win_3_11_12\Scripts\python.exe start_integrated_services.py

# 使用批處理檔案（推薦）
start_services_utf8.bat
```

#### 個別服務端點
```bash
# Flask 郵件收件夾服務
http://localhost:5000              # 主要管理界面
http://localhost:5000/inbox        # 收件箱管理
http://localhost:5000/api/status   # 服務狀態

# FastAPI FT-EQC 服務
http://localhost:8010/ui           # 處理界面
http://localhost:8010/docs         # Swagger API 文檔
http://localhost:8010/redoc        # ReDoc API 文檔
http://localhost:8010/api/status   # 系統狀態
```

### Docker 部署

#### 容器化服務
```bash
# 啟動所有服務
docker-compose up -d

# 檢視服務狀態
docker-compose ps

# 檢視日誌
docker-compose logs -f

# 停止服務
docker-compose down

# 重建並啟動
docker-compose up --build -d
```

#### 監控服務
```bash
# Prometheus 指標收集
http://localhost:9090

# Grafana 監控儀表板
http://localhost:3000

# 預設登入： admin/admin
```

### Windows 專用設定

#### 編碼設定
```bash
# UTF-8 編碼（Windows 必要）
chcp 65001
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

# PowerShell 執行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 系統工具安裝
```bash
# 安裝必要的壓縮工具
# 需要安裝 7-Zip 或使用 Windows 內建 ZIP 支援
# RAR 支援需要額外安裝 WinRAR 或 7-Zip

# 檢查 Python 版本和環境
python --version
where python
```

## 開發標準

### 強制要求
- **虛擬環境**：開發前必須啟動虛擬環境
- **TDD 方法**：實作前先撰寫測試，特別是後端程式碼
- **測試覆蓋率**：核心業務邏輯最低 90%，API 端點 100%
- **型別提示**：所有函數必須有完整型別註解
- **程式碼格式**：必須符合 Black + Flake8 規範
- **API 測試**：所有 REST 端點必須實際測試

### 品質門檻
- **測試通過率**：100% 測試必須通過
- **MyPy 型別檢查**：100% 符合，無型別錯誤
- **測試覆蓋率**：整體 >90%，領域邏輯 >95%
- **安全掃描**：Bandit 掃描無高風險問題
- **效能要求**：API 回應時間 < 2 秒
- **記憶體使用**：單次處理 < 1GB RAM

### 架構合規性
- **六角架構模式**：嚴格遵循層級分離
- **依賴注入**：基於介面的依賴注入
- **錯誤處理**：完整的異常處理和日誌記錄
- **文檔化**：所有公開 API 必須有文檔
- **版本控制**：語義化版本控制

## 效能最佳化

### 並行處理
- **多程序處理**：使用 multiprocessing 處理大型檔案
- **異步處理**：FastAPI 異步端點
- **批次處理**：大量資料的批次操作
- **快取機制**：Redis 或記憶體快取

### 資料庫最佳化
- **連接池**：SQLAlchemy 連接池管理
- **查詢最佳化**：索引和查詢調校
- **批次操作**：大量資料插入最佳化
- **讀寫分離**：讀寫資料庫分離（生產環境）

### 檔案處理最佳化
- **串流處理**：大型檔案串流讀取
- **壓縮處理**：並行解壓縮
- **暫存管理**：自動清理暫存檔案
- **記憶體管理**：分塊處理大型資料集

## 安全性

### 資料安全
- **輸入驗證**：Pydantic 資料驗證
- **SQL 注入防護**：SQLAlchemy ORM 保護
- **檔案上傳安全**：檔案類型和大小限制
- **路徑安全**：防止目錄遍歷攻擊

### 系統安全
- **環境變數**：敏感資料使用環境變數
- **日誌安全**：避免記錄敏感資訊
- **網路安全**：HTTPS 支援和安全標頭
- **認證授權**：API 金鑰和權限管理

## 監控與日誌

### 日誌系統
- **結構化日誌**：JSON 格式日誌
- **日誌等級**：DEBUG、INFO、WARNING、ERROR、CRITICAL
- **日誌輪轉**：自動檔案輪轉和壓縮
- **中央化日誌**：ELK Stack 整合（可選）

### 監控指標
- **應用指標**：處理成功率、回應時間、錯誤率
- **系統指標**：CPU、記憶體、磁碟使用率
- **業務指標**：廠商處理量、檔案處理效率
- **告警機制**：異常狀況自動告警

## 部署策略

### 開發環境
- **本地開發**：SQLite + 本地服務
- **Docker 開發**：完整容器化開發環境
- **熱重載**：開發時自動重載

### 測試環境
- **CI/CD 整合**：GitHub Actions 或 GitLab CI
- **自動測試**：Pull Request 觸發測試
- **環境隔離**：獨立的測試資料庫

### 生產環境
- **容器化部署**：Docker + Kubernetes（可選）
- **負載平衡**：Nginx 反向代理
- **監控完整**：Prometheus + Grafana + 告警
- **備份恢復**：資料庫定期備份
- **藍綠部署**：零停機部署策略

<!-- 最後更新: 2025-07-26 -->