<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>即時狀態監控儀表板 - Outlook Summary</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/realtime-dashboard.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
</head>
<body>
    <!-- 連接狀態指示器 -->
    <div id="connection-status" class="connection-status disconnected">
        連接中...
    </div>
    
    <!-- 告警容器 -->
    <div id="alert-container" class="alert-container"></div>
    
    <!-- 主儀表板 -->
    <div class="realtime-dashboard">
        <!-- 儀表板標題 -->
        <header class="dashboard-header">
            <div>
                <h1 class="dashboard-title">即時狀態監控儀表板</h1>
                <p class="dashboard-subtitle">基於 WebSocket 的異步任務管理與系統監控</p>
            </div>
            <div class="dashboard-controls">
                <button class="control-button" onclick="toggleAutoRefresh()">
                    <i class="fas fa-sync-alt"></i> 自動重新整理
                </button>
                <button class="control-button" onclick="exportData()">
                    <i class="fas fa-download"></i> 匯出數據
                </button>
                <button class="control-button" onclick="wsClient.ping()">
                    <i class="fas fa-heartbeat"></i> 測試連接
                </button>
            </div>
        </header>
        
        <!-- 系統概覽 -->
        <section class="system-overview">
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">CPU 使用率</span>
                    <i class="fas fa-microchip metric-icon"></i>
                </div>
                <div id="cpu-usage" class="metric-value">--</div>
                <div class="metric-label">處理器負載</div>
                <div class="metric-trend">
                    <i class="fas fa-arrow-up trend-up"></i>
                    <span>較上一分鐘</span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">記憶體使用</span>
                    <i class="fas fa-memory metric-icon"></i>
                </div>
                <div id="memory-usage" class="metric-value">--</div>
                <div class="metric-label">記憶體負載</div>
                <div class="metric-trend">
                    <i class="fas fa-arrow-down trend-down"></i>
                    <span>較上一分鐘</span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">活躍任務</span>
                    <i class="fas fa-tasks metric-icon"></i>
                </div>
                <div id="active-tasks" class="metric-value">--</div>
                <div class="metric-label">正在執行的任務</div>
                <div class="metric-trend">
                    <i class="fas fa-arrow-up trend-up"></i>
                    <span>任務負載增加</span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">API 響應時間</span>
                    <i class="fas fa-clock metric-icon"></i>
                </div>
                <div id="avg-response-time" class="metric-value">--</div>
                <div class="metric-label">平均響應時間</div>
                <div class="metric-trend">
                    <i class="fas fa-arrow-down trend-down"></i>
                    <span>性能良好</span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">錯誤率</span>
                    <i class="fas fa-exclamation-triangle metric-icon"></i>
                </div>
                <div id="error-rate" class="metric-value">--</div>
                <div class="metric-label">系統錯誤比率</div>
                <div class="metric-trend">
                    <i class="fas fa-arrow-down trend-down"></i>
                    <span>穩定運行</span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">WebSocket 連接</span>
                    <i class="fas fa-wifi metric-icon"></i>
                </div>
                <div id="websocket-connections" class="metric-value">--</div>
                <div class="metric-label">即時連接數</div>
                <div class="metric-trend">
                    <i class="fas fa-arrow-up trend-up"></i>
                    <span>連接穩定</span>
                </div>
            </div>
        </section>
        
        <!-- 任務狀態列表 -->
        <section class="tasks-section">
            <div class="section-header">
                <h2 class="section-title">即時任務狀態</h2>
                <div class="task-filter">
                    <button class="filter-button active" data-status="all">全部</button>
                    <button class="filter-button" data-status="running">執行中</button>
                    <button class="filter-button" data-status="pending">等待中</button>
                    <button class="filter-button" data-status="completed">已完成</button>
                    <button class="filter-button" data-status="failed">失敗</button>
                </div>
            </div>
            
            <div id="tasks-list" class="tasks-list">
                <!-- 動態生成的任務項目 -->
                <div class="task-item" data-task-id="sample-task-1">
                    <div class="task-info">
                        <div class="task-name">郵件處理任務</div>
                        <div class="task-id">task-id: sample-task-1</div>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-bar-fill task-progress" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">0%</div>
                        </div>
                        <div class="task-error" style="display: none;"></div>
                    </div>
                    <div>
                        <span class="task-status status-pending">PENDING</span>
                        <button class="control-button" onclick="cancelTask('sample-task-1')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 進程進度監控 -->
        <section class="process-progress-section">
            <div class="section-header">
                <h2 class="section-title">處理進度監控</h2>
                <div class="dashboard-controls">
                    <button class="control-button" onclick="startSampleProcess()">
                        <i class="fas fa-play"></i> 啟動測試處理
                    </button>
                </div>
            </div>
            
            <div id="process-list">
                <!-- 動態生成的進程項目 -->
                <div class="process-item" data-process-id="sample-process-1">
                    <div class="process-header">
                        <span class="process-name">檔案處理進程</span>
                        <span class="step-text">步驟 0/0</span>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-bar-fill" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="progress-text">0%</div>
                    </div>
                    <div class="message-text">等待開始...</div>
                </div>
            </div>
        </section>
        
        <!-- 任務控制區 -->
        <section class="tasks-section">
            <div class="section-header">
                <h2 class="section-title">任務控制中心</h2>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                <button class="control-button" style="padding: 16px; text-align: left;" onclick="startEmailProcessingTask()">
                    <i class="fas fa-envelope" style="margin-right: 8px;"></i>
                    <div>
                        <strong>郵件處理任務</strong>
                        <div style="font-size: 12px; opacity: 0.8;">處理收件匣中的郵件</div>
                    </div>
                </button>
                
                <button class="control-button" style="padding: 16px; text-align: left;" onclick="startFileProcessingTask()">
                    <i class="fas fa-file" style="margin-right: 8px;"></i>
                    <div>
                        <strong>檔案處理任務</strong>
                        <div style="font-size: 12px; opacity: 0.8;">批量處理上傳檔案</div>
                    </div>
                </button>
                
                <button class="control-button" style="padding: 16px; text-align: left;" onclick="startSummaryTask()">
                    <i class="fas fa-list-alt" style="margin-right: 8px;"></i>
                    <div>
                        <strong>摘要生成任務</strong>
                        <div style="font-size: 12px; opacity: 0.8;">生成內容摘要</div>
                    </div>
                </button>
                
                <button class="control-button" style="padding: 16px; text-align: left;" onclick="startSystemCleanup()">
                    <i class="fas fa-broom" style="margin-right: 8px;"></i>
                    <div>
                        <strong>系統清理任務</strong>
                        <div style="font-size: 12px; opacity: 0.8;">清理臨時檔案</div>
                    </div>
                </button>
            </div>
        </section>
    </div>
    
    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/websocket-client.js') }}"></script>
    <script>
        // 儀表板控制函數
        let autoRefreshEnabled = true;
        
        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            const button = document.querySelector('[onclick="toggleAutoRefresh()"]');
            if (autoRefreshEnabled) {
                button.classList.add('active');
                button.innerHTML = '<i class="fas fa-sync-alt"></i> 自動重新整理';
            } else {
                button.classList.remove('active');
                button.innerHTML = '<i class="fas fa-pause"></i> 已暫停';
            }
        }
        
        function exportData() {
            const data = {
                timestamp: new Date().toISOString(),
                system_metrics: {
                    cpu_usage: document.getElementById('cpu-usage').textContent,
                    memory_usage: document.getElementById('memory-usage').textContent,
                    active_tasks: document.getElementById('active-tasks').textContent,
                    avg_response_time: document.getElementById('avg-response-time').textContent,
                    error_rate: document.getElementById('error-rate').textContent
                },
                tasks: Array.from(document.querySelectorAll('.task-item')).map(item => ({
                    task_id: item.dataset.taskId,
                    name: item.querySelector('.task-name').textContent,
                    status: item.querySelector('.task-status').textContent,
                    progress: item.querySelector('.progress-text').textContent
                }))
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dashboard-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function cancelTask(taskId) {
            if (window.wsClient && window.wsClient.isConnected) {
                window.wsClient.cancelTask(taskId);
            } else {
                alert('WebSocket 未連接，無法取消任務');
            }
        }
        
        function startEmailProcessingTask() {
            if (window.wsClient && window.wsClient.isConnected) {
                window.wsClient.startTask('email_processing', {
                    source: 'dashboard'
                });
            } else {
                alert('WebSocket 未連接，無法啟動任務');
            }
        }
        
        function startFileProcessingTask() {
            if (window.wsClient && window.wsClient.isConnected) {
                window.wsClient.startTask('file_processing', {
                    source: 'dashboard'
                });
            } else {
                alert('WebSocket 未連接，無法啟動任務');
            }
        }
        
        function startSummaryTask() {
            if (window.wsClient && window.wsClient.isConnected) {
                window.wsClient.startTask('summary_generation', {
                    source: 'dashboard'
                });
            } else {
                alert('WebSocket 未連接，無法啟動任務');
            }
        }
        
        function startSystemCleanup() {
            if (window.wsClient && window.wsClient.isConnected) {
                window.wsClient.startTask('system_cleanup', {
                    source: 'dashboard'
                });
            } else {
                alert('WebSocket 未連接，無法啟動任務');
            }
        }
        
        function startSampleProcess() {
            if (window.wsClient && window.wsClient.isConnected) {
                window.wsClient.startTask('sample_process', {
                    steps: 10,
                    delay: 1000
                });
            } else {
                alert('WebSocket 未連接，無法啟動進程');
            }
        }
        
        // 過濾器按鈕事件
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.filter-button');
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按鈕的 active 狀態
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    
                    // 添加當前按鈕的 active 狀態
                    this.classList.add('active');
                    
                    // 過濾任務
                    filterTasks(this.dataset.status);
                });
            });
        });
        
        function filterTasks(status) {
            const taskItems = document.querySelectorAll('.task-item');
            taskItems.forEach(item => {
                const taskStatus = item.querySelector('.task-status').textContent.toLowerCase();
                
                if (status === 'all' || taskStatus === status) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }
        
        // WebSocket 事件監聽
        document.addEventListener('DOMContentLoaded', function() {
            // 等待 WebSocket 初始化完成
            setTimeout(() => {
                if (window.wsClient) {
                    // 監聽任務狀態更新
                    window.wsClient.on('taskStatusUpdate', function(data) {
                        console.log('Task status update:', data);
                        updateTaskInList(data);
                    });
                    
                    // 監聽系統指標更新
                    window.wsClient.on('systemMetricsUpdate', function(data) {
                        console.log('System metrics update:', data);
                    });
                    
                    // 監聽進程進度更新
                    window.wsClient.on('processProgressUpdate', function(data) {
                        console.log('Process progress update:', data);
                    });
                    
                    // 監聽告警
                    window.wsClient.on('alert', function(data) {
                        console.log('Alert:', data);
                    });
                }
            }, 1000);
        });
        
        function updateTaskInList(taskData) {
            const { task_id, status, progress } = taskData;
            let taskElement = document.querySelector(`[data-task-id="${task_id}"]`);
            
            // 如果任務不存在，創建新的任務項目
            if (!taskElement) {
                taskElement = createTaskElement(task_id, taskData);
                document.getElementById('tasks-list').appendChild(taskElement);
            }
        }
        
        function createTaskElement(taskId, taskData) {
            const taskElement = document.createElement('div');
            taskElement.className = 'task-item';
            taskElement.setAttribute('data-task-id', taskId);
            
            taskElement.innerHTML = `
                <div class="task-info">
                    <div class="task-name">${taskData.task_type || '未知任務'}</div>
                    <div class="task-id">task-id: ${taskId}</div>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-bar-fill task-progress" style="width: ${taskData.progress || 0}%"></div>
                        </div>
                        <div class="progress-text">${taskData.progress || 0}%</div>
                    </div>
                    <div class="task-error" style="display: none;"></div>
                </div>
                <div>
                    <span class="task-status status-${(taskData.status || 'pending').toLowerCase()}">${taskData.status || 'PENDING'}</span>
                    <button class="control-button" onclick="cancelTask('${taskId}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            return taskElement;
        }
        
        // 模擬數據更新（用於測試）
        function simulateDataUpdate() {
            if (!autoRefreshEnabled) return;
            
            // 更新指標
            const metrics = {
                cpu_percent: Math.random() * 100,
                memory_percent: Math.random() * 100,
                active_tasks: Math.floor(Math.random() * 20),
                avg_response_time: Math.random() * 1000,
                error_rate: Math.random() * 5
            };
            
            if (window.wsClient) {
                window.wsClient.updateSystemMetrics(metrics);
            }
        }
        
        // 每10秒模擬一次數據更新（僅用於測試）
        // setInterval(simulateDataUpdate, 10000);
    </script>
</body>
</html>