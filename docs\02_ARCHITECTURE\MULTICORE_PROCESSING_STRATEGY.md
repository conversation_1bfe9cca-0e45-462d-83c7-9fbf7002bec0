# 多核心處理策略設計 - Outlook Summary 系統

## 🎯 多核心處理目標和挑戰

### 當前 GIL 限制問題
```python
# 當前問題 - GIL 限制的假並行
with ThreadPoolExecutor(max_workers=8) as executor:
    # 實際上只有 I/O 等待時才能切換線程
    # CPU 密集型任務仍然序列化執行
    futures = [executor.submit(cpu_intensive_task, data) for data in dataset]
    results = [future.result() for future in futures]
```

### 目標多核心真並行
```python
# 目標解決方案 - ProcessPoolExecutor 突破 GIL
with ProcessPoolExecutor(max_workers=cpu_count()) as executor:
    # 真正的並行執行，每個進程有獨立的 GIL
    loop = asyncio.get_event_loop()
    tasks = [
        loop.run_in_executor(executor, cpu_intensive_task, data) 
        for data in dataset
    ]
    results = await asyncio.gather(*tasks)
```

---

## 🏗️ 多核心處理架構設計

### 整體架構圖
```mermaid
graph TD
    A[主進程 - 異步調度器] --> B[I/O 任務池]
    A --> C[CPU 任務池]
    B --> D[數據庫連接池]
    B --> E[文件 I/O 池]
    B --> F[網路請求池]
    C --> G[郵件解析進程池]
    C --> H[文件處理進程池]
    C --> I[數據分析進程池]
    G --> J[進程 1: 附件解析]
    G --> K[進程 2: 內容分析]
    G --> L[進程 N: 分類處理]
```

### 1. CPU vs I/O 任務分離策略

#### 1.1 任務類型識別器
```python
from enum import Enum
from typing import Callable, Any, Dict
import psutil
import time

class TaskType(Enum):
    IO_BOUND = "io_bound"
    CPU_BOUND = "cpu_bound"
    MIXED = "mixed"

class TaskClassifier:
    """任務類型分類器"""
    
    # 預定義任務類型映射
    TASK_TYPE_MAP = {
        'file_read': TaskType.IO_BOUND,
        'file_write': TaskType.IO_BOUND,
        'http_request': TaskType.IO_BOUND,
        'database_query': TaskType.IO_BOUND,
        'email_parsing': TaskType.CPU_BOUND,
        'pdf_extraction': TaskType.CPU_BOUND,
        'excel_processing': TaskType.CPU_BOUND,
        'data_analysis': TaskType.CPU_BOUND,
        'email_classification': TaskType.MIXED
    }
    
    @classmethod
    def classify_task(cls, task_name: str, task_function: Callable = None) -> TaskType:
        """分類任務類型"""
        # 首先檢查預定義映射
        if task_name in cls.TASK_TYPE_MAP:
            return cls.TASK_TYPE_MAP[task_name]
        
        # 基於函數名啟發式分類
        if task_function:
            func_name = task_function.__name__.lower()
            
            # I/O 密集型特徵
            io_keywords = ['read', 'write', 'fetch', 'download', 'upload', 'request', 'query']
            if any(keyword in func_name for keyword in io_keywords):
                return TaskType.IO_BOUND
            
            # CPU 密集型特徵
            cpu_keywords = ['parse', 'analyze', 'process', 'compute', 'calculate', 'extract']
            if any(keyword in func_name for keyword in cpu_keywords):
                return TaskType.CPU_BOUND
        
        return TaskType.MIXED  # 默認混合型

    @classmethod
    def profile_task_runtime(cls, task_function: Callable, sample_data: Any) -> TaskType:
        """動態分析任務類型"""
        start_time = time.time()
        cpu_start = psutil.cpu_percent()
        
        # 執行小樣本測試
        try:
            task_function(sample_data)
        except Exception:
            pass  # 忽略執行錯誤，只關注性能特徵
        
        cpu_usage = psutil.cpu_percent() - cpu_start
        execution_time = time.time() - start_time
        
        # 基於 CPU 使用率和執行時間判斷
        if cpu_usage > 70 and execution_time > 0.1:
            return TaskType.CPU_BOUND
        elif cpu_usage < 30:
            return TaskType.IO_BOUND
        else:
            return TaskType.MIXED
```

#### 1.2 智能任務路由器
```python
class TaskRouter:
    """智能任務路由器"""
    
    def __init__(self):
        # CPU 核心數檢測
        self.cpu_cores = psutil.cpu_count(logical=False)
        self.logical_cores = psutil.cpu_count(logical=True)
        
        # 進程池配置
        self.cpu_pool_size = min(self.cpu_cores, 8)  # 避免過度創建進程
        self.io_pool_size = min(self.logical_cores * 2, 20)  # I/O 可以更多線程
        
        # 創建執行器
        self.cpu_executor = None
        self.io_executor = None
        self.setup_executors()
    
    def setup_executors(self):
        """設置執行器"""
        from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
        
        # CPU 密集型任務使用進程池
        self.cpu_executor = ProcessPoolExecutor(
            max_workers=self.cpu_pool_size,
            mp_context=multiprocessing.get_context('spawn')  # 更穩定的進程創建方式
        )
        
        # I/O 密集型任務使用線程池
        self.io_executor = ThreadPoolExecutor(
            max_workers=self.io_pool_size,
            thread_name_prefix="IOWorker"
        )
        
        logger.info(f"[OK] 任務路由器初始化完成")
        logger.info(f"    CPU 核心數: {self.cpu_cores}, 邏輯核心數: {self.logical_cores}")
        logger.info(f"    CPU 進程池大小: {self.cpu_pool_size}")
        logger.info(f"    I/O 線程池大小: {self.io_pool_size}")
    
    async def route_task(self, task_type: TaskType, task_function: Callable, *args, **kwargs):
        """路由任務到合適的執行器"""
        loop = asyncio.get_event_loop()
        
        if task_type == TaskType.CPU_BOUND:
            # CPU 密集型任務到進程池
            return await loop.run_in_executor(
                self.cpu_executor, task_function, *args
            )
        elif task_type == TaskType.IO_BOUND:
            # I/O 密集型任務到線程池
            return await loop.run_in_executor(
                self.io_executor, task_function, *args
            )
        else:  # MIXED
            # 混合型任務根據當前負載動態選擇
            cpu_usage = psutil.cpu_percent()
            if cpu_usage < 60:
                return await loop.run_in_executor(
                    self.cpu_executor, task_function, *args
                )
            else:
                return await loop.run_in_executor(
                    self.io_executor, task_function, *args
                )
    
    def shutdown(self):
        """關閉執行器"""
        if self.cpu_executor:
            self.cpu_executor.shutdown(wait=True)
        if self.io_executor:
            self.io_executor.shutdown(wait=True)
```

### 2. ProcessPoolExecutor 配置策略

#### 2.1 優化的進程池管理器
```python
class OptimizedProcessPool:
    """優化的進程池管理器"""
    
    def __init__(self, 
                 max_workers: int = None,
                 initializer: Callable = None,
                 initargs: tuple = None,
                 mp_context=None):
        
        # 自動計算最佳工作進程數
        if max_workers is None:
            max_workers = min(psutil.cpu_count(), 8)
        
        self.max_workers = max_workers
        self.initializer = initializer
        self.initargs = initargs or ()
        
        # 使用 spawn 方式創建進程（更穩定，避免資源洩漏）
        if mp_context is None:
            mp_context = multiprocessing.get_context('spawn')
        
        self.mp_context = mp_context
        self.executor = None
        self.active_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.lock = threading.Lock()
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器退出"""
        await self.shutdown()
    
    async def start(self):
        """啟動進程池"""
        if self.executor is None:
            self.executor = ProcessPoolExecutor(
                max_workers=self.max_workers,
                mp_context=self.mp_context,
                initializer=self.initializer,
                initargs=self.initargs
            )
            logger.info(f"[OK] 進程池已啟動，工作進程數: {self.max_workers}")
    
    async def submit_task(self, func: Callable, *args, **kwargs):
        """提交任務到進程池"""
        if self.executor is None:
            await self.start()
        
        with self.lock:
            self.active_tasks += 1
        
        loop = asyncio.get_event_loop()
        
        try:
            # 包裝函數以支持 kwargs
            wrapper_func = functools.partial(func, **kwargs) if kwargs else func
            result = await loop.run_in_executor(self.executor, wrapper_func, *args)
            
            with self.lock:
                self.completed_tasks += 1
                
            return result
            
        except Exception as e:
            with self.lock:
                self.failed_tasks += 1
            raise e
        finally:
            with self.lock:
                self.active_tasks -= 1
    
    async def map_tasks(self, func: Callable, iterable, chunk_size: int = None):
        """批量映射任務"""
        if chunk_size is None:
            chunk_size = max(1, len(iterable) // (self.max_workers * 4))
        
        tasks = []
        for item in iterable:
            task = self.submit_task(func, item)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
    
    async def shutdown(self, wait: bool = True):
        """關閉進程池"""
        if self.executor:
            self.executor.shutdown(wait=wait)
            self.executor = None
            logger.info("[OK] 進程池已關閉")
    
    def get_stats(self) -> dict:
        """獲取統計信息"""
        with self.lock:
            return {
                'max_workers': self.max_workers,
                'active_tasks': self.active_tasks,
                'completed_tasks': self.completed_tasks,
                'failed_tasks': self.failed_tasks,
                'total_tasks': self.completed_tasks + self.failed_tasks,
                'success_rate': (
                    self.completed_tasks / (self.completed_tasks + self.failed_tasks) * 100
                    if (self.completed_tasks + self.failed_tasks) > 0 else 0
                )
            }
```

#### 2.2 進程初始化和資源管理
```python
def process_initializer():
    """進程池工作進程初始化函數"""
    import os
    import sys
    import signal
    from loguru import logger
    
    # 設置工作進程 ID
    process_id = os.getpid()
    
    # 配置日誌
    logger.add(
        f"logs/worker_{process_id}.log",
        rotation="100 MB",
        retention="7 days",
        level="INFO"
    )
    
    # 設置 Unicode 環境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 設置信號處理（優雅關閉）
    def signal_handler(signum, frame):
        logger.info(f"工作進程 {process_id} 接收到終止信號 {signum}")
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    logger.info(f"[OK] 工作進程 {process_id} 初始化完成")

class ProcessResourceManager:
    """進程資源管理器"""
    
    @staticmethod
    def optimize_process_memory():
        """優化進程記憶體使用"""
        import gc
        import resource
        
        # 設置記憶體限制（避免單個進程消耗過多記憶體）
        max_memory = 512 * 1024 * 1024  # 512MB
        resource.setrlimit(resource.RLIMIT_AS, (max_memory, max_memory))
        
        # 強制垃圾回收
        gc.collect()
    
    @staticmethod
    def monitor_process_health():
        """監控進程健康狀態"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        return {
            'pid': process.pid,
            'memory_percent': process.memory_percent(),
            'cpu_percent': process.cpu_percent(),
            'num_threads': process.num_threads(),
            'status': process.status()
        }
```

### 3. 進程間通訊機制

#### 3.1 高效的進程間數據傳輸
```python
import pickle
import json
from multiprocessing import Queue, Manager
from concurrent.futures import ProcessPoolExecutor
import redis

class InterProcessCommunication:
    """進程間通訊管理器"""
    
    def __init__(self, method: str = "queue"):
        self.method = method
        self.queue = None
        self.manager = None
        self.redis_client = None
        
        self.setup_communication()
    
    def setup_communication(self):
        """設置通訊機制"""
        if self.method == "queue":
            # 使用 multiprocessing.Queue
            self.queue = Queue(maxsize=1000)
            
        elif self.method == "manager":
            # 使用 Manager 共享對象
            self.manager = Manager()
            self.shared_dict = self.manager.dict()
            self.shared_list = self.manager.list()
            
        elif self.method == "redis":
            # 使用 Redis 作為中介
            try:
                self.redis_client = redis.Redis(
                    host='localhost', 
                    port=6379, 
                    db=0,
                    decode_responses=True
                )
                self.redis_client.ping()
            except Exception as e:
                logger.warning(f"Redis 連接失敗，回退到 Queue: {e}")
                self.method = "queue"
                self.queue = Queue(maxsize=1000)
    
    async def send_data(self, data: dict, key: str = None):
        """發送數據到其他進程"""
        if self.method == "queue":
            self.queue.put(data)
            
        elif self.method == "manager":
            if key:
                self.shared_dict[key] = data
            else:
                self.shared_list.append(data)
                
        elif self.method == "redis":
            serialized_data = json.dumps(data, ensure_ascii=False)
            channel = key or "default_channel"
            self.redis_client.publish(channel, serialized_data)
    
    async def receive_data(self, timeout: int = 10, key: str = None):
        """從其他進程接收數據"""
        if self.method == "queue":
            import queue
            try:
                return self.queue.get(timeout=timeout)
            except queue.Empty:
                return None
                
        elif self.method == "manager":
            if key and key in self.shared_dict:
                return self.shared_dict.pop(key)
            elif self.shared_list:
                return self.shared_list.pop(0)
            return None
            
        elif self.method == "redis":
            channel = key or "default_channel"
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe(channel)
            
            try:
                message = pubsub.get_message(timeout=timeout)
                if message and message['type'] == 'message':
                    return json.loads(message['data'])
            except Exception as e:
                logger.error(f"Redis 接收數據失敗: {e}")
            finally:
                pubsub.unsubscribe(channel)
            
            return None

class SharedMemoryManager:
    """共享記憶體管理器"""
    
    def __init__(self):
        from multiprocessing import shared_memory
        self.shared_memory = shared_memory
        self.memory_blocks = {}
    
    def create_shared_array(self, name: str, size: int, dtype='float64'):
        """創建共享數組"""
        import numpy as np
        
        try:
            # 嘗試創建新的共享記憶體
            shm = self.shared_memory.SharedMemory(create=True, size=size, name=name)
            array = np.ndarray((size,), dtype=dtype, buffer=shm.buf)
            
            self.memory_blocks[name] = {
                'shm': shm,
                'array': array,
                'size': size,
                'dtype': dtype
            }
            
            return array
            
        except FileExistsError:
            # 共享記憶體已存在，連接到現有的
            return self.connect_shared_array(name, size, dtype)
    
    def connect_shared_array(self, name: str, size: int, dtype='float64'):
        """連接到現有共享數組"""
        import numpy as np
        
        shm = self.shared_memory.SharedMemory(name=name)
        array = np.ndarray((size,), dtype=dtype, buffer=shm.buf)
        
        self.memory_blocks[name] = {
            'shm': shm,
            'array': array,
            'size': size,
            'dtype': dtype
        }
        
        return array
    
    def cleanup(self):
        """清理共享記憶體"""
        for name, block in self.memory_blocks.items():
            try:
                block['shm'].close()
                block['shm'].unlink()
            except Exception as e:
                logger.warning(f"清理共享記憶體 {name} 失敗: {e}")
        
        self.memory_blocks.clear()
```

### 4. 負載均衡和資源管理

#### 4.1 動態負載均衡器
```python
class DynamicLoadBalancer:
    """動態負載均衡器"""
    
    def __init__(self):
        self.process_loads = {}
        self.process_capabilities = {}
        self.monitoring_interval = 5.0
        self.monitoring_task = None
    
    async def start_monitoring(self):
        """啟動負載監控"""
        self.monitoring_task = asyncio.create_task(self._monitor_processes())
    
    async def stop_monitoring(self):
        """停止負載監控"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
    
    async def _monitor_processes(self):
        """監控進程負載"""
        while True:
            try:
                current_processes = psutil.pids()
                for pid in current_processes:
                    try:
                        process = psutil.Process(pid)
                        if process.name().startswith('python'):
                            self.process_loads[pid] = {
                                'cpu_percent': process.cpu_percent(),
                                'memory_percent': process.memory_percent(),
                                'num_threads': process.num_threads(),
                                'timestamp': time.time()
                            }
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"進程監控失敗: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    def get_least_loaded_process(self) -> int:
        """獲取負載最低的進程"""
        if not self.process_loads:
            return None
        
        # 計算綜合負載分數
        best_pid = None
        best_score = float('inf')
        
        current_time = time.time()
        
        for pid, load_info in self.process_loads.items():
            # 忽略過期數據
            if current_time - load_info['timestamp'] > 30:
                continue
            
            # 綜合負載分數（CPU + 記憶體使用率）
            score = load_info['cpu_percent'] + load_info['memory_percent']
            
            if score < best_score:
                best_score = score
                best_pid = pid
        
        return best_pid
    
    def should_scale_up(self) -> bool:
        """判斷是否需要擴展進程"""
        if not self.process_loads:
            return True
        
        # 計算平均負載
        total_cpu = sum(load['cpu_percent'] for load in self.process_loads.values())
        avg_cpu = total_cpu / len(self.process_loads)
        
        # 如果平均 CPU 使用率超過 80%，建議擴展
        return avg_cpu > 80
    
    def should_scale_down(self) -> bool:
        """判斷是否需要縮減進程"""
        if len(self.process_loads) <= 2:  # 至少保留 2 個進程
            return False
        
        # 計算平均負載
        total_cpu = sum(load['cpu_percent'] for load in self.process_loads.values())
        avg_cpu = total_cpu / len(self.process_loads)
        
        # 如果平均 CPU 使用率低於 30%，可以考慮縮減
        return avg_cpu < 30

class ResourceManager:
    """資源管理器"""
    
    def __init__(self):
        self.memory_threshold = 80  # 記憶體使用率閾值
        self.cpu_threshold = 90     # CPU 使用率閾值
        self.disk_threshold = 85    # 磁盤使用率閾值
    
    def check_system_resources(self) -> dict:
        """檢查系統資源狀態"""
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        disk = psutil.disk_usage('/')
        
        return {
            'memory': {
                'percent': memory.percent,
                'available': memory.available,
                'total': memory.total,
                'warning': memory.percent > self.memory_threshold
            },
            'cpu': {
                'percent': cpu_percent,
                'warning': cpu_percent > self.cpu_threshold
            },
            'disk': {
                'percent': disk.percent,
                'free': disk.free,
                'total': disk.total,
                'warning': disk.percent > self.disk_threshold
            }
        }
    
    def can_accept_new_task(self, estimated_memory_mb: int = 0) -> bool:
        """判斷是否可以接受新任務"""
        resources = self.check_system_resources()
        
        # 記憶體檢查
        if resources['memory']['warning']:
            return False
        
        # CPU 檢查
        if resources['cpu']['warning']:
            return False
        
        # 預估記憶體需求檢查
        if estimated_memory_mb > 0:
            available_mb = resources['memory']['available'] / (1024 * 1024)
            if available_mb < estimated_memory_mb * 1.2:  # 留 20% 緩衝
                return False
        
        return True
    
    async def wait_for_resources(self, max_wait_time: int = 300):
        """等待資源可用"""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            if self.can_accept_new_task():
                return True
            
            logger.info("系統資源不足，等待資源釋放...")
            await asyncio.sleep(5)
        
        return False
```

### 5. 具體應用場景實現

#### 5.1 郵件處理多核心化
```python
class MultiCoreEmailProcessor:
    """多核心郵件處理器"""
    
    def __init__(self):
        self.process_pool = OptimizedProcessPool(
            initializer=process_initializer,
            initargs=()
        )
        self.task_router = TaskRouter()
        self.load_balancer = DynamicLoadBalancer()
    
    async def process_email_batch(self, emails: List[dict]) -> List[dict]:
        """批量處理郵件（多核心）"""
        await self.load_balancer.start_monitoring()
        
        try:
            # 分離不同類型的任務
            parsing_tasks = []
            analysis_tasks = []
            io_tasks = []
            
            for email in emails:
                # 郵件解析（CPU 密集型）
                parsing_task = self._create_parsing_task(email)
                parsing_tasks.append(parsing_task)
                
                # 附件分析（CPU 密集型）
                if email.get('attachments'):
                    analysis_task = self._create_analysis_task(email)
                    analysis_tasks.append(analysis_task)
                
                # 資料庫保存（I/O 密集型）
                io_task = self._create_io_task(email)
                io_tasks.append(io_task)
            
            # 並行執行不同類型的任務
            parsing_results = await asyncio.gather(*parsing_tasks)
            analysis_results = await asyncio.gather(*analysis_tasks)
            io_results = await asyncio.gather(*io_tasks)
            
            # 整合結果
            return self._integrate_results(parsing_results, analysis_results, io_results)
            
        finally:
            await self.load_balancer.stop_monitoring()
    
    async def _create_parsing_task(self, email: dict):
        """創建郵件解析任務"""
        return await self.task_router.route_task(
            TaskType.CPU_BOUND,
            self._parse_email_content,
            email
        )
    
    async def _create_analysis_task(self, email: dict):
        """創建附件分析任務"""
        return await self.task_router.route_task(
            TaskType.CPU_BOUND,
            self._analyze_attachments,
            email['attachments']
        )
    
    async def _create_io_task(self, email: dict):
        """創建 I/O 任務"""
        return await self.task_router.route_task(
            TaskType.IO_BOUND,
            self._save_email_data,
            email
        )
    
    @staticmethod
    def _parse_email_content(email: dict) -> dict:
        """解析郵件內容（CPU 密集型）"""
        # 這個函數會在子進程中執行
        import re
        import html
        
        content = email.get('content', '')
        
        # 提取關鍵信息
        patterns = {
            'urls': r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
            'emails': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone_numbers': r'\b\d{10,11}\b',
        }
        
        extracted_data = {}
        for key, pattern in patterns.items():
            matches = re.findall(pattern, content)
            extracted_data[key] = list(set(matches))  # 去重
        
        # HTML 解碼
        if '<' in content and '>' in content:
            extracted_data['html_content'] = html.unescape(content)
        
        return {
            'email_id': email['id'],
            'parsed_data': extracted_data,
            'content_length': len(content),
            'processing_time': time.time()
        }
    
    @staticmethod
    def _analyze_attachments(attachments: List[dict]) -> dict:
        """分析附件（CPU 密集型）"""
        import hashlib
        import mimetypes
        
        analysis_results = []
        
        for attachment in attachments:
            file_path = attachment.get('file_path', '')
            file_data = attachment.get('data', b'')
            
            # 計算文件哈希
            file_hash = hashlib.md5(file_data).hexdigest() if file_data else None
            
            # 檢測文件類型
            mime_type, _ = mimetypes.guess_type(file_path)
            
            # 文件安全檢查
            is_safe = not any(ext in file_path.lower() for ext in ['.exe', '.bat', '.cmd', '.scr'])
            
            analysis_results.append({
                'file_path': file_path,
                'file_hash': file_hash,
                'mime_type': mime_type,
                'is_safe': is_safe,
                'file_size': len(file_data) if file_data else 0
            })
        
        return {
            'attachment_analysis': analysis_results,
            'total_attachments': len(attachments),
            'safe_attachments': sum(1 for r in analysis_results if r['is_safe'])
        }
    
    @staticmethod
    def _save_email_data(email: dict) -> dict:
        """保存郵件數據（I/O 密集型）"""
        # 這個函數處理數據庫保存等 I/O 操作
        # 模擬數據庫操作
        time.sleep(0.1)  # 模擬 I/O 延遲
        
        return {
            'email_id': email['id'],
            'saved': True,
            'save_time': time.time()
        }
    
    def _integrate_results(self, parsing_results, analysis_results, io_results) -> List[dict]:
        """整合處理結果"""
        integrated = []
        
        # 根據 email_id 整合結果
        email_results = {}
        
        for result in parsing_results:
            email_id = result['email_id']
            email_results[email_id] = {'parsing': result}
        
        for result in analysis_results:
            # analysis_results 可能不包含 email_id，需要其他方式關聯
            pass
        
        for result in io_results:
            email_id = result['email_id']
            if email_id in email_results:
                email_results[email_id]['io'] = result
        
        return list(email_results.values())
```

#### 5.2 文件處理多核心化
```python
class MultiCoreFileProcessor:
    """多核心文件處理器"""
    
    def __init__(self):
        self.process_pool = OptimizedProcessPool()
        self.chunk_size = 1024 * 1024  # 1MB chunks
    
    async def process_large_files_parallel(self, file_paths: List[str]) -> List[dict]:
        """並行處理大文件"""
        # 按文件大小排序，大文件優先處理
        files_with_size = []
        for file_path in file_paths:
            try:
                file_size = os.path.getsize(file_path)
                files_with_size.append((file_path, file_size))
            except OSError:
                continue
        
        # 按文件大小降序排列
        files_with_size.sort(key=lambda x: x[1], reverse=True)
        
        # 創建處理任務
        tasks = []
        for file_path, file_size in files_with_size:
            if file_size > self.chunk_size * 10:  # 大於 10MB 的文件分塊處理
                task = self._process_large_file_chunked(file_path, file_size)
            else:
                task = self._process_small_file(file_path)
            
            tasks.append(task)
        
        # 並行執行所有任務
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 處理異常結果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'file_path': files_with_size[i][0],
                    'status': 'error',
                    'error': str(result)
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _process_large_file_chunked(self, file_path: str, file_size: int) -> dict:
        """分塊處理大文件"""
        chunk_count = (file_size + self.chunk_size - 1) // self.chunk_size
        chunk_tasks = []
        
        # 為每個塊創建處理任務
        for chunk_index in range(chunk_count):
            start_pos = chunk_index * self.chunk_size
            end_pos = min(start_pos + self.chunk_size, file_size)
            
            chunk_task = self.process_pool.submit_task(
                self._process_file_chunk,
                file_path, start_pos, end_pos, chunk_index
            )
            chunk_tasks.append(chunk_task)
        
        # 等待所有塊處理完成
        chunk_results = await asyncio.gather(*chunk_tasks)
        
        # 整合塊結果
        return self._merge_chunk_results(file_path, chunk_results)
    
    async def _process_small_file(self, file_path: str) -> dict:
        """處理小文件"""
        return await self.process_pool.submit_task(
            self._process_entire_file,
            file_path
        )
    
    @staticmethod
    def _process_file_chunk(file_path: str, start_pos: int, end_pos: int, chunk_index: int) -> dict:
        """處理文件塊（在子進程中執行）"""
        try:
            with open(file_path, 'rb') as f:
                f.seek(start_pos)
                chunk_data = f.read(end_pos - start_pos)
            
            # 對塊數據進行處理（例如：計算哈希、提取信息等）
            import hashlib
            chunk_hash = hashlib.md5(chunk_data).hexdigest()
            
            return {
                'chunk_index': chunk_index,
                'start_pos': start_pos,
                'end_pos': end_pos,
                'chunk_size': len(chunk_data),
                'chunk_hash': chunk_hash,
                'status': 'success'
            }
            
        except Exception as e:
            return {
                'chunk_index': chunk_index,
                'start_pos': start_pos,
                'end_pos': end_pos,
                'status': 'error',
                'error': str(e)
            }
    
    @staticmethod
    def _process_entire_file(file_path: str) -> dict:
        """處理整個文件（在子進程中執行）"""
        try:
            import hashlib
            import os
            from datetime import datetime
            
            # 獲取文件信息
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size
            
            # 計算文件哈希
            hash_md5 = hashlib.md5()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            
            file_hash = hash_md5.hexdigest()
            
            # 提取文件元數據
            return {
                'file_path': file_path,
                'file_size': file_size,
                'file_hash': file_hash,
                'modified_time': datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                'processing_method': 'entire_file',
                'status': 'success'
            }
            
        except Exception as e:
            return {
                'file_path': file_path,
                'status': 'error',
                'error': str(e)
            }
    
    def _merge_chunk_results(self, file_path: str, chunk_results: List[dict]) -> dict:
        """合併塊處理結果"""
        successful_chunks = [r for r in chunk_results if r.get('status') == 'success']
        failed_chunks = [r for r in chunk_results if r.get('status') == 'error']
        
        total_size = sum(r.get('chunk_size', 0) for r in successful_chunks)
        
        return {
            'file_path': file_path,
            'total_chunks': len(chunk_results),
            'successful_chunks': len(successful_chunks),
            'failed_chunks': len(failed_chunks),
            'total_processed_size': total_size,
            'chunk_hashes': [r.get('chunk_hash') for r in successful_chunks],
            'processing_method': 'chunked',
            'status': 'success' if len(failed_chunks) == 0 else 'partial_success'
        }
```

---

## 📊 性能預期和基準測試

### 性能提升目標
```yaml
CPU 利用率提升:
  當前: 25-50% (GIL 限制)
  目標: 80%+ (多進程真並行)
  提升倍數: 1.6x - 3.2x

處理吞吐量:
  郵件處理: 50 郵件/小時 → 500+ 郵件/小時 (10x)
  文件處理: 10 文件/分鐘 → 100+ 文件/分鐘 (10x)
  數據分析: 100 記錄/分鐘 → 1000+ 記錄/分鐘 (10x)

響應時間:
  CPU 密集型任務: 減少 60-80%
  混合型任務: 減少 40-60%
  系統整體響應: 提升 50-70%
```

### 基準測試框架
```python
class MultiCorePerformanceBenchmark:
    """多核心性能基準測試"""
    
    def __init__(self):
        self.results = {}
        self.baseline_results = {}
    
    async def run_cpu_bound_benchmark(self, iterations: int = 1000):
        """CPU 密集型任務基準測試"""
        import time
        
        # 測試數據
        test_data = [i for i in range(iterations)]
        
        # 單線程基準
        start_time = time.time()
        single_thread_results = [self._cpu_intensive_task(x) for x in test_data]
        single_thread_time = time.time() - start_time
        
        # 多進程測試
        async with OptimizedProcessPool() as pool:
            start_time = time.time()
            multi_process_results = await pool.map_tasks(self._cpu_intensive_task, test_data)
            multi_process_time = time.time() - start_time
        
        # 計算性能提升
        speedup = single_thread_time / multi_process_time if multi_process_time > 0 else 0
        
        self.results['cpu_bound'] = {
            'single_thread_time': single_thread_time,
            'multi_process_time': multi_process_time,
            'speedup': speedup,
            'efficiency': speedup / psutil.cpu_count(),
            'iterations': iterations
        }
        
        return self.results['cpu_bound']
    
    async def run_mixed_workload_benchmark(self):
        """混合工作負載基準測試"""
        # 模擬真實工作負載：CPU + I/O 混合
        tasks = [
            ('cpu_task', self._cpu_intensive_task, 100),
            ('io_task', self._io_intensive_task, 'test_file.txt'),
            ('mixed_task', self._mixed_task, {'cpu_work': 50, 'io_work': 5})
        ] * 10
        
        # 順序執行基準
        start_time = time.time()
        sequential_results = []
        for task_type, task_func, task_arg in tasks:
            if task_type == 'io_task':
                result = await task_func(task_arg)
            else:
                result = task_func(task_arg)
            sequential_results.append(result)
        sequential_time = time.time() - start_time
        
        # 並行執行測試
        task_router = TaskRouter()
        start_time = time.time()
        
        parallel_tasks = []
        for task_type, task_func, task_arg in tasks:
            if task_type == 'cpu_task':
                task = task_router.route_task(TaskType.CPU_BOUND, task_func, task_arg)
            elif task_type == 'io_task':
                task = task_router.route_task(TaskType.IO_BOUND, task_func, task_arg)
            else:
                task = task_router.route_task(TaskType.MIXED, task_func, task_arg)
            
            parallel_tasks.append(task)
        
        parallel_results = await asyncio.gather(*parallel_tasks)
        parallel_time = time.time() - start_time
        
        # 清理
        task_router.shutdown()
        
        speedup = sequential_time / parallel_time if parallel_time > 0 else 0
        
        self.results['mixed_workload'] = {
            'sequential_time': sequential_time,
            'parallel_time': parallel_time,
            'speedup': speedup,
            'task_count': len(tasks)
        }
        
        return self.results['mixed_workload']
    
    @staticmethod
    def _cpu_intensive_task(n: int) -> int:
        """CPU 密集型任務（計算質數）"""
        if n <= 1:
            return 0
        
        count = 0
        for i in range(2, n + 1):
            is_prime = True
            for j in range(2, int(i**0.5) + 1):
                if i % j == 0:
                    is_prime = False
                    break
            if is_prime:
                count += 1
        return count
    
    @staticmethod
    async def _io_intensive_task(filename: str) -> dict:
        """I/O 密集型任務"""
        import aiofiles
        import os
        
        # 寫入文件
        content = "Test content " * 1000
        async with aiofiles.open(filename, 'w') as f:
            await f.write(content)
        
        # 讀取文件
        async with aiofiles.open(filename, 'r') as f:
            read_content = await f.read()
        
        # 清理
        if os.path.exists(filename):
            os.remove(filename)
        
        return {
            'filename': filename,
            'written_bytes': len(content),
            'read_bytes': len(read_content),
            'match': content == read_content
        }
    
    @staticmethod
    def _mixed_task(params: dict) -> dict:
        """混合型任務"""
        import time
        
        # CPU 工作
        cpu_result = sum(i**2 for i in range(params['cpu_work']))
        
        # 模擬 I/O 等待
        time.sleep(params['io_work'] / 1000)  # 毫秒轉秒
        
        return {
            'cpu_result': cpu_result,
            'io_wait': params['io_work']
        }
    
    def generate_report(self) -> str:
        """生成性能報告"""
        report = "# 多核心處理性能測試報告\n\n"
        
        for test_name, results in self.results.items():
            report += f"## {test_name.replace('_', ' ').title()}\n\n"
            
            if 'speedup' in results:
                report += f"- **加速比**: {results['speedup']:.2f}x\n"
            
            if 'efficiency' in results:
                report += f"- **效率**: {results['efficiency']:.2f}\n"
            
            for key, value in results.items():
                if key not in ['speedup', 'efficiency']:
                    if isinstance(value, float):
                        report += f"- **{key}**: {value:.3f}\n"
                    else:
                        report += f"- **{key}**: {value}\n"
            
            report += "\n"
        
        return report
```

---

*📅 文檔版本: v1.0*  
*👤 架構師: Backend-Architect Agent*  
*🔄 最後更新: 2025-07-30*  
*⏱️ 預估實施時間: 36 小時*