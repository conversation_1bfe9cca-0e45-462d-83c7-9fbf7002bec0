"""
高級日誌系統管理器
TASK_003: 建立結構化日誌系統
特殊要求：支援 info, debug, error, warning, 效能 等級別，不同顏色，包含檔案和函式名稱
"""

import logging
import logging.handlers
import json
import os
import sys
import inspect
import threading
import queue
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from enum import Enum
from contextlib import contextmanager
from dataclasses import dataclass
import colorama
from colorama import Fore, Back, Style

# 初始化 colorama
colorama.init(autoreset=True)


class LogLevel(Enum):
    """日誌級別枚舉"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"
    PERFORMANCE = "PERFORMANCE"


class LogFormat(Enum):
    """日誌格式枚舉"""
    SIMPLE = "SIMPLE"
    DETAILED = "DETAILED"
    JSON = "JSON"


@dataclass
class ColorScheme:
    """顏色配置"""
    debug: str = Fore.BLUE
    info: str = Fore.GREEN
    warning: str = Fore.YELLOW
    error: str = Fore.RED
    critical: str = Back.RED + Fore.WHITE
    performance: str = Fore.MAGENTA
    reset: str = Style.RESET_ALL


class PerformanceTimer:
    """效能計時器"""
    
    def __init__(self, logger_manager: 'LoggerManager', operation_name: str):
        self.logger_manager = logger_manager
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.logger_manager.log_performance_metrics({
                "operation": self.operation_name,
                "duration_seconds": duration,
                "timestamp": datetime.now().isoformat()
            })


class PerformanceLogger:
    """效能日誌記錄器"""
    
    def __init__(self, logger_manager: 'LoggerManager'):
        self.logger_manager = logger_manager
    
    def timer(self, operation_name: str) -> PerformanceTimer:
        """建立效能計時器"""
        return PerformanceTimer(self.logger_manager, operation_name)
    
    def log_metrics(self, metrics: Dict[str, Union[float, int, str]]) -> None:
        """記錄效能指標"""
        self.logger_manager.log_performance_metrics(metrics)


class StructuredLogger:
    """結構化日誌記錄器"""
    
    def __init__(self, logger_manager: 'LoggerManager', name: str):
        self.logger_manager = logger_manager
        self.name = name
        self.logger = logger_manager._get_raw_logger(name)
    
    def log_structured(self, level: LogLevel, message: str, **kwargs) -> None:
        """記錄結構化日誌"""
        self.logger_manager.log_with_context(
            self.logger, level, message, kwargs
        )
    
    def debug(self, message: str, **kwargs) -> None:
        """記錄 DEBUG 級別日誌"""
        self.log_structured(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """記錄 INFO 級別日誌"""
        self.log_structured(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """記錄 WARNING 級別日誌"""
        self.log_structured(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """記錄 ERROR 級別日誌"""
        self.log_structured(LogLevel.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """記錄 CRITICAL 級別日誌"""
        self.log_structured(LogLevel.CRITICAL, message, **kwargs)
    
    def performance(self, message: str, **kwargs) -> None:
        """記錄 PERFORMANCE 級別日誌"""
        self.log_structured(LogLevel.PERFORMANCE, message, **kwargs)


class ColoredFormatter(logging.Formatter):
    """帶顏色的日誌格式化器"""
    
    def __init__(self, color_scheme: ColorScheme, include_caller_info: bool = True):
        self.color_scheme = color_scheme
        self.include_caller_info = include_caller_info
        
        if include_caller_info:
            fmt = '%(asctime)s [%(levelname)s] - %(name)s - %(caller_filename)s:%(caller_function)s:%(caller_line)d - %(message)s'
        else:
            fmt = '%(asctime)s [%(levelname)s] - %(name)s - %(message)s'
        
        super().__init__(fmt, datefmt='%Y-%m-%d %H:%M:%S')
    
    def format(self, record):
        # 選擇顏色
        if record.levelname == 'DEBUG':
            color = self.color_scheme.debug
        elif record.levelname == 'INFO':
            color = self.color_scheme.info
        elif record.levelname == 'WARNING':
            color = self.color_scheme.warning
        elif record.levelname == 'ERROR':
            color = self.color_scheme.error
        elif record.levelname == 'CRITICAL':
            color = self.color_scheme.critical
        elif hasattr(record, 'performance') and record.performance:
            color = self.color_scheme.performance
        else:
            color = ""
        
        # 格式化訊息
        formatted = super().format(record)
        
        # 加上顏色
        if color:
            return f"{color}{formatted}{self.color_scheme.reset}"
        return formatted


class JSONFormatter(logging.Formatter):
    """JSON 格式化器"""
    
    def __init__(self, include_caller_info: bool = True):
        self.include_caller_info = include_caller_info
        super().__init__()
    
    def format(self, record):
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "thread": record.thread,
            "thread_name": record.threadName,
        }
        
        if self.include_caller_info:
            log_data.update({
                "filename": record.filename,
                "function": record.funcName,
                "line_number": record.lineno,
                "module": record.module,
            })
        
        # 加入額外的上下文資訊
        if hasattr(record, 'context'):
            log_data["context"] = record.context
        
        if hasattr(record, 'performance'):
            log_data["performance"] = record.performance
        
        if hasattr(record, 'email_data'):
            log_data["email_data"] = record.email_data
        
        # 加入例外資訊
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }
        
        return json.dumps(log_data, ensure_ascii=False, indent=None)


class AsyncLogHandler:
    """非同步日誌處理器"""
    
    def __init__(self, logger_manager: 'LoggerManager'):
        self.logger_manager = logger_manager
        self.log_queue = queue.Queue()
        self.worker_thread = threading.Thread(target=self._process_logs, daemon=True)
        self.running = True
        self.worker_thread.start()
    
    def add_log(self, logger, level: LogLevel, message: str, context: Optional[Dict] = None):
        """加入日誌到佇列"""
        self.log_queue.put((logger, level, message, context))
    
    def _process_logs(self):
        """處理日誌佇列"""
        while self.running:
            try:
                log_item = self.log_queue.get(timeout=1)
                if log_item:
                    logger, level, message, context = log_item
                    self.logger_manager._write_log(logger, level, message, context)
                    self.log_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"日誌處理錯誤: {e}", file=sys.stderr)
    
    def flush(self):
        """清空佇列"""
        self.log_queue.join()
    
    def stop(self):
        """停止非同步處理"""
        self.running = False
        self.flush()


class LoggerManager:
    """日誌管理器主類"""
    
    def __init__(
        self,
        log_dir: Path = Path("logs"),
        default_level: LogLevel = LogLevel.INFO,
        enable_colors: bool = True,
        log_format: LogFormat = LogFormat.DETAILED,
        include_caller_info: bool = True,
        max_log_size_mb: float = 10.0,
        backup_count: int = 5,
        async_logging: bool = False
    ):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.default_level = default_level
        self.enable_colors = enable_colors
        self.log_format = log_format
        self.include_caller_info = include_caller_info
        self.max_log_size_mb = max_log_size_mb
        self.backup_count = backup_count
        
        self.color_scheme = ColorScheme()
        self.loggers: Dict[str, logging.Logger] = {}
        self.formatters: Dict[str, logging.Formatter] = {}
        
        # 非同步日誌
        self.async_logging = async_logging
        self.async_handler = AsyncLogHandler(self) if async_logging else None
        
        # 建立各種格式化器
        self._setup_formatters()
        
        # 建立預設日誌器
        self._setup_default_loggers()
    
    def _setup_formatters(self):
        """設定格式化器"""
        if self.log_format == LogFormat.JSON:
            json_formatter = JSONFormatter(self.include_caller_info)
            self.formatters['default'] = json_formatter
            self.formatters['file'] = json_formatter
            self.formatters['console'] = json_formatter
        else:
            # 檔案格式化器（無顏色）
            if self.include_caller_info:
                file_fmt = '%(asctime)s [%(levelname)s] - %(name)s - %(caller_filename)s:%(caller_function)s:%(caller_line)d - %(message)s'
            else:
                file_fmt = '%(asctime)s [%(levelname)s] - %(name)s - %(message)s'
            
            file_formatter = logging.Formatter(
                file_fmt, datefmt='%Y-%m-%d %H:%M:%S'
            )
            self.formatters['file'] = file_formatter
            self.formatters['default'] = file_formatter
            
            # 控制台格式化器（帶顏色）
            if self.enable_colors:
                self.formatters['console'] = ColoredFormatter(
                    self.color_scheme, self.include_caller_info
                )
            else:
                self.formatters['console'] = file_formatter
    
    def _setup_default_loggers(self):
        """設定預設日誌器"""
        # 主應用程式日誌
        self.get_logger("application")
        
        # 錯誤日誌
        self.get_logger("error")
        
        # 效能日誌
        self.get_logger("performance")
        
        # 郵件處理日誌
        self.get_logger("email_processing")
    
    def _get_raw_logger(self, name: str) -> logging.Logger:
        """取得原始 logging.Logger 物件"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            logger.setLevel(getattr(logging, self.default_level.value))
            
            # 清[EXCEPT_CHAR]現有處理器
            logger.handlers.clear()
            
            # 檔案處理器
            log_file = self.log_dir / f"{name}.log"
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=int(self.max_log_size_mb * 1024 * 1024),
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(self.formatters.get('file', self.formatters['default']))
            logger.addHandler(file_handler)
            
            # 控制台處理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(self.formatters.get('console', self.formatters['default']))
            logger.addHandler(console_handler)
            
            # 防止重複日誌
            logger.propagate = False
            
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def get_logger(self, name: str) -> StructuredLogger:
        """取得結構化日誌器"""
        return StructuredLogger(self, name)
    
    def get_performance_logger(self) -> PerformanceLogger:
        """取得效能日誌器"""
        return PerformanceLogger(self)
    
    def log(self, logger, level: LogLevel, message: str, context: Optional[Dict] = None):
        """記錄日誌"""
        # 確保使用原始的 logging.Logger
        if isinstance(logger, StructuredLogger):
            raw_logger = logger.logger
        else:
            raw_logger = logger
            
        if self.async_logging and self.async_handler:
            self.async_handler.add_log(raw_logger, level, message, context)
        else:
            self._write_log(raw_logger, level, message, context)
    
    def _write_log(self, logger: logging.Logger, level: LogLevel, message: str, context: Optional[Dict] = None):
        """實際寫入日誌"""
        # 建立日誌記錄
        if level == LogLevel.PERFORMANCE:
            log_level = logging.INFO
            # 標記為效能日誌
            extra = {'performance': True}
        else:
            log_level = getattr(logging, level.value)
            extra = {}
        
        if context:
            extra['context'] = context
        
        # 取得呼叫者資訊（加入到 extra 但不覆蓋內建屬性）
        if self.include_caller_info:
            frame = inspect.currentframe()
            try:
                # 往上找到實際的呼叫者（跳過 log 相關函數）
                caller_frame = frame
                while caller_frame:
                    caller_frame = caller_frame.f_back
                    if caller_frame and not (
                        # 跳過日誌管理器檔案的所有調用
                        'logger_manager.py' in caller_frame.f_code.co_filename or
                        # 跳過日誌相關函數
                        any(log_func in caller_frame.f_code.co_name.lower()
                            for log_func in ['log', '_write_log', 'format', 'info', 'debug', 'warning', 'error', 'critical'])
                    ):
                        break
                
                if caller_frame:
                    extra.update({
                        'caller_filename': os.path.basename(caller_frame.f_code.co_filename),
                        'caller_function': caller_frame.f_code.co_name,
                        'caller_line': caller_frame.f_lineno
                    })
            finally:
                del frame
        
        logger.log(log_level, message, extra=extra)
    
    def log_with_context(self, logger: logging.Logger, level: LogLevel, message: str, context: Dict[str, Any]):
        """記錄帶上下文的日誌"""
        self.log(logger, level, message, context)
    
    def log_async(self, logger: logging.Logger, level: LogLevel, message: str, context: Optional[Dict] = None):
        """記錄非同步日誌"""
        if self.async_handler:
            self.async_handler.add_log(logger, level, message, context)
        else:
            self.log(logger, level, message, context)
    
    def log_exception(self, logger: logging.Logger, exception: Exception, context: Optional[Dict] = None):
        """記錄例外"""
        error_logger = self._get_raw_logger("error")
        
        extra = {'context': context} if context else {}
        error_logger.error(f"例外發生: {str(exception)}", exc_info=exception, extra=extra)
    
    def log_performance_metrics(self, metrics: Dict[str, Union[float, int, str]]):
        """記錄效能指標"""
        perf_logger = self._get_raw_logger("performance")
        
        # 格式化效能指標
        metrics_str = ", ".join([f"{k}: {v}" for k, v in metrics.items()])
        message = f"效能指標 - {metrics_str}"
        
        self._write_log(perf_logger, LogLevel.PERFORMANCE, message, {'metrics': metrics})
    
    def log_email_processing(self, email_data: Dict[str, Any], stage: str, additional_info: Optional[Dict] = None):
        """記錄郵件處理日誌"""
        email_logger = self._get_raw_logger("email_processing")
        
        message = f"郵件處理 - {stage}: {email_data.get('subject', 'Unknown Subject')}"
        
        context = {
            'email_id': email_data.get('id'),
            'vendor': email_data.get('vendor'),
            'stage': stage,
            'sender': email_data.get('sender'),
            'email_data': email_data
        }
        
        if additional_info:
            context.update(additional_info)
        
        self._write_log(email_logger, LogLevel.INFO, message, context)
    
    def flush_async_logs(self):
        """清空非同步日誌佇列"""
        if self.async_handler:
            self.async_handler.flush()
    
    @classmethod
    def from_config(cls, config: Dict[str, Any], log_dir: Optional[Path] = None) -> 'LoggerManager':
        """從配置建立日誌管理器"""
        kwargs = {}
        
        if log_dir:
            kwargs['log_dir'] = log_dir
        
        if 'log_level' in config:
            kwargs['default_level'] = LogLevel(config['log_level'])
        
        if 'enable_colors' in config:
            kwargs['enable_colors'] = config['enable_colors']
        
        if 'log_format' in config:
            kwargs['log_format'] = LogFormat(config['log_format'])
        
        if 'include_caller_info' in config:
            kwargs['include_caller_info'] = config['include_caller_info']
        
        if 'max_log_size_mb' in config:
            kwargs['max_log_size_mb'] = config['max_log_size_mb']
        
        if 'backup_count' in config:
            kwargs['backup_count'] = config['backup_count']
        
        if 'async_logging' in config:
            kwargs['async_logging'] = config['async_logging']
        
        return cls(**kwargs)