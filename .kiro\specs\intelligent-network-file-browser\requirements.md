# 需求文件

## 簡介

智慧型網路檔案瀏覽系統是一個增強型檔案管理介面，擴展了現有的網路瀏覽器 http://localhost:5555/network/ui。此系統提供智慧搜尋功能、LLM 驅動的自動化，以及與現有資料處理工具（csv_to_summary.py 和 code_comparison.py）的無縫整合，用於管理儲存在網路共享上的半導體測試資料。

## 需求

### 需求 1：網路目錄瀏覽

**使用者故事：** 身為半導體測試工程師，我希望能瀏覽 \\************\test_log 中的所有資料夾和檔案，以便我能導覽完整的目錄結構並定位測試資料檔案。

#### 驗收標準

1. 當使用者存取網路檔案瀏覽器時，系統應顯示 \\************\test_log 的完整目錄樹
2. 當使用者點擊資料夾時，系統應展開並顯示該目錄內的所有子資料夾和檔案
3. 當使用者在目錄間導覽時，系統應維持當前路徑並允許向後導覽
4. 當系統遇到存取權限問題時，系統應使用 .env 檔案中的憑證（EMAIL_ADDRESS 和 EMAIL_PASSWORD）自動驗證
5. 當目錄結構載入時，系統應顯示檔案中繼資料，包括大小、修改日期和檔案類型

### 需求 2：智慧產品搜尋

**使用者故事：** 身為品質保證工程師，我希望能搜尋特定產品資料夾（例如「AAA」）並找出該資料夾下指定時間範圍內（例如最近 6 個月）的所有檔案，以便我能快速定位相關測試檔案而無需手動瀏覽。

#### 驗收標準

1. 當使用者在搜尋欄位輸入產品名稱時，系統應先定位包含該產品識別碼的資料夾
2. 當找到產品資料夾時，系統應使用高效搜尋方法掃描該資料夾下的所有子目錄和檔案（支援 os.walk()、pathlib.Path.rglob()、或 Windows Search API）
3. 當使用者指定時間範圍時，系統應篩選該產品資料夾下在指定時間範圍內修改的所有檔案，並支援並行搜尋以提升效能
4. 當找到搜尋結果時，系統應顯示產品資料夾路徑及其下符合時間條件的所有檔案清單
5. 當使用者選擇搜尋結果時，系統應能夠選取整個產品資料夾或特定時間範圍內的檔案進行後續處理

### 需求 3：LLM 驅動的自動搜尋

**使用者故事：** 身為資料分析師，我希望系統使用 LLM 功能智慧地理解我的搜尋查詢並自動定位相關資料，以便我能在測試資料中找到複雜的模式和關係而無需手動篩選。

#### 驗收標準

1. 當使用者輸入自然語言搜尋查詢時，LLM 應解釋查詢並產生適當的搜尋參數
2. 當 LLM 處理查詢時，系統應自動搜尋匹配的資料夾、檔案和內容模式
3. 當 LLM 識別相關資料時，系統應呈現結果並解釋為何選擇每個項目
4. 當搜尋涉及時間基準時，LLM 應正確解釋相對日期（例如「最近 6 個月」、「本季」）
5. 當 LLM 搜尋完成時，系統應提供發現摘要和建議的下一步行動

### 需求 4：整合處理工具

**使用者故事：** 身為測試營運經理，我希望能直接從檔案瀏覽器介面存取 csv_to_summary.py 和 code_comparison.py 功能，以便我能處理選定的資料而無需在不同工具間切換。

#### 驗收標準

1. 當使用者選擇檔案或資料夾時，系統應顯示兩個動作按鈕：「產生摘要」和「比較程式碼」
2. 當使用者點擊「產生摘要」時，系統應以選定的資料作為輸入執行 csv_to_summary.py
3. 當使用者點擊「比較程式碼」時，系統應以選定的資料作為輸入執行 code_comparison.py
4. 當執行任一工具時，系統應顯示進度指示器和即時狀態更新
5. 當處理完成時，系統應顯示結果並提供進一步行動的選項

### 需求 5：自動資料暫存

**使用者故事：** 身為生產工程師，我希望系統自動將選定的搜尋結果複製到本地暫存目錄（d:\temp\[產品名稱]），以便我能在上傳結果前本地處理資料。

#### 驗收標準

1. 當使用者選擇搜尋結果進行處理時，系統應在 d:\temp\[產品名稱] 自動建立暫存目錄
2. 當暫存目錄建立時，系統應複製所有選定檔案並維持原始目錄結構
3. 當複製操作完成時，系統應驗證檔案完整性並報告任何錯誤
4. 當檔案暫存時，系統應自動將暫存目錄路徑傳遞給處理工具
5. 當暫存失敗時，系統應提供清楚的錯誤訊息並清理任何部分複製

### 需求 6：自動結果上傳

**使用者故事：** 身為測試工程師，我希望處理結果自動上傳到 \\************\temp_7days\[產品名稱]，以便其他團隊成員能存取處理後的資料而無需手動檔案傳輸。

#### 驗收標準

1. 當資料處理成功完成時，系統應在 \\************\temp_7days\[產品名稱] 自動建立目標目錄
2. 當目標目錄準備就緒時，系統應上傳所有處理後的檔案和結果
3. 當上傳完成時，系統應驗證檔案完整性並提供確認
4. 當上傳失敗時，系統應重試最多 3 次並記錄詳細錯誤資訊
5. 當所有操作完成時，系統應清理暫時暫存檔案並提供摘要報告

### 需求 7：驗證與安全性

**使用者故事：** 身為系統管理員，我希望系統使用儲存的憑證安全地驗證網路資源，以便使用者能存取受保護的目錄而無需手動登入提示。

#### 驗收標準

1. 當系統啟動時，應從 .env 檔案讀取驗證憑證（EMAIL_ADDRESS 和 EMAIL_PASSWORD）
2. 當存取網路資源時，系統應使用儲存的憑證自動驗證
3. 當驗證失敗時，系統應記錄錯誤並提示手動憑證驗證
4. 當使用憑證時，系統應確保它們不會在日誌或使用者介面中暴露
5. 當網路會話過期時，系統應根據需要自動重新驗證

### 需求 8：使用者介面整合

**使用者故事：** 身為終端使用者，我希望所有新功能無縫整合到現有的網路瀏覽器介面 http://localhost:5555/network/ui，以便我能使用熟悉的導覽模式同時存取增強功能。

#### 驗收標準

1. 當使用者存取 http://localhost:5555/network/ui 時，增強介面應載入並顯示所有新功能
2. 當顯示新功能時，應遵循現有的 UI 設計模式和樣式
3. 當使用者與增強功能互動時，介面應提供清楚的回饋和狀態更新
4. 當操作進行中時，UI 應保持回應並允許取消長時間執行的任務
5. 當發生錯誤時，系統應顯示使用者友善的錯誤訊息和建議的補救步驟