"""統一服務配置管理器
基於現有配置系統的企業級整合方案
支援多環境、端口管理、服務發現
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

# 重用現有配置管理器
from src.infrastructure.config.config_manager import Confi<PERSON><PERSON><PERSON><PERSON> as BaseConfigManager
from src.infrastructure.config.settings import Settings


class ServiceType(Enum):
    """服務類型枚舉"""
    FLASK = "flask"
    FASTAPI = "fastapi"
    SCHEDULER = "scheduler"
    NETWORK = "network"


@dataclass
class ServiceConfig:
    """單一服務配置"""
    name: str
    service_type: ServiceType
    port: int
    path_prefix: str
    enabled: bool = True
    host: str = "0.0.0.0"
    workers: int = 1
    timeout: int = 300
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass 
class IntegratedServiceConfig:
    """整合服務配置"""
    main_port: int = 5555
    services: Dict[str, ServiceConfig] = None
    cors_origins: List[str] = None
    security_enabled: bool = True
    logging_level: str = "INFO"
    
    def __post_init__(self):
        if self.services is None:
            self.services = self._default_services()
        if self.cors_origins is None:
            self.cors_origins = ["*"]
    
    def _default_services(self) -> Dict[str, ServiceConfig]:
        """預設服務配置"""
        return {
            "inbox": ServiceConfig(
                name="郵件收件夾服務",
                service_type=ServiceType.FLASK,
                port=5001,  # 內部端口，通過代理訪問
                path_prefix="/inbox",
                metadata={"description": "Flask郵件收件夾處理"}
            ),
            "ft_eqc": ServiceConfig(
                name="FT-EQC處理服務", 
                service_type=ServiceType.FASTAPI,
                port=5002,
                path_prefix="/ft-eqc",
                metadata={"description": "FastAPI FT-EQC分析處理"}
            ),
            "scheduler": ServiceConfig(
                name="增強任務排程器",
                service_type=ServiceType.FASTAPI,
                port=5003,
                path_prefix="/scheduler",
                metadata={"description": "GTK特殊排程系統"}
            ),
            "network": ServiceConfig(
                name="網路瀏覽器API",
                service_type=ServiceType.FASTAPI,
                port=5004,
                path_prefix="/network",
                metadata={"description": "網路共享資料夾瀏覽"}
            )
        }


class UnifiedConfigManager:
    """統一配置管理器
    
    整合現有配置系統與新的服務整合需求
    """
    
    def __init__(self, env: str = "development", config_dir: Optional[Path] = None):
        # 重用現有配置管理系統
        self.base_config = BaseConfigManager(env, config_dir)
        
        # 新增整合服務配置
        self.service_config = self._load_service_config()
        
        # 環境變數覆蓋
        self._apply_env_overrides()
    
    def _load_service_config(self) -> IntegratedServiceConfig:
        """載入服務配置"""
        config_file = self.base_config.config_dir / "integrated_services.yaml"
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                return self._parse_service_config(data)
        
        # 使用預設配置
        return IntegratedServiceConfig()
    
    def _parse_service_config(self, data: Dict[str, Any]) -> IntegratedServiceConfig:
        """解析服務配置數據"""
        services = {}
        
        if "services" in data:
            for name, service_data in data["services"].items():
                service_data["service_type"] = ServiceType(service_data["service_type"])
                services[name] = ServiceConfig(**service_data)
        
        config_data = data.copy()
        config_data["services"] = services
        
        return IntegratedServiceConfig(**config_data)
    
    def _apply_env_overrides(self):
        """應用環境變數覆蓋"""
        # 主端口覆蓋
        if "MAIN_PORT" in os.environ:
            self.service_config.main_port = int(os.environ["MAIN_PORT"])
        
        # 服務端口覆蓋
        for service_name in self.service_config.services:
            env_key = f"{service_name.upper()}_PORT"
            if env_key in os.environ:
                self.service_config.services[service_name].port = int(os.environ[env_key])
    
    def get_service_config(self, service_name: str) -> Optional[ServiceConfig]:
        """獲取服務配置"""
        return self.service_config.services.get(service_name)
    
    def get_enabled_services(self) -> Dict[str, ServiceConfig]:
        """獲取所有啟用的服務"""
        return {
            name: config 
            for name, config in self.service_config.services.items()
            if config.enabled
        }
    
    def get_main_port(self) -> int:
        """獲取主端口"""
        return self.service_config.main_port
    
    def get_cors_origins(self) -> List[str]:
        """獲取CORS配置"""
        return self.service_config.cors_origins
    
    def is_port_available(self, port: int) -> bool:
        """檢查端口是否可用"""
        import socket
        
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('localhost', port))
                return True
            except OSError:
                return False
    
    def get_service_url(self, service_name: str, endpoint: str = "") -> str:
        """獲取服務完整URL"""
        service = self.get_service_config(service_name)
        if not service:
            raise ValueError(f"服務 {service_name} 不存在")
        
        base_url = f"http://{service.host}:{self.service_config.main_port}"
        path = service.path_prefix.rstrip('/')
        endpoint = endpoint.lstrip('/')
        
        if endpoint:
            return f"{base_url}{path}/{endpoint}"
        return f"{base_url}{path}"
    
    def validate_configuration(self) -> Dict[str, Any]:
        """驗證配置"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "port_conflicts": []
        }
        
        # 檢查端口衝突
        used_ports = set()
        for name, service in self.service_config.services.items():
            if service.port in used_ports:
                validation_result["port_conflicts"].append(
                    f"服務 {name} 端口 {service.port} 衝突"
                )
                validation_result["valid"] = False
            used_ports.add(service.port)
        
        # 檢查主端口是否被佔用
        if not self.is_port_available(self.service_config.main_port):
            validation_result["warnings"].append(
                f"主端口 {self.service_config.main_port} 可能被佔用"
            )
        
        return validation_result
    
    def save_service_config(self):
        """保存服務配置"""
        config_file = self.base_config.config_dir / "integrated_services.yaml"
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 序列化配置
        data = {
            "main_port": self.service_config.main_port,
            "cors_origins": self.service_config.cors_origins,
            "security_enabled": self.service_config.security_enabled,
            "logging_level": self.service_config.logging_level,
            "services": {}
        }
        
        for name, service in self.service_config.services.items():
            data["services"][name] = {
                "name": service.name,
                "service_type": service.service_type.value,
                "port": service.port,
                "path_prefix": service.path_prefix,
                "enabled": service.enabled,
                "host": service.host,
                "workers": service.workers,
                "timeout": service.timeout,
                "metadata": service.metadata
            }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, allow_unicode=True, default_flow_style=False)
    
    @property
    def base_settings(self) -> Any:
        """獲取基礎配置設定"""
        return self.base_config.config


# 全域配置管理器實例
_config_manager: Optional[UnifiedConfigManager] = None


def get_config_manager() -> UnifiedConfigManager:
    """獲取全域配置管理器實例"""
    global _config_manager
    
    if _config_manager is None:
        env = os.getenv("ENVIRONMENT", "development")
        _config_manager = UnifiedConfigManager(env=env)
    
    return _config_manager


def init_config_manager(env: str = "development", config_dir: Optional[Path] = None) -> UnifiedConfigManager:
    """初始化配置管理器"""
    global _config_manager
    _config_manager = UnifiedConfigManager(env=env, config_dir=config_dir)
    return _config_manager
