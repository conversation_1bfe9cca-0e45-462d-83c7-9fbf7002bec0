"""
並發任務管理器 - 重構版
基於異步架構設計的企業級任務調度系統

核心改進：
- 真正的異步任務調度
- 智能優先級管理
- 強化錯誤恢復機制
- 高效資源管理
- 實時監控和告警
"""

import os
import sys
import json
import uuid
import time
import asyncio
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from enum import Enum, auto
from concurrent.futures import Future, ProcessPoolExecutor
import weakref
import heapq
from contextlib import asynccontextmanager

# 導入核心模組
from .concurrent_task_core import (
    TaskStatus, TaskPriority, TaskInfo, TaskExecutor, 
    TaskRegistry, TaskStatusTracker, DEFAULT_HANDLERS
)

# 新增異步相關導入
try:
    import aiofiles
    import aiosqlite
    ASYNC_AVAILABLE = True
except ImportError:
    ASYNC_AVAILABLE = False

# 動態添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from src.infrastructure.logging.logger_manager import LoggerManager
from src.infrastructure.adapters.notification.line_notification_service import LineNotificationService


# 增強的任務配置和結果模型
@dataclass
class TaskConfiguration:
    """任務配置"""
    timeout_seconds: float = 300.0
    max_retries: int = 3
    retry_delay_seconds: float = 1.0
    retry_backoff_multiplier: float = 2.0
    priority_decay_rate: float = 0.1
    dependencies: List[str] = field(default_factory=list)
    resource_requirements: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TaskResult:
    """任務執行結果"""
    success: bool
    result_data: Any = None
    error_message: Optional[str] = None
    error_type: Optional[str] = None
    execution_time_seconds: float = 0.0
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    output_artifacts: List[str] = field(default_factory=list)

@dataclass
class TaskContext:
    """任務上下文"""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    source_component: Optional[str] = None
    correlation_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class AsyncTaskScheduler:
    """異步任務調度器"""
    
    def __init__(self, max_concurrent: int = 20):
        self.max_concurrent = max_concurrent
        self._priority_queue = []
        self._task_registry = {}
        self._running_tasks = {}
        self._dependency_graph = {}
        self._scheduler_running = False
        self._scheduler_task = None
        
        # 統計信息
        self._stats = {
            'total_scheduled': 0,
            'total_completed': 0, 
            'total_failed': 0,
            'avg_wait_time': 0.0,
            'avg_execution_time': 0.0
        }
    
    async def start(self):
        """啟動調度器"""
        if self._scheduler_running:
            return
        self._scheduler_running = True
        self._scheduler_task = asyncio.create_task(self._scheduling_loop())
    
    async def stop(self):
        """停止調度器"""
        self._scheduler_running = False
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消運行中的任務
        for task in self._running_tasks.values():
            if hasattr(task, 'cancel'):
                task.cancel()
    
    async def schedule_task(self, task_func: Callable, task_id: str, 
                          priority: TaskPriority = TaskPriority.NORMAL,
                          config: Optional[TaskConfiguration] = None) -> str:
        """調度任務"""
        config = config or TaskConfiguration()
        
        task_info = {
            'task_id': task_id,
            'task_func': task_func,
            'priority': priority,
            'config': config,
            'created_at': time.time(),
            'status': TaskStatus.PENDING
        }
        
        self._task_registry[task_id] = task_info
        
        # 加入優先級隊列
        heapq.heappush(self._priority_queue, (
            -priority.value,  # 負值使其成為最大堆
            task_info['created_at'],
            task_id
        ))
        
        self._stats['total_scheduled'] += 1
        return task_id
    
    async def _scheduling_loop(self):
        """調度循環"""
        while self._scheduler_running:
            try:
                await self._process_scheduling_cycle()
                await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                break
            except Exception as e:
                # 記錄錯誤但繼續運行
                await asyncio.sleep(1.0)
    
    async def _process_scheduling_cycle(self):
        """處理調度週期"""
        # 清理已完成任務
        await self._cleanup_completed_tasks()
        
        # 調度新任務
        available_slots = self.max_concurrent - len(self._running_tasks)
        
        while available_slots > 0 and self._priority_queue:
            try:
                _, _, task_id = heapq.heappop(self._priority_queue)
            except IndexError:
                break
            
            if task_id not in self._task_registry:
                continue
            
            task_info = self._task_registry[task_id]
            if task_info['status'] != TaskStatus.PENDING:
                continue
            
            # 執行任務
            await self._execute_task(task_info)
            available_slots -= 1
    
    async def _execute_task(self, task_info: Dict):
        """執行任務"""
        task_id = task_info['task_id']
        task_info['status'] = TaskStatus.RUNNING
        task_info['started_at'] = time.time()
        
        # 創建異步任務
        async_task = asyncio.create_task(
            self._run_task_with_monitoring(task_info)
        )
        self._running_tasks[task_id] = async_task
    
    async def _run_task_with_monitoring(self, task_info: Dict):
        """監控任務執行"""
        task_id = task_info['task_id']
        start_time = time.time()
        
        try:
            # 執行任務函數
            result = await self._invoke_task_function(task_info['task_func'])
            
            # 記錄成功結果
            execution_time = time.time() - start_time
            task_result = TaskResult(
                success=True,
                result_data=result,
                execution_time_seconds=execution_time
            )
            
            await self._handle_task_completion(task_info, task_result)
            
        except Exception as e:
            execution_time = time.time() - start_time
            task_result = TaskResult(
                success=False,
                error_message=str(e),
                error_type=type(e).__name__,
                execution_time_seconds=execution_time
            )
            
            await self._handle_task_failure(task_info, task_result)
    
    async def _invoke_task_function(self, task_func: Callable) -> Any:
        """調用任務函數"""
        if asyncio.iscoroutinefunction(task_func):
            return await task_func()
        else:
            # 同步函數在執行器中運行
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, task_func)
    
    async def _handle_task_completion(self, task_info: Dict, result: TaskResult):
        """處理任務完成"""
        task_info['status'] = TaskStatus.COMPLETED
        task_info['completed_at'] = time.time()
        task_info['result'] = result
        
        self._stats['total_completed'] += 1
        
        wait_time = task_info['started_at'] - task_info['created_at']
        self._update_average_stat('avg_wait_time', wait_time)
        self._update_average_stat('avg_execution_time', result.execution_time_seconds)
    
    async def _handle_task_failure(self, task_info: Dict, result: TaskResult):
        """處理任務失敗"""
        retry_count = task_info.get('retry_count', 0)
        max_retries = task_info['config'].max_retries
        
        if retry_count < max_retries:
            # 重試任務
            task_info['retry_count'] = retry_count + 1
            task_info['status'] = TaskStatus.PENDING
            
            # 延遲後重新加入隊列
            delay = (task_info['config'].retry_delay_seconds * 
                    (task_info['config'].retry_backoff_multiplier ** retry_count))
            
            asyncio.create_task(self._delayed_requeue(task_info, delay))
        else:
            # 最終失敗
            task_info['status'] = TaskStatus.FAILED
            task_info['completed_at'] = time.time()
            task_info['result'] = result
            self._stats['total_failed'] += 1
    
    async def _delayed_requeue(self, task_info: Dict, delay: float):
        """延遲重新加入隊列"""
        await asyncio.sleep(delay)
        heapq.heappush(self._priority_queue, (
            -task_info['priority'].value,
            time.time(),
            task_info['task_id']
        ))
    
    async def _cleanup_completed_tasks(self):
        """清理已完成任務"""
        completed_task_ids = []
        for task_id, async_task in self._running_tasks.items():
            if async_task.done():
                completed_task_ids.append(task_id)
        
        for task_id in completed_task_ids:
            del self._running_tasks[task_id]
    
    def _update_average_stat(self, stat_name: str, new_value: float):
        """更新平均統計值"""
        current_avg = self._stats[stat_name]
        total_count = self._stats['total_completed']
        
        if total_count > 0:
            self._stats[stat_name] = ((current_avg * (total_count - 1)) + new_value) / total_count
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計信息"""
        return {
            **self._stats,
            'queue_size': len(self._priority_queue),
            'running_tasks': len(self._running_tasks),
            'total_tasks': len(self._task_registry)
        }


class EnhancedConcurrentTaskManager:
    """
    增強版企業級並發任務管理器
    
    核心功能：
    - 混合異步+同步執行模式
    - 智能任務調度
    - 優先級管理
    - 錯誤恢復機制
    - 實時監控
    """
    
    def __init__(self, 
                 max_workers: int = 4,
                 enable_notifications: bool = True,
                 enable_async: bool = True,
                 logger=None):
        """初始化任務管理器"""
        self.logger = logger or LoggerManager().get_logger("EnhancedConcurrentTaskManager")
        self.enable_async = enable_async and ASYNC_AVAILABLE
        
        # 核心組件
        self._executor = TaskExecutor(max_workers=max_workers, logger=self.logger)
        self._registry = TaskRegistry()
        self._tracker = TaskStatusTracker()
        
        # 異步調度器 (如果可用)
        self._async_scheduler = None
        if self.enable_async:
            self._async_scheduler = AsyncTaskScheduler(max_concurrent=max_workers * 2)
            # 啟動異步調度器
            asyncio.create_task(self._async_scheduler.start())
        
        # 通知服務
        self._notification_service = None
        if enable_notifications:
            try:
                self._notification_service = LineNotificationService()
            except Exception as e:
                self.logger.warning(f"[WARNING] 通知服務初始化失敗: {e}")
        
        # 任務執行追蹤
        self._futures: Dict[str, Future] = {}
        self._future_lock = threading.RLock()
        
        # 註冊預設處理器
        for task_type, handler in DEFAULT_HANDLERS.items():
            self._registry.register_handler(task_type, handler)
        
        mode = "異步+同步" if self.enable_async else "同步"
        self.logger.info(f"[OK] 增強版並發任務管理器已初始化 (模式: {mode}, 最大執行緒: {max_workers})")
    
    def register_handler(self, task_type: str, handler: Callable):
        """註冊任務處理器"""
        self._registry.register_handler(task_type, handler)
        self.logger.info(f"[OK] 已註冊任務處理器: {task_type}")
    
    def submit_task(self, 
                   task_type: str, 
                   task_params: Dict[str, Any] = None,
                   priority: TaskPriority = TaskPriority.NORMAL,
                   max_retries: int = 3,
                   use_async: bool = None,
                   configuration: Optional[TaskConfiguration] = None,
                   context: Optional[TaskContext] = None) -> str:
        """
        提交任務到執行佇列（非阻塞）
        
        Args:
            task_type: 任務類型
            task_params: 任務參數
            priority: 任務優先級
            max_retries: 最大重試次數
            use_async: 是否使用異步執行（None時自動選擇）
            configuration: 任務配置
            context: 任務上下文
        
        Returns:
            str: 任務ID
        """
        task_id = str(uuid.uuid4())
        task_params = task_params or {}
        
        # 自動選擇執行模式
        if use_async is None:
            use_async = self.enable_async and self._should_use_async(task_type)
        
        # 準備配置
        if not configuration:
            configuration = TaskConfiguration(max_retries=max_retries)
        
        context = context or TaskContext()
        
        # 檢查處理器
        handler = self._registry.get_handler(task_type)
        if not handler:
            raise ValueError(f"未找到任務類型處理器: {task_type}")
        
        # 創建任務資訊
        task_info = TaskInfo(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.PENDING,
            priority=priority,
            created_at=datetime.now(),
            input_params=task_params,
            max_retries=max_retries
        )
        
        # 添加到追蹤器
        self._tracker.add_task(task_info)
        
        # 根據模式提交任務
        if use_async and self._async_scheduler:
            # 異步模式
            task_func = lambda: self._execute_async_task(task_id, task_type, task_params)
            asyncio.create_task(
                self._async_scheduler.schedule_task(
                    task_func, task_id, priority, configuration
                )
            )
            future = None
        else:
            # 同步模式 (原有邏輯)
            future = self._executor.submit_task(self._execute_task_wrapper, task_id, task_type)
        
        with self._future_lock:
            self._futures[task_id] = future
        
        mode = "異步" if (use_async and self._async_scheduler) else "同步"
        self.logger.info(f"[SUBMIT] 任務已提交: {task_id} ({task_type}, {mode})")
        return task_id
    
    def _should_use_async(self, task_type: str) -> bool:
        """判斷是否應該使用異步執行"""
        # 根據任務類型判斷
        async_preferred_types = {
            'email_processing',
            'file_processing', 
            'network_request',
            'database_operation',
            'notification'
        }
        return task_type in async_preferred_types
    
    async def _execute_async_task(self, task_id: str, task_type: str, task_params: Dict[str, Any]):
        """異步任務執行包裝器"""
        try:
            # 更新狀態
            self._tracker.update_task(
                task_id,
                status=TaskStatus.RUNNING, 
                started_at=datetime.now()
            )
            
            # 獲取處理器
            handler = self._registry.get_handler(task_type)
            if not handler:
                raise ValueError(f"未找到任務處理器: {task_type}")
            
            start_time = time.time()
            
            # 執行任務（支持異步和同步處理器）
            if asyncio.iscoroutinefunction(handler):
                result = await handler(**task_params)
            else:
                # 同步處理器在執行器中運行
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, lambda: handler(**task_params))
            
            duration = time.time() - start_time
            
            # 更新完成狀態
            self._tracker.update_task(
                task_id,
                status=TaskStatus.COMPLETED,
                completed_at=datetime.now(),
                result=result,
                actual_duration=duration,
                progress=100.0
            )
            
            # 發送成功通知
            self._send_notification(
                f"✅ 異步任務完成: {task_type}\n任務ID: {task_id}\n執行時間: {duration:.2f}秒"
            )
            
            self.logger.info(f"[ASYNC_COMPLETED] 任務完成: {task_id} (耗時: {duration:.2f}秒)")
            
        except Exception as e:
            # 處理錯誤
            await self._handle_async_task_error(task_id, task_type, str(e))
    
    async def _handle_async_task_error(self, task_id: str, task_type: str, error_message: str):
        """處理異步任務錯誤"""
        task_info = self._tracker.get_task(task_id)
        
        # 更新錯誤狀態
        self._tracker.update_task(
            task_id,
            status=TaskStatus.FAILED,
            completed_at=datetime.now(),
            error_message=error_message,
            retry_count=task_info.retry_count + 1 if task_info else 1
        )
        
        # 發送錯誤通知
        self._send_notification(
            f"❌ 異步任務失敗: {task_type}\n任務ID: {task_id}\n錯誤: {error_message}"
        )
        
        self.logger.error(f"[ASYNC_FAILED] 任務失敗: {task_id} - {error_message}")
    
    def _execute_task_wrapper(self, task_id: str, task_type: str):
        """任務執行包裝器（同步模式）"""
        try:
            # 更新狀態為執行中
            self._tracker.update_task(
                task_id,
                status=TaskStatus.RUNNING,
                started_at=datetime.now()
            )
            
            # 獲取任務資訊和處理器
            task_info = self._tracker.get_task(task_id)
            handler = self._registry.get_handler(task_type)
            
            start_time = time.time()
            
            # 執行任務
            result = handler(**task_info.input_params)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 更新為完成狀態
            self._tracker.update_task(
                task_id,
                status=TaskStatus.COMPLETED,
                completed_at=datetime.now(),
                result=result,
                actual_duration=duration,
                progress=100.0
            )
            
            # 發送成功通知
            self._send_notification(
                f"✅ 同步任務完成: {task_type}\n任務ID: {task_id}\n執行時間: {duration:.2f}秒"
            )
            
            self.logger.info(f"[SYNC_COMPLETED] 任務完成: {task_id} (耗時: {duration:.2f}秒)")
            
        except Exception as e:
            # 處理錯誤
            self._handle_task_error(task_id, str(e))
        
        finally:
            # 清理 future 引用
            with self._future_lock:
                self._futures.pop(task_id, None)
    
    def _handle_task_error(self, task_id: str, error_message: str):
        """處理任務錯誤（同步模式）"""
        task_info = self._tracker.get_task(task_id)
        
        # 更新錯誤狀態
        self._tracker.update_task(
            task_id,
            status=TaskStatus.FAILED,
            completed_at=datetime.now(),
            error_message=error_message,
            retry_count=task_info.retry_count + 1 if task_info else 1
        )
        
        # 發送錯誤通知
        self._send_notification(
            f"❌ 同步任務失敗: {task_info.task_type if task_info else 'Unknown'}\n"
            f"任務ID: {task_id}\n錯誤: {error_message}"
        )
        
        self.logger.error(f"[SYNC_FAILED] 任務失敗: {task_id} - {error_message}")
    
    def _send_notification(self, message: str):
        """發送通知"""
        if self._notification_service:
            try:
                self._notification_service.send_notification(message)
            except Exception as e:
                self.logger.warning(f"[WARNING] 通知發送失敗: {e}")
    
    async def submit_async_task(self, 
                               task_type: str,
                               task_params: Dict[str, Any] = None,
                               priority: TaskPriority = TaskPriority.NORMAL,
                               configuration: Optional[TaskConfiguration] = None,
                               context: Optional[TaskContext] = None) -> str:
        """提交異步任務的便利方法"""
        return self.submit_task(
            task_type=task_type,
            task_params=task_params,
            priority=priority,
            use_async=True,
            configuration=configuration,
            context=context
        )
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取任務狀態"""
        task_info = self._tracker.get_task(task_id)
        if not task_info:
            return None
        
        return {
            'task_id': task_info.task_id,
            'task_type': task_info.task_type,
            'status': task_info.status.value,
            'priority': task_info.priority.value,
            'created_at': task_info.created_at.isoformat(),
            'started_at': task_info.started_at.isoformat() if task_info.started_at else None,
            'completed_at': task_info.completed_at.isoformat() if task_info.completed_at else None,
            'progress': task_info.progress,
            'error_message': task_info.error_message,
            'actual_duration': task_info.actual_duration,
            'retry_count': task_info.retry_count
        }
    
    def list_tasks(self, status: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """列出任務"""
        status_filter = TaskStatus(status) if status else None
        tasks = self._tracker.list_tasks(status=status_filter)
        
        # 限制返回數量
        tasks = tasks[:limit]
        
        return [
            {
                'task_id': task.task_id,
                'task_type': task.task_type,
                'status': task.status.value,
                'created_at': task.created_at.isoformat(),
                'progress': task.progress,
                'error_message': task.error_message
            }
            for task in tasks
        ]
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        with self._future_lock:
            future = self._futures.get(task_id)
            if future and not future.done():
                cancelled = future.cancel()
                if cancelled:
                    self._tracker.update_task(
                        task_id,
                        status=TaskStatus.CANCELLED,
                        completed_at=datetime.now()
                    )
                    self.logger.info(f"[CANCELLED] 任務已取消: {task_id}")
                    return True
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計資訊"""
        all_tasks = self._tracker.list_tasks()
        
        # 基本統計
        basic_stats = self._get_basic_stats(all_tasks)
        
        # 異步調度器統計
        async_stats = {}
        if self._async_scheduler:
            async_stats = self._async_scheduler.get_statistics()
        
        return {
            **basic_stats,
            'async_scheduler': async_stats,
            'mode': '異步+同步' if self.enable_async else '同步'
        }
    
    def _get_basic_stats(self, all_tasks: List) -> Dict[str, Any]:
        """獲取基本統計信息"""
        stats = {
            'total_tasks': len(all_tasks),
            'completed_tasks': len([t for t in all_tasks if t.status == TaskStatus.COMPLETED]),
            'failed_tasks': len([t for t in all_tasks if t.status == TaskStatus.FAILED]),
            'running_tasks': len([t for t in all_tasks if t.status == TaskStatus.RUNNING]),
            'pending_tasks': len([t for t in all_tasks if t.status == TaskStatus.PENDING]),
            'cancelled_tasks': len([t for t in all_tasks if t.status == TaskStatus.CANCELLED])
        }
        
        # 計算成功率
        if stats['total_tasks'] > 0:
            stats['success_rate'] = (stats['completed_tasks'] / stats['total_tasks']) * 100
        else:
            stats['success_rate'] = 0
        
        return stats
    
    async def shutdown_async(self, wait: bool = True):
        """異步關閉任務管理器"""
        self.logger.info("[SHUTDOWN] 正在異步關閉並發任務管理器...")
        
        # 停止異步調度器
        if self._async_scheduler:
            await self._async_scheduler.stop()
        
        # 調用同步關閉
        self.shutdown(wait=wait)
        
        self.logger.info("[OK] 並發任務管理器異步關閉完成")
    
    def shutdown(self, wait: bool = True):
        """關閉任務管理器"""
        self.logger.info("[SHUTDOWN] 正在關閉並發任務管理器...")
        
        if wait:
            # 等待所有任務完成
            with self._future_lock:
                futures = list(self._futures.values())
            
            for future in futures:
                if future:  # 異步任務的 future 可能為 None
                    try:
                        future.result(timeout=30)  # 最多等待30秒
                    except Exception:
                        pass
        
        # 關閉執行器
        self._executor.shutdown(wait=wait)
        
        self.logger.info("[OK] 並發任務管理器已關閉")
    
    def get_task_details(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取任務詳細信息（包含異步調度器信息）"""
        # 從追蹤器獲取基本信息
        basic_info = self.get_task_status(task_id)
        
        # 從異步調度器獲取額外信息
        async_info = {}
        if self._async_scheduler and task_id in self._async_scheduler._task_registry:
            task_data = self._async_scheduler._task_registry[task_id]
            async_info = {
                'scheduler_status': task_data.get('status'),
                'retry_count': task_data.get('retry_count', 0),
                'created_at_scheduler': task_data.get('created_at'),
                'started_at_scheduler': task_data.get('started_at'),
                'execution_mode': '異步'
            }
        
        if basic_info:
            basic_info.update(async_info)
            return basic_info
        
        return None


# 全域實例（單例模式）
_enhanced_task_manager_instance = None
_enhanced_task_manager_lock = threading.Lock()

def get_enhanced_task_manager(**kwargs) -> EnhancedConcurrentTaskManager:
    """獲取增強版任務管理器單例實例"""
    global _enhanced_task_manager_instance
    
    if _enhanced_task_manager_instance is None:
        with _enhanced_task_manager_lock:
            if _enhanced_task_manager_instance is None:
                _enhanced_task_manager_instance = EnhancedConcurrentTaskManager(**kwargs)
    
    return _enhanced_task_manager_instance


# 便利函數
def submit_code_comparison_task_enhanced(input_path: str, **kwargs) -> str:
    """提交程式碼對比任務的便利函數（增強版）"""
    return get_enhanced_task_manager().submit_task(
        'code_comparison',
        {'input_path': input_path, **kwargs}
    )

def submit_email_processing_task_enhanced(email_data: Dict[str, Any], **kwargs) -> str:
    """提交郵件處理任務的便利函數（增強版）"""
    return get_enhanced_task_manager().submit_task(
        'email_processing',
        {'email_data': email_data, **kwargs}
    )

# 新增異步便利函數
async def submit_async_email_processing_task(email_data: Dict[str, Any], **kwargs) -> str:
    """提交異步郵件處理任務的便利函數"""
    return await get_enhanced_task_manager().submit_async_task(
        'email_processing',
        {'email_data': email_data, **kwargs}
    )

async def submit_async_file_processing_task(file_path: str, **kwargs) -> str:
    """提交異步檔案處理任務的便利函數"""
    return await get_enhanced_task_manager().submit_async_task(
        'file_processing',
        {'file_path': file_path, **kwargs}
    )


# 任務監控類
class TaskMonitor:
    """任務監控器"""
    
    def __init__(self, task_manager: EnhancedConcurrentTaskManager):
        self.task_manager = task_manager
        self.monitoring_active = False
        self.alert_thresholds = {
            'queue_size_warning': 50,
            'failure_rate_warning': 0.1,
            'avg_wait_time_warning': 300.0
        }
    
    async def start_monitoring(self, interval: float = 30.0):
        """開始監控"""
        self.monitoring_active = True
        
        while self.monitoring_active:
            try:
                await self._check_system_health()
                await asyncio.sleep(interval)
            except Exception as e:
                await asyncio.sleep(interval)
    
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring_active = False
    
    async def _check_system_health(self):
        """檢查系統健康狀態"""
        stats = self.task_manager.get_statistics()
        
        # 檢查隊列大小
        if 'async_scheduler' in stats:
            queue_size = stats['async_scheduler'].get('queue_size', 0)
            if queue_size >= self.alert_thresholds['queue_size_warning']:
                await self._trigger_alert('WARNING', 'Queue Size', 
                                         f'任務隊列較高: {queue_size} 個任務等待處理')
        
        # 檢查失敗率
        total_tasks = stats.get('total_tasks', 0)
        failed_tasks = stats.get('failed_tasks', 0)
        if total_tasks > 0:
            failure_rate = failed_tasks / total_tasks
            if failure_rate >= self.alert_thresholds['failure_rate_warning']:
                await self._trigger_alert('WARNING', 'Failure Rate',
                                         f'任務失敗率較高: {failure_rate:.1%}')
    
    async def _trigger_alert(self, level: str, category: str, message: str):
        """觸發告警"""
        alert_message = f"[{level}] {category}: {message}"
        # 這裡可以整合實際的告警系統
        print(f"ALERT: {alert_message}")


# 性能基準測試
class TaskManagerBenchmark:
    """任務管理器性能基準測試"""
    
    @staticmethod
    async def benchmark_task_throughput(task_count: int = 100):
        """基準測試：任務吞吐量"""
        task_manager = get_enhanced_task_manager()
        
        # 註冊測試任務
        async def test_task():
            await asyncio.sleep(0.01)  # 模擬短時間任務
            return "test_result"
        
        task_manager.register_handler('benchmark_test', test_task)
        
        start_time = time.time()
        task_ids = []
        
        # 提交任務
        for i in range(task_count):
            task_id = await task_manager.submit_async_task('benchmark_test')
            task_ids.append(task_id)
        
        # 等待完成
        completed = 0
        while completed < task_count:
            await asyncio.sleep(0.1)
            stats = task_manager.get_statistics()
            completed = stats.get('completed_tasks', 0)
        
        total_time = time.time() - start_time
        
        return {
            'task_count': task_count,
            'total_time': total_time,
            'throughput': task_count / total_time,
            'avg_time_per_task': total_time / task_count
        }