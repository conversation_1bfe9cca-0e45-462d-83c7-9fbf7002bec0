# 設計文件

## 概述

智慧型網路檔案瀏覽系統是一個基於現有網路瀏覽器的增強型檔案管理解決方案。系統採用六角架構模式，整合 LLM 智慧搜尋、自動化檔案處理，以及與現有工具的無縫整合。

## 架構

### 系統架構圖

```mermaid
graph TB
    subgraph "展示層 (Presentation Layer)"
        UI[網路瀏覽器 UI<br/>http://localhost:5555/network/ui]
        API[REST API 端點]
    end
    
    subgraph "應用層 (Application Layer)"
        FS[檔案搜尋服務]
        PS[處理服務]
        US[上傳服務]
        AS[驗證服務]
        LS[LLM 搜尋服務]
    end
    
    subgraph "領域層 (Domain Layer)"
        FE[檔案實體]
        SE[搜尋實體]
        PE[處理實體]
    end
    
    subgraph "基礎設施層 (Infrastructure Layer)"
        NFS[網路檔案系統適配器]
        LLM[LLM 適配器]
        CSV[CSV 處理適配器]
        CC[程式碼比較適配器]
        AUTH[網路驗證適配器]
    end
    
    subgraph "外部系統"
        NET[\\************\test_log]
        TEMP[d:\temp\]
        UPLOAD[\\************\temp_7days\]
        ENV[.env 憑證]
        CSVTOOL[csv_to_summary.py]
        CCTOOL[code_comparison.py]
    end
    
    UI --> API
    API --> FS
    API --> PS
    API --> US
    API --> AS
    API --> LS
    
    FS --> FE
    PS --> PE
    LS --> SE
    
    FS --> NFS
    PS --> CSV
    PS --> CC
    US --> NFS
    AS --> AUTH
    LS --> LLM
    
    NFS --> NET
    NFS --> UPLOAD
    CSV --> TEMP
    CSV --> CSVTOOL
    CC --> TEMP
    CC --> CCTOOL
    AUTH --> ENV
```

### 技術堆疊

- **後端框架**: FastAPI (擴展現有的 8010 埠服務)
- **前端**: HTML/JavaScript (擴展現有 UI)
- **檔案系統**: Python pathlib + os.walk()
- **LLM 整合**: Ollama 本地 LLM
- **網路存取**: SMB/CIFS 協定
- **並行處理**: asyncio + concurrent.futures
- **驗證**: Windows 網路驗證

## 元件和介面

### 1. 檔案搜尋服務 (FileSearchService)

```python
class FileSearchService:
    async def search_product_folder(
        self, 
        product_name: str, 
        time_range: TimeRange
    ) -> SearchResult
    
    async def scan_directory_fast(
        self, 
        path: Path, 
        filters: SearchFilters
    ) -> List[FileInfo]
    
    async def get_file_metadata(
        self, 
        file_path: Path
    ) -> FileMetadata
```

**職責:**
- 快速掃描網路目錄結構
- 基於產品名稱定位資料夾
- 時間範圍篩選
- 並行檔案系統操作

### 2. LLM 搜尋服務 (LLMSearchService)

```python
class LLMSearchService:
    async def interpret_query(
        self, 
        natural_query: str
    ) -> SearchParameters
    
    async def analyze_search_results(
        self, 
        results: List[FileInfo], 
        query_context: str
    ) -> AnalyzedResults
    
    async def suggest_next_actions(
        self, 
        results: AnalyzedResults
    ) -> List[ActionSuggestion]
```

**職責:**
- 自然語言查詢解析
- 搜尋結果智慧分析
- 行動建議生成

### 3. 處理服務 (ProcessingService)

```python
class ProcessingService:
    async def stage_files(
        self, 
        source_files: List[Path], 
        product_name: str
    ) -> StagingResult
    
    async def execute_csv_summary(
        self, 
        staging_path: Path
    ) -> ProcessingResult
    
    async def execute_code_comparison(
        self, 
        staging_path: Path
    ) -> ProcessingResult
    
    async def upload_results(
        self, 
        results: ProcessingResult, 
        product_name: str
    ) -> UploadResult
```

**職責:**
- 檔案暫存管理
- 工具執行協調
- 結果上傳處理

### 4. 網路檔案系統適配器 (NetworkFileSystemAdapter)

```python
class NetworkFileSystemAdapter:
    async def authenticate(
        self, 
        credentials: NetworkCredentials
    ) -> AuthSession
    
    async def list_directory(
        self, 
        path: Path, 
        recursive: bool = False
    ) -> List[FileInfo]
    
    async def copy_files(
        self, 
        source_files: List[Path], 
        destination: Path
    ) -> CopyResult
    
    async def upload_files(
        self, 
        local_files: List[Path], 
        remote_destination: Path
    ) -> UploadResult
```

**職責:**
- 網路檔案系統存取
- 檔案複製和上傳
- 網路驗證管理

## 資料模型

### 核心實體

```python
@dataclass
class FileInfo:
    path: Path
    name: str
    size: int
    modified_time: datetime
    file_type: str
    is_directory: bool

@dataclass
class SearchResult:
    product_folder: Path
    matched_files: List[FileInfo]
    total_files: int
    search_duration: float
    filters_applied: SearchFilters

@dataclass
class ProcessingResult:
    success: bool
    output_files: List[Path]
    processing_time: float
    tool_used: str
    error_message: Optional[str]

@dataclass
class TimeRange:
    start_date: datetime
    end_date: datetime
    
    @classmethod
    def last_months(cls, months: int) -> 'TimeRange':
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        return cls(start_date, end_date)
```

### 配置模型

```python
@dataclass
class NetworkConfig:
    base_path: str = "\\\\************\\test_log"
    upload_path: str = "\\\\************\\temp_7days"
    staging_path: str = "d:\\temp"
    username: str
    password: str
    
    @classmethod
    def from_env(cls) -> 'NetworkConfig':
        return cls(
            username=os.getenv('EMAIL_ADDRESS'),
            password=os.getenv('EMAIL_PASSWORD')
        )
```

## 錯誤處理

### 錯誤類型層次

```python
class NetworkFileSystemError(Exception):
    """網路檔案系統相關錯誤基類"""
    pass

class AuthenticationError(NetworkFileSystemError):
    """網路驗證失敗"""
    pass

class FileAccessError(NetworkFileSystemError):
    """檔案存取權限錯誤"""
    pass

class SearchTimeoutError(NetworkFileSystemError):
    """搜尋操作逾時"""
    pass

class ProcessingError(Exception):
    """檔案處理錯誤基類"""
    pass

class StagingError(ProcessingError):
    """檔案暫存錯誤"""
    pass

class UploadError(ProcessingError):
    """檔案上傳錯誤"""
    pass
```

### 錯誤處理策略

1. **網路連線錯誤**: 自動重試機制（最多 3 次）
2. **驗證失敗**: 記錄錯誤並提示使用者檢查憑證
3. **檔案存取錯誤**: 提供詳細的權限錯誤訊息
4. **處理逾時**: 允許使用者取消長時間執行的操作
5. **部分失敗**: 提供詳細的成功/失敗報告

## 測試策略

### 測試層級

1. **單元測試**
   - 各服務類別的獨立功能測試
   - 資料模型驗證測試
   - 錯誤處理邏輯測試

2. **整合測試**
   - 網路檔案系統存取測試
   - LLM 服務整合測試
   - 工具執行整合測試

3. **端對端測試**
   - 完整搜尋到處理工作流程測試
   - UI 互動測試
   - 效能測試

### 測試資料

```python
# 測試用的模擬網路結構
TEST_NETWORK_STRUCTURE = {
    "test_log": {
        "AAA": {
            "2024-01": ["file1.csv", "file2.xlsx"],
            "2024-02": ["file3.csv", "file4.xlsx"],
        },
        "BBB": {
            "2024-01": ["file5.csv"],
        }
    }
}
```

### 效能測試目標

- **搜尋回應時間**: < 5 秒（1000 個檔案）
- **檔案複製速度**: > 10 MB/s
- **並行搜尋**: 支援最多 10 個同時搜尋請求
- **記憶體使用**: < 500 MB（正常操作）

## 安全性考量

### 憑證管理

1. **環境變數儲存**: 敏感憑證儲存在 .env 檔案
2. **記憶體保護**: 憑證不在日誌中記錄
3. **會話管理**: 自動處理網路會話過期
4. **存取控制**: 僅允許授權的網路路徑存取

### 檔案安全

1. **路徑驗證**: 防止路徑遍歷攻擊
2. **檔案類型檢查**: 限制允許的檔案類型
3. **大小限制**: 防止過大檔案的處理
4. **暫存清理**: 自動清理暫存檔案

## 效能最佳化

### 搜尋最佳化

1. **並行掃描**: 使用 asyncio 並行處理多個目錄
2. **快取機制**: 快取最近搜尋的目錄結構
3. **索引建立**: 為常用產品建立檔案索引
4. **分頁載入**: 大量結果分頁顯示

### 網路最佳化

1. **連線池**: 重用網路連線
2. **批次操作**: 批次處理檔案操作
3. **壓縮傳輸**: 大檔案傳輸時使用壓縮
4. **斷點續傳**: 支援大檔案的斷點續傳

## 部署考量

### 系統需求

- **作業系統**: Windows 10/11 或 Windows Server
- **Python 版本**: >= 3.9
- **記憶體**: 最少 4GB RAM
- **網路**: 穩定的區域網路連線
- **磁碟空間**: 最少 10GB 可用空間（暫存用）

### 配置檔案

```env
# .env 檔案範例
EMAIL_ADDRESS=telowyield1
EMAIL_PASSWORD=GMTgmt88TE
NETWORK_BASE_PATH=\\************\test_log
NETWORK_UPLOAD_PATH=\\************\temp_7days
LOCAL_STAGING_PATH=d:\temp
LLM_MODEL=llama2
SEARCH_TIMEOUT=300
MAX_CONCURRENT_SEARCHES=10
```

### 監控和日誌

1. **操作日誌**: 記錄所有檔案操作
2. **效能監控**: 追蹤搜尋和處理時間
3. **錯誤追蹤**: 詳細的錯誤堆疊追蹤
4. **使用統計**: 追蹤功能使用頻率