#!/usr/bin/env python3
"""
智能檔案拆分器
根據代碼結構自動拆分超過限制的檔案
"""

import ast
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import logging

class FileSplitter:
    """智能檔案拆分器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('FileSplitter')
        
        # 拆分配置
        self.max_lines = config.get('max_lines', 500)
        self.min_split_size = config.get('min_split_size', 50)
        self.preserve_imports = config.get('preserve_imports', True)
        
    async def split_file(self, file_path: Path, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """拆分單一檔案"""
        self.logger.info(f"🔨 開始拆分檔案: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.split('\n')
            total_lines = len(lines)
            
            if total_lines <= self.max_lines:
                return {
                    'status': 'no_action_needed',
                    'message': f'檔案行數 ({total_lines}) 未超過限制 ({self.max_lines})',
                    'original_lines': total_lines
                }
            
            # 分析檔案結構
            tree = ast.parse(content)
            structure_analysis = await self._analyze_file_structure(tree, lines, file_path)
            
            # 生成拆分策略
            split_strategy = await self._generate_split_strategy(
                structure_analysis, lines, analysis_data
            )
            
            if not split_strategy['can_split']:
                return {
                    'status': 'cannot_split',
                    'message': split_strategy['reason'],
                    'original_lines': total_lines,
                    'suggestions': split_strategy.get('suggestions', [])
                }
            
            # 執行拆分
            split_result = await self._execute_split(
                file_path, content, lines, structure_analysis, split_strategy
            )
            
            self.logger.info(f"✅ 拆分完成: {file_path} -> {len(split_result['new_files'])} 個檔案")
            return split_result
            
        except Exception as e:
            self.logger.error(f"拆分失敗 {file_path}: {e}")
            return {
                'status': 'error',
                'message': f'拆分失敗: {str(e)}',
                'error': str(e)
            }
    
    async def _analyze_file_structure(self, tree: ast.AST, lines: List[str], file_path: Path) -> Dict[str, Any]:
        """分析檔案結構"""
        structure = {
            'imports': [],
            'classes': [],
            'functions': [],
            'constants': [],
            'module_docstring': None,
            'dependencies': defaultdict(set),
            'line_ranges': {}
        }
        
        # 提取模組文檔字符串
        if (tree.body and isinstance(tree.body[0], ast.Expr) and 
            isinstance(tree.body[0].value, ast.Constant) and 
            isinstance(tree.body[0].value.value, str)):
            structure['module_docstring'] = tree.body[0].value.value
        
        # 分析頂層節點
        for node in tree.body:
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                structure['imports'].append(self._extract_import_info(node))
            
            elif isinstance(node, ast.ClassDef):
                class_info = await self._extract_class_info(node, lines)
                structure['classes'].append(class_info)
                structure['line_ranges'][f"class_{node.name}"] = (node.lineno, class_info['end_line'])
            
            elif isinstance(node, ast.FunctionDef):
                func_info = await self._extract_function_info(node, lines)
                structure['functions'].append(func_info)
                structure['line_ranges'][f"function_{node.name}"] = (node.lineno, func_info['end_line'])
            
            elif isinstance(node, ast.Assign):
                const_info = self._extract_constant_info(node, lines)
                if const_info:
                    structure['constants'].append(const_info)
        
        # 分析依賴關係
        structure['dependencies'] = await self._analyze_dependencies(tree)
        
        return structure
    
    async def _extract_class_info(self, class_node: ast.ClassDef, lines: List[str]) -> Dict[str, Any]:
        """提取類別資訊"""
        end_line = self._get_end_line(class_node, lines)
        
        methods = []
        for node in class_node.body:
            if isinstance(node, ast.FunctionDef):
                method_info = await self._extract_function_info(node, lines)
                methods.append(method_info)
        
        return {
            'name': class_node.name,
            'start_line': class_node.lineno,
            'end_line': end_line,
            'line_count': end_line - class_node.lineno + 1,
            'methods': methods,
            'bases': [base.id if isinstance(base, ast.Name) else str(base) for base in class_node.bases],
            'docstring': ast.get_docstring(class_node),
            'is_large': end_line - class_node.lineno + 1 > 200,
            'can_split': len(methods) > 3 and end_line - class_node.lineno + 1 > 100
        }
    
    async def _extract_function_info(self, func_node: ast.FunctionDef, lines: List[str]) -> Dict[str, Any]:
        """提取函數資訊"""
        end_line = self._get_end_line(func_node, lines)
        
        # 分析函數使用的變數和調用
        used_names = set()
        for node in ast.walk(func_node):
            if isinstance(node, ast.Name):
                used_names.add(node.id)
            elif isinstance(node, ast.Attribute):
                if isinstance(node.value, ast.Name):
                    used_names.add(node.value.id)
        
        return {
            'name': func_node.name,
            'start_line': func_node.lineno,
            'end_line': end_line,
            'line_count': end_line - func_node.lineno + 1,
            'args': [arg.arg for arg in func_node.args.args],
            'is_async': isinstance(func_node, ast.AsyncFunctionDef),
            'docstring': ast.get_docstring(func_node),
            'used_names': used_names,
            'is_method': False,  # 會在類別分析時更新
            'is_private': func_node.name.startswith('_'),
            'complexity': self._calculate_complexity(func_node)
        }
    
    def _extract_import_info(self, import_node: ast.AST) -> Dict[str, Any]:
        """提取導入資訊"""
        if isinstance(import_node, ast.Import):
            return {
                'type': 'import',
                'line': import_node.lineno,
                'modules': [alias.name for alias in import_node.names],
                'aliases': {alias.name: alias.asname for alias in import_node.names if alias.asname}
            }
        elif isinstance(import_node, ast.ImportFrom):
            return {
                'type': 'from_import',
                'line': import_node.lineno,
                'module': import_node.module,
                'names': [alias.name for alias in import_node.names],
                'aliases': {alias.name: alias.asname for alias in import_node.names if alias.asname},
                'level': import_node.level
            }
    
    def _extract_constant_info(self, assign_node: ast.Assign, lines: List[str]) -> Optional[Dict[str, Any]]:
        """提取常數資訊"""
        if len(assign_node.targets) == 1 and isinstance(assign_node.targets[0], ast.Name):
            name = assign_node.targets[0].id
            if name.isupper():  # 常數慣例
                return {
                    'name': name,
                    'line': assign_node.lineno,
                    'value_type': type(assign_node.value).__name__
                }
        return None
    
    async def _analyze_dependencies(self, tree: ast.AST) -> Dict[str, set]:
        """分析代碼依賴關係"""
        dependencies = defaultdict(set)
        
        # 建立名稱定義映射
        defined_names = {}
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                defined_names[node.name] = 'class'
            elif isinstance(node, ast.FunctionDef):
                defined_names[node.name] = 'function'
        
        # 分析使用關係
        for node in ast.walk(tree):
            if isinstance(node, (ast.ClassDef, ast.FunctionDef)):
                node_name = node.name
                
                # 檢查此節點使用了哪些其他定義
                for child in ast.walk(node):
                    if isinstance(child, ast.Name) and child.id in defined_names:
                        if child.id != node_name:  # 不包括自己
                            dependencies[node_name].add(child.id)
        
        return dict(dependencies)
    
    async def _generate_split_strategy(self, structure: Dict[str, Any], 
                                     lines: List[str], 
                                     analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成拆分策略"""
        total_lines = len(lines)
        
        # 檢查是否可以拆分
        if total_lines <= self.max_lines:
            return {
                'can_split': False,
                'reason': f'檔案行數 ({total_lines}) 未超過限制 ({self.max_lines})'
            }
        
        # 分析拆分選項
        split_options = []
        
        # 策略1: 按類別拆分
        if structure['classes']:
            class_split = await self._analyze_class_split_option(structure, lines)
            if class_split['viable']:
                split_options.append(class_split)
        
        # 策略2: 按功能模組拆分
        if structure['functions']:
            function_split = await self._analyze_function_split_option(structure, lines)
            if function_split['viable']:
                split_options.append(function_split)
        
        # 策略3: 混合拆分
        mixed_split = await self._analyze_mixed_split_option(structure, lines)
        if mixed_split['viable']:
            split_options.append(mixed_split)
        
        if not split_options:
            return {
                'can_split': False,
                'reason': '無法找到合適的拆分策略',
                'suggestions': [
                    '考慮手動重構大型函數或類別',
                    '檢查是否有重複代碼可以提取',
                    '評估是否可以分離不同職責的代碼'
                ]
            }
        
        # 選擇最佳策略
        best_strategy = max(split_options, key=lambda x: x['score'])
        
        return {
            'can_split': True,
            'strategy': best_strategy,
            'all_options': split_options
        }
    
    async def _analyze_class_split_option(self, structure: Dict[str, Any], lines: List[str]) -> Dict[str, Any]:
        """分析按類別拆分的選項"""
        classes = structure['classes']
        total_lines = len(lines)
        
        # 檢查是否有大型類別可以獨立拆分
        large_classes = [cls for cls in classes if cls['line_count'] > 100]
        
        if large_classes:
            # 計算拆分後的檔案大小
            estimated_sizes = []
            
            # 主檔案保留導入和小型元素
            main_file_lines = len(structure['imports']) * 2  # 估算導入行數
            main_file_lines += sum(func['line_count'] for func in structure['functions'])
            main_file_lines += sum(cls['line_count'] for cls in classes if cls not in large_classes)
            
            estimated_sizes.append(main_file_lines)
            
            # 每個大型類別一個檔案
            for cls in large_classes:
                estimated_sizes.append(cls['line_count'] + 10)  # 加上導入
            
            # 檢查是否所有檔案都在合理範圍內
            max_size = max(estimated_sizes)
            min_size = min(estimated_sizes)
            
            viable = (max_size <= self.max_lines * 0.8 and 
                     min_size >= self.min_split_size)
            
            return {
                'type': 'class_split',
                'viable': viable,
                'score': 0.8 if viable else 0.2,
                'estimated_files': len(large_classes) + 1,
                'estimated_sizes': estimated_sizes,
                'split_targets': [cls['name'] for cls in large_classes],
                'description': f'將 {len(large_classes)} 個大型類別拆分到獨立檔案'
            }
        
        return {'type': 'class_split', 'viable': False, 'score': 0}
    
    async def _analyze_function_split_option(self, structure: Dict[str, Any], lines: List[str]) -> Dict[str, Any]:
        """分析按功能拆分的選項"""
        functions = structure['functions']
        
        if not functions or len(functions) < 3:
            return {'type': 'function_split', 'viable': False, 'score': 0}
        
        # 按功能前綴分組
        function_groups = defaultdict(list)
        for func in functions:
            prefix = func['name'].split('_')[0] if '_' in func['name'] else 'misc'
            function_groups[prefix].append(func)
        
        # 檢查是否有足夠的分組
        significant_groups = {
            prefix: funcs for prefix, funcs in function_groups.items() 
            if len(funcs) >= 2 and sum(f['line_count'] for f in funcs) >= self.min_split_size
        }
        
        if len(significant_groups) >= 2:
            estimated_sizes = []
            
            # 主檔案保留類別和部分函數
            main_lines = len(structure['imports']) * 2
            main_lines += sum(cls['line_count'] for cls in structure['classes'])
            main_lines += sum(f['line_count'] for f in function_groups.get('misc', []))
            estimated_sizes.append(main_lines)
            
            # 每個功能組一個檔案
            for prefix, funcs in significant_groups.items():
                if prefix != 'misc':
                    group_lines = sum(f['line_count'] for f in funcs) + 10
                    estimated_sizes.append(group_lines)
            
            max_size = max(estimated_sizes)
            viable = max_size <= self.max_lines * 0.8
            
            return {
                'type': 'function_split',
                'viable': viable,
                'score': 0.6 if viable else 0.1,
                'estimated_files': len(significant_groups),
                'estimated_sizes': estimated_sizes,
                'split_targets': list(significant_groups.keys()),
                'description': f'按功能分組拆分為 {len(significant_groups)} 個模組'
            }
        
        return {'type': 'function_split', 'viable': False, 'score': 0}
    
    async def _analyze_mixed_split_option(self, structure: Dict[str, Any], lines: List[str]) -> Dict[str, Any]:
        """分析混合拆分選項"""
        # 結合類別和函數拆分
        classes = structure['classes']
        functions = structure['functions']
        
        if not classes and not functions:
            return {'type': 'mixed_split', 'viable': False, 'score': 0}
        
        # 找出可以獨立的元素
        independent_classes = [cls for cls in classes if cls['line_count'] > 80]
        independent_functions = [func for func in functions if func['line_count'] > 50]
        
        if len(independent_classes) + len(independent_functions) >= 2:
            estimated_files = 1  # 主檔案
            estimated_sizes = []
            
            # 主檔案
            main_lines = len(structure['imports']) * 2
            main_lines += sum(cls['line_count'] for cls in classes if cls not in independent_classes)
            main_lines += sum(func['line_count'] for func in functions if func not in independent_functions)
            estimated_sizes.append(main_lines)
            
            # 獨立類別檔案
            for cls in independent_classes:
                estimated_sizes.append(cls['line_count'] + 10)
                estimated_files += 1
            
            # 獨立函數檔案 (可能合併小函數)
            if independent_functions:
                func_file_size = sum(func['line_count'] for func in independent_functions) + 10
                estimated_sizes.append(func_file_size)
                estimated_files += 1
            
            max_size = max(estimated_sizes)
            viable = max_size <= self.max_lines * 0.8
            
            return {
                'type': 'mixed_split',
                'viable': viable,
                'score': 0.7 if viable else 0.3,
                'estimated_files': estimated_files,
                'estimated_sizes': estimated_sizes,
                'split_targets': {
                    'classes': [cls['name'] for cls in independent_classes],
                    'functions': [func['name'] for func in independent_functions]
                },
                'description': f'混合拆分為 {estimated_files} 個檔案'
            }
        
        return {'type': 'mixed_split', 'viable': False, 'score': 0}
    
    async def _execute_split(self, file_path: Path, content: str, lines: List[str], 
                           structure: Dict[str, Any], split_strategy: Dict[str, Any]) -> Dict[str, Any]:
        """執行檔案拆分"""
        strategy = split_strategy['strategy']
        new_files = []
        
        # 準備共用導入
        common_imports = self._generate_common_imports(structure['imports'])
        
        if strategy['type'] == 'class_split':
            new_files = await self._execute_class_split(
                file_path, content, lines, structure, strategy, common_imports
            )
        elif strategy['type'] == 'function_split':
            new_files = await self._execute_function_split(
                file_path, content, lines, structure, strategy, common_imports
            )
        elif strategy['type'] == 'mixed_split':
            new_files = await self._execute_mixed_split(
                file_path, content, lines, structure, strategy, common_imports
            )
        
        # 更新主檔案
        updated_main_content = await self._generate_updated_main_file(
            file_path, content, structure, strategy, new_files
        )
        
        return {
            'status': 'success',
            'original_file': str(file_path.relative_to(self.repo_root)),
            'original_lines': len(lines),
            'new_files': new_files,
            'updated_main_content': updated_main_content,
            'strategy_used': strategy['type'],
            'estimated_total_lines': sum(nf['line_count'] for nf in new_files) + len(updated_main_content.split('\n'))
        }
    
    async def _execute_class_split(self, file_path: Path, content: str, lines: List[str], 
                                 structure: Dict[str, Any], strategy: Dict[str, Any], 
                                 common_imports: str) -> List[Dict[str, Any]]:
        """執行類別拆分"""
        new_files = []
        
        for class_name in strategy['split_targets']:
            class_info = next(cls for cls in structure['classes'] if cls['name'] == class_name)
            
            # 生成新檔案內容
            file_content = self._generate_class_file_content(
                class_info, structure, lines, common_imports
            )
            
            # 新檔案路徑
            new_file_path = file_path.parent / f"{class_name.lower()}.py"
            
            new_files.append({
                'path': str(new_file_path.relative_to(self.repo_root)),
                'content': file_content,
                'line_count': len(file_content.split('\n')),
                'type': 'class_module',
                'contains': [class_name]
            })
        
        return new_files
    
    async def _execute_function_split(self, file_path: Path, content: str, lines: List[str], 
                                    structure: Dict[str, Any], strategy: Dict[str, Any], 
                                    common_imports: str) -> List[Dict[str, Any]]:
        """執行函數拆分"""
        new_files = []
        
        for group_name in strategy['split_targets']:
            if group_name == 'misc':
                continue
                
            # 找出該組的函數
            group_functions = [
                func for func in structure['functions'] 
                if func['name'].startswith(group_name)
            ]
            
            if group_functions:
                file_content = self._generate_function_group_file_content(
                    group_functions, structure, lines, common_imports
                )
                
                new_file_path = file_path.parent / f"{group_name}_functions.py"
                
                new_files.append({
                    'path': str(new_file_path.relative_to(self.repo_root)),
                    'content': file_content,
                    'line_count': len(file_content.split('\n')),
                    'type': 'function_module',
                    'contains': [func['name'] for func in group_functions]
                })
        
        return new_files
    
    async def _execute_mixed_split(self, file_path: Path, content: str, lines: List[str], 
                                 structure: Dict[str, Any], strategy: Dict[str, Any], 
                                 common_imports: str) -> List[Dict[str, Any]]:
        """執行混合拆分"""
        new_files = []
        
        # 拆分類別
        for class_name in strategy['split_targets']['classes']:
            class_info = next(cls for cls in structure['classes'] if cls['name'] == class_name)
            
            file_content = self._generate_class_file_content(
                class_info, structure, lines, common_imports
            )
            
            new_file_path = file_path.parent / f"{class_name.lower()}.py"
            
            new_files.append({
                'path': str(new_file_path.relative_to(self.repo_root)),
                'content': file_content,
                'line_count': len(file_content.split('\n')),
                'type': 'class_module',
                'contains': [class_name]
            })
        
        # 拆分函數（合併到一個檔案）
        if strategy['split_targets']['functions']:
            independent_functions = [
                func for func in structure['functions'] 
                if func['name'] in strategy['split_targets']['functions']
            ]
            
            file_content = self._generate_function_group_file_content(
                independent_functions, structure, lines, common_imports
            )
            
            new_file_path = file_path.parent / f"{file_path.stem}_utils.py"
            
            new_files.append({
                'path': str(new_file_path.relative_to(self.repo_root)),
                'content': file_content,
                'line_count': len(file_content.split('\n')),
                'type': 'utility_module',
                'contains': [func['name'] for func in independent_functions]
            })
        
        return new_files
    
    def _generate_class_file_content(self, class_info: Dict[str, Any], 
                                   structure: Dict[str, Any], lines: List[str], 
                                   common_imports: str) -> str:
        """生成類別檔案內容"""
        content_parts = []
        
        # 檔案頭部註釋
        content_parts.append(f'"""\n{class_info["name"]} 類別模組\n從主檔案拆分而來\n"""')
        content_parts.append('')
        
        # 導入
        content_parts.append(common_imports.rstrip())
        content_parts.append('')
        
        # 提取類別代碼
        start_line = class_info['start_line'] - 1
        end_line = class_info['end_line'] - 1
        class_lines = lines[start_line:end_line + 1]
        
        content_parts.extend(class_lines)
        
        return '\n'.join(content_parts)
    
    def _generate_function_group_file_content(self, functions: List[Dict[str, Any]], 
                                            structure: Dict[str, Any], lines: List[str], 
                                            common_imports: str) -> str:
        """生成函數組檔案內容"""
        content_parts = []
        
        # 檔案頭部註釋
        func_names = [func['name'] for func in functions]
        content_parts.append(f'"""\n工具函數模組\n包含: {", ".join(func_names)}\n"""')
        content_parts.append('')
        
        # 導入
        content_parts.append(common_imports.rstrip())
        content_parts.append('')
        
        # 添加常數（如果函數使用到）
        for const in structure['constants']:
            content_parts.append(lines[const['line'] - 1])
        
        if structure['constants']:
            content_parts.append('')
        
        # 提取函數代碼
        for func in functions:
            start_line = func['start_line'] - 1
            end_line = func['end_line'] - 1
            func_lines = lines[start_line:end_line + 1]
            
            content_parts.extend(func_lines)
            content_parts.append('')  # 函數間空行
        
        return '\n'.join(content_parts)
    
    async def _generate_updated_main_file(self, file_path: Path, original_content: str, 
                                        structure: Dict[str, Any], strategy: Dict[str, Any], 
                                        new_files: List[Dict[str, Any]]) -> str:
        """生成更新後的主檔案內容"""
        content_parts = []
        lines = original_content.split('\n')
        
        # 保留模組文檔字符串
        if structure['module_docstring']:
            content_parts.append(f'"""{structure["module_docstring"]}"""')
            content_parts.append('')
        
        # 原有導入
        for imp in structure['imports']:
            content_parts.append(lines[imp['line'] - 1])
        
        # 新增導入（導入拆分出去的模組）
        content_parts.append('')
        content_parts.append('# 從拆分檔案導入')
        
        for new_file in new_files:
            module_name = Path(new_file['path']).stem
            if new_file['type'] == 'class_module':
                for class_name in new_file['contains']:
                    content_parts.append(f'from .{module_name} import {class_name}')
            elif new_file['type'] in ['function_module', 'utility_module']:
                for func_name in new_file['contains']:
                    content_parts.append(f'from .{module_name} import {func_name}')
        
        content_parts.append('')
        
        # 保留未拆分的內容
        preserved_elements = self._get_preserved_elements(structure, strategy)
        
        for element in preserved_elements:
            if element['type'] == 'constant':
                content_parts.append(lines[element['line'] - 1])
            elif element['type'] in ['class', 'function']:
                start_line = element['start_line'] - 1
                end_line = element['end_line'] - 1
                element_lines = lines[start_line:end_line + 1]
                content_parts.extend(element_lines)
                content_parts.append('')
        
        return '\n'.join(content_parts)
    
    def _get_preserved_elements(self, structure: Dict[str, Any], strategy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """獲取需要保留在主檔案中的元素"""
        preserved = []
        
        # 添加常數
        for const in structure['constants']:
            preserved.append({
                'type': 'constant',
                'line': const['line']
            })
        
        # 根據策略確定保留的類別和函數
        split_targets = strategy['strategy']['split_targets']
        
        if isinstance(split_targets, list):
            # 類別或函數拆分
            if strategy['strategy']['type'] == 'class_split':
                for cls in structure['classes']:
                    if cls['name'] not in split_targets:
                        preserved.append({
                            'type': 'class',
                            'start_line': cls['start_line'],
                            'end_line': cls['end_line']
                        })
                # 保留所有函數
                for func in structure['functions']:
                    preserved.append({
                        'type': 'function',
                        'start_line': func['start_line'],
                        'end_line': func['end_line']
                    })
            
            elif strategy['strategy']['type'] == 'function_split':
                # 保留所有類別
                for cls in structure['classes']:
                    preserved.append({
                        'type': 'class',
                        'start_line': cls['start_line'],
                        'end_line': cls['end_line']
                    })
                # 保留未拆分的函數
                for func in structure['functions']:
                    if not any(func['name'].startswith(target) for target in split_targets):
                        preserved.append({
                            'type': 'function',
                            'start_line': func['start_line'],
                            'end_line': func['end_line']
                        })
        
        elif isinstance(split_targets, dict):
            # 混合拆分
            for cls in structure['classes']:
                if cls['name'] not in split_targets.get('classes', []):
                    preserved.append({
                        'type': 'class',
                        'start_line': cls['start_line'],
                        'end_line': cls['end_line']
                    })
            
            for func in structure['functions']:
                if func['name'] not in split_targets.get('functions', []):
                    preserved.append({
                        'type': 'function',
                        'start_line': func['start_line'],
                        'end_line': func['end_line']
                    })
        
        # 按行號排序
        preserved.sort(key=lambda x: x.get('line', x.get('start_line', 0)))
        return preserved
    
    def _generate_common_imports(self, imports: List[Dict[str, Any]]) -> str:
        """生成共用導入"""
        import_lines = []
        
        for imp in imports:
            if imp['type'] == 'import':
                modules = imp['modules']
                aliases = imp.get('aliases', {})
                
                for module in modules:
                    if module in aliases:
                        import_lines.append(f"import {module} as {aliases[module]}")
                    else:
                        import_lines.append(f"import {module}")
            
            elif imp['type'] == 'from_import':
                module = imp['module'] or ''
                names = imp['names']
                aliases = imp.get('aliases', {})
                level = imp.get('level', 0)
                
                if names == ['*']:
                    import_lines.append(f"from {'.' * level}{module} import *")
                else:
                    name_parts = []
                    for name in names:
                        if name in aliases:
                            name_parts.append(f"{name} as {aliases[name]}")
                        else:
                            name_parts.append(name)
                    
                    import_lines.append(f"from {'.' * level}{module} import {', '.join(name_parts)}")
        
        return '\n'.join(import_lines) + '\n' if import_lines else ''
    
    def _get_end_line(self, node: ast.AST, lines: List[str]) -> int:
        """計算 AST 節點的結束行號"""
        if hasattr(node, 'end_lineno') and node.end_lineno:
            return node.end_lineno
        
        # 估算結束行號
        start_line = node.lineno - 1
        if start_line >= len(lines):
            return len(lines)
        
        start_indent = len(lines[start_line]) - len(lines[start_line].lstrip())
        
        for i in range(start_line + 1, len(lines)):
            line = lines[i]
            if line.strip():
                current_indent = len(line) - len(line.lstrip())
                if current_indent <= start_indent:
                    return i
        
        return len(lines)
    
    def _calculate_complexity(self, func_node: ast.FunctionDef) -> int:
        """計算函數複雜度"""
        complexity = 1
        
        for node in ast.walk(func_node):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
        
        return complexity