{"hooks": {"PostToolUse": [{"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "python D:/project/python/outlook_summary/.claude/run_hooks.py --analyze", "timeout": 300}]}], "Stop": [{"hooks": [{"type": "command", "command": "python D:/project/python/outlook_summary/.claude/run_hooks.py --analyze", "run_in_background": true}, {"type": "command", "command": "python D:/project/python/outlook_summary/.claude/hooks/update_docs_safe.py", "run_in_background": true}]}]}, "env": {"DISABLE_AUTOUPDATER": "1"}}