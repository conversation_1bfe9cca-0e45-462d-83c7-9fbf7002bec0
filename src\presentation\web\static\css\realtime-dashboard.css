/* WebSocket 即時狀態更新儀表板樣式 */

/* 連接狀態指示器 */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.connection-status.disconnected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    animation: pulse 2s infinite;
}

/* 即時儀表板布局 */
.realtime-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-title {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.dashboard-subtitle {
    font-size: 16px;
    color: #7f8c8d;
    margin: 4px 0 0 0;
}

.dashboard-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.control-button {
    padding: 8px 16px;
    border: 1px solid #3498db;
    background: white;
    color: #3498db;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.control-button:hover {
    background: #3498db;
    color: white;
}

.control-button.active {
    background: #3498db;
    color: white;
}

/* 系統概覽卡片 */
.system-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.metric-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.metric-icon {
    width: 24px;
    height: 24px;
    opacity: 0.7;
}

.metric-value {
    font-size: 32px;
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.metric-value.warning {
    color: #f39c12;
}

.metric-value.critical {
    color: #e74c3c;
}

.metric-updated {
    animation: metricUpdate 0.5s ease;
}

@keyframes metricUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.metric-label {
    font-size: 14px;
    color: #7f8c8d;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    margin-top: 8px;
}

.trend-up {
    color: #27ae60;
}

.trend-down {
    color: #e74c3c;
}

/* 任務狀態列表 */
.tasks-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
}

.task-filter {
    display: flex;
    gap: 8px;
}

.filter-button {
    padding: 6px 12px;
    border: 1px solid #bdc3c7;
    background: white;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-button.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.tasks-list {
    max-height: 400px;
    overflow-y: auto;
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.task-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.task-info {
    flex: 1;
}

.task-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.task-id {
    font-size: 12px;
    color: #7f8c8d;
    font-family: monospace;
}

.task-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    margin-right: 12px;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-running {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
}

.status-cancelled {
    background-color: #e2e3e5;
    color: #383d41;
}

/* 進度條 */
.progress-container {
    margin-top: 8px;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #ecf0f1;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background-color: #3498db;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #7f8c8d;
    margin-top: 4px;
}

/* 進程進度區塊 */
.process-progress-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.process-item {
    padding: 16px;
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    margin-bottom: 16px;
}

.process-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.process-name {
    font-weight: 600;
    color: #2c3e50;
}

.step-text {
    font-size: 14px;
    color: #7f8c8d;
}

.message-text {
    font-size: 14px;
    color: #34495e;
    margin-top: 8px;
    font-style: italic;
}

/* 告警容器 */
.alert-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1000;
    width: 350px;
}

.alert {
    margin-bottom: 12px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.alert-content {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    gap: 12px;
}

.alert-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.alert-text {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

.alert-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    flex-shrink: 0;
}

.alert-close:hover {
    opacity: 1;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

/* 錯誤通知 */
.error-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 20px;
    gap: 12px;
}

.notification-icon {
    font-size: 24px;
}

.notification-message {
    font-size: 16px;
    color: #2c3e50;
}

.notification-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #7f8c8d;
    margin-left: 12px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .realtime-dashboard {
        padding: 10px;
    }
    
    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .dashboard-controls {
        justify-content: center;
    }
    
    .system-overview {
        grid-template-columns: 1fr;
    }
    
    .task-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .alert-container {
        right: 10px;
        left: 10px;
        width: auto;
    }
    
    .connection-status {
        top: 10px;
        right: 10px;
    }
}

/* 暗黑模式支援 */
@media (prefers-color-scheme: dark) {
    .realtime-dashboard {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .dashboard-header,
    .metric-card,
    .tasks-section,
    .process-progress-section {
        background: #2d2d2d;
        color: #e0e0e0;
    }
    
    .dashboard-title,
    .section-title,
    .metric-title,
    .task-name,
    .process-name {
        color: #f0f0f0;
    }
    
    .task-item {
        border-color: #404040;
        background-color: transparent;
    }
    
    .task-item:hover {
        background-color: #3a3a3a;
    }
    
    .control-button,
    .filter-button {
        background: #404040;
        border-color: #606060;
        color: #e0e0e0;
    }
    
    .control-button:hover,
    .filter-button:hover {
        background-color: #505050;
    }
}

/* 動畫和過渡效果 */
.task-item,
.metric-card,
.alert {
    transition: all 0.3s ease;
}

.progress-bar-fill {
    background: linear-gradient(90deg, #3498db, #2980b9);
    animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 滾動條樣式 */
.tasks-list::-webkit-scrollbar {
    width: 6px;
}

.tasks-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.tasks-list::-webkit-scrollbar-thumb {
    background: #bdc3c7;
    border-radius: 3px;
}

.tasks-list::-webkit-scrollbar-thumb:hover {
    background: #95a5a6;
}