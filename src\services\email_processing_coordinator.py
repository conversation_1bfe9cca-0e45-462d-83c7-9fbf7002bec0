"""
郵件處理協調器
整合增強任務排程器與現有郵件處理系統的統一入口點

核心功能：
- 統一郵件處理入口
- 廠商類型自動識別
- GTK排程邏輯整合
- 其他廠商立即處理
- 錯誤處理和恢復
"""

import os
import sys
import threading
from typing import Dict, Any, Optional, List
from datetime import datetime

# Dynamic path setup
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from src.infrastructure.logging.logger_manager import LoggerManager
from src.infrastructure.adapters.notification.line_notification_service import LineNotificationService
from .concurrent_task_manager import get_task_manager, TaskPriority
from .enhanced_task_scheduler import get_enhanced_scheduler, VendorType


class EmailProcessingCoordinator:
    """
    郵件處理協調器
    
    負責整合增強任務排程器與現有郵件處理系統，
    提供統一的郵件處理入口點和廠商分流邏輯。
    """
    
    def __init__(self, 
                 enable_gtk_scheduling: bool = True,
                 logger: Optional[object] = None):
        """
        初始化郵件處理協調器
        
        Args:
            enable_gtk_scheduling: 是否啟用GTK排程功能
            logger: 自定義日誌記錄器
        """
        self.logger = logger or LoggerManager().get_logger("EmailProcessingCoordinator")
        self.enable_gtk_scheduling = enable_gtk_scheduling
        
        # 初始化服務組件
        self._initialize_services()
        
        # 廠商處理映射
        self._vendor_processors = {
            'GTK': self._process_gtk_vendor,
            'JCET': self._process_standard_vendor,
            'AMIC': self._process_standard_vendor,
            'OSE': self._process_standard_vendor,
            'SPIL': self._process_standard_vendor,
            'ASE': self._process_standard_vendor,
            'MSEC': self._process_standard_vendor,
            # 可以根據需要添加更多廠商
        }
        
        self.logger.info(f"[OK] 郵件處理協調器已初始化 - GTK排程: {enable_gtk_scheduling}")
    
    def _initialize_services(self):
        """初始化所需的服務組件"""
        try:
            # 獲取並發任務管理器
            self.task_manager = get_task_manager()
            
            # 獲取增強任務排程器（移除不支援的參數）
            self.scheduler = get_enhanced_scheduler(
                enable_notifications=True,
                max_workers=4
            )
            # 手動設定GTK排程功能
            self.scheduler.enable_gtk_scheduling = self.enable_gtk_scheduling
            
            # 獲取通知服務
            try:
                self.notification_service = LineNotificationService()
                # 設置排程器的通知服務
                self.scheduler.notification_service = self.notification_service
            except Exception as e:
                self.logger.warning(f"無法初始化通知服務: {e}")
                self.notification_service = None
            
            self.logger.info("[OK] 服務組件初始化完成")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 服務組件初始化失敗: {e}")
            raise
    
    def process_email(self, 
                     vendor_code: str,
                     email_data: Dict[str, Any],
                     processing_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        統一郵件處理入口點
        
        根據廠商代碼自動選擇處理策略：
        - GTK廠商：使用排程邏輯
        - 其他廠商：立即處理
        
        Args:
            vendor_code: 廠商代碼
            email_data: 郵件資料
            processing_options: 處理選項
            
        Returns:
            Dict: 處理結果
        """
        try:
            # 預處理參數
            if processing_options is None:
                processing_options = {}
            
            # 標準化廠商代碼
            vendor_name = self._normalize_vendor_code(vendor_code)
            
            # 記錄處理開始
            email_id = email_data.get('id', 'unknown')
            self.logger.info(f"[PROCESS_START] 開始處理郵件: {email_id}, 廠商: {vendor_name}")
            
            # 根據廠商選擇處理器
            processor = self._vendor_processors.get(vendor_name, self._process_standard_vendor)
            
            # 執行處理
            result = processor(vendor_name, email_data, processing_options)
            
            # 記錄處理結果
            self.logger.info(f"[PROCESS_OK] 郵件處理完成: {email_id}, 結果: {result.get('status', 'unknown')}")
            
            return result
            
        except Exception as e:
            error_msg = f"郵件處理失敗: {str(e)}"
            self.logger.error(f"[PROCESS_ERROR] {error_msg}")
            
            # 發送錯誤通知
            self._send_error_notification(vendor_code, email_data, error_msg)
            
            return {
                'status': 'error',
                'message': error_msg,
                'email_id': email_data.get('id'),
                'vendor_code': vendor_code,
                'timestamp': datetime.now().isoformat()
            }
    
    def _normalize_vendor_code(self, vendor_code: str) -> str:
        """
        標準化廠商代碼
        
        Args:
            vendor_code: 原始廠商代碼
            
        Returns:
            str: 標準化後的廠商代碼
        """
        if not vendor_code:
            return 'UNKNOWN'
        
        # 轉換為大寫並去除空白
        normalized = vendor_code.strip().upper()
        
        # 處理常見的變體
        vendor_mapping = {
            'GLOBALTECH': 'GTK',
            'GLOBAL_TECH': 'GTK',
            'GLOBAL-TECH': 'GTK',
            'JCET_GROUP': 'JCET',
            'JCET-GROUP': 'JCET',
            'ADVANCED_MICRO': 'AMIC',
            'OSE_GROUP': 'OSE',
            'SPIL_GROUP': 'SPIL',
            'ASE_GROUP': 'ASE',
            'MSEC_GROUP': 'MSEC'
        }
        
        return vendor_mapping.get(normalized, normalized)
    
    def _process_gtk_vendor(self, 
                           vendor_name: str, 
                           email_data: Dict[str, Any], 
                           processing_options: Dict[str, Any]) -> Dict[str, Any]:
        """
        處理GTK廠商郵件（使用排程邏輯）
        
        Args:
            vendor_name: 廠商名稱
            email_data: 郵件資料
            processing_options: 處理選項
            
        Returns:
            Dict: 處理結果
        """
        try:
            # 檢查是否啟用GTK排程
            if not self.enable_gtk_scheduling:
                self.logger.info(f"[GTK_DIRECT] GTK排程已停用，直接處理郵件")
                return self._process_standard_vendor(vendor_name, email_data, processing_options)
            
            # 使用排程器處理GTK郵件
            task_id = self.scheduler.schedule_email_processing(
                vendor_name=vendor_name,
                email_data=email_data,
                processing_params=processing_options,
                priority=processing_options.get('priority', TaskPriority.NORMAL)
            )
            
            # 計算執行時間
            received_time = self._parse_received_time(email_data)
            scheduled_time = self.scheduler._calculate_gtk_execution_time(received_time)
            
            return {
                'status': 'scheduled',
                'message': f'GTK郵件已加入排程佇列',
                'task_id': task_id,
                'vendor_name': vendor_name,
                'email_id': email_data.get('id'),
                'scheduled_time': scheduled_time.isoformat(),
                'processing_type': 'gtk_scheduled',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"[GTK_ERROR] GTK郵件排程失敗: {e}")
            # 如果排程失敗，降級為立即處理
            return self._process_standard_vendor(vendor_name, email_data, processing_options)
    
    def _process_standard_vendor(self, 
                               vendor_name: str, 
                               email_data: Dict[str, Any], 
                               processing_options: Dict[str, Any]) -> Dict[str, Any]:
        """
        處理標準廠商郵件（立即處理）
        
        Args:
            vendor_name: 廠商名稱
            email_data: 郵件資料
            processing_options: 處理選項
            
        Returns:
            Dict: 處理結果
        """
        try:
            # 使用排程器的立即處理功能
            result = self.scheduler._process_immediate_task(
                vendor_name, 
                email_data, 
                processing_options
            )
            
            # 標記為立即處理
            result['processing_type'] = 'immediate'
            result['vendor_name'] = vendor_name
            
            return result
            
        except Exception as e:
            self.logger.error(f"[STANDARD_ERROR] 標準廠商郵件處理失敗: {e}")
            raise
    
    def _parse_received_time(self, email_data: Dict[str, Any]) -> datetime:
        """
        解析郵件接收時間
        
        Args:
            email_data: 郵件資料
            
        Returns:
            datetime: 解析後的時間
        """
        received_time_str = email_data.get('received_time')
        
        if received_time_str:
            try:
                # 處理ISO格式時間
                if received_time_str.endswith('Z'):
                    received_time_str = received_time_str[:-1] + '+00:00'
                return datetime.fromisoformat(received_time_str)
            except Exception as e:
                self.logger.warning(f"解析接收時間失敗: {e}")
        
        # 如果無法解析，使用當前時間
        return datetime.now()
    
    def _send_error_notification(self, vendor_code: str, email_data: Dict[str, Any], error_msg: str):
        """
        發送錯誤通知
        
        Args:
            vendor_code: 廠商代碼
            email_data: 郵件資料
            error_msg: 錯誤訊息
        """
        if not self.notification_service:
            return
        
        try:
            # 更新郵件資料以包含錯誤資訊
            email_data_with_error = email_data.copy()
            email_data_with_error['error_message'] = error_msg
            email_data_with_error['vendor_code'] = vendor_code
            
            # 發送解析失敗通知
            self.notification_service.send_parsing_failure_notification(email_data_with_error)
            
        except Exception as e:
            self.logger.error(f"[ERROR] 發送錯誤通知失敗: {e}")
    
    def get_processing_status(self) -> Dict[str, Any]:
        """
        獲取處理狀態
        
        Returns:
            Dict: 處理狀態資訊
        """
        try:
            # 獲取排程器狀態（使用統計信息）
            scheduler_status = {
                'statistics': self.scheduler.get_statistics(),
                'uptime_seconds': 0,  # 簡化實現
                'active_jobs': len(self.scheduler._scheduled_tasks)
            }
            
            # 獲取任務管理器狀態（使用統計信息）
            task_manager_status = self.task_manager.get_statistics() if self.task_manager else {}
            
            return {
                'coordinator_status': 'active',
                'gtk_scheduling_enabled': self.enable_gtk_scheduling,
                'supported_vendors': list(self._vendor_processors.keys()),
                'scheduler_status': scheduler_status,
                'task_manager_status': task_manager_status,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 獲取處理狀態失敗: {e}")
            return {
                'coordinator_status': 'error',
                'error_message': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_scheduled_tasks(self, vendor_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        獲取排程任務列表
        
        Args:
            vendor_filter: 廠商過濾器
            
        Returns:
            List: 排程任務列表
        """
        try:
            tasks = self.scheduler.list_scheduled_tasks()
            
            # 如果有廠商過濾器，進行過濾
            if vendor_filter:
                vendor_filter = self._normalize_vendor_code(vendor_filter)
                tasks = [task for task in tasks if task.get('vendor_name') == vendor_filter]
            
            return tasks
            
        except Exception as e:
            self.logger.error(f"[ERROR] 獲取排程任務失敗: {e}")
            return []
    
    def cancel_scheduled_task(self, task_id: str) -> bool:
        """
        取消排程任務
        
        Args:
            task_id: 任務ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            return self.scheduler.cancel_scheduled_task(task_id)
        except Exception as e:
            self.logger.error(f"[ERROR] 取消排程任務失敗: {e}")
            return False
    
    def get_next_gtk_executions(self, hours_ahead: int = 24) -> List[Dict[str, Any]]:
        """
        獲取未來的GTK執行時間
        
        Args:
            hours_ahead: 向前查看的小時數
            
        Returns:
            List: 執行時間列表
        """
        try:
            return self.scheduler.get_next_execution_times(hours_ahead)
        except Exception as e:
            self.logger.error(f"[ERROR] 獲取GTK執行時間失敗: {e}")
            return []
    
    def shutdown(self):
        """關閉協調器"""
        self.logger.info("[SHUTDOWN] 正在關閉郵件處理協調器...")
        
        try:
            # 關閉排程器
            if hasattr(self, 'scheduler'):
                self.scheduler.shutdown()
            
            self.logger.info("[OK] 郵件處理協調器已關閉")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 關閉協調器時發生錯誤: {e}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.shutdown()


# 全域實例（單例模式）
_coordinator_instance = None
_coordinator_lock = threading.Lock()

def get_email_coordinator(**kwargs) -> EmailProcessingCoordinator:
    """
    獲取郵件處理協調器單例實例
    
    Returns:
        EmailProcessingCoordinator: 協調器實例
    """
    global _coordinator_instance
    
    if _coordinator_instance is None:
        with _coordinator_lock:
            if _coordinator_instance is None:
                _coordinator_instance = EmailProcessingCoordinator(**kwargs)
    
    return _coordinator_instance