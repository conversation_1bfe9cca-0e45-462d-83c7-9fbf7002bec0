#!/usr/bin/env python3
"""
重複代碼檢測器
使用 AST 分析檢測相似和重複的代碼，提供清理建議
"""

import ast
import hashlib
import difflib
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from collections import defaultdict
import logging
from dataclasses import dataclass

@dataclass
class CodeBlock:
    """代碼區塊"""
    file_path: str
    start_line: int
    end_line: int
    content: str
    ast_hash: str
    function_name: Optional[str] = None
    class_name: Optional[str] = None
    
class DuplicateDetector:
    """重複代碼檢測器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('DuplicateDetector')
        self.similarity_threshold = config.get('duplicate_threshold', 0.85)
        self.min_lines = config.get('min_duplicate_lines', 5)
    
    async def analyze(self, files: Optional[List[str]] = None) -> Dict[str, Any]:
        """分析重複代碼"""
        self.logger.info("🔍 開始重複代碼檢測...")
        
        if files:
            target_files = [self.repo_root / f for f in files if f.endswith('.py')]
        else:
            target_files = self._find_python_files()
        
        # 提取所有代碼區塊
        code_blocks = []
        for file_path in target_files:
            try:
                blocks = await self._extract_code_blocks(file_path)
                code_blocks.extend(blocks)
            except Exception as e:
                self.logger.warning(f"提取代碼區塊失敗 {file_path}: {e}")
        
        # 檢測重複
        duplicate_groups = await self._find_duplicates(code_blocks)
        
        # 分析重複模式
        patterns = await self._analyze_patterns(duplicate_groups)
        
        result = {
            'total_files_analyzed': len(target_files),
            'total_code_blocks': len(code_blocks),
            'duplicate_groups': duplicate_groups,
            'patterns': patterns,
            'statistics': {
                'total_duplicates': len(duplicate_groups),
                'avg_similarity': sum(g['similarity'] for g in duplicate_groups) / max(len(duplicate_groups), 1),
                'most_duplicated_pattern': patterns[0] if patterns else None
            }
        }
        
        self.logger.info(f"✅ 重複代碼檢測完成: 發現 {len(duplicate_groups)} 組重複代碼")
        return result
    
    async def _extract_code_blocks(self, file_path: Path) -> List[CodeBlock]:
        """從檔案中提取代碼區塊"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            tree = ast.parse(content)
            lines = content.split('\n')
            blocks = []
            
            # 提取函數
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    block = await self._create_code_block(
                        file_path, node, lines, 'function', node.name
                    )
                    if block and self._is_significant_block(block):
                        blocks.append(block)
                
                elif isinstance(node, ast.ClassDef):
                    # 提取整個類別
                    block = await self._create_code_block(
                        file_path, node, lines, 'class', node.name
                    )
                    if block and self._is_significant_block(block):
                        blocks.append(block)
                    
                    # 提取類別中的方法
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_block = await self._create_code_block(
                                file_path, item, lines, 'method', item.name, node.name
                            )
                            if method_block and self._is_significant_block(method_block):
                                blocks.append(method_block)
            
            return blocks
            
        except Exception as e:
            self.logger.error(f"解析檔案失敗 {file_path}: {e}")
            return []
    
    async def _create_code_block(self, file_path: Path, node: ast.AST, lines: List[str], 
                               block_type: str, name: str, class_name: Optional[str] = None) -> Optional[CodeBlock]:
        """建立代碼區塊物件"""
        try:
            start_line = node.lineno
            end_line = self._get_end_line(node, lines)
            
            # 提取內容
            block_lines = lines[start_line-1:end_line]
            content = '\n'.join(block_lines)
            
            # 計算 AST hash
            ast_hash = self._compute_ast_hash(node)
            
            return CodeBlock(
                file_path=str(file_path.relative_to(self.repo_root)),
                start_line=start_line,
                end_line=end_line,
                content=content,
                ast_hash=ast_hash,
                function_name=name if block_type in ['function', 'method'] else None,
                class_name=class_name
            )
            
        except Exception as e:
            self.logger.warning(f"建立代碼區塊失敗: {e}")
            return None
    
    def _compute_ast_hash(self, node: ast.AST) -> str:
        """計算 AST 的結構化 hash"""
        try:
            # 標準化 AST（移除變數名稱等）
            normalized = self._normalize_ast(node)
            ast_dump = ast.dump(normalized, annotate_fields=False)
            return hashlib.md5(ast_dump.encode()).hexdigest()
        except:
            return hashlib.md5(str(node).encode()).hexdigest()
    
    def _normalize_ast(self, node: ast.AST) -> ast.AST:
        """標準化 AST，移除變數名稱等差異"""
        if isinstance(node, ast.Name):
            # 將變數名稱標準化
            return ast.Name(id='VAR', ctx=node.ctx)
        elif isinstance(node, ast.FunctionDef):
            # 標準化函數名稱
            new_node = ast.copy_location(
                ast.FunctionDef(
                    name='FUNC',
                    args=self._normalize_ast(node.args),
                    body=[self._normalize_ast(n) for n in node.body],
                    decorator_list=[self._normalize_ast(n) for n in node.decorator_list],
                    returns=self._normalize_ast(node.returns) if node.returns else None
                ),
                node
            )
            return new_node
        elif isinstance(node, list):
            return [self._normalize_ast(n) for n in node]
        elif hasattr(node, '_fields'):
            new_node = node.__class__()
            for field_name in node._fields:
                field_value = getattr(node, field_name, None)
                if field_value is not None:
                    if isinstance(field_value, list):
                        normalized_value = [self._normalize_ast(item) for item in field_value]
                    elif isinstance(field_value, ast.AST):
                        normalized_value = self._normalize_ast(field_value)
                    else:
                        normalized_value = field_value
                    setattr(new_node, field_name, normalized_value)
            return ast.copy_location(new_node, node)
        else:
            return node
    
    async def _find_duplicates(self, code_blocks: List[CodeBlock]) -> List[Dict[str, Any]]:
        """找出重複的代碼區塊"""
        # 按 AST hash 分組
        hash_groups = defaultdict(list)
        for block in code_blocks:
            hash_groups[block.ast_hash].append(block)
        
        duplicate_groups = []
        
        # 找出完全相同的 AST
        for ast_hash, blocks in hash_groups.items():
            if len(blocks) > 1:
                group = await self._create_duplicate_group(blocks, 1.0, 'exact_match')
                duplicate_groups.append(group)
        
        # 找出相似的代碼（使用文字相似度）
        processed_pairs = set()
        for i, block1 in enumerate(code_blocks):
            for j, block2 in enumerate(code_blocks[i+1:], i+1):
                pair_key = tuple(sorted([block1.file_path, block2.file_path, 
                                      str(block1.start_line), str(block2.start_line)]))
                if pair_key in processed_pairs:
                    continue
                processed_pairs.add(pair_key)
                
                # 跳過已經在完全匹配組中的
                if block1.ast_hash == block2.ast_hash:
                    continue
                
                similarity = self._calculate_similarity(block1.content, block2.content)
                if similarity >= self.similarity_threshold:
                    group = await self._create_duplicate_group([block1, block2], similarity, 'similar')
                    duplicate_groups.append(group)
        
        # 按相似度排序
        duplicate_groups.sort(key=lambda x: x['similarity'], reverse=True)
        
        return duplicate_groups
    
    async def _create_duplicate_group(self, blocks: List[CodeBlock], similarity: float, 
                                    match_type: str) -> Dict[str, Any]:
        """建立重複代碼組"""
        # 找出最新的檔案
        newest_block = await self._find_newest_block(blocks)
        
        group = {
            'id': hashlib.md5(f"{blocks[0].ast_hash}_{len(blocks)}".encode()).hexdigest()[:8],
            'match_type': match_type,
            'similarity': similarity,
            'block_count': len(blocks),
            'blocks': [],
            'newest_block': newest_block.file_path if newest_block else None,
            'suggested_action': await self._suggest_cleanup_action(blocks, match_type)
        }
        
        for block in blocks:
            group['blocks'].append({
                'file_path': block.file_path,
                'start_line': block.start_line,
                'end_line': block.end_line,
                'function_name': block.function_name,
                'class_name': block.class_name,
                'lines': block.end_line - block.start_line + 1
            })
        
        return group
    
    async def _find_newest_block(self, blocks: List[CodeBlock]) -> Optional[CodeBlock]:
        """找出最新修改的代碼區塊"""
        try:
            newest_block = None
            newest_time = 0
            
            for block in blocks:
                # 使用 git blame 找出最新修改時間
                result = subprocess.run([
                    'git', 'log', '-1', '--format=%ct', '--', block.file_path
                ], capture_output=True, text=True, cwd=self.repo_root)
                
                if result.returncode == 0:
                    mod_time = int(result.stdout.strip())
                    if mod_time > newest_time:
                        newest_time = mod_time
                        newest_block = block
            
            return newest_block or blocks[0]
            
        except Exception as e:
            self.logger.warning(f"查找最新檔案失敗: {e}")
            return blocks[0]
    
    async def _suggest_cleanup_action(self, blocks: List[CodeBlock], match_type: str) -> str:
        """建議清理動作"""
        if match_type == 'exact_match':
            if len(blocks) > 2:
                return 'extract_to_shared_module'
            else:
                return 'remove_duplicate'
        elif match_type == 'similar':
            return 'refactor_to_common_pattern'
        else:
            return 'manual_review'
    
    def _calculate_similarity(self, content1: str, content2: str) -> float:
        """計算兩段代碼的相似度"""
        lines1 = content1.strip().split('\n')
        lines2 = content2.strip().split('\n')
        
        # 使用 difflib 計算相似度
        matcher = difflib.SequenceMatcher(None, lines1, lines2)
        return matcher.ratio()
    
    async def _analyze_patterns(self, duplicate_groups: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析重複模式"""
        patterns = []
        
        # 按檔案路徑分組，找出重複較多的模組
        file_duplicate_count = defaultdict(int)
        for group in duplicate_groups:
            files = set(block['file_path'] for block in group['blocks'])
            for file_path in files:
                file_duplicate_count[file_path] += 1
        
        # 找出重複最多的檔案
        if file_duplicate_count:
            most_duplicated_file = max(file_duplicate_count.items(), key=lambda x: x[1])
            patterns.append({
                'type': 'file_with_most_duplicates',
                'file_path': most_duplicated_file[0],
                'duplicate_count': most_duplicated_file[1],
                'suggestion': '考慮重構此檔案，提取共用功能'
            })
        
        # 分析函數名稱模式
        function_patterns = defaultdict(list)
        for group in duplicate_groups:
            for block in group['blocks']:
                if block['function_name']:
                    function_patterns[block['function_name']].append(group)
        
        # 找出重複的函數名稱
        for func_name, groups in function_patterns.items():
            if len(groups) > 1:
                patterns.append({
                    'type': 'repeated_function_name',
                    'function_name': func_name,
                    'occurrences': len(groups),
                    'suggestion': f'函數名稱 "{func_name}" 重複出現，考慮統一實作'
                })
        
        return patterns
    
    def _find_python_files(self) -> List[Path]:
        """找出所有 Python 檔案"""
        files = []
        ignore_dirs = {'.git', '__pycache__', 'venv', 'venv_win_3_11_12', '.claude'}
        
        for py_file in self.repo_root.rglob('*.py'):
            if any(ignore_dir in py_file.parts for ignore_dir in ignore_dirs):
                continue
            files.append(py_file)
        
        return files
    
    def _get_end_line(self, node: ast.AST, lines: List[str]) -> int:
        """取得 AST 節點的結束行號"""
        if hasattr(node, 'end_lineno') and node.end_lineno:
            return node.end_lineno
        
        # 簡單估算
        start_line = node.lineno - 1
        if start_line >= len(lines):
            return len(lines)
        
        start_indent = len(lines[start_line]) - len(lines[start_line].lstrip())
        
        for i in range(start_line + 1, len(lines)):
            line = lines[i]
            if line.strip():
                current_indent = len(line) - len(line.lstrip())
                if current_indent <= start_indent:
                    return i
        
        return len(lines)
    
    def _is_significant_block(self, block: CodeBlock) -> bool:
        """檢查代碼區塊是否足夠重要"""
        lines_count = block.end_line - block.start_line + 1
        return lines_count >= self.min_lines and len(block.content.strip()) > 50