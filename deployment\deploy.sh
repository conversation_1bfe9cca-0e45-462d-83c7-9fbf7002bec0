#!/bin/bash
# deploy.sh - 多進程系統自動化部署腳本

set -e

# 配置變數
APP_NAME="outlook-summary"
APP_USER="outlook-user"
APP_DIR="/opt/$APP_NAME"
SERVICE_NAME="$APP_NAME.service"
PYTHON_VERSION="3.11"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 開始部署 $APP_NAME 多進程系統..."

# 檢查系統要求
check_requirements() {
    log_info "檢查系統要求..."
    
    # 檢查是否為root用戶
    if [[ $EUID -ne 0 ]]; then
        log_error "此腳本需要root權限運行"
        exit 1
    fi
    
    # 檢查作業系統
    if [[ ! -f /etc/os-release ]]; then
        log_error "無法識別作業系統"
        exit 1
    fi
    
    # 檢查CPU核心數
    CPU_CORES=$(nproc)
    if [ $CPU_CORES -lt 4 ]; then
        log_warning "CPU核心數不足 ($CPU_CORES < 4)，可能影響性能"
    else
        log_success "CPU核心數: $CPU_CORES"
    fi
    
    # 檢查記憶體
    MEMORY_GB=$(free -g | grep '^Mem:' | awk '{print $2}')
    if [ $MEMORY_GB -lt 8 ]; then
        log_warning "記憶體不足 ($MEMORY_GB GB < 8 GB)，可能影響性能"
    else
        log_success "記憶體: ${MEMORY_GB}GB"
    fi
    
    # 檢查磁碟空間
    DISK_AVAIL_GB=$(df / | tail -1 | awk '{print $4}' | xargs -I {} echo "scale=1; {}/1024/1024" | bc)
    if (( $(echo "$DISK_AVAIL_GB < 10" | bc -l) )); then
        log_error "磁碟空間不足 ($DISK_AVAIL_GB GB < 10 GB)"
        exit 1
    else
        log_success "可用磁碟空間: ${DISK_AVAIL_GB}GB"
    fi
    
    # 安裝基礎工具
    log_info "安裝基礎工具..."
    apt-get update -qq
    apt-get install -y curl wget git python3.11 python3.11-venv python3.11-dev \
                       build-essential supervisor nginx bc > /dev/null 2>&1
    log_success "基礎工具安裝完成"
}

# 創建用戶和目錄
setup_user_and_dirs() {
    log_info "設置用戶和目錄結構..."
    
    # 創建用戶
    if ! id "$APP_USER" &>/dev/null; then
        useradd -r -s /bin/bash -m -d $APP_DIR $APP_USER
        log_success "創建用戶: $APP_USER"
    else
        log_info "用戶已存在: $APP_USER"
    fi
    
    # 創建目錄結構
    mkdir -p $APP_DIR/{src,config,logs,temp,data,scripts,monitoring}
    mkdir -p $APP_DIR/logs/{app,worker,system}
    mkdir -p $APP_DIR/temp/{uploads,processing,downloads}
    
    # 設置權限
    chown -R $APP_USER:$APP_USER $APP_DIR
    chmod 755 $APP_DIR
    chmod 750 $APP_DIR/{logs,temp,data}
    
    log_success "目錄結構創建完成"
}

# 複製應用文件
deploy_application() {
    log_info "部署應用代碼..."
    
    # 檢查源代碼目錄
    if [[ ! -d "src" ]]; then
        log_error "源代碼目錄不存在，請在項目根目錄運行此腳本"
        exit 1
    fi
    
    # 複製代碼
    cp -r src/* $APP_DIR/src/
    cp -r config/* $APP_DIR/config/ 2>/dev/null || true
    
    # 複製部署文件
    cp deployment/systemd/$SERVICE_NAME /etc/systemd/system/
    cp deployment/supervisor/*.conf /etc/supervisor/conf.d/ 2>/dev/null || true
    cp deployment/nginx/*.conf /etc/nginx/sites-available/ 2>/dev/null || true
    
    # 設置權限
    chown -R $APP_USER:$APP_USER $APP_DIR/src $APP_DIR/config
    chmod +x $APP_DIR/src/main.py 2>/dev/null || true
    
    log_success "應用代碼部署完成"
}

# 設置Python虛擬環境
setup_python_env() {
    log_info "設置Python虛擬環境..."
    
    # 創建虛擬環境
    sudo -u $APP_USER python3.11 -m venv $APP_DIR/venv
    
    # 升級pip
    sudo -u $APP_USER $APP_DIR/venv/bin/pip install --upgrade pip > /dev/null 2>&1
    
    # 安裝依賴
    if [[ -f "requirements.txt" ]]; then
        sudo -u $APP_USER $APP_DIR/venv/bin/pip install -r requirements.txt > /dev/null 2>&1
        log_success "Python依賴安裝完成"
    else
        log_warning "未找到requirements.txt，跳過依賴安裝"
    fi
}

# 配置系統服務
setup_services() {
    log_info "配置系統服務..."
    
    # 配置systemd服務
    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    
    # 配置Nginx (如果存在配置文件)
    if [[ -f /etc/nginx/sites-available/$APP_NAME ]]; then
        ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
        nginx -t && systemctl reload nginx
        log_success "Nginx配置完成"
    fi
    
    # 配置Supervisor (如果存在配置文件)
    if [[ -f /etc/supervisor/conf.d/$APP_NAME.conf ]]; then
        supervisorctl reread
        supervisorctl update
        log_success "Supervisor配置完成"
    fi
    
    log_success "系統服務配置完成"
}

# 系統優化
optimize_system() {
    log_info "進行系統優化..."
    
    # 調整檔案描述符限制
    cat >> /etc/security/limits.conf << EOF
$APP_USER soft nofile 65536
$APP_USER hard nofile 65536
$APP_USER soft nproc 32768
$APP_USER hard nproc 32768
EOF
    
    # 調整系統參數
    cat >> /etc/sysctl.conf << EOF
# 多進程系統優化
fs.file-max = 131072
vm.swappiness = 10
vm.overcommit_memory = 1
net.core.somaxconn = 65535
EOF
    
    sysctl -p > /dev/null 2>&1
    
    # 創建優化腳本
    cat > $APP_DIR/scripts/optimize.sh << 'EOF'
#!/bin/bash
# 運行時優化腳本

# CPU頻率調節器設置
for cpu in /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor; do
    [ -f "$cpu" ] && echo performance > "$cpu" 2>/dev/null || true
done

# 調整進程優先級
pgrep -f "outlook-summary" | xargs -I {} renice -5 {} 2>/dev/null || true

echo "系統優化完成"
EOF
    
    chmod +x $APP_DIR/scripts/optimize.sh
    chown $APP_USER:$APP_USER $APP_DIR/scripts/optimize.sh
    
    log_success "系統優化完成"
}

# 創建監控腳本
setup_monitoring() {
    log_info "設置監控腳本..."
    
    # 健康檢查腳本
    cat > $APP_DIR/scripts/health-check.sh << 'EOF'
#!/bin/bash
# 健康檢查腳本

HEALTH_URL="http://localhost:8000/health"
MAX_RETRIES=3
RETRY_DELAY=5

check_health() {
    local retry=0
    while [ $retry -lt $MAX_RETRIES ]; do
        if curl -f -s "$HEALTH_URL" > /dev/null 2>&1; then
            echo "✅ 服務健康檢查通過"
            return 0
        fi
        
        retry=$((retry + 1))
        echo "❌ 健康檢查失敗 ($retry/$MAX_RETRIES)，${RETRY_DELAY}秒後重試..."
        sleep $RETRY_DELAY
    done
    
    echo "❌ 服務健康檢查失敗，請檢查服務狀態"
    return 1
}

check_health
EOF
    
    # 資源監控腳本
    cat > $APP_DIR/scripts/monitor-resources.sh << 'EOF'
#!/bin/bash
# 資源監控腳本

LOG_FILE="/opt/outlook-summary/logs/system/resource-monitor.log"
ALERT_CPU_THRESHOLD=90
ALERT_MEMORY_THRESHOLD=90
ALERT_DISK_THRESHOLD=85

while true; do
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # CPU使用率
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    
    # 記憶體使用率
    memory_usage=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
    
    # 磁碟使用率
    disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1)
    
    # 記錄監控數據
    echo "[$timestamp] CPU: ${cpu_usage}%, Memory: ${memory_usage}%, Disk: ${disk_usage}%" >> "$LOG_FILE"
    
    # 檢查告警閾值
    if (( $(echo "$cpu_usage > $ALERT_CPU_THRESHOLD" | bc -l) )); then
        echo "[$timestamp] ALERT: High CPU usage: ${cpu_usage}%" >> "$LOG_FILE"
    fi
    
    if (( $(echo "$memory_usage > $ALERT_MEMORY_THRESHOLD" | bc -l) )); then
        echo "[$timestamp] ALERT: High memory usage: ${memory_usage}%" >> "$LOG_FILE"
    fi
    
    if [ "$disk_usage" -gt "$ALERT_DISK_THRESHOLD" ]; then
        echo "[$timestamp] ALERT: High disk usage: ${disk_usage}%" >> "$LOG_FILE"
    fi
    
    sleep 60
done
EOF
    
    chmod +x $APP_DIR/scripts/*.sh
    chown -R $APP_USER:$APP_USER $APP_DIR/scripts
    
    log_success "監控腳本設置完成"
}

# 創建管理腳本
create_management_scripts() {
    log_info "創建管理腳本..."
    
    # 服務管理腳本
    cat > /usr/local/bin/outlook-summary << 'EOF'
#!/bin/bash
# Outlook Summary 服務管理腳本

SERVICE_NAME="outlook-summary"
APP_DIR="/opt/outlook-summary"

case "$1" in
    start)
        echo "啟動 $SERVICE_NAME 服務..."
        systemctl start $SERVICE_NAME
        sleep 2
        systemctl status $SERVICE_NAME --no-pager
        ;;
    stop)
        echo "停止 $SERVICE_NAME 服務..."
        systemctl stop $SERVICE_NAME
        ;;
    restart)
        echo "重啟 $SERVICE_NAME 服務..."
        systemctl restart $SERVICE_NAME
        sleep 2
        systemctl status $SERVICE_NAME --no-pager
        ;;
    status)
        systemctl status $SERVICE_NAME --no-pager
        ;;
    logs)
        journalctl -u $SERVICE_NAME -f
        ;;
    health)
        $APP_DIR/scripts/health-check.sh
        ;;
    optimize)
        echo "執行系統優化..."
        $APP_DIR/scripts/optimize.sh
        ;;
    monitor)
        echo "開始資源監控 (Ctrl+C停止)..."
        $APP_DIR/scripts/monitor-resources.sh
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|health|optimize|monitor}"
        exit 1
        ;;
esac
EOF
    
    chmod +x /usr/local/bin/outlook-summary
    
    log_success "管理腳本創建完成"
}

# 最終驗證
verify_deployment() {
    log_info "驗證部署..."
    
    # 檢查文件權限
    if [[ ! -r $APP_DIR/src ]]; then
        log_error "應用目錄權限錯誤"
        exit 1
    fi
    
    # 檢查Python環境
    if ! sudo -u $APP_USER $APP_DIR/venv/bin/python --version > /dev/null 2>&1; then
        log_error "Python虛擬環境異常"
        exit 1
    fi
    
    # 檢查服務配置
    if ! systemctl is-enabled $SERVICE_NAME > /dev/null 2>&1; then
        log_error "服務未正確配置"
        exit 1
    fi
    
    log_success "部署驗證通過"
}

# 部署總結
deployment_summary() {
    echo ""
    echo "🎉 $APP_NAME 多進程系統部署完成！"
    echo ""
    echo "📋 部署信息:"
    echo "   應用目錄: $APP_DIR"
    echo "   服務用戶: $APP_USER"
    echo "   CPU核心數: $CPU_CORES"
    echo "   可用記憶體: ${MEMORY_GB}GB"
    echo ""
    echo "🚀 管理命令:"
    echo "   啟動服務: outlook-summary start"
    echo "   停止服務: outlook-summary stop"
    echo "   查看狀態: outlook-summary status"
    echo "   查看日誌: outlook-summary logs"
    echo "   健康檢查: outlook-summary health"
    echo "   系統優化: outlook-summary optimize"
    echo "   資源監控: outlook-summary monitor"
    echo ""
    echo "🔗 服務地址:"
    echo "   應用服務: http://localhost:8000"
    echo "   健康檢查: http://localhost:8000/health"
    echo "   系統指標: http://localhost:8000/metrics"
    echo ""
    echo "📝 重要提醒:"
    echo "   1. 首次啟動前請檢查配置文件"
    echo "   2. 建議運行系統優化: outlook-summary optimize"
    echo "   3. 可通過健康檢查驗證服務狀態"
    echo ""
}

# 執行部署流程
main() {
    check_requirements
    setup_user_and_dirs
    deploy_application
    setup_python_env
    setup_services
    optimize_system
    setup_monitoring
    create_management_scripts
    verify_deployment
    deployment_summary
}

# 錯誤處理
trap 'log_error "部署過程中發生錯誤，請檢查日誌"; exit 1' ERR

# 執行主函數
main "$@"