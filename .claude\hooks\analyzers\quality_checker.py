#!/usr/bin/env python3
"""
代碼品質檢查器
檢查代碼複雜度、可讀性、安全性和最佳實踐遵循情況
"""

import ast
import re
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import logging

class QualityChecker:
    """代碼品質檢查器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('QualityChecker')
        
        # 品質檢查配置
        self.max_function_lines = config.get('max_function_lines', 50)
        self.max_class_lines = config.get('max_class_lines', 200)
        self.max_cyclomatic_complexity = config.get('max_cyclomatic_complexity', 10)
        self.max_nesting_depth = config.get('max_nesting_depth', 4)
    
    async def analyze(self, files: Optional[List[str]] = None) -> Dict[str, Any]:
        """檢查代碼品質"""
        self.logger.info("✨ 開始代碼品質檢查...")
        
        if files:
            target_files = [self.repo_root / f for f in files if f.endswith('.py')]
        else:
            target_files = self._find_python_files()
        
        quality_issues = []
        quality_metrics = {
            'total_functions': 0,
            'complex_functions': 0,
            'long_functions': 0,
            'deep_nested_functions': 0,
            'total_classes': 0,
            'large_classes': 0,
            'security_issues': 0,
            'style_violations': 0
        }
        
        for file_path in target_files:
            try:
                file_issues = await self._analyze_file_quality(file_path)
                quality_issues.extend(file_issues)
                
                # 更新指標
                for issue in file_issues:
                    self._update_metrics(quality_metrics, issue)
                    
            except Exception as e:
                self.logger.warning(f"品質檢查失敗 {file_path}: {e}")
        
        # 按嚴重程度排序
        quality_issues.sort(key=lambda x: self._get_severity_weight(x['severity']), reverse=True)
        
        # 計算品質評分
        quality_score = self._calculate_quality_score(quality_metrics, len(target_files))
        
        result = {
            'total_files_analyzed': len(target_files),
            'quality_score': quality_score,
            'quality_issues': quality_issues,
            'metrics': quality_metrics,
            'recommendations': await self._generate_recommendations(quality_issues, quality_metrics)
        }
        
        self.logger.info(f"✅ 品質檢查完成: 發現 {len(quality_issues)} 個問題，品質評分: {quality_score:.1f}/100")
        return result
    
    async def _analyze_file_quality(self, file_path: Path) -> List[Dict[str, Any]]:
        """分析單一檔案的品質"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            tree = ast.parse(content)
            lines = content.split('\n')
            
            # 分析 AST 結構
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    issues.extend(await self._check_function_quality(node, file_path, lines))
                elif isinstance(node, ast.ClassDef):
                    issues.extend(await self._check_class_quality(node, file_path, lines))
            
            # 檢查安全問題
            security_issues = await self._check_security_issues(content, file_path)
            issues.extend(security_issues)
            
            # 檢查代碼風格
            style_issues = await self._check_code_style(content, file_path)
            issues.extend(style_issues)
            
            # 檢查最佳實踐
            best_practice_issues = await self._check_best_practices(tree, content, file_path)
            issues.extend(best_practice_issues)
            
        except SyntaxError as e:
            issues.append({
                'type': 'syntax_error',
                'severity': 'critical',
                'file_path': str(file_path.relative_to(self.repo_root)),
                'line': e.lineno,
                'message': f"語法錯誤: {e.msg}",
                'suggestion': '修復語法錯誤以繼續品質檢查'
            })
        
        return issues
    
    async def _check_function_quality(self, func_node: ast.FunctionDef, file_path: Path, lines: List[str]) -> List[Dict[str, Any]]:
        """檢查函數品質"""
        issues = []
        relative_path = str(file_path.relative_to(self.repo_root))
        
        # 計算函數行數
        start_line = func_node.lineno
        end_line = self._get_end_line(func_node, lines)
        func_lines = end_line - start_line + 1
        
        # 檢查函數長度
        if func_lines > self.max_function_lines:
            issues.append({
                'type': 'long_function',
                'severity': 'medium' if func_lines <= self.max_function_lines * 1.5 else 'high',
                'file_path': relative_path,
                'line': start_line,
                'function_name': func_node.name,
                'current_lines': func_lines,
                'max_lines': self.max_function_lines,
                'message': f"函數 '{func_node.name}' 過長 ({func_lines} 行)",
                'suggestion': '考慮將函數拆分為更小的子函數'
            })
        
        # 檢查圈複雜度
        complexity = self._calculate_cyclomatic_complexity(func_node)
        if complexity > self.max_cyclomatic_complexity:
            issues.append({
                'type': 'high_complexity',
                'severity': 'medium' if complexity <= self.max_cyclomatic_complexity * 1.5 else 'high',
                'file_path': relative_path,
                'line': start_line,
                'function_name': func_node.name,
                'complexity': complexity,
                'max_complexity': self.max_cyclomatic_complexity,
                'message': f"函數 '{func_node.name}' 圈複雜度過高 ({complexity})",
                'suggestion': '簡化條件邏輯，考慮使用策略模式或提早返回'
            })
        
        # 檢查嵌套深度
        max_depth = self._calculate_nesting_depth(func_node)
        if max_depth > self.max_nesting_depth:
            issues.append({
                'type': 'deep_nesting',
                'severity': 'medium',
                'file_path': relative_path,
                'line': start_line,
                'function_name': func_node.name,
                'nesting_depth': max_depth,
                'max_depth': self.max_nesting_depth,
                'message': f"函數 '{func_node.name}' 嵌套過深 ({max_depth} 層)",
                'suggestion': '使用提早返回或重構為多個函數減少嵌套'
            })
        
        # 檢查參數數量
        arg_count = len(func_node.args.args)
        if arg_count > 5:
            issues.append({
                'type': 'too_many_parameters',
                'severity': 'low' if arg_count <= 7 else 'medium',
                'file_path': relative_path,
                'line': start_line,
                'function_name': func_node.name,
                'parameter_count': arg_count,
                'message': f"函數 '{func_node.name}' 參數過多 ({arg_count} 個)",
                'suggestion': '考慮使用類別或字典來組合相關參數'
            })
        
        return issues
    
    async def _check_class_quality(self, class_node: ast.ClassDef, file_path: Path, lines: List[str]) -> List[Dict[str, Any]]:
        """檢查類別品質"""
        issues = []
        relative_path = str(file_path.relative_to(self.repo_root))
        
        # 計算類別行數
        start_line = class_node.lineno
        end_line = self._get_end_line(class_node, lines)
        class_lines = end_line - start_line + 1
        
        # 檢查類別大小
        if class_lines > self.max_class_lines:
            issues.append({
                'type': 'large_class',
                'severity': 'medium' if class_lines <= self.max_class_lines * 1.5 else 'high',
                'file_path': relative_path,
                'line': start_line,
                'class_name': class_node.name,
                'current_lines': class_lines,
                'max_lines': self.max_class_lines,
                'message': f"類別 '{class_node.name}' 過大 ({class_lines} 行)",
                'suggestion': '考慮拆分為多個類別或使用組合模式'
            })
        
        # 檢查方法數量
        methods = [node for node in class_node.body if isinstance(node, ast.FunctionDef)]
        method_count = len(methods)
        
        if method_count > 20:
            issues.append({
                'type': 'too_many_methods',
                'severity': 'medium',
                'file_path': relative_path,
                'line': start_line,
                'class_name': class_node.name,
                'method_count': method_count,
                'message': f"類別 '{class_node.name}' 方法過多 ({method_count} 個)",
                'suggestion': '考慮將相關方法提取到獨立的類別中'
            })
        
        # 檢查單一責任原則
        if method_count > 10:
            method_prefixes = defaultdict(int)
            for method in methods:
                if method.name.startswith('_'):
                    continue
                prefix = method.name.split('_')[0]
                method_prefixes[prefix] += 1
            
            diverse_prefixes = len([k for k, v in method_prefixes.items() if v >= 2])
            if diverse_prefixes > 3:
                issues.append({
                    'type': 'multiple_responsibilities',
                    'severity': 'medium',
                    'file_path': relative_path,
                    'line': start_line,
                    'class_name': class_node.name,
                    'responsibility_indicators': diverse_prefixes,
                    'message': f"類別 '{class_node.name}' 可能承擔多個責任",
                    'suggestion': '考慮按職責拆分類別'
                })
        
        return issues
    
    async def _check_security_issues(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """檢查安全問題"""
        issues = []
        relative_path = str(file_path.relative_to(self.repo_root))
        lines = content.split('\n')
        
        # 檢查常見安全問題
        security_patterns = {
            r'eval\s*\(': '使用 eval() 可能導致代碼注入',
            r'exec\s*\(': '使用 exec() 可能導致代碼注入',
            r'subprocess\.call\([^)]*shell\s*=\s*True': '使用 shell=True 可能導致命令注入',
            r'pickle\.loads?\s*\(': '使用 pickle 可能導致代碼執行攻擊',
            r'password\s*=\s*["\'][^"\']+["\']': '硬編碼密碼',
            r'api_key\s*=\s*["\'][^"\']+["\']': '硬編碼 API 金鑰',
            r'secret\s*=\s*["\'][^"\']+["\']': '硬編碼秘密資訊'
        }
        
        for i, line in enumerate(lines, 1):
            for pattern, message in security_patterns.items():
                if re.search(pattern, line, re.IGNORECASE):
                    issues.append({
                        'type': 'security_issue',
                        'severity': 'high',
                        'file_path': relative_path,
                        'line': i,
                        'message': message,
                        'code_snippet': line.strip(),
                        'suggestion': '使用更安全的替代方案或環境變數'
                    })
        
        return issues
    
    async def _check_code_style(self, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """檢查代碼風格"""
        issues = []
        relative_path = str(file_path.relative_to(self.repo_root))
        lines = content.split('\n')
        
        # 檢查行長度
        for i, line in enumerate(lines, 1):
            if len(line) > 100:
                issues.append({
                    'type': 'long_line',
                    'severity': 'low',
                    'file_path': relative_path,
                    'line': i,
                    'line_length': len(line),
                    'message': f"行過長 ({len(line)} 字符)",
                    'suggestion': '考慮分行或重構長表達式'
                })
        
        # 檢查空行規則
        consecutive_empty_lines = 0
        for i, line in enumerate(lines, 1):
            if not line.strip():
                consecutive_empty_lines += 1
                if consecutive_empty_lines > 2:
                    issues.append({
                        'type': 'too_many_blank_lines',
                        'severity': 'low',
                        'file_path': relative_path,
                        'line': i,
                        'message': '連續空行過多',
                        'suggestion': '移除多餘的空行'
                    })
            else:
                consecutive_empty_lines = 0
        
        return issues
    
    async def _check_best_practices(self, tree: ast.AST, content: str, file_path: Path) -> List[Dict[str, Any]]:
        """檢查最佳實踐"""
        issues = []
        relative_path = str(file_path.relative_to(self.repo_root))
        
        # 檢查文檔字符串
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                if not ast.get_docstring(node):
                    issues.append({
                        'type': 'missing_docstring',
                        'severity': 'low',
                        'file_path': relative_path,
                        'line': node.lineno,
                        'name': node.name,
                        'node_type': 'function' if isinstance(node, ast.FunctionDef) else 'class',
                        'message': f"缺少文檔字符串: {node.name}",
                        'suggestion': '添加描述功能、參數和返回值的文檔字符串'
                    })
        
        # 檢查異常處理
        bare_except_count = 0
        for node in ast.walk(tree):
            if isinstance(node, ast.ExceptHandler) and node.type is None:
                bare_except_count += 1
                issues.append({
                    'type': 'bare_except',
                    'severity': 'medium',
                    'file_path': relative_path,
                    'line': node.lineno,
                    'message': '使用裸露的 except 語句',
                    'suggestion': '指定具體的異常類型'
                })
        
        return issues
    
    def _calculate_cyclomatic_complexity(self, func_node: ast.FunctionDef) -> int:
        """計算圈複雜度"""
        complexity = 1  # 基礎複雜度
        
        for node in ast.walk(func_node):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, ast.With):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                # and/or 操作符增加複雜度
                complexity += len(node.values) - 1
        
        return complexity
    
    def _calculate_nesting_depth(self, func_node: ast.FunctionDef) -> int:
        """計算最大嵌套深度"""
        def calculate_depth(node, current_depth=0):
            max_depth = current_depth
            
            for child in ast.iter_child_nodes(node):
                child_depth = current_depth
                
                if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor, ast.With, ast.Try)):
                    child_depth += 1
                
                max_depth = max(max_depth, calculate_depth(child, child_depth))
            
            return max_depth
        
        return calculate_depth(func_node)
    
    def _get_end_line(self, node: ast.AST, lines: List[str]) -> int:
        """取得 AST 節點的結束行號"""
        if hasattr(node, 'end_lineno') and node.end_lineno:
            return node.end_lineno
        
        # 簡單估算
        start_line = node.lineno - 1
        if start_line >= len(lines):
            return len(lines)
        
        start_indent = len(lines[start_line]) - len(lines[start_line].lstrip())
        
        for i in range(start_line + 1, len(lines)):
            line = lines[i]
            if line.strip():
                current_indent = len(line) - len(line.lstrip())
                if current_indent <= start_indent:
                    return i
        
        return len(lines)
    
    def _update_metrics(self, metrics: Dict[str, int], issue: Dict[str, Any]):
        """更新品質指標"""
        issue_type = issue['type']
        
        if issue_type == 'long_function':
            metrics['long_functions'] += 1
        elif issue_type == 'high_complexity':
            metrics['complex_functions'] += 1
        elif issue_type == 'deep_nesting':
            metrics['deep_nested_functions'] += 1
        elif issue_type == 'large_class':
            metrics['large_classes'] += 1
        elif issue_type == 'security_issue':
            metrics['security_issues'] += 1
        elif issue_type in ['long_line', 'too_many_blank_lines']:
            metrics['style_violations'] += 1
    
    def _calculate_quality_score(self, metrics: Dict[str, int], total_files: int) -> float:
        """計算整體品質評分"""
        if total_files == 0:
            return 100.0
        
        # 基礎分數
        base_score = 100.0
        
        # 扣分項目 (每個文件的平均問題數)
        avg_issues_per_file = {
            'complex_functions': metrics['complex_functions'] / total_files,
            'long_functions': metrics['long_functions'] / total_files,
            'deep_nested_functions': metrics['deep_nested_functions'] / total_files,
            'large_classes': metrics['large_classes'] / total_files,
            'security_issues': metrics['security_issues'] / total_files,
            'style_violations': metrics['style_violations'] / total_files
        }
        
        # 權重和扣分
        penalties = {
            'complex_functions': 5,      # 複雜函數扣5分
            'long_functions': 3,         # 長函數扣3分
            'deep_nested_functions': 4,  # 深度嵌套扣4分
            'large_classes': 4,          # 大類別扣4分
            'security_issues': 10,       # 安全問題扣10分
            'style_violations': 1        # 風格問題扣1分
        }
        
        total_penalty = sum(
            avg_issues * penalties[issue_type]
            for issue_type, avg_issues in avg_issues_per_file.items()
        )
        
        final_score = max(0.0, base_score - total_penalty)
        return round(final_score, 1)
    
    def _get_severity_weight(self, severity: str) -> int:
        """獲取嚴重程度權重"""
        weights = {
            'critical': 4,
            'high': 3,
            'medium': 2,
            'low': 1
        }
        return weights.get(severity, 1)
    
    async def _generate_recommendations(self, quality_issues: List[Dict[str, Any]], 
                                      metrics: Dict[str, int]) -> List[Dict[str, Any]]:
        """生成改進建議"""
        recommendations = []
        
        # 按問題類型統計
        issue_counts = defaultdict(int)
        for issue in quality_issues:
            issue_counts[issue['type']] += 1
        
        # 生成針對性建議
        if issue_counts['high_complexity'] > 0:
            recommendations.append({
                'type': 'reduce_complexity',
                'priority': 'high',
                'title': '降低函數複雜度',
                'description': f"發現 {issue_counts['high_complexity']} 個高複雜度函數",
                'actions': [
                    '使用提早返回減少嵌套',
                    '提取子函數',
                    '使用策略模式替代複雜條件'
                ]
            })
        
        if issue_counts['long_function'] > 0:
            recommendations.append({
                'type': 'split_functions',
                'priority': 'medium',
                'title': '拆分長函數',
                'description': f"發現 {issue_counts['long_function']} 個過長函數",
                'actions': [
                    '按職責拆分函數',
                    '提取通用邏輯',
                    '使用類別組織相關函數'
                ]
            })
        
        if issue_counts['security_issue'] > 0:
            recommendations.append({
                'type': 'fix_security',
                'priority': 'critical',
                'title': '修復安全問題',
                'description': f"發現 {issue_counts['security_issue']} 個安全問題",
                'actions': [
                    '移除硬編碼的敏感資訊',
                    '使用安全的替代方案',
                    '添加輸入驗證'
                ]
            })
        
        return recommendations
    
    def _find_python_files(self) -> List[Path]:
        """找出所有 Python 檔案"""
        files = []
        ignore_dirs = {'.git', '__pycache__', 'venv', 'venv_win_3_11_12', '.claude'}
        
        for py_file in self.repo_root.rglob('*.py'):
            if any(ignore_dir in py_file.parts for ignore_dir in ignore_dirs):
                continue
            files.append(py_file)
        
        return files