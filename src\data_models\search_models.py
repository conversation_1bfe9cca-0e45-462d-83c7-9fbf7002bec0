"""搜尋相關的資料傳輸物件 (DTOs)"""

from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class TimeRangeType(str, Enum):
    """時間範圍類型"""
    LAST_WEEK = "last_week"
    LAST_MONTH = "last_month"
    LAST_3_MONTHS = "last_3_months"
    LAST_6_MONTHS = "last_6_months"
    CURRENT_QUARTER = "current_quarter"
    CUSTOM = "custom"


class ProductSearchRequest(BaseModel):
    """產品搜尋請求模型"""
    product_name: str = Field(..., description="產品名稱", min_length=1)
    search_directory: str = Field(default="auto", description="指定搜尋目錄 (auto/ETD/FT/GTK/JCAP/JCET/JSSI/JCAP_JCET/AMAT/ASML/LAM/TEL/KLA/all)")
    time_range_type: TimeRangeType = Field(default=TimeRangeType.LAST_6_MONTHS, description="時間範圍類型")
    custom_start_date: Optional[datetime] = Field(None, description="自訂開始日期（當 time_range_type 為 custom 時使用）")
    custom_end_date: Optional[datetime] = Field(None, description="自訂結束日期（當 time_range_type 為 custom 時使用）")
    file_types: Optional[List[str]] = Field(None, description="檔案類型篩選（如 ['.csv', '.xlsx']）")
    min_size_mb: Optional[float] = Field(None, description="最小檔案大小（MB）", ge=0)
    max_size_mb: Optional[float] = Field(None, description="最大檔案大小（MB）", ge=0)
    include_directories: bool = Field(True, description="是否包含目錄")
    max_results: int = Field(1000, description="最大結果數量", ge=1, le=10000)


class FileInfoDTO(BaseModel):
    """檔案資訊 DTO"""
    path: str
    name: str
    size: int
    size_mb: float
    modified_time: str
    file_type: str
    is_directory: bool


class ProductSearchResponse(BaseModel):
    """產品搜尋回應模型"""
    status: str
    product_name: str
    product_folder: Optional[str] = None
    matched_files: List[FileInfoDTO] = []
    total_files: int = 0
    total_files_in_directory: int = 0  # 目錄中的總檔案數
    total_size_mb: float = 0.0
    search_duration: float = 0.0
    filters_applied: dict = {}
    error_message: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class SearchTaskStatus(str, Enum):
    """搜尋任務狀態"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class SearchTaskResponse(BaseModel):
    """搜尋任務回應模型"""
    task_id: str
    product_name: str
    status: SearchTaskStatus
    created_at: str
    completed_at: Optional[str] = None
    result: Optional[ProductSearchResponse] = None
    progress: float = Field(0.0, description="進度百分比 (0-100)", ge=0, le=100)


class SearchProgressUpdate(BaseModel):
    """搜尋進度更新模型"""
    task_id: str
    progress: float = Field(..., ge=0, le=100)
    current_path: Optional[str] = None
    files_processed: int = 0
    message: Optional[str] = None


class SmartSearchRequest(BaseModel):
    """智慧搜尋請求模型"""
    query: str = Field(..., description="自然語言搜尋查詢", min_length=1)
    path: str = Field(default="\\\\************\\test_log", description="網路資料夾路徑")
    max_results: int = Field(default=100, description="最大結果數量", ge=1, le=1000)


class SmartSearchResponse(BaseModel):
    """智慧搜尋回應模型"""
    status: str
    query: str
    interpretation: dict = {}
    results: List[dict] = []
    analysis: dict = {}
    suggestions: List[dict] = []
    search_duration: float = 0.0
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    error_message: Optional[str] = None