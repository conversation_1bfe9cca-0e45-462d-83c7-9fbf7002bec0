# 設計文件：Outlook 摘要系統

## 系統概述

Outlook 摘要系統是一個企業級的半導體製造測試資料處理平台，採用現代化的六角架構設計。系統從傳統 VBA Excel 解決方案成功遷移至 Python 架構，提供高效、可靠且可擴展的自動化資料處理能力。

### 系統規模與成熟度
- **專案規模**：19,811 個檔案，274 個 Python 檔案
- **開發歷程**：133 次提交，3 位貢獻者
- **程式碼結構**：238 個函數，446 個類別
- **測試覆蓋**：39 個測試檔案，1,130 個文檔檔案
- **廠商支援**：12+ 個半導體測試廠商完整整合
- **架構模式**：六角架構（Ports and Adapters）

### 核心設計原則
- **領域驅動設計**：清晰的業務邏輯分離
- **依賴反轉**：高層模組不依賴低層模組
- **單一職責**：每個元件專注於特定功能
- **開放封閉**：對擴展開放，對修改封閉
- **測試驅動**：TDD 方法確保程式碼品質
- **企業級整合**：統一服務管理和配置

## 系統架構設計

### 企業級整合服務架構

```
┌─────────────────────────────────────────────────────────────────┐
│                企業級整合服務管理器 (Port 5555)                    │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ IntegratedServiceManager (start_integrated_services.py)    │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │UnifiedConfig│  │ServiceInteg │  │UnifiedLogger        │ │ │
│  │  │Manager      │  │rator        │  │                     │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │ (WSGIMiddleware & Router 整合)
┌───────────────────────────▼─────────────────────────────────────┐
│                    整合服務層                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │Flask 郵件   │  │FastAPI EQC  │  │任務排程器   │  │網路API  │ │
│  │服務 /inbox  │  │服務 /ft-eqc │  │/scheduler   │  │/network │ │
│  │(Port 5001)  │  │(Port 5002)  │  │(Port 5003)  │  │(Port 5004)│ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────────┐
│                    展示層 (src/presentation/)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │API Layer    │  │Web Layer    │  │CLI Layer    │  │Admin UI │ │
│  │/api/*       │  │/web/*       │  │/cli/*       │  │/admin   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────────┐
│                    應用層 (src/application/)                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │Use Cases    │  │Services     │  │Interfaces   │  │Workflows│ │
│  │/use_cases/  │  │/services/   │  │/interfaces/ │  │         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────────┐
│                    領域層 (src/domain/)                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │Entities     │  │Services     │  │Value Objects│  │Exception│ │
│  │/entities/   │  │/services/   │  │/value_obj/  │  │/except/ │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────────┐
│                基礎設施層 (src/infrastructure/)                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │Adapters     │  │Parsers      │  │Database     │  │LLM      │ │
│  │/adapters/   │  │/parsers/    │  │/database/   │  │/llm/    │ │
│  │• Email      │  │• 12+ 廠商   │  │• SQLite     │  │• Ollama │ │
│  │• Outlook    │  │• LLM混合    │  │• PostgreSQL │  │• Grok   │ │
│  │• POP3       │  │• 工廠模式   │  │• 連接池     │  │• 統一   │ │
│  │• Excel      │  │• 註冊表     │  │• 遷移       │  │• 客戶端 │ │
│  │• 檔案處理   │  │             │  │             │  │         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 核心元件設計

### 展示層設計

#### 企業級整合服務管理器
```python
class IntegratedServiceManager:
    """整合服務管理器 - 統一管理所有服務"""
    
    def __init__(self):
        self.config_manager = init_config_manager()
        self.unified_logger = init_unified_logger()
        self.service_integrator = get_service_integrator()
        self.route_manager = get_route_manager()
        
        self.app: Optional[FastAPI] = None
        self.server_process: Optional[threading.Thread] = None
        self.is_running = False
    
    async def initialize(self) -> bool:
        """初始化所有服務"""
        # 驗證配置
        validation_result = self.config_manager.validate_configuration()
        if not validation_result["valid"]:
            return False
        
        # 創建整合應用程式
        self.app = self.service_integrator.create_integrated_app()
        
        # 添加管理介面
        self._add_management_interface()
        
        # 啟動所有服務
        await self.service_integrator.start_all_services()
        
        return True
    
    def _add_management_interface(self):
        """添加管理介面"""
        @self.app.get("/admin", response_class=HTMLResponse)
        async def admin_dashboard():
            """管理後台首頁"""
            return self._generate_admin_dashboard()
        
        @self.app.get("/admin/api/status")
        async def get_service_status():
            """獲取所有服務狀態"""
            return self._get_all_service_status()
```

#### 統一配置管理系統
```python
class UnifiedConfigManager:
    """統一配置管理器 - 整合現有配置系統與新的服務整合需求"""
    
    def __init__(self, env: str = "development"):
        self.base_config = BaseConfigManager(env)
        self.service_config = self._load_service_config()
        self._apply_env_overrides()
    
    def _load_service_config(self) -> IntegratedServiceConfig:
        """載入服務配置"""
        return IntegratedServiceConfig(
            main_port=5555,  # 主整合端口
            services={
                "inbox": ServiceConfig(
                    name="郵件收件夾服務",
                    service_type=ServiceType.FLASK,
                    port=5001,
                    path_prefix="/inbox"
                ),
                "ft_eqc": ServiceConfig(
                    name="FT-EQC處理服務", 
                    service_type=ServiceType.FASTAPI,
                    port=5002,
                    path_prefix="/ft-eqc"
                ),
                "scheduler": ServiceConfig(
                    name="增強任務排程器",
                    service_type=ServiceType.FASTAPI,
                    port=5003,
                    path_prefix="/scheduler"
                ),
                "network": ServiceConfig(
                    name="網路瀏覽器API",
                    service_type=ServiceType.FASTAPI,
                    port=5004,
                    path_prefix="/network"
                )
            }
        )
    
    def get_enabled_services(self) -> Dict[str, ServiceConfig]:
        """獲取所有啟用的服務"""
        return {
            name: config 
            for name, config in self.service_config.services.items()
            if config.enabled
        }
```

#### 服務整合器設計
```python
class ServiceIntegrator:
    """服務整合器 - 將所有服務整合到單一 FastAPI 應用中"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.app: Optional[FastAPI] = None
        self.service_processes: Dict[str, ServiceProcess] = {}
        self.service_apps: Dict[str, Any] = {}
    
    def create_integrated_app(self) -> FastAPI:
        """建立整合應用程式"""
        self.app = FastAPI(
            title="企業級整合服務平台",
            description="整合所有郵件處理、文件分析和任務排程服務",
            version="1.0.0"
        )
        
        # 設定 CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config_manager.get_cors_origins(),
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"]
        )
        
        # 整合所有啟用的服務
        self._integrate_services()
        
        return self.app
    
    def _integrate_services(self):
        """整合所有服務"""
        enabled_services = self.config_manager.get_enabled_services()
        
        for service_name, service_config in enabled_services.items():
            if service_config.service_type == ServiceType.FLASK:
                self._integrate_flask_service(service_name, service_config)
            elif service_config.service_type == ServiceType.FASTAPI:
                self._integrate_fastapi_service(service_name, service_config)
    
    def _integrate_flask_service(self, service_name: str, config: ServiceConfig):
        """整合 Flask 服務 - 使用 WSGIMiddleware"""
        if service_name == "inbox":
            from email_inbox_app import create_flask_app
            flask_app = create_flask_app()
            
            # 通過 WSGIMiddleware 整合
            self.app.mount(
                config.path_prefix,
                WSGIMiddleware(flask_app)
            )
    
    def _integrate_fastapi_service(self, service_name: str, config: ServiceConfig):
        """整合 FastAPI 服務 - 使用 Router"""
        if service_name == "ft_eqc":
            from src.presentation.api.ft_eqc_api import app as ft_eqc_app
            router = self._convert_app_to_router(ft_eqc_app, config.path_prefix)
            
        elif service_name == "network":
            from src.presentation.api.network_browser_api import app as network_app
            router = self._convert_app_to_router(network_app, config.path_prefix)
        
        if router:
            self.app.include_router(
                router,
                prefix=config.path_prefix,
                tags=[config.name]
            )
```
### 應用層設計

#### 郵件處理用例
```python
class EmailProcessingUseCase:
    """郵件處理核心用例"""
    
    def __init__(self, email_repository, vendor_service, llm_service):
        self.email_repository = email_repository
        self.vendor_service = vendor_service
        self.llm_service = llm_service
    
    async def process_email(self, email: Email) -> ProcessingResult:
        """處理單封郵件的完整流程"""
        # 1. 廠商識別
        vendor = await self.vendor_service.identify_vendor(email)
        
        # 2. 資料解析
        if vendor:
            result = await vendor.parse_email(email)
        else:
            result = await self.llm_service.parse_email(email)
        
        # 3. 資料驗證和儲存
        validated_result = await self.validate_and_store(result)
        
        return validated_result
```

#### EQC 處理用例
```python
class EQCProcessingUseCase:
    """EQC 一鍵完成處理用例"""
    
    def __init__(self):
        self.stage1_processor = Stage1FileIntegrator()
        self.stage2_processor = Stage2AdvancedProcessor()
    
    async def execute_eqc_process(self, request: EQCRequest) -> EQCResult:
        """執行完整的 EQC 處理流程"""
        
        # 第一階段：檔案整合 (目標: < 2.5 秒)
        stage1_result = await self.stage1_processor.integrate_files(request.files)
        
        # 第二階段：8 步驟處理
        stage2_result = await self.stage2_processor.process_advanced(
            stage1_result, request.settings
        )
        
        return EQCResult(stage1_result, stage2_result)
```

### 領域層設計

#### 核心實體設計
```python
@dataclass
class Email:
    """郵件實體"""
    id: str
    sender: str
    subject: str
    received_time: datetime
    content: str
    attachments: List[Attachment]
    vendor_code: Optional[str] = None
    parse_status: ParseStatus = ParseStatus.PENDING
    confidence_score: Optional[float] = None
    extraction_method: Optional[str] = None
    
    def is_processed(self) -> bool:
        return self.parse_status == ParseStatus.PARSED
    
    def get_vendor_identifier(self) -> Optional[str]:
        """獲取廠商識別符"""
        return self.vendor_code

@dataclass
class ProcessingResult:
    """處理結果實體"""
    email_id: str
    mo: Optional[str]  # 製造訂單
    lot: Optional[str]  # 批次號
    pd: Optional[str]   # 產品代碼
    yield_value: Optional[float]  # 良率
    extracted_data: Dict[str, Any]
    processing_time: float
    confidence_score: float
```

#### 廠商服務設計
```python
class VendorService:
    """廠商識別和路由服務"""
    
    def __init__(self):
        self.parsers = {
            # 主要廠商解析器
            'GTK': GTKParser(),          # ft hold, ft lot
            'ETD': ETDParser(),          # anf
            'XAHT': XAHTParser(),        # tianshui, 西安
            'JCET': JCETParser(),        # jcet
            'LINGSEN': LINGSENParser(),  # lingsen
            
            # 擴展廠商解析器
            'MSEC': MSECParser(),        # MSEC 廠商
            'NFME': NFMEParser(),        # NFME 廠商
            'NANOTECH': NanotechParser(), # 奈米科技廠商
            'TSHT': TSHTParser(),        # TSHT 廠商
            'CHUZHOU': ChuzhouParser(),  # 滁州廠商
            'SUQIAN': SuqianParser(),    # 宿遷廠商
            
            # LLM 混合解析器 (可選)
            'LLM': HybridLLMParser()     # 通用 LLM 解析器
        }
        
        # 解析器工廠和註冊表
        self.parser_factory = ParserFactory()
        self.parser_registry = ParserRegistry()
    
    async def identify_vendor(self, email: Email) -> Optional[VendorParser]:
        """識別郵件對應的廠商 - 使用工廠模式"""
        best_parser, vendor_result = self.parser_factory.identify_vendor(email)
        
        if vendor_result.is_identified and vendor_result.confidence_score > 0.5:
            return best_parser
        return None
    
    def get_vendor_statistics(self) -> Dict[str, VendorStats]:
        """獲取各廠商統計資料"""
        stats = {}
        for vendor_code in self.parser_registry.get_registered_vendors():
            parser = self.parser_registry.get_parser(vendor_code)
            stats[vendor_code] = parser.get_statistics()
        return stats
    
    def get_supported_vendors(self) -> List[str]:
        """獲取所有支援的廠商列表"""
        return self.parser_factory.get_supported_vendors()
```

### 基礎設施層設計

#### 資料庫適配器設計
```python
class EmailDatabase:
    """郵件資料庫適配器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection_pool = self._create_connection_pool()
    
    # 核心資料表
    EMAIL_TABLE = """
    CREATE TABLE IF NOT EXISTS emails (
        id TEXT PRIMARY KEY,
        sender TEXT NOT NULL,
        subject TEXT NOT NULL,
        received_time TEXT NOT NULL,
        content TEXT,
        vendor_code TEXT,
        parse_status TEXT DEFAULT 'pending',
        confidence_score REAL,
        extraction_method TEXT,
        mo TEXT,
        lot TEXT,
        pd TEXT,
        yield_value REAL,
        is_read INTEGER DEFAULT 0,
        is_processed INTEGER DEFAULT 0,
        has_attachments INTEGER DEFAULT 0,
        attachment_count INTEGER DEFAULT 0,
        parsed_at TEXT,
        llm_analysis_timestamp TEXT,
        llm_service_used TEXT,
        parse_error TEXT
    )
    """
    
    EMAIL_PROCESS_STATUS_TABLE = """
    CREATE TABLE IF NOT EXISTS email_process_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email_id TEXT NOT NULL,
        process_step TEXT NOT NULL,
        status TEXT NOT NULL,
        start_time TEXT,
        end_time TEXT,
        error_message TEXT,
        step_data TEXT,
        FOREIGN KEY (email_id) REFERENCES emails (id)
    )
    """
    
    SENDER_TABLE = """
    CREATE TABLE IF NOT EXISTS senders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email_address TEXT UNIQUE NOT NULL,
        display_name TEXT,
        vendor_code TEXT,
        total_emails INTEGER DEFAULT 0,
        processed_emails INTEGER DEFAULT 0,
        last_email_time TEXT,
        is_whitelisted INTEGER DEFAULT 0
    )
    """
```

#### LLM 整合適配器
```python
class UnifiedLLMClient:
    """統一 LLM 客戶端"""
    
    def __init__(self):
        self.ollama_client = OllamaClient()
        self.grok_client = GrokClient()
        self.fallback_strategy = FallbackStrategy()
    
    async def analyze_email(self, email: Email) -> LLMAnalysisResult:
        """使用 LLM 分析郵件"""
        try:
            # 優先使用 Ollama 本地服務
            if self.ollama_client.is_available():
                result = await self.ollama_client.analyze(email)
                result.service_used = 'ollama'
                return result
            
            # 備用 Grok API 服務
            elif self.grok_client.is_available():
                result = await self.grok_client.analyze(email)
                result.service_used = 'grok'
                return result
            
            # 降級到傳統解析
            else:
                return await self.fallback_strategy.parse(email)
                
        except Exception as e:
            logger.error(f"LLM analysis failed: {e}")
            return await self.fallback_strategy.parse(email)
```

## 廠商解析器架構

### 解析器工廠和註冊表系統
```python
class ParserFactory:
    """解析器工廠 - 自動註冊和管理所有解析器"""
    
    def __init__(self):
        self.registry = ParserRegistry()
        self._register_all_parsers()
    
    def _register_all_parsers(self):
        """自動註冊所有解析器"""
        # 註冊傳統解析器
        parsers_to_register = [
            ("GTK", "src.infrastructure.parsers.gtk_parser", "GTKParser"),
            ("ETD", "src.infrastructure.parsers.etd_parser", "ETDParser"),
            ("JCET", "src.infrastructure.parsers.jcet_parser", "JCETParser"),
            ("LINGSEN", "src.infrastructure.parsers.lingsen_parser", "LINGSENParser"),
            ("MSEC", "src.infrastructure.parsers.msec_parser", "MSECParser"),
            ("NFME", "src.infrastructure.parsers.nfme_parser", "NFMEParser"),
            ("NANOTECH", "src.infrastructure.parsers.nanotech_parser", "NanotechParser"),
            ("TSHT", "src.infrastructure.parsers.tsht_parser", "TSHTParser"),
            ("CHUZHOU", "src.infrastructure.parsers.chuzhou_parser", "ChuzhouParser"),
            ("SUQIAN", "src.infrastructure.parsers.suqian_parser", "SuqianParser"),
            ("XAHT", "src.infrastructure.parsers.xaht_parser", "XAHTParser")
        ]
        
        for vendor_code, module_path, class_name in parsers_to_register:
            try:
                module = __import__(module_path, fromlist=[class_name])
                parser_class = getattr(module, class_name)
                parser_instance = parser_class()
                self.registry.register_parser(vendor_code, parser_instance)
            except Exception as e:
                logger.error(f"{vendor_code} 解析器註冊失敗: {e}")
        
        # 註冊 LLM 混合解析器 (如果啟用)
        if os.getenv('LLM_PARSING_ENABLED', 'false').lower() == 'true':
            self._register_llm_parsers()
    
    def identify_vendor(self, email_data: EmailData) -> Tuple[Optional[BaseParser], VendorIdentificationResult]:
        """識別廠商並返回最佳解析器 - 兩階段策略"""
        best_parser = None
        best_result = VendorIdentificationResult(
            vendor_code=None,
            vendor_name=None,
            confidence_score=0.0,
            matching_patterns=[],
            is_identified=False
        )
        
        # 第一階段：只讓傳統解析器參與廠商識別
        traditional_parsers = {k: v for k, v in self.registry.get_all_parsers().items()
                             if not k.endswith('_LLM')}
        
        for vendor_code, parser in traditional_parsers.items():
            try:
                result = parser.identify_vendor(email_data)
                if result.confidence_score > best_result.confidence_score:
                    best_parser = parser
                    best_result = result
            except Exception as e:
                logger.error(f"廠商識別錯誤 ({vendor_code}): {e}")
                continue
        
        return best_parser, best_result
    
    def parse_email(self, email_data: EmailData) -> Tuple[VendorIdentificationResult, EmailParsingResult]:
        """解析郵件 - 完整流程，支援 LLM fallback"""
        # 第一階段：識別廠商（僅傳統解析器）
        best_parser, vendor_result = self.identify_vendor(email_data)
        
        if not vendor_result.is_identified or best_parser is None:
            parsing_result = EmailParsingResult(
                is_success=False,
                error_message="無法識別郵件廠商",
                validation_errors=["未找到匹配的廠商模式"],
                extraction_method="failed"
            )
            return vendor_result, parsing_result
        
        # 第二階段：解析郵件（遵守 LLM_PARSING_MODE 設定）
        context = ParsingContext(
            email_data=email_data,
            vendor_code=vendor_result.vendor_code,
            parsing_strategy=ParsingStrategy.HYBRID
        )
        
        try:
            llm_enabled = os.getenv('LLM_PARSING_ENABLED', 'false').lower() == 'true'
            llm_parsing_mode = os.getenv('LLM_PARSING_MODE', 'fallback').lower()
            
            if llm_enabled and llm_parsing_mode == 'fallback':
                # fallback 模式：先用傳統解析器，失敗時使用 LLM
                parsing_result = best_parser.parse_email(context)
                if parsing_result.extraction_method is None:
                    parsing_result.extraction_method = "traditional"
                
                if not parsing_result.is_success:
                    # 使用混合 LLM 解析器
                    from src.infrastructure.parsers.llm_parser import HybridLLMParser
                    hybrid_parser = HybridLLMParser(best_parser)
                    llm_parsing_result = hybrid_parser.parse_email(context)
                    
                    if llm_parsing_result.is_success:
                        parsing_result = llm_parsing_result
            else:
                # 傳統解析
                parsing_result = best_parser.parse_email(context)
                if parsing_result.extraction_method is None:
                    parsing_result.extraction_method = "traditional"
        
        except Exception as e:
            logger.error(f"解析錯誤: {e}")
            parsing_result = best_parser.parse_email(context)
            if parsing_result.extraction_method is None:
                parsing_result.extraction_method = "traditional"
        
        return vendor_result, parsing_result
```

### 基礎解析器設計
```python
class BaseParser(ABC):
    """廠商解析器基礎類別"""
    
    @property
    @abstractmethod
    def vendor_code(self) -> str:
        """廠商代碼"""
        pass
    
    @property
    @abstractmethod
    def vendor_name(self) -> str:
        """廠商名稱"""
        pass
    
    @property
    @abstractmethod
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        pass
    
    @abstractmethod
    def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
        """識別廠商"""
        pass
    
    @abstractmethod
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件"""
        pass
    
    def can_parse(self, email_data: EmailData) -> bool:
        """檢查是否可以解析此郵件"""
        try:
            result = self.identify_vendor(email_data)
            return result.is_identified and result.confidence_score > 0.5
        except Exception:
            return False
```

### 具體廠商解析器實現
```python
class GTKParser(VendorParser):
    """GTK 廠商解析器"""
    
    @property
    def vendor_code(self) -> str:
        return "GTK"
    
    @property
    def vendor_name(self) -> str:
        return "GTK 半導體"
    
    @property
    def supported_patterns(self) -> List[str]:
        return ['ft hold', 'ft lot', 'gtk']
    
    def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
        """GTK 廠商識別邏輯"""
        confidence_score, matched_patterns = self.calculate_confidence_score(email_data)
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=confidence_score > self.get_confidence_threshold()
        )

class JCETParser(VendorParser):
    """JCET 廠商解析器 - 最高成功率"""
    
    def __init__(self):
        super().__init__()
        self.kui_gyc_handler = KUIGYCModeHandler()
    
    @property
    def vendor_code(self) -> str:
        return "JCET"
    
    @property
    def vendor_name(self) -> str:
        return "江蘇長電科技"
    
    @property
    def supported_patterns(self) -> List[str]:
        return ['jcet', '长电', '長電']
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """JCET 特定解析，支援 KUI/GYC 模式"""
        if self.kui_gyc_handler.detect_mode(context.email_data):
            return self.kui_gyc_handler.parse(context)
        else:
            return self.standard_parse(context)

# 其他廠商解析器：MSEC, NFME, NANOTECH, TSHT, CHUZHOU, SUQIAN, XAHT 等
# 每個都有類似的結構，但有各自特定的識別模式和解析邏輯
```

## EQC 處理系統設計

### 兩階段處理架構
```python
class EQCProcessor:
    """EQC 處理器主控制器"""
    
    def __init__(self):
        self.stage1 = Stage1FileIntegrator()
        self.stage2 = Stage2AdvancedProcessor()
        self.performance_manager = AdvancedPerformanceManager()
    
    async def process(self, request: EQCRequest) -> EQCResult:
        """執行完整的兩階段處理"""
        
        # 記錄開始時間
        start_time = time.time()
        
        # 第一階段：檔案整合 (目標 < 2.5 秒)
        stage1_start = time.time()
        stage1_result = await self.stage1.integrate_files(request.files)
        stage1_time = time.time() - stage1_start
        
        # 第二階段：8 步驟處理
        stage2_result = await self.stage2.process_eight_steps(
            stage1_result, request.settings
        )
        
        total_time = time.time() - start_time
        
        # 記錄效能資料
        self.performance_manager.record_processing_time(
            stage1_time, len(stage1_result.files), total_time
        )
        
        return EQCResult(stage1_result, stage2_result, total_time)

class Stage2AdvancedProcessor:
    """第二階段 8 步驟處理器"""
    
    def __init__(self):
        self.steps = [
            EQCTotalDataGenerator(),      # 步驟 1
            CodeRegionDetector(),         # 步驟 2
            DualSearchMechanism(),        # 步驟 3
            InsEqcRtData2Processor(),     # 步驟 4
            ExcelGeneratorMarker(),       # 步驟 5
            FileRenamer(),                # 步驟 6
            CSVToExcelConverter(),        # 步驟 7
            CompleteReportGenerator()     # 步驟 8
        ]
    
    async def process_eight_steps(self, stage1_result, settings) -> Stage2Result:
        """執行 8 個處理步驟"""
        current_data = stage1_result
        step_results = []
        
        for i, step in enumerate(self.steps, 1):
            step_start = time.time()
            
            try:
                step_result = await step.process(current_data, settings)
                step_time = time.time() - step_start
                
                step_results.append(StepResult(
                    step_number=i,
                    step_name=step.__class__.__name__,
                    processing_time=step_time,
                    success=True,
                    data=step_result
                ))
                
                current_data = step_result
                
            except Exception as e:
                step_results.append(StepResult(
                    step_number=i,
                    step_name=step.__class__.__name__,
                    processing_time=time.time() - step_start,
                    success=False,
                    error=str(e)
                ))
                raise ProcessingError(f"Step {i} failed: {e}")
        
        return Stage2Result(step_results, current_data)
```

### 雙重搜尋機制
```python
class DualSearchMechanism:
    """雙重搜尋機制 - 提高 CODE 匹配成功率"""
    
    def __init__(self):
        self.main_region_matcher = MainRegionMatcher()
        self.backup_region_mapper = BackupRegionMapper()
        self.statistics = DualSearchStats()
    
    async def process(self, data, settings) -> SearchResult:
        """執行雙重搜尋"""
        
        # 主要區間精確匹配
        main_result = await self.main_region_matcher.match(
            data, settings.main_region
        )
        
        if main_result.success:
            self.statistics.record_main_success()
            return main_result
        
        # 備用區間映射匹配
        backup_result = await self.backup_region_mapper.match(
            data, settings.backup_region
        )
        
        if backup_result.success:
            self.statistics.record_backup_success()
            return backup_result
        
        # 兩種方法都失敗
        self.statistics.record_failure()
        raise SearchError("Both main and backup search failed")
```

## Excel 處理系統設計

### 8 步驟處理流程
```python
class ExcelProcessor:
    """Excel 處理系統主控制器"""
    
    def __init__(self):
        self.bin1_protector = BIN1ProtectionMechanism()
        self.performance_manager = AdvancedPerformanceManager()
        self.vectorized_processor = VectorizedProcessor()
        
    async def process_excel(self, file_path: str) -> ExcelResult:
        """執行 8 步驟 Excel 處理"""
        
        steps = [
            FileLoader(),           # 1. 檔案載入
            FormatDetector(),       # 2. 格式檢測
            DataCleaner(),          # 3. 資料清洗
            DataTransformer(),      # 4. 資料轉換
            DataCalculator(),       # 5. 資料計算
            ExcelGenerator(),       # 6. Excel 生成
            StyleApplicator(),      # 7. 樣式應用
            FileOutputter()         # 8. 檔案輸出
        ]
        
        current_data = file_path
        step_results = []
        
        for i, step in enumerate(steps, 1):
            step_start = time.time()
            
            # 在關鍵步驟啟用 BIN1 保護
            if isinstance(step, (DataTransformer, DataCalculator, ExcelGenerator)):
                current_data = await self.bin1_protector.protect_during_step(
                    step, current_data
                )
            else:
                current_data = await step.process(current_data)
            
            step_time = time.time() - step_start
            step_results.append(ExcelStepResult(i, step.__class__.__name__, step_time))
        
        return ExcelResult(step_results, current_data)

class BIN1ProtectionMechanism:
    """BIN1 保護機制 - 99.99% 保護準確率"""
    
    def __init__(self):
        self.protection_accuracy = 99.99
        self.protected_tests = set()
        self.protection_log = []
    
    async def protect_during_step(self, step, data):
        """在處理步驟中保護 BIN1 設備"""
        
        # 識別 BIN1 關鍵測試項目
        bin1_devices = self.identify_bin1_devices(data)
        
        # 建立保護快照
        protection_snapshot = self.create_protection_snapshot(bin1_devices)
        
        try:
            # 執行處理步驟
            result = await step.process(data)
            
            # 驗證 BIN1 資料完整性
            if self.verify_bin1_integrity(result, protection_snapshot):
                self.record_protection_success()
                return result
            else:
                # 恢復 BIN1 資料
                restored_result = self.restore_bin1_data(result, protection_snapshot)
                self.record_protection_recovery()
                return restored_result
                
        except Exception as e:
            # 異常情況下恢復 BIN1 資料
            self.record_protection_error(e)
            return self.restore_bin1_data(data, protection_snapshot)
```

## 監控系統設計

### 即時監控架構
```python
class MonitoringSystem:
    """即時監控系統"""
    
    def __init__(self):
        self.system_monitor = SystemStatusMonitor()
        self.performance_monitor = PerformanceMonitor()
        self.alert_manager = AlertManager()
        self.dashboard_service = DashboardService()
    
    def start_monitoring(self):
        """啟動監控系統"""
        
        # 系統狀態監控 (每 30 秒)
        self.system_monitor.start_monitoring(interval=30)
        
        # 效能監控 (每 60 秒)
        self.performance_monitor.start_monitoring(interval=60)
        
        # 警報系統
        self.alert_manager.start_alert_processing()
        
        # 儀表板服務
        self.dashboard_service.start_websocket_service()

class SystemStatusMonitor:
    """系統狀態監控器"""
    
    def __init__(self):
        self.services = {
            'flask': {'port': 5000, 'name': 'Flask 郵件服務'},
            'fastapi': {'port': 8010, 'name': 'FastAPI 處理服務'},
            'network_api': {'port': 8009, 'name': '網路瀏覽器 API'}
        }
    
    async def check_all_services(self) -> SystemStatus:
        """檢查所有服務狀態"""
        service_statuses = {}
        
        for service_id, config in self.services.items():
            status = await self.check_service_health(config['port'])
            service_statuses[service_id] = ServiceStatus(
                name=config['name'],
                port=config['port'],
                status=status['status'],
                response_time=status['response_time'],
                last_checked=datetime.now()
            )
        
        return SystemStatus(
            services=service_statuses,
            resources=await self.get_system_resources(),
            timestamp=datetime.now()
        )
```

## 資料流設計

### 郵件處理資料流
```
郵件接收 → 廠商識別 → 資料解析 → 資料驗證 → 資料儲存 → 報告生成
    ↓           ↓           ↓           ↓           ↓           ↓
POP3/Outlook  VendorService  Parser    Validator   Database   ReportGen
    ↓           ↓           ↓           ↓           ↓           ↓
EmailAdapter  GTK/ETD/...   LLMClient  DataQuality EmailDB    ExcelOutput
```

### EQC 處理資料流
```
檔案上傳 → 第一階段整合 → 第二階段處理 → 報告生成
    ↓           ↓              ↓             ↓
FileUpload  FileIntegrator  8StepProcessor  ReportGen
    ↓           ↓              ↓             ↓
TempStorage  FileValidator   DualSearch     ExcelOutput
                             CodeDetection
                             BIN1Protection
```

## 效能設計

### 效能目標
- **郵件處理**：< 30 秒/封
- **EQC 第一階段**：< 2.5 秒
- **Excel 處理**：< 60 秒/MB
- **API 回應**：< 2 秒
- **系統資源**：CPU < 80%, Memory < 70%

### 效能優化策略
```python
class PerformanceOptimizer:
    """效能優化器"""
    
    def __init__(self):
        self.vectorized_ops = VectorizedOperations()
        self.chunk_processor = ChunkProcessor(chunk_size=10000)
        self.cache_manager = CacheManager()
        self.connection_pool = ConnectionPool(max_connections=20)
    
    def optimize_data_processing(self, data):
        """優化資料處理效能"""
        
        # 使用向量化操作
        if len(data) > 1000:
            return self.vectorized_ops.process(data)
        
        # 大型資料集分塊處理
        if len(data) > 10000:
            return self.chunk_processor.process(data)
        
        # 標準處理
        return self.standard_process(data)
```

## 安全性設計

### 資料安全
```python
class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.encryption_service = EncryptionService()
        self.access_control = AccessControlService()
        self.audit_logger = AuditLogger()
    
    def secure_email_content(self, email: Email) -> SecureEmail:
        """保護郵件內容安全"""
        
        # 敏感資料加密
        encrypted_content = self.encryption_service.encrypt(email.content)
        
        # 存取控制
        access_token = self.access_control.generate_token(email.id)
        
        # 審計日誌
        self.audit_logger.log_access(email.id, "content_access")
        
        return SecureEmail(email.id, encrypted_content, access_token)
```

---

**文件版本**：1.0  
**建立日期**：2025-01-30  
**最後更新**：2025-01-30  
**狀態**：待審核