<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增強任務排程器儀表板</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <style>
        body { 
            font-family: 'Microsoft JhengHei', Arial, sans-serif; 
            margin: 0; 
            padding: 20px;
            background-color: #f5f5f5;
        }
        .dashboard-container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .dashboard-header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .dashboard-header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.2em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .stat-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .controls-section {
            padding: 30px;
            background: white;
        }
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .control-group h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.1em;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-info { background: #17a2b8; }
        .btn-info:hover { background: #138496; }
        .task-list {
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }
        .task-list h4 {
            background: #667eea;
            color: white;
            margin: 0;
            padding: 15px 20px;
            font-size: 1.1em;
        }
        .task-item {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .task-item:last-child {
            border-bottom: none;
        }
        .task-info {
            flex: 1;
        }
        .task-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .task-details {
            font-size: 0.9em;
            color: #666;
        }
        .task-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-completed { background: #cce7ff; color: #004085; }
        .status-error { background: #f8d7da; color: #721c24; }
        .refresh-indicator {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        .refresh-indicator.loading {
            display: block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <h1>📅 增強任務排程器</h1>
            <div class="subtitle">企業級任務管理與自動化平台</div>
        </header>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>系統狀態</h3>
                <div class="stat-value" id="system-status">檢查中...</div>
                <div class="stat-label">排程器服務狀態</div>
            </div>
            <div class="stat-card">
                <h3>活躍任務</h3>
                <div class="stat-value" id="active-tasks">-</div>
                <div class="stat-label">當前執行中的任務</div>
            </div>
            <div class="stat-card">
                <h3>排程任務</h3>
                <div class="stat-value" id="scheduled-tasks">-</div>
                <div class="stat-label">等待執行的任務</div>
            </div>
            <div class="stat-card">
                <h3>下次執行</h3>
                <div class="stat-value" id="next-execution">-</div>
                <div class="stat-label">GTK 任務下次運行時間</div>
            </div>
        </div>

        <div class="controls-section">
            <h2>任務控制面板</h2>
            
            <div class="controls-grid">
                <div class="control-group">
                    <h4>📧 郵件處理</h4>
                    <button class="btn btn-success" onclick="processEmail()">處理郵件</button>
                    <button class="btn btn-info" onclick="checkEmailStatus()">檢查狀態</button>
                </div>
                
                <div class="control-group">
                    <h4>⏱️ GTK 排程</h4>
                    <button class="btn btn-success" onclick="getNextExecutions()">查看下次執行</button>
                    <button class="btn btn-warning" onclick="rescheduleGTK()">重新排程</button>
                </div>
                
                <div class="control-group">
                    <h4>📊 系統監控</h4>
                    <button class="btn btn-info" onclick="refreshStatus()">刷新狀態</button>
                    <button class="btn" onclick="viewLogs()">查看日誌</button>
                </div>
                
                <div class="control-group">
                    <h4>🔧 系統管理</h4>
                    <button class="btn btn-warning" onclick="clearCache()">清除快取</button>
                    <button class="btn btn-danger" onclick="resetScheduler()">重置排程器</button>
                </div>
            </div>

            <div class="refresh-indicator" id="loading-indicator">
                <div class="spinner"></div>正在處理請求...
            </div>

            <div class="task-list">
                <h4>📋 當前排程任務</h4>
                <div id="tasks-container">
                    <div class="task-item">
                        <div class="task-info">
                            <div class="task-name">載入中...</div>
                            <div class="task-details">正在獲取任務列表</div>
                        </div>
                        <span class="task-status status-pending">載入中</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API 基礎路徑
        const API_BASE = '/scheduler';
        
        // 載入指示器控制
        function showLoading() {
            document.getElementById('loading-indicator').classList.add('loading');
        }
        
        function hideLoading() {
            document.getElementById('loading-indicator').classList.remove('loading');
        }
        
        // API 調用封裝
        async function apiCall(endpoint, method = 'GET', data = null) {
            showLoading();
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.message || '請求失敗');
                }
                
                return result;
            } catch (error) {
                console.error('API調用錯誤:', error);
                alert('操作失敗: ' + error.message);
                throw error;
            } finally {
                hideLoading();
            }
        }
        
        // 郵件處理
        async function processEmail() {
            try {
                // 調用正確的 inbox parser API 來處理郵件
                const response = await fetch('/inbox/api/parser/emails/batch-parse', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        limit: 50,
                        failed_only: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success) {
                    alert('郵件處理已啟動: ' + (result.message || `成功處理 ${result.parsed_count || 0} 封郵件`));
                } else {
                    alert('郵件處理失敗: ' + (result.error || '未知錯誤'));
                }

                refreshStatus();
            } catch (error) {
                console.error('處理郵件失敗:', error);
                alert('處理郵件失敗: ' + error.message);
            }
        }

        // 檢查郵件狀態
        async function checkEmailStatus() {
            try {
                const result = await apiCall('/api/scheduler/status');
                alert('系統狀態: ' + JSON.stringify(result, null, 2));
            } catch (error) {
                console.error('獲取狀態失敗:', error);
                alert('獲取狀態失敗: ' + error.message);
            }
        }
        
        // 獲取下次執行時間
        async function getNextExecutions() {
            try {
                const result = await apiCall('/api/scheduler/gtk/next_executions');
                const executions = result.data || [];
                let message = '下次執行時間:\n';
                executions.forEach(exec => {
                    message += `${exec.task}: ${exec.next_run}\n`;
                });
                alert(message || '沒有排程任務');
                updateNextExecution(executions);
            } catch (error) {
                console.error('獲取執行時間失敗:', error);
            }
        }
        
        // 重新排程 GTK
        async function rescheduleGTK() {
            try {
                // 這個功能需要實現對應的 API 端點
                alert('GTK 重新排程功能正在開發中');
                refreshStatus();
            } catch (error) {
                console.error('重新排程失敗:', error);
                alert('重新排程失敗: ' + error.message);
            }
        }
        
        // 刷新狀態
        async function refreshStatus() {
            try {
                const [statusResult, tasksResult] = await Promise.all([
                    apiCall('/api/scheduler/status'),
                    apiCall('/api/scheduler/tasks/scheduled')
                ]);
                
                updateStatusDisplay(statusResult);
                updateTasksList(tasksResult.tasks || []);
            } catch (error) {
                console.error('刷新狀態失敗:', error);
            }
        }
        
        // 更新狀態顯示
        function updateStatusDisplay(status) {
            document.getElementById('system-status').textContent = status.status || '未知';
            document.getElementById('active-tasks').textContent = status.active_tasks || '0';
            document.getElementById('scheduled-tasks').textContent = status.scheduled_tasks || '0';
        }
        
        // 更新下次執行時間
        function updateNextExecution(executions) {
            const nextExecElement = document.getElementById('next-execution');
            if (executions && executions.length > 0) {
                const nextExec = executions[0];
                nextExecElement.textContent = nextExec.next_run || '未排程';
            } else {
                nextExecElement.textContent = '無排程';
            }
        }
        
        // 更新任務列表
        function updateTasksList(tasks) {
            const container = document.getElementById('tasks-container');
            
            if (tasks.length === 0) {
                container.innerHTML = '<div class="task-item"><div class="task-info"><div class="task-name">無排程任務</div><div class="task-details">系統中沒有活躍的排程任務</div></div><span class="task-status status-pending">空閒</span></div>';
                return;
            }
            
            container.innerHTML = tasks.map(task => `
                <div class="task-item">
                    <div class="task-info">
                        <div class="task-name">${task.name || '未命名任務'}</div>
                        <div class="task-details">
                            下次執行: ${task.next_run || '未排程'} | 
                            狀態: ${task.status || '未知'}
                        </div>
                    </div>
                    <span class="task-status status-${getStatusClass(task.status)}">${task.status || '未知'}</span>
                </div>
            `).join('');
        }
        
        // 獲取狀態樣式類
        function getStatusClass(status) {
            switch(status?.toLowerCase()) {
                case 'active': return 'active';
                case 'running': return 'active';
                case 'completed': return 'completed';
                case 'error': return 'error';
                case 'failed': return 'error';
                default: return 'pending';
            }
        }
        
        // 查看日誌
        function viewLogs() {
            window.open('/scheduler/logs', '_blank');
        }
        
        // 清除快取
        async function clearCache() {
            if (confirm('確定要清除系統快取嗎？這可能會影響正在運行的任務。')) {
                try {
                    // 這個功能需要實現對應的 API 端點
                    alert('清除快取功能正在開發中');
                } catch (error) {
                    console.error('清除快取失敗:', error);
                    alert('清除快取失敗: ' + error.message);
                }
            }
        }

        // 重置排程器
        async function resetScheduler() {
            if (confirm('確定要重置排程器嗎？這將停止所有正在運行的任務！')) {
                try {
                    // 這個功能需要實現對應的 API 端點
                    alert('重置排程器功能正在開發中');
                    refreshStatus();
                } catch (error) {
                    console.error('重置失敗:', error);
                    alert('重置失敗: ' + error.message);
                }
            }
        }
        
        // 自動刷新
        function startAutoRefresh() {
            // 每30秒自動刷新一次狀態
            setInterval(refreshStatus, 30000);
        }
        
        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('排程器儀表板已載入');
            refreshStatus();
            startAutoRefresh();
        });
    </script>
</body>
</html>