#!/usr/bin/env python3
"""
Import 依賴分析器
分析項目中的 import 關係，找出重複實現和可優化的依賴
"""

import ast
import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict, Counter
import logging
import importlib.util

class ImportAnalyzer:
    """Import 依賴分析器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('ImportAnalyzer')
        
        # 內建模組和常見第三方模組
        self.builtin_modules = set(sys.builtin_module_names)
        self.common_third_party = {
            'pandas', 'numpy', 'requests', 'flask', 'fastapi', 'pytest',
            'sqlalchemy', 'pydantic', 'asyncio', 'datetime', 'json', 'os',
            'sys', 'pathlib', 'typing', 'collections', 'itertools'
        }
    
    async def analyze(self, files: Optional[List[str]] = None) -> Dict[str, Any]:
        """分析 Import 依賴關係"""
        self.logger.info("📦 開始 Import 依賴分析...")
        
        if files:
            target_files = [self.repo_root / f for f in files if f.endswith('.py')]
        else:
            target_files = self._find_python_files()
        
        # 分析每個檔案的 imports
        file_imports = {}
        project_modules = await self._discover_project_modules()
        all_functions = await self._discover_all_functions()
        
        for file_path in target_files:
            try:
                imports = await self._analyze_file_imports(file_path)
                file_imports[str(file_path.relative_to(self.repo_root))] = imports
            except Exception as e:
                self.logger.warning(f"分析檔案 imports 失敗 {file_path}: {e}")
        
        # 分析依賴關係
        dependency_graph = await self._build_dependency_graph(file_imports)
        circular_deps = await self._detect_circular_dependencies(dependency_graph)
        unused_imports = await self._find_unused_imports(file_imports, target_files)
        optimization_opportunities = await self._find_optimization_opportunities(
            file_imports, project_modules, all_functions
        )
        
        result = {
            'total_files_analyzed': len(target_files),
            'file_imports': file_imports,
            'dependency_graph': dependency_graph,
            'circular_dependencies': circular_deps,
            'unused_imports': unused_imports,
            'optimization_opportunities': optimization_opportunities,
            'statistics': {
                'total_imports': sum(len(imports['imports']) for imports in file_imports.values()),
                'unique_modules': len(set(
                    imp['module'] for imports in file_imports.values() 
                    for imp in imports['imports']
                )),
                'circular_dependency_count': len(circular_deps),
                'optimization_potential': len(optimization_opportunities)
            }
        }
        
        self.logger.info(f"✅ Import 分析完成: 發現 {len(optimization_opportunities)} 個優化機會")
        return result
    
    async def _analyze_file_imports(self, file_path: Path) -> Dict[str, Any]:
        """分析單一檔案的 imports"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            tree = ast.parse(content)
            imports = []
            from_imports = []
            used_names = set()
            defined_functions = []
            defined_classes = []
            
            # 收集 imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append({
                            'type': 'import',
                            'module': alias.name,
                            'alias': alias.asname,
                            'line': node.lineno
                        })
                
                elif isinstance(node, ast.ImportFrom):
                    module_name = node.module or ''
                    for alias in node.names:
                        from_imports.append({
                            'type': 'from_import',
                            'module': module_name,
                            'name': alias.name,
                            'alias': alias.asname,
                            'line': node.lineno
                        })
                
                elif isinstance(node, ast.Name):
                    used_names.add(node.id)
                
                elif isinstance(node, ast.FunctionDef):
                    defined_functions.append({
                        'name': node.name,
                        'line': node.lineno,
                        'args': [arg.arg for arg in node.args.args]
                    })
                
                elif isinstance(node, ast.ClassDef):
                    defined_classes.append({
                        'name': node.name,
                        'line': node.lineno,
                        'methods': [item.name for item in node.body if isinstance(item, ast.FunctionDef)]
                    })
            
            return {
                'imports': imports + from_imports,
                'used_names': list(used_names),
                'defined_functions': defined_functions,
                'defined_classes': defined_classes,
                'total_imports': len(imports) + len(from_imports)
            }
            
        except Exception as e:
            self.logger.error(f"解析檔案 imports 失敗 {file_path}: {e}")
            return {'imports': [], 'used_names': [], 'defined_functions': [], 'defined_classes': []}
    
    async def _discover_project_modules(self) -> Dict[str, Any]:
        """發現項目中的所有模組"""
        modules = {}
        
        for py_file in self._find_python_files():
            module_path = str(py_file.relative_to(self.repo_root))
            module_name = module_path.replace('/', '.').replace('\\', '.').replace('.py', '')
            
            try:
                with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                # 收集模組中定義的函數和類別
                functions = []
                classes = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        if not any(isinstance(parent, ast.ClassDef) 
                                 for parent in ast.walk(tree) 
                                 if hasattr(parent, 'body') and node in getattr(parent, 'body', [])):
                            functions.append(node.name)
                    
                    elif isinstance(node, ast.ClassDef):
                        classes.append(node.name)
                
                modules[module_name] = {
                    'file_path': module_path,
                    'functions': functions,
                    'classes': classes
                }
                
            except Exception as e:
                self.logger.warning(f"分析模組失敗 {py_file}: {e}")
        
        return modules
    
    async def _discover_all_functions(self) -> Dict[str, List[Dict[str, Any]]]:
        """發現所有可重用的函數"""
        functions = defaultdict(list)
        
        for py_file in self._find_python_files():
            try:
                with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        # 簡單的函數簽名分析
                        func_info = {
                            'name': node.name,
                            'file': str(py_file.relative_to(self.repo_root)),
                            'line': node.lineno,
                            'args': [arg.arg for arg in node.args.args],
                            'returns': self._get_return_type(node),
                            'complexity': self._estimate_complexity(node)
                        }
                        
                        functions[node.name].append(func_info)
                        
            except Exception as e:
                self.logger.warning(f"分析函數失敗 {py_file}: {e}")
        
        return dict(functions)
    
    async def _build_dependency_graph(self, file_imports: Dict[str, Any]) -> Dict[str, List[str]]:
        """建立依賴關係圖"""
        graph = defaultdict(list)
        
        for file_path, imports_data in file_imports.items():
            dependencies = []
            
            for imp in imports_data['imports']:
                module = imp['module']
                
                # 只關注項目內部的依賴
                if self._is_internal_module(module):
                    dependencies.append(module)
            
            graph[file_path] = dependencies
        
        return dict(graph)
    
    async def _detect_circular_dependencies(self, dependency_graph: Dict[str, List[str]]) -> List[List[str]]:
        """檢測循環依賴"""
        def dfs(node, path, visited, rec_stack):
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for neighbor in dependency_graph.get(node, []):
                if neighbor not in visited:
                    cycle = dfs(neighbor, path.copy(), visited, rec_stack)
                    if cycle:
                        return cycle
                elif neighbor in rec_stack:
                    # 找到循環
                    cycle_start = path.index(neighbor)
                    return path[cycle_start:] + [neighbor]
            
            rec_stack.remove(node)
            return None
        
        visited = set()
        cycles = []
        
        for node in dependency_graph:
            if node not in visited:
                cycle = dfs(node, [], visited, set())
                if cycle:
                    cycles.append(cycle)
        
        return cycles
    
    async def _find_unused_imports(self, file_imports: Dict[str, Any], files: List[Path]) -> List[Dict[str, Any]]:
        """找出未使用的 imports"""
        unused = []
        
        for file_path, imports_data in file_imports.items():
            used_names = set(imports_data['used_names'])
            
            for imp in imports_data['imports']:
                import_name = imp.get('alias') or imp.get('name') or imp['module'].split('.')[-1]
                
                if import_name not in used_names:
                    unused.append({
                        'file': file_path,
                        'line': imp['line'],
                        'import': imp,
                        'suggestion': 'remove_unused_import'
                    })
        
        return unused
    
    async def _find_optimization_opportunities(self, file_imports: Dict[str, Any], 
                                            project_modules: Dict[str, Any],
                                            all_functions: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """找出優化機會"""
        opportunities = []
        
        # 1. 檢查重複實現的函數
        for func_name, implementations in all_functions.items():
            if len(implementations) > 1:
                # 檢查是否為相似的實現
                similar_implementations = await self._find_similar_implementations(implementations)
                if similar_implementations:
                    opportunities.append({
                        'type': 'duplicate_function',
                        'function_name': func_name,
                        'implementations': similar_implementations,
                        'suggestion': 'consolidate_to_shared_module',
                        'potential_savings': len(similar_implementations) - 1
                    })
        
        # 2. 檢查可以使用現有模組的情況
        for file_path, imports_data in file_imports.items():
            defined_functions = [f['name'] for f in imports_data['defined_functions']]
            
            for func_name in defined_functions:
                if func_name in all_functions:
                    existing_implementations = [
                        impl for impl in all_functions[func_name] 
                        if impl['file'] != file_path
                    ]
                    
                    if existing_implementations:
                        opportunities.append({
                            'type': 'reuse_existing_function',
                            'file': file_path,
                            'function_name': func_name,
                            'existing_implementations': existing_implementations,
                            'suggestion': 'import_instead_of_reimplement'
                        })
        
        # 3. 檢查 import 組織
        for file_path, imports_data in file_imports.items():
            if imports_data['total_imports'] > 20:
                opportunities.append({
                    'type': 'too_many_imports',
                    'file': file_path,
                    'import_count': imports_data['total_imports'],
                    'suggestion': 'refactor_or_split_file'
                })
        
        return opportunities
    
    async def _find_similar_implementations(self, implementations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """找出相似的函數實現"""
        similar = []
        
        if len(implementations) < 2:
            return similar
        
        # 簡單的相似度檢查：參數數量和名稱
        for i, impl1 in enumerate(implementations):
            for impl2 in implementations[i+1:]:
                # 檢查參數相似度
                args1 = set(impl1['args'])
                args2 = set(impl2['args'])
                
                if len(args1) == len(args2) and len(args1 & args2) / len(args1 | args2) > 0.7:
                    if impl1 not in similar:
                        similar.append(impl1)
                    if impl2 not in similar:
                        similar.append(impl2)
        
        return similar
    
    def _is_internal_module(self, module_name: str) -> bool:
        """檢查是否為項目內部模組"""
        if not module_name:
            return False
        
        # 檢查是否為相對 import 或 src 開頭
        return (module_name.startswith('.') or 
                module_name.startswith('src') or
                module_name not in self.builtin_modules and 
                module_name.split('.')[0] not in self.common_third_party)
    
    def _get_return_type(self, func_node: ast.FunctionDef) -> Optional[str]:
        """取得函數返回類型"""
        if func_node.returns:
            if isinstance(func_node.returns, ast.Name):
                return func_node.returns.id
            elif isinstance(func_node.returns, ast.Constant):
                return str(func_node.returns.value)
        return None
    
    def _estimate_complexity(self, func_node: ast.FunctionDef) -> str:
        """估算函數複雜度"""
        node_count = len(list(ast.walk(func_node)))
        
        if node_count < 20:
            return 'low'
        elif node_count < 50:
            return 'medium'
        else:
            return 'high'
    
    def _find_python_files(self) -> List[Path]:
        """找出所有 Python 檔案"""
        files = []
        ignore_dirs = {'.git', '__pycache__', 'venv', 'venv_win_3_11_12', '.claude'}
        
        for py_file in self.repo_root.rglob('*.py'):
            if any(ignore_dir in py_file.parts for ignore_dir in ignore_dirs):
                continue
            files.append(py_file)
        
        return files

# 需要 import sys 模組
import sys