#!/usr/bin/env python3
"""
全域 Unicode 編碼修復
一次性解決所有 Windows cp950 編碼問題
"""

import sys
import os
import locale

def setup_unicode_environment():
    """
    設置全域 Unicode 環境，避免 cp950 編碼錯誤
    """
    # 1. 設置環境變數
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

    # 2. 重新設置標準輸出編碼
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            if sys.stdout.encoding != 'utf-8':
                sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        except Exception:
            pass

    if hasattr(sys.stderr, 'reconfigure'):
        try:
            if sys.stderr.encoding != 'utf-8':
                sys.stderr.reconfigure(encoding='utf-8', errors='replace')
        except Exception:
            pass

    # 3. 設置預設編碼
    if hasattr(sys, 'setdefaultencoding'):
        try:
            sys.setdefaultencoding('utf-8')
        except Exception:
            pass

    # 4. 設置 locale
    try:
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except Exception:
        try:
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        except Exception:
            pass
    
    # 4. 覆蓋 print 函數以安全處理 Unicode
    import builtins
    original_print = builtins.print
    
    def safe_print(*args, **kwargs):
        """安全的 print 函數，自動處理 Unicode 字符"""
        try:
            # 直接使用原始print，依賴系統UTF-8配置
            original_print(*args, **kwargs)
        except UnicodeEncodeError:
            # 如果出現編碼錯誤，轉換為安全格式
            safe_args = []
            for arg in args:
                if isinstance(arg, str):
                    # 使用更保守的替換策略
                    safe_str = arg.encode('ascii', errors='replace').decode('ascii')
                    safe_args.append(safe_str)
                else:
                    safe_args.append(str(arg))
            original_print(*safe_args, **kwargs)
    
    # 替換內建的 print 函數
    builtins.print = safe_print
    
    print("Unicode 環境已設置完成")

def apply_unicode_patches():
    """
    應用 Unicode 補丁到常用模組
    """
    # 補丁 logging 模組
    import logging
    
    # 創建自定義的 Handler 來處理 Unicode
    class SafeStreamHandler(logging.StreamHandler):
        def emit(self, record):
            try:
                msg = self.format(record)
                # 安全地處理 Unicode 字符
                safe_msg = msg.encode('ascii', errors='replace').decode('ascii')
                self.stream.write(safe_msg + '\n')
                self.flush()
            except Exception:
                self.handleError(record)
    
    # 替換所有現有的 StreamHandler
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        if isinstance(handler, logging.StreamHandler):
            root_logger.removeHandler(handler)
            safe_handler = SafeStreamHandler(handler.stream)
            safe_handler.setFormatter(handler.formatter)
            safe_handler.setLevel(handler.level)
            root_logger.addHandler(safe_handler)

if __name__ == "__main__":
    setup_unicode_environment()
    apply_unicode_patches()
    print("全域 Unicode 修復已完成")