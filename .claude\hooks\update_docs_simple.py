#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台文檔自動更新腳本
解決 Windows/Linux 路徑和命令相容性問題
"""

import os
import sys
import json
import subprocess
from datetime import datetime
from pathlib import Path
import shutil

# 設定 Windows 環境的編碼
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    os.environ['PYTHONIOENCODING'] = 'utf-8'

def safe_print(text):
    """安全的列印函數，處理編碼問題"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果有編碼問題，移除 emoji 並重試
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)
    except Exception:
        # 最後的備案
        print("[INFO] Document update in progress...")

def main():
    """主函數"""
    print("🚀 開始自動更新專案文檔...")
    
    # 確保在正確的專案目錄
    script_dir = Path(__file__).parent.parent.parent
    os.chdir(script_dir)
    
    print(f"📂 工作目錄: {script_dir}")
    
    try:
        # 取得當前時間
        current_date = datetime.now().strftime('%Y-%m-%d')
        current_time = datetime.now().strftime('%H:%M:%S')
        current_datetime = f"{current_date} {current_time}"
        
        print("📊 收集專案統計資料...")
        
        # 收集專案統計
        stats = collect_project_stats(script_dir)
        
        # 更新各種文檔
        update_readme(script_dir, current_date, stats)
        update_changelog(script_dir, current_date, stats)
        update_project_info(script_dir, current_datetime, stats)
        create_doc_summary(script_dir, current_datetime, stats)
        
        print("🎉 文檔自動更新完成！")
        print_summary(stats)
        
    except Exception as e:
        print(f"❌ 更新失敗: {e}")
        sys.exit(1)

def collect_project_stats(project_root):
    """收集專案統計資料"""
    stats = {
        'total_files': 0,
        'python_files': 0,
        'test_files': 0,
        'doc_files': 0,
        'function_count': 0,
        'class_count': 0,
        'commit_count': 'N/A',
        'contributors': 'N/A',
        'current_branch': 'main'
    }
    
    try:
        # 計算檔案數量
        all_files = list(project_root.rglob('*'))
        stats['total_files'] = len([f for f in all_files 
                                  if f.is_file() 
                                  and '.git' not in f.parts 
                                  and '.venv' not in f.parts
                                  and '__pycache__' not in f.parts])
        
        # Python 檔案
        python_files = list(project_root.rglob('*.py'))
        python_files = [f for f in python_files 
                       if '.venv' not in f.parts and '__pycache__' not in f.parts]
        stats['python_files'] = len(python_files)
        
        # 測試檔案
        test_files = [f for f in python_files if 'test' in f.name.lower()]
        stats['test_files'] = len(test_files)
        
        # 文檔檔案
        doc_files = list(project_root.rglob('*.md'))
        stats['doc_files'] = len(doc_files)
        
        # 計算函數和類別數量
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    stats['function_count'] += content.count('\ndef ')
                    stats['class_count'] += content.count('\nclass ')
            except:
                continue
        
        # Git 統計
        if shutil.which('git') and (project_root / '.git').exists():
            try:
                # 提交數量
                result = subprocess.run(['git', 'rev-list', '--all', '--count'], 
                                      capture_output=True, text=True, cwd=project_root)
                if result.returncode == 0:
                    stats['commit_count'] = result.stdout.strip()
                
                # 貢獻者數量
                result = subprocess.run(['git', 'log', '--format=%an'], 
                                      capture_output=True, text=True, cwd=project_root)
                if result.returncode == 0:
                    contributors = set(result.stdout.strip().split('\n'))
                    stats['contributors'] = len(contributors) if contributors != {''} else 0
                
                # 當前分支
                result = subprocess.run(['git', 'branch', '--show-current'], 
                                      capture_output=True, text=True, cwd=project_root)
                if result.returncode == 0:
                    stats['current_branch'] = result.stdout.strip() or 'main'
                    
            except:
                pass
                
    except Exception as e:
        print(f"⚠️ 統計收集部分失敗: {e}")
    
    return stats

def update_readme(project_root, current_date, stats):
    """更新 README.md"""
    readme_path = project_root / 'README.md'
    
    if not readme_path.exists():
        print("⚠️ README.md 不存在，跳過更新")
        return
    
    try:
        with open(readme_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新最新更新時間
        import re
        content = re.sub(
            r'## \[ROCKET\] \*\*最新更新.*?\*\*',
            f'## [ROCKET] **最新更新 ({current_date})**',
            content
        )
        
        # 更新統計表格
        stats_table = f"""| 項目 | 數量 | 備註 |
|------|------|------|
| 📁 總檔案數 | {stats['total_files']} | 專案所有檔案 |
| 🐍 Python 檔案 | {stats['python_files']} | 源碼檔案數 |
| 📝 程式碼行數 | N/A | Python 程式碼行數 |
| 🧪 測試檔案 | {stats['test_files']} | 單元/整合測試 |
| 📚 文檔檔案 | {stats['doc_files']} | Markdown 文檔 |
| 🏭 支援廠商 | 6 | 半導體測試廠商 |
| 📦 函數數量 | {stats['function_count']} | Python 函數總數 |
| 🔧 類別數量 | {stats['class_count']} | Python 類別總數 |
| 🌿 Git 提交 | {stats['commit_count']} | 版本控制歷史 |
| 👥 貢獻者 | {stats['contributors']} | 開發團隊成員 |"""
        
        # 替換統計表格
        pattern = r'## 📊 專案統計.*?(?=\n##|\n$|\Z)'
        replacement = f"## 📊 專案統計\n{stats_table}\n"
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ README.md 已更新")
        
    except Exception as e:
        print(f"❌ README.md 更新失敗: {e}")

def update_changelog(project_root, current_date, stats):
    """更新 CHANGELOG.md"""
    changelog_path = project_root / 'CHANGELOG.md'
    
    # 獲取最近的 Git 變更
    recent_changes = ["- 自動更新專案文檔"]
    if shutil.which('git') and (project_root / '.git').exists():
        try:
            result = subprocess.run(['git', 'log', '--oneline', '-5', '--pretty=format:- %s (%h)'], 
                                  capture_output=True, text=True, cwd=project_root)
            if result.returncode == 0 and result.stdout.strip():
                recent_changes = result.stdout.strip().split('\n')
        except:
            pass
    
    # 建立新的 CHANGELOG 內容
    new_entry = f"""# 變更日誌

## [{current_date}] - 自動更新

### 🔄 變更內容
{chr(10).join(recent_changes)}

### 📊 專案統計
- 總檔案數: {stats['total_files']}
- Python 檔案: {stats['python_files']}
- 測試檔案: {stats['test_files']}
- Git 提交: {stats['commit_count']}

"""
    
    if changelog_path.exists():
        try:
            with open(changelog_path, 'r', encoding='utf-8') as f:
                old_content = f.read()
            
            # 跳過舊的標題，追加舊內容
            if '# 變更日誌' in old_content:
                old_content = old_content.split('# 變更日誌', 1)[1].strip()
                if old_content:
                    new_entry += "\n" + old_content
            else:
                new_entry += "\n" + old_content
                
        except Exception as e:
            print(f"⚠️ 讀取舊 CHANGELOG 失敗: {e}")
    else:
        # 建立新的 CHANGELOG
        new_entry = f"""# 變更日誌

## [{current_date}] - 專案初始化

### 🚀 初始功能
- Outlook 郵件處理系統
- 多廠商解析器支援
- Web UI 介面
- 自動化測試框架

### 📊 專案統計
- 總檔案數: {stats['total_files']}
- Python 檔案: {stats['python_files']}
- 測試檔案: {stats['test_files']}
"""
    
    try:
        with open(changelog_path, 'w', encoding='utf-8') as f:
            f.write(new_entry)
        print("✅ CHANGELOG.md 已更新")
    except Exception as e:
        print(f"❌ CHANGELOG.md 更新失敗: {e}")

def update_project_info(project_root, current_datetime, stats):
    """更新 project_info.json"""
    info_path = project_root / 'project_info.json'
    
    # 獲取最後一次提交資訊
    last_commit = "N/A"
    if shutil.which('git') and (project_root / '.git').exists():
        try:
            result = subprocess.run(['git', 'log', '-1', '--pretty=format:%h - %s'], 
                                  capture_output=True, text=True, cwd=project_root)
            if result.returncode == 0:
                last_commit = result.stdout.strip()
        except:
            pass
    
    project_info = {
        "project_name": "Outlook Summary System",
        "last_updated": current_datetime,
        "update_source": "claude_hooks",
        "statistics": {
            "total_files": stats['total_files'],
            "python_files": stats['python_files'],
            "test_files": stats['test_files'],
            "doc_files": stats['doc_files'],
            "function_count": stats['function_count'],
            "class_count": stats['class_count'],
            "commit_count": stats['commit_count'],
            "contributors": stats['contributors']
        },
        "git_info": {
            "current_branch": stats['current_branch'],
            "last_commit": last_commit
        },
        "generated_at": datetime.now().isoformat()
    }
    
    try:
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(project_info, f, indent=2, ensure_ascii=False)
        print("✅ project_info.json 已更新")
    except Exception as e:
        print(f"❌ project_info.json 更新失敗: {e}")

def create_doc_summary(project_root, current_datetime, stats):
    """建立文檔摘要"""
    summary_path = project_root / 'DOC_SUMMARY.md'
    
    # 計算總字數（估算）
    total_words = "N/A"
    try:
        word_count = 0
        for md_file in project_root.rglob('*.md'):
            try:
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    word_count += len(content.split())
            except:
                continue
        total_words = str(word_count)
    except:
        pass
    
    summary_content = f"""# 文檔摘要

**更新時間:** {current_datetime}  
**更新來源:** Claude Code Hooks 自動更新

## 📚 主要文檔

### 核心文檔
- **README.md** - 專案概述與快速開始
- **CLAUDE.md** - AI 程式設計指導規則
- **PYTHON_MIGRATION_PLAN.md** - Python 遷移計畫
- **PROJECT_STATUS_TEMPLATE.md** - 專案狀態追蹤

### 技術文檔
- **VBA_TO_PYTHON_MAPPING.md** - 架構對照表
- **DOMAIN_MODELS_DESIGN.md** - 領域模型設計
- **UPDATED_ARCHITECTURE_WITH_DATABASE.md** - 完整架構文檔

### 資料檔案
- **CHANGELOG.md** - 變更歷史記錄
- **project_info.json** - 專案統計資料

## 📊 文檔統計
- Markdown 檔案: {stats['doc_files']} 個
- 總字數: {total_words}
- 最後更新: {current_datetime}

## 🔄 自動更新功能
- ✅ 專案統計自動更新
- ✅ 變更日誌自動生成  
- ✅ 版本資訊自動同步
- ✅ Git 資訊自動提取

---
*此文檔由 Claude Code Hooks 自動生成*
"""
    
    try:
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        print("✅ DOC_SUMMARY.md 已生成")
    except Exception as e:
        print(f"❌ DOC_SUMMARY.md 建立失敗: {e}")

def print_summary(stats):
    """顯示摘要"""
    print()
    print("📋 更新摘要:")
    print("   ✅ README.md - 專案統計和更新時間")
    print("   ✅ CHANGELOG.md - 最新變更記錄")  
    print("   ✅ project_info.json - 專案資訊")
    print("   ✅ DOC_SUMMARY.md - 文檔摘要")
    print()
    print("📈 目前專案統計:")
    print(f"   📁 總檔案: {stats['total_files']}")
    print(f"   🐍 Python 檔案: {stats['python_files']}")
    print(f"   🧪 測試檔案: {stats['test_files']}")
    print(f"   📚 文檔檔案: {stats['doc_files']}")
    print(f"   🌿 Git 提交: {stats['commit_count']}")
    print()

if __name__ == "__main__":
    main()
