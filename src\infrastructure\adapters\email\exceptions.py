"""
郵件白名單相關異常類別
定義白名單處理過程中可能出現的各種異常
"""

from typing import Optional


class EmailWhitelistError(Exception):
    """白名單處理基礎異常"""
    
    def __init__(self, message: str, error_code: Optional[str] = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class EmaillistFileError(EmailWhitelistError):
    """emaillist 檔案處理異常"""
    
    def __init__(self, filename: str, message: str, line_number: Optional[int] = None):
        self.filename = filename
        self.line_number = line_number
        error_msg = f"檔案 '{filename}' 處理錯誤"
        if line_number:
            error_msg += f" (第 {line_number} 行)"
        error_msg += f": {message}"
        super().__init__(error_msg, "EMAILLIST_FILE_ERROR")


class EmaillistParseError(EmailWhitelistError):
    """emaillist 檔案解析異常"""
    
    def __init__(self, line: str, line_number: int, reason: str):
        self.line = line
        self.line_number = line_number
        self.reason = reason
        message = f"第 {line_number} 行解析失敗: '{line.strip()}' - {reason}"
        super().__init__(message, "EMAILLIST_PARSE_ERROR")


class EmaillistValidationError(EmailWhitelistError):
    """emaillist 檔案驗證異常"""
    
    def __init__(self, pattern: str, reason: str):
        self.pattern = pattern
        self.reason = reason
        message = f"白名單模式驗證失敗: '{pattern}' - {reason}"
        super().__init__(message, "EMAILLIST_VALIDATION_ERROR")


class WhitelistManagerError(EmailWhitelistError):
    """白名單管理器異常"""
    
    def __init__(self, operation: str, message: str):
        self.operation = operation
        error_msg = f"白名單管理操作 '{operation}' 失敗: {message}"
        super().__init__(error_msg, "WHITELIST_MANAGER_ERROR")


class EmailAddressValidationError(EmailWhitelistError):
    """郵件地址驗證異常"""
    
    def __init__(self, email_address: str, reason: str):
        self.email_address = email_address
        self.reason = reason
        message = f"郵件地址驗證失敗: '{email_address}' - {reason}"
        super().__init__(message, "EMAIL_ADDRESS_VALIDATION_ERROR")