"""
ETD 廠商檔案處理器
對應 VBA 的 CopyFilesETD 和 CopyFilesETD2 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class ETDFileHandler(BaseFileHandler):
    """
    ETD 廠商檔案處理器
    
    VBA 邏輯：
    - CopyFilesETD: 從 \ETD\FT\{model}\{lot}\ 複製檔案
    - CopyFilesETD2: 從 \Etrend\FT\{pd}\{lot} 複製整個資料夾
    """
    
    def __init__(self, source_base_path: str):
        """初始化 ETD 檔案處理器"""
        super().__init__(source_base_path, "ETD")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """
        ETD 有兩個可能的來源路徑
        
        VBA:
        sourcePathETD = sourcePath & "\ETD\FT\" & model & "\" & lot & "\"
        sourcePathETD = sourcePath & "\Etrend\FT\" & pd & "\" & lot
        """
        paths = []
        
        if pd != "default" and lot != "default":
            # 主要路徑：ETD
            paths.append(self.source_base_path / "ETD" / "FT" / pd / lot)
            # 備用路徑：Etrend
            paths.append(self.source_base_path / "Etrend" / "FT" / pd / lot)
            
        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        ETD 的檔案搜尋模式
        主要使用資料夾複製，較少用檔案搜尋
        """
        patterns = []
        
        if mo:
            patterns.append(f"*{mo}*")
            
        if lot:
            patterns.append(f"*{lot}*")
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """ETD 支援資料夾複製（對應 CopyFilesETD2）"""
        return True
        
    def copy_files(self, file_name: str, file_temp: str,
                  pd: str = "default", lot: str = "default") -> bool:
        """
        ETD 特殊邏輯：優先嘗試資料夾複製
        """
        self.logger.info(f"ETD 檔案處理開始: MO={file_name}, LOT={lot}, PD={pd}")

        # file_temp 已經是最終的目標目錄，直接使用
        destination_path = Path(file_temp)
        destination_path.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"建立目標資料夾: {destination_path}")
            
        # ETD 需要 PD 和 LOT 才能運作
        if pd == "default" or lot == "default":
            self.logger.warning("ETD 需要 PD 和 LOT 資訊")
            return False
            
        # 取得來源路徑
        source_paths = self.get_source_paths(pd, lot, file_name)
        
        # 嘗試每個來源路徑
        for source_path in source_paths:
            # ETD 優先嘗試資料夾複製
            if source_path.exists() and source_path.is_dir():
                # 直接複製整個資料夾
                try:
                    import shutil
                    dest_folder = destination_path / pd / lot
                    shutil.copytree(source_path, dest_folder, dirs_exist_ok=True)
                    self.logger.info(f"ETD 資料夾複製成功: {source_path} -> {dest_folder}")
                    return True
                except Exception as e:
                    self.logger.error(f"ETD 資料夾複製失敗: {e}")
                    
        self.logger.error(f"ETD 檔案處理失敗: 找不到資料夾")
        return False