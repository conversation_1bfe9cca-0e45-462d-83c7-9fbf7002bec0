"""
檔案處理器基礎類別
對應 VBA CopyFiles 系列函數的共同邏輯
"""

import os
import shutil
import tempfile
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import time

from src.infrastructure.logging.logger_manager import LoggerManager


class BaseFileHandler(ABC):
    """
    檔案處理器基礎類別
    
    實作 VBA CopyFiles 函數的共同邏輯：
    1. 建立目標資料夾
    2. 搜尋檔案（優先 MO，其次 LOT）
    3. 檢查檔案是否已存在
    4. 複製並驗證
    """
    
    def __init__(self, source_base_path: str, vendor_code: str):
        """
        初始化檔案處理器
        
        Args:
            source_base_path: 來源基礎路徑 (對應 VBA sourcePath)
            vendor_code: 廠商代碼
        """
        self.source_base_path = Path(source_base_path)
        self.vendor_code = vendor_code
        self.logger = LoggerManager().get_logger(f"{vendor_code}FileHandler")
        
        # 支援的壓縮檔副檔名（對應 VBA 的檢查邏輯）
        self.archive_extensions = {'.zip', '.rar', '.7z'}
        
    @abstractmethod
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """
        取得廠商特定的來源路徑列表
        每個廠商可能有不同的目錄結構
        
        Args:
            pd: 產品名稱
            lot: 批號
            mo: MO 編號
            
        Returns:
            可能的來源路徑列表
        """
        pass
        
    @abstractmethod
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        取得廠商特定的檔案搜尋模式
        
        Args:
            mo: MO 編號
            lot: 批號
            pd: 產品名稱
            
        Returns:
            檔案搜尋模式列表
        """
        pass
        
    def copy_files(self, file_name: str, file_temp: str,
                  pd: str = "default", lot: str = "default",
                  email_subject: str = "", email_body: str = "") -> bool:
        """
        主要複製函數 - 對應 VBA CopyFilesXXX

        Args:
            file_name: MO 編號（對應 VBA fileName）
            file_temp: 目標暫存路徑（已經是最終的vendor_files目錄）
            pd: 產品名稱
            lot: 批號
            email_subject: 郵件主旨（用於路徑判斷）
            email_body: 郵件內文（用於路徑判斷）

        Returns:
            bool: 複製是否成功
        """
        try:
            self.logger.info(f"開始處理 {self.vendor_code} 檔案: MO={file_name}, LOT={lot}, PD={pd}")

            # 🔧 新增：設置郵件內容（如果處理器支援）
            if hasattr(self, 'set_email_content'):
                self.set_email_content(email_subject, email_body)

            # file_temp 已經是最終的目標目錄，直接使用
            destination_path = Path(file_temp)
            destination_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"建立目標資料夾: {destination_path}")

            # 3. 取得來源路徑
            source_paths = self.get_source_paths(pd, lot, file_name)

            # 🔧 增強：顯示詳細的搜尋資訊
            self.logger.info(f"📂 {self.vendor_code} 檔案搜尋計劃:")
            self.logger.info(f"   🎯 目標檔案: MO={file_name}, LOT={lot}, PD={pd}")
            self.logger.info(f"   📍 預計搜尋路徑: {len(source_paths)} 個")
            for i, path in enumerate(source_paths, 1):
                self.logger.info(f"      {i}. {path}")

            # 4. 嘗試從每個來源路徑複製檔案
            for i, source_path in enumerate(source_paths, 1):
                self.logger.info(f"🔍 正在搜尋路徑 {i}/{len(source_paths)}: {source_path}")

                if not source_path.exists():
                    self.logger.warning(f"   ❌ 路徑不存在: {source_path}")
                    continue

                self.logger.info(f"   ✅ 路徑存在，開始搜尋檔案...")

                # 嘗試不同的搜尋策略（對應 VBA 的搜尋順序）
                # 4.1 優先使用 MO 搜尋壓縮檔
                self.logger.info(f"   🔎 策略1: 使用 MO '{file_name}' 搜尋壓縮檔 (*.zip, *.rar, *.7z)")
                if self._copy_by_mo(source_path, destination_path, file_name):
                    return True

                # 4.2 使用 LOT 搜尋
                self.logger.info(f"   🔎 策略2: 使用 LOT '{lot}' 搜尋檔案")
                if self._copy_by_lot(source_path, destination_path, lot):
                    return True

                # 4.3 某些廠商支援複製整個資料夾
                if self._supports_folder_copy():
                    self.logger.info(f"   🔎 策略3: 嘗試複製整個資料夾 {pd}/{lot}")
                    if self._copy_entire_folder(source_path, destination_path, pd, lot):
                        return True
                else:
                    self.logger.info(f"   ⏭️  策略3: 跳過資料夾複製 ({self.vendor_code} 不支援)")

            # 🔧 增強：詳細的失敗總結
            self.logger.error(f"❌ {self.vendor_code} 檔案複製完全失敗")
            self.logger.error(f"📋 失敗總結:")
            self.logger.error(f"   🎯 搜尋目標: MO={file_name}, LOT={lot}, PD={pd}")
            self.logger.error(f"   📂 搜尋路徑: {len(source_paths)} 個路徑都無法找到檔案")
            self.logger.error(f"   🔍 搜尋策略: MO搜尋、LOT搜尋、資料夾複製 都失敗")
            self.logger.error(f"   💡 可能原因: 1)遠端路徑不存在 2)檔案不存在 3)權限問題 4)網路問題")
            return False
            
        except Exception as e:
            self.logger.error(f"複製檔案時發生錯誤: {e}")
            return False
            
    def _create_folders(self, base_path: Path, pd: str, mo: str) -> bool:
        """
        建立目標資料夾結構 - 對應 VBA CreateFolders

        VBA: destinationPath = tempPath & "\" & pd & "\" & UCase(fileName)

        邏輯：
        - 如果 MO 包含多個（用逗號分隔），只建立到 PD 層級
        - 如果 MO 是單一編號，建立 PD/MO 結構
        """
        try:
            # 檢查 MO 是否包含多個編號（用逗號分隔）
            if ',' in mo:
                # 多個 MO，只建立到 PD 層級
                target_dir = base_path / pd
                self.logger.info(f"檢測到多個 MO 編號，只建立到 PD 層級: {target_dir}")
            else:
                # 單一 MO，建立 PD/MO 結構
                target_dir = base_path / pd / mo
                self.logger.info(f"單一 MO 編號，建立 PD/MO 結構: {target_dir}")

            target_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"建立目標資料夾: {target_dir}")
            return True
        except Exception as e:
            self.logger.error(f"建立資料夾失敗: {e}")
            return False
            
    def _copy_by_mo(self, source_path: Path, dest_path: Path, mo: str) -> bool:
        """
        使用 MO 搜尋並複製檔案（優先搜尋壓縮檔）
        對應 VBA: file = Dir(sourcePathXXX & "*" & fileName & "*")

        支援多個 MO（逗號分隔）的搜尋
        """
        try:
            # 檢查是否有多個 MO（逗號分隔）
            if ',' in mo:
                # 多個 MO，分別搜尋每個 MO
                mo_list = [m.strip() for m in mo.split(',')]
                copied_count = 0

                for individual_mo in mo_list:
                    pattern = f"*{individual_mo}*"
                    newest_file = self._find_newest_archive(source_path, pattern)

                    if newest_file:
                        self.logger.info(f"找到符合的壓縮檔 ({individual_mo}): {newest_file}")
                        if self._copy_file_with_check(newest_file, dest_path):
                            copied_count += 1
                        else:
                            self.logger.warning(f"複製檔案失敗: {newest_file}")
                    else:
                        self.logger.warning(f"未找到符合 MO {individual_mo} 的檔案")

                if copied_count > 0:
                    self.logger.info(f"成功複製 {copied_count}/{len(mo_list)} 個 MO 的檔案")
                    return True
                else:
                    self.logger.error(f"所有 MO 的檔案都複製失敗")
                    return False
            else:
                # 單一 MO，使用原有邏輯
                pattern = f"*{mo}*"
                self.logger.info(f"      🔍 搜尋模式: {pattern}")

                # 🔧 增強：顯示搜尋到的所有檔案
                all_files = list(source_path.glob(pattern))
                archive_files = [f for f in all_files if f.suffix.lower() in self.archive_extensions]

                self.logger.info(f"      📁 找到 {len(all_files)} 個符合模式的檔案")
                self.logger.info(f"      📦 其中 {len(archive_files)} 個是壓縮檔")

                if archive_files:
                    for f in archive_files[:5]:  # 只顯示前5個
                        self.logger.info(f"         - {f.name}")
                    if len(archive_files) > 5:
                        self.logger.info(f"         ... 還有 {len(archive_files) - 5} 個檔案")

                newest_file = self._find_newest_archive(source_path, pattern)

                if newest_file:
                    self.logger.info(f"      ✅ 選擇最新的壓縮檔: {newest_file.name}")
                    return self._copy_file_with_check(newest_file, dest_path)  # 直接複製到目標目錄
                else:
                    self.logger.warning(f"      ❌ 沒有找到符合的壓縮檔")

                return False

        except Exception as e:
            self.logger.error(f"使用 MO 複製失敗: {e}")
            return False
            
    def _copy_by_lot(self, source_path: Path, dest_path: Path, lot: str) -> bool:
        """
        使用 LOT 搜尋並複製檔案
        對應 VBA: file = Dir(sourcePathXXX & lot & "*")
        """
        try:
            # 搜尋包含 LOT 的檔案
            pattern = f"{lot}*"
            files = list(source_path.glob(pattern))
            
            if not files:
                return False
                
            self.logger.info(f"找到 {len(files)} 個符合 LOT 的檔案")
            
            # 複製所有符合的檔案
            success = False
            for file_path in files:
                if file_path.is_file():
                    if self._copy_file_with_check(file_path, dest_path):
                        success = True
                        
            return success
            
        except Exception as e:
            self.logger.error(f"使用 LOT 複製失敗: {e}")
            return False
            
    def _copy_entire_folder(self, source_base: Path, dest_base: Path, 
                           pd: str, lot: str) -> bool:
        """
        複製整個資料夾 - 對應 VBA CopyFolder
        某些廠商（如 GTK、ETD）支援此功能
        """
        try:
            # 建構資料夾路徑
            source_folder = source_base / pd / lot
            dest_folder = dest_base / pd / lot
            
            if source_folder.exists() and source_folder.is_dir():
                shutil.copytree(source_folder, dest_folder, dirs_exist_ok=True)
                self.logger.info(f"已複製整個資料夾: {source_folder} -> {dest_folder}")
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"複製資料夾失敗: {e}")
            return False
            
    def _find_newest_archive(self, directory: Path, pattern: str) -> Optional[Path]:
        """
        尋找最新的壓縮檔案
        對應 VBA 的檔案日期比較邏輯：
        If FileDateTime(sourcePathXXX & file) > newestDate Then
        """
        newest_file = None
        newest_date = datetime.min
        
        for file_path in directory.glob(pattern):
            if file_path.suffix.lower() in self.archive_extensions:
                file_date = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_date > newest_date:
                    newest_date = file_date
                    newest_file = file_path
                    
        return newest_file
        
    def _copy_file_with_check(self, source: Path, dest_dir: Path) -> bool:
        """
        複製檔案並檢查
        對應 VBA 的檔案大小檢查邏輯：
        If FileLen(sourcePathXXX & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
        """
        try:
            # 確保目標目錄存在
            dest_dir.mkdir(parents=True, exist_ok=True)
            dest_file = dest_dir / source.name
            
            # 檢查目標檔案是否已存在且大小相同
            if dest_file.exists():
                if dest_file.stat().st_size == source.stat().st_size:
                    self.logger.info(f"檔案已存在且大小相同，跳過: {dest_file}")
                    return True
                    
            # 延遲一秒（對應 VBA DelayOneSecond）
            time.sleep(1)
            
            # 複製檔案（對應 VBA FileCopy）
            shutil.copy2(source, dest_file)
            
            # 驗證複製（對應 VBA 的大小比較）
            if dest_file.stat().st_size == source.stat().st_size:
                self.logger.info(f"檔案複製成功: {source} -> {dest_file}")
                return True
            else:
                self.logger.error(f"檔案複製驗證失敗: 大小不符")
                return False
                
        except Exception as e:
            self.logger.error(f"複製檔案失敗 {source}: {e}")
            return False
            
    def _supports_folder_copy(self) -> bool:
        """
        是否支援資料夾複製
        某些廠商（如 GTK、ETD）支援複製整個資料夾
        子類別可以覆寫此方法
        """
        return False
        
    def cleanup_temp_files(self, temp_dir: Path, days_old: int = 7):
        """
        清理舊的暫存檔案
        
        Args:
            temp_dir: 暫存目錄
            days_old: 清理幾天前的檔案
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            
            for file_path in temp_dir.rglob('*'):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        self.logger.debug(f"已刪[EXCEPT_CHAR]舊檔案: {file_path}")
                        
        except Exception as e:
            self.logger.error(f"清理暫存檔案失敗: {e}")