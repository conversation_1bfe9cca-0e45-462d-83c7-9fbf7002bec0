version: '3.8'

services:
  outlook-summary:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile
      args:
        BUILD_DATE: "${BUILD_DATE:-$(date -u +%Y-%m-%dT%H:%M:%SZ)}"
        VCS_REF: "${VCS_REF:-$(git rev-parse --short HEAD)}"
        VERSION: "${VERSION:-latest}"
    container_name: outlook-summary-app
    hostname: outlook-summary
    restart: unless-stopped
    
    # 網路配置
    ports:
      - "8000:8000"
    networks:
      - outlook-network
    
    # 環境變數
    environment:
      - OUTLOOK_ENV=production
      - PYTHONUNBUFFERED=1
      - PYTHONIOENCODING=utf-8
      - PYTHONUTF8=1
      - TZ=Asia/Taipei
    
    # 資源限制
    deploy:
      resources:
        limits:
          cpus: '8.0'
          memory: 4G
          pids: 1024
        reservations:
          cpus: '4.0'
          memory: 2G
    
    # 卷掛載
    volumes:
      - app-logs:/app/logs
      - app-temp:/app/temp
      - app-data:/app/data
      - type: bind
        source: ./config
        target: /app/config
        read_only: true
    
    # 健康檢查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 依賴服務
    depends_on:
      redis:
        condition: service_healthy
    
    # 標籤
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.outlook-summary.rule=Host(`outlook.local`)"
      - "traefik.http.services.outlook-summary.loadbalancer.server.port=8000"

  # Redis服務 (用於進程間通訊)
  redis:
    image: redis:7-alpine
    container_name: outlook-redis
    hostname: redis
    restart: unless-stopped
    
    # 網路配置
    ports:
      - "6379:6379"
    networks:
      - outlook-network
    
    # 資源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    
    # 配置
    command: >
      redis-server
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 60 1
      --loglevel notice
    
    # 卷掛載
    volumes:
      - redis-data:/data
    
    # 健康檢查
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # Prometheus監控
  prometheus:
    image: prom/prometheus:latest
    container_name: outlook-prometheus
    hostname: prometheus
    restart: unless-stopped
    
    # 網路配置
    ports:
      - "9090:9090"
    networks:
      - outlook-network
    
    # 資源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # 配置文件
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    # 啟動參數
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    # 依賴服務
    depends_on:
      - outlook-summary
    
    # 健康檢查
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Grafana儀表板
  grafana:
    image: grafana/grafana:latest
    container_name: outlook-grafana
    hostname: grafana
    restart: unless-stopped
    
    # 網路配置
    ports:
      - "3000:3000"
    networks:
      - outlook-network
    
    # 資源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    
    # 環境變數
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_DOMAIN=localhost
    
    # 卷掛載
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    
    # 依賴服務
    depends_on:
      - prometheus
    
    # 健康檢查
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx反向代理 (可選)
  nginx:
    image: nginx:alpine
    container_name: outlook-nginx
    hostname: nginx
    restart: unless-stopped
    
    # 網路配置
    ports:
      - "80:80"
      - "443:443"
    networks:
      - outlook-network
    
    # 資源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.2'
          memory: 128M
    
    # 配置文件
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx-logs:/var/log/nginx
    
    # 依賴服務
    depends_on:
      - outlook-summary
      - grafana
    
    # 健康檢查
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

# 網路配置
networks:
  outlook-network:
    driver: bridge
    name: outlook-network
    ipam:
      config:
        - subnet: **********/16

# 卷配置
volumes:
  # 應用數據
  app-logs:
    driver: local
    name: outlook-app-logs
  app-temp:
    driver: local
    name: outlook-app-temp
  app-data:
    driver: local
    name: outlook-app-data
  
  # Redis數據
  redis-data:
    driver: local
    name: outlook-redis-data
  
  # 監控數據
  prometheus-data:
    driver: local
    name: outlook-prometheus-data
  grafana-data:
    driver: local
    name: outlook-grafana-data
  
  # Nginx日誌
  nginx-logs:
    driver: local
    name: outlook-nginx-logs

# 配置
x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "100m"
    max-file: "3"

# 重啟策略
x-restart-policy: &default-restart-policy
  restart_policy:
    condition: on-failure
    delay: 5s
    max_attempts: 3
    window: 120s