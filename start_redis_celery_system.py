#!/usr/bin/env python3
"""
Redis + Celery 完整系統啟動腳本
"""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path

def print_banner():
    """顯示啟動橫幅"""
    print("=" * 60)
    print("🔴 Redis + Celery 完整系統啟動器")
    print("=" * 60)
    print("📋 服務清單:")
    print("  1. 🔴 Redis 服務器 (Port 6379)")
    print("  2. 👷 Celery Worker (2 並發)")
    print("  3. 🌸 Flower 監控 (Port 5566)")
    print("  4. 🚀 FastAPI 服務器 (Port 5555)")
    print("=" * 60)

def check_redis():
    """檢查 Redis 是否可用"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis 連接成功")
        return True
    except Exception as e:
        print(f"❌ Redis 連接失敗: {e}")
        return False

def start_services():
    """啟動所有服務"""
    processes = []
    
    try:
        print_banner()
        
        # 檢查 Redis
        if not check_redis():
            print("🔴 正在啟動 Redis 服務器...")
            redis_process = subprocess.Popen(
                ["redis-server", "--port", "6379"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            processes.append(("Redis", redis_process))
            time.sleep(3)  # 等待 Redis 啟動
            
            if not check_redis():
                print("❌ Redis 啟動失敗")
                return
        
        # 啟動 Celery Worker
        print("👷 正在啟動 Celery Worker...")
        worker_process = subprocess.Popen([
            sys.executable, "-m", "celery", 
            "-A", "src.tasks.celery_app", 
            "worker", 
            "--loglevel=info", 
            "--pool=solo", 
            "--concurrency=2"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        processes.append(("Celery Worker", worker_process))
        time.sleep(3)
        
        # 啟動 Flower 監控
        print("🌸 正在啟動 Flower 監控...")
        flower_process = subprocess.Popen([
            sys.executable, "-m", "celery", 
            "-A", "src.tasks.celery_app", 
            "flower", 
            "--port=5566"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        processes.append(("Flower", flower_process))
        time.sleep(3)
        
        # 啟動 FastAPI 服務器
        print("🚀 正在啟動 FastAPI 服務器...")
        fastapi_process = subprocess.Popen([
            sys.executable, "start_integrated_services.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        processes.append(("FastAPI", fastapi_process))
        time.sleep(5)
        
        print("\n🎉 所有服務啟動完成！")
        print("📊 服務狀態:")
        print("  🔴 Redis:        http://localhost:6379")
        print("  🌸 Flower 監控:  http://localhost:5566")
        print("  🚀 FastAPI:      http://localhost:5555")
        print("  📱 網路瀏覽器:    http://localhost:5555/network/ui")
        print("\n按 Ctrl+C 停止所有服務...")
        
        # 等待用戶中斷
        try:
            while True:
                time.sleep(1)
                # 檢查進程是否還在運行
                for name, process in processes:
                    if process.poll() is not None:
                        print(f"⚠️  {name} 進程已停止")
        except KeyboardInterrupt:
            print("\n🛑 正在停止所有服務...")
            
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
    
    finally:
        # 停止所有進程
        for name, process in processes:
            try:
                print(f"🛑 停止 {name}...")
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"🔪 強制終止 {name}...")
                process.kill()
            except Exception as e:
                print(f"❌ 停止 {name} 時發生錯誤: {e}")
        
        print("✅ 所有服務已停止")

if __name__ == "__main__":
    start_services()
