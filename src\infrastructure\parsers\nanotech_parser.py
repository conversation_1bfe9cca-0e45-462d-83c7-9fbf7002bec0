"""
Nanotech 廠商解析器實作
基於 VBA NanotechInfoFromStrings 邏輯

VBA 邏輯參考：
- 識別條件：通過產品代碼格式 (G|M|AT)\d{1} 和 Nanotech 關鍵字
- 解析規則：尋找產品代碼（排除Z開頭），解析格式如 G693L293_ZG032303026結批lowyield 98.97%
- MO 編號格式：ZG + 9位數字（如 ZG032303026）
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


class NanotechParser(VendorParser):
    """
    Nanotech 廠商郵件解析器
    
    識別條件：
    - 郵件主旨或內文包含 "nanotech" 關鍵字
    - 包含符合 (G|M|AT)\d 格式的產品代碼（排除Z開頭）
    - 包含 ZG + 9位數字的 MO 編號格式
    
    解析機制：
    1. 使用正則表達式 \b(G|M|AT)\d+ 尋找產品代碼（排除Z開頭）
    2. 使用正則表達式 \bZG\d{9}\b 尋找 MO 編號
    3. 從 "_" 分隔的格式中解析產品和 MO：產品_MO結批信息
    """
    
    def __init__(self):
        """初始化 Nanotech 解析器"""
        super().__init__()
        self._vendor_code = "NANOTECH"
        self._vendor_name = "Nanotech"
        self._identification_patterns = [
            "nanotech",       # 主要識別關鍵字
            "結批",           # 常見關鍵字
            "lowyield"       # 常見關鍵字
        ]
        self.set_confidence_threshold(0.7)
        
        # 初始化 logger
        self.logger = LoggerManager().get_logger("NanotechParser")
        
        # Nanotech 特有的模式
        self.product_pattern = r'\b(G|M|AT)\d+'  # 產品代碼：G/M/AT 開頭 + 數字
        self.mo_pattern = r'\bZG\d{9}\b'         # MO 編號：ZG + 9位數字

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        content = (subject + " " + body).lower()
        
        # 檢查 Nanotech 關鍵字
        if "nanotech" in content:
            matched_patterns.append("nanotech")
            confidence_score += 0.8
            
        # 檢查寄件者是否包含 Nanotech
        if "nanotech" in sender_lower or "nano" in sender_lower:
            if "nanotech" not in matched_patterns:
                matched_patterns.append("nanotech")
            confidence_score += 0.3
        
        # 檢查常見的 Nanotech 特徵詞
        if "結批" in content:
            matched_patterns.append("結批")
            confidence_score += 0.4
            
        if "lowyield" in content:
            matched_patterns.append("lowyield")
            confidence_score += 0.3
            
        # 檢查是否有 ZG + 9位數字的 MO 模式（Nanotech 特有）
        if re.search(self.mo_pattern, subject, re.IGNORECASE):
            confidence_score += 0.6
            matched_patterns.append("zg_mo_pattern")
                
        # 檢查是否有產品代碼模式
        if re.search(self.product_pattern, subject, re.IGNORECASE):
            confidence_score += 0.3
            # 進一步檢查是否有 "_" 分隔的格式
            underscore_pattern = r'\b(G|M|AT)\d+[A-Z0-9]*_ZG\d{9}'
            if re.search(underscore_pattern, subject, re.IGNORECASE):
                confidence_score += 0.4
                matched_patterns.append("product_mo_underscore_pattern")
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="nanotech_keyword_pattern_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        try:
            # 基於 VBA NanotechInfoFromStrings 邏輯進行解析
            subject = email_data.subject or ""
            body = email_data.body or ""
            
            # 首先嘗試從主旨解析
            nanotech_result = self.parse_nanotech_pattern(subject)
            
            # 如果主旨解析不完整，嘗試從郵件內文解析補充信息
            if not nanotech_result["product"] or not nanotech_result["mo_number"]:
                body_result = self.parse_nanotech_pattern(body)
                
                # 合併解析結果
                if body_result["product"] and not nanotech_result["product"]:
                    nanotech_result["product"] = body_result["product"]
                if body_result["mo_number"] and not nanotech_result["mo_number"]:
                    nanotech_result["mo_number"] = body_result["mo_number"]
                if body_result["yield_value"] and not nanotech_result["yield_value"]:
                    nanotech_result["yield_value"] = body_result["yield_value"]
                if body_result["method"] != "no_pattern":
                    nanotech_result["method"] = body_result["method"] + "_from_body"
            
            # 提取結果
            product_code = nanotech_result["product"] if nanotech_result["product"] else None
            mo_number = nanotech_result["mo_number"] if nanotech_result["mo_number"] else None
            
            # Nanotech 通常沒有明確的 LOT 編號，使用 MO 編號
            lot_number = mo_number
            
            # 檢查是否成功解析
            missing_fields = []
            if not product_code:
                missing_fields.append("產品代碼")
            if not mo_number:
                missing_fields.append("MO編號")

            is_success = len(missing_fields) == 0
            error_message = None

            if not is_success:
                error_message = f"Nanotech 傳統解析失敗：缺少 {', '.join(missing_fields)}"
                self.logger.warning(f"Nanotech 傳統解析失敗詳情:")
                self.logger.warning(f"  主旨: {email_data.subject}")
                self.logger.warning(f"  解析方法: {nanotech_result['method']}")
                self.logger.warning(f"  產品代碼: {product_code or '未找到'}")
                self.logger.warning(f"  MO編號: {mo_number or '未找到'}")
                self.logger.warning(f"  良率: {nanotech_result.get('yield_value', '未找到')}")
                self.logger.warning(f"  缺少欄位: {', '.join(missing_fields)}")
            else:
                self.logger.info(f"Nanotech 傳統解析成功:")
                self.logger.info(f"  產品代碼: {product_code}")
                self.logger.info(f"  MO編號: {mo_number}")
                self.logger.info(f"  LOT編號: {lot_number}")
                self.logger.info(f"  良率: {nanotech_result.get('yield_value', 'N/A')}")
                self.logger.info(f"  解析方法: {nanotech_result['method']}")

            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=product_code,
                mo_number=mo_number,
                lot_number=lot_number,
                is_success=is_success,
                error_message=error_message,
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': nanotech_result["product"],
                    'mo_number': nanotech_result["mo_number"],
                    'yield_value': nanotech_result.get("yield_value", ""),
                    'parsing_method': nanotech_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'nanotech_underscore_pattern_parsing'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                extraction_method="traditional",
                is_success=False,
                error_message=f"Nanotech parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_nanotech_pattern(self, text: str) -> Dict[str, Any]:
        """
        解析 Nanotech 模式：基於 VBA NanotechInfoFromStrings 邏輯
        
        VBA 邏輯：
        1. 使用正則表達式 \b(G|M|AT)\d{1} 尋找產品代碼
        2. 排除Z開頭的產品代碼
        3. 使用正則表達式 \bZG\d{9}\b 尋找 MO 編號
        
        範例：G693L293_ZG032303026結批lowyield 98.97%
        解析為：產品: G693L293, MO: ZG032303026, 良率: 98.97
        """
        if not text:
            return {
                "product": "",
                "mo_number": "",
                "yield_value": "",
                "method": "no_pattern"
            }
        
        product = ""
        mo_number = ""
        yield_value = ""
        method = "no_pattern"
        
        # VBA: 使用正則表達式尋找產品代碼模式，排除Z開頭
        # For Each match In matches
        #     If Not Left(match.value, 1) = "Z" Then
        #         producttemp = match.value
        #         Exit For
        #     End If
        # Next match
        
        # 優先檢查是否有 "_" 分隔的格式：產品_MO
        underscore_pattern = r'([GM][A-Z0-9]+)_([A-Z]{2}\d{9})'
        underscore_match = re.search(underscore_pattern, text, re.IGNORECASE)
        
        if underscore_match:
            product = underscore_match.group(1)
            mo_number = underscore_match.group(2)
            method = "underscore_separated_pattern"
        
        # 如果沒有找到 _ 格式，才分別尋找產品代碼和 MO 編號
        if not product or not mo_number:
            # 如果沒有 _ 格式，分別尋找產品代碼和 MO 編號
            product_matches = re.findall(self.product_pattern, text, re.IGNORECASE)
            
            if product_matches:
                # 排除Z開頭的產品代碼
                for match in product_matches:
                    if not match.upper().startswith('Z'):
                        # 在文本中找到完整的產品代碼詞
                        words = re.split(r'[_\s,，]', text)
                        for word in words:
                            if match.upper() in word.upper() and not word.upper().startswith('Z'):
                                # 提取完整的產品代碼（可能包含字母數字組合）
                                product_match = re.search(r'\b' + re.escape(match) + r'[A-Z0-9]*', word, re.IGNORECASE)
                                if product_match:
                                    product = product_match.group(0)
                                    method = "product_pattern_found"
                                    break
                        if product:
                            break
            
            # 尋找 MO 編號：ZG + 9位數字
            # VBA: moStringPattern = "\b[A-Z]{2}\d{9}\b"（在 Nanotechbody 函數中）
            mo_match = re.search(self.mo_pattern, text, re.IGNORECASE)
            if mo_match:
                mo_number = mo_match.group(0)
                if method == "no_pattern":
                    method = "mo_pattern_found"
                else:
                    method += "_with_mo"
        
        # 尋找良率值
        # 支援多種良率格式：lowyield 98.97%, 良率 98.97%, 等
        yield_patterns = [
            r'lowyield\s*(\d+\.?\d*)%?',
            r'良率[：:]?\s*(\d+\.?\d*)%?',
            r'yield[：:]?\s*(\d+\.?\d*)%?'
        ]
        
        for pattern in yield_patterns:
            yield_match = re.search(pattern, text, re.IGNORECASE)
            if yield_match:
                yield_value = yield_match.group(1)
                break
        
        # 如果沒有找到標準模式，嘗試其他可能的組合
        if not product and not mo_number:
            # 尋找可能的產品編號（不限於G/M/AT開頭）
            general_product_pattern = r'\b([A-Z]{1,2}\d{3,6}[A-Z0-9]*)\b'
            general_matches = re.findall(general_product_pattern, text, re.IGNORECASE)
            
            for match in general_matches:
                if not match.upper().startswith('ZG') and not match.upper().startswith('Z'):
                    product = match
                    method = "general_product_pattern"
                    break
        
        return {
            "product": product,
            "mo_number": mo_number,
            "yield_value": yield_value,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'Underscore separated format (G693L293_ZG032303026)',
                'ZG + 9 digits MO pattern',
                'Product code with yield information',
                'General pattern fallback'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'chinese_support': True,
            'based_on': 'VBA NanotechInfoFromStrings',
            'extraction_capabilities': [
                'Product code extraction using (G|M|AT)\\d+ pattern (excluding Z-prefix)',
                'MO number extraction using ZG\\d{9} pattern',
                'Yield value extraction from multiple formats',
                'Underscore-separated parsing logic'
            ],
            'special_features': [
                'Z-prefix exclusion for product codes',
                'ZG MO number pattern matching',
                'Yield percentage extraction',
                'Multiple format recognition'
            ]
        }