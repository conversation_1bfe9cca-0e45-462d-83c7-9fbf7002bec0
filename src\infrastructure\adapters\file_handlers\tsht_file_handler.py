"""
TSHT 廠商檔案處理器
對應 VBA 的 CopyFilesTSHT 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class TSHTFileHandler(BaseFileHandler):
    """
    TSHT 廠商檔案處理器

    VBA 邏輯：
    - 來源路徑：sourcePath & "\TSHT\FT\"
    - 搜尋模式：Dir(sourcePathTSHT & "*" & fileName & "*")
    - 檔案類型：所有檔案（主要是壓縮檔）
    """

    def __init__(self, source_base_path: str):
        """初始化 TSHT 檔案處理器"""
        super().__init__(source_base_path, "TSHT")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default",
                        mo: str = "") -> List[Path]:
        """
        TSHT 的來源路徑
        
        VBA: sourcePathTSHT = sourcePath & "\TSHT\FT\"
        """
        paths = []
        
        # TSHT 固定路徑：\TSHT\FT\
        tsht_path = self.source_base_path / "TSHT" / "FT"
        paths.append(tsht_path)
        
        return paths
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        TSHT 的檔案搜尋模式
        
        VBA: file = Dir(sourcePathTSHT & "*" & fileName & "*")
        """
        patterns = []
        
        if mo:
            patterns.append(f"*{mo}*")  # VBA 模式：包含 MO
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """TSHT 不支援資料夾複製"""
        return False