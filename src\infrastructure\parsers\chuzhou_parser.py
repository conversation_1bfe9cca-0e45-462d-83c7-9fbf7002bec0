"""
Chuzhou（滁州）廠商解析器實作
基於 VBA ChuzhouInfoFromStrings 邏輯

VBA 邏輯參考：
- 識別條件：通過產品代碼格式 (G|M|AT)\d{1} 和 Chuzhou 關鍵字
- 解析規則：尋找產品代碼（處理括號），尋找 C + 6-8位數字的 MO 編號格式
- 範例：G753T11U(金???) LOT：RPM8A.G ??批次 ********* ??低良反?
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


class ChuzhouParser(VendorParser):
    """
    Chuzhou（滁州）廠商郵件解析器
    
    識別條件：
    - 郵件主旨或內文包含 "chuzhou" 或 "滁州" 關鍵字
    - 包含符合 (G|M|AT)\d 格式的產品代碼
    - 包含 C + 6-8位數字的 MO 編號格式
    
    解析機制：
    1. 使用正則表達式 \b(G|M|AT)\d+ 尋找產品代碼
    2. 處理產品代碼中的括號部分（如 G753T11U(金???)）
    3. 使用正則表達式 C\d{6,8} 尋找 MO 編號
    4. 尋找 LOT：XXX 格式的 LOT 編號
    """
    
    def __init__(self):
        """初始化 Chuzhou 解析器"""
        super().__init__()
        self._vendor_code = "CHUZHOU"
        self._vendor_name = "Chuzhou"
        self._identification_patterns = [
            "chuzhou",        # 英文關鍵字
            "滁州",           # 中文關鍵字
            "批次",           # 常見關鍵字
            "低良反"          # 常見關鍵字
        ]
        self.set_confidence_threshold(0.7)
        
        # 初始化 logger
        self.logger = LoggerManager().get_logger("ChuzhouParser")
        
        # Chuzhou 特有的模式
        self.product_pattern = r'\b(G|M|AT)\d+'     # 產品代碼：G/M/AT 開頭 + 數字
        self.mo_pattern = r'\bC\d{6,8}\b'           # MO 編號：C + 6-8位數字

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        content = (subject + " " + body).lower()
        
        # 檢查 Chuzhou 關鍵字
        if "chuzhou" in content or "滁州" in content:
            matched_patterns.append("chuzhou")
            confidence_score += 0.8
            
        # 檢查寄件者是否包含 Chuzhou
        if "chuzhou" in sender_lower or "滁州" in sender_lower:
            if "chuzhou" not in matched_patterns:
                matched_patterns.append("chuzhou")
            confidence_score += 0.3
        
        # 檢查常見的 Chuzhou 特徵詞
        if "批次" in content:
            matched_patterns.append("批次")
            confidence_score += 0.4
            
        if "低良反" in content:
            matched_patterns.append("低良反")
            confidence_score += 0.4
            
        # 檢查是否有 C + 數字的 MO 模式（Chuzhou 特有）
        if re.search(self.mo_pattern, subject, re.IGNORECASE):
            confidence_score += 0.6
            matched_patterns.append("c_mo_pattern")
                
        # 檢查是否有產品代碼模式
        if re.search(self.product_pattern, subject, re.IGNORECASE):
            confidence_score += 0.3
            # 進一步檢查是否有括號格式
            parentheses_pattern = r'\b(G|M|AT)\d+[A-Z0-9]*\([^)]+\)'
            if re.search(parentheses_pattern, subject, re.IGNORECASE):
                confidence_score += 0.3
                matched_patterns.append("product_parentheses_pattern")
                
        # 檢查是否有 LOT：格式
        if re.search(r'LOT[：:]', subject, re.IGNORECASE):
            confidence_score += 0.3
            matched_patterns.append("lot_colon_pattern")
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="chuzhou_keyword_pattern_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        try:
            # 基於 VBA ChuzhouInfoFromStrings 邏輯進行解析
            subject = email_data.subject or ""
            body = email_data.body or ""
            
            # 首先嘗試從主旨解析
            chuzhou_result = self.parse_chuzhou_pattern(subject)
            
            # 如果主旨解析不完整，嘗試從郵件內文解析補充信息
            if not chuzhou_result["product"] or not chuzhou_result["mo_number"]:
                body_result = self.parse_chuzhou_pattern(body)
                
                # 合併解析結果
                if body_result["product"] and not chuzhou_result["product"]:
                    chuzhou_result["product"] = body_result["product"]
                if body_result["mo_number"] and not chuzhou_result["mo_number"]:
                    chuzhou_result["mo_number"] = body_result["mo_number"]
                if body_result["lot_number"] and not chuzhou_result["lot_number"]:
                    chuzhou_result["lot_number"] = body_result["lot_number"]
                if body_result["method"] != "no_pattern":
                    chuzhou_result["method"] = body_result["method"] + "_from_body"
            
            # 提取結果
            product_code = chuzhou_result["product"] if chuzhou_result["product"] else None
            mo_number = chuzhou_result["mo_number"] if chuzhou_result["mo_number"] else None
            lot_number = chuzhou_result["lot_number"] if chuzhou_result["lot_number"] else None
            
            # 檢查是否成功解析
            missing_fields = []
            if not product_code:
                missing_fields.append("產品代碼")
            if not mo_number:
                missing_fields.append("MO編號")

            is_success = len(missing_fields) == 0
            error_message = None

            if not is_success:
                error_message = f"Chuzhou 傳統解析失敗：缺少 {', '.join(missing_fields)}"
                self.logger.warning(f"Chuzhou 傳統解析失敗詳情:")
                self.logger.warning(f"  主旨: {email_data.subject}")
                self.logger.warning(f"  解析方法: {chuzhou_result['method']}")
                self.logger.warning(f"  產品代碼: {product_code or '未找到'}")
                self.logger.warning(f"  MO編號: {mo_number or '未找到'}")
                self.logger.warning(f"  LOT編號: {lot_number or '未找到'}")
                self.logger.warning(f"  缺少欄位: {', '.join(missing_fields)}")
            else:
                self.logger.info(f"Chuzhou 傳統解析成功:")
                self.logger.info(f"  產品代碼: {product_code}")
                self.logger.info(f"  MO編號: {mo_number}")
                self.logger.info(f"  LOT編號: {lot_number}")
                self.logger.info(f"  解析方法: {chuzhou_result['method']}")

            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=product_code,
                mo_number=mo_number,
                lot_number=lot_number,
                is_success=is_success,
                error_message=error_message,
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': chuzhou_result["product"],
                    'mo_number': chuzhou_result["mo_number"],
                    'lot_number': chuzhou_result["lot_number"],
                    'parsing_method': chuzhou_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'chuzhou_pattern_parsing'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                extraction_method="traditional",
                is_success=False,
                error_message=f"Chuzhou parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_chuzhou_pattern(self, text: str) -> Dict[str, Any]:
        """
        解析 Chuzhou 模式：基於 VBA ChuzhouInfoFromStrings 邏輯
        
        VBA 邏輯：
        1. 使用正則表達式 \b(G|M|AT)\d{1} 尋找產品代碼
        2. 處理產品代碼中的括號：如果有括號，只取括號前的部分
        3. 使用正則表達式 C\d{6,8} 尋找 MO 編號
        
        範例：G753T11U(金???) LOT：RPM8A.G ??批次 *********
        解析為：產品: G753T11U, LOT: RPM8A.G, MO: *********
        """
        if not text:
            return {
                "product": "",
                "mo_number": "",
                "lot_number": "",
                "method": "no_pattern"
            }
        
        product = ""
        mo_number = ""
        lot_number = ""
        method = "no_pattern"
        
        # VBA: 使用正則表達式尋找產品代碼模式
        # productString = "\b(G|M|AT)\d{1}"
        product_matches = re.findall(self.product_pattern, text, re.IGNORECASE)
        
        if product_matches:
            # VBA: 在詞陣列中尋找包含產品代碼的詞
            words = text.split()
            
            for word in words:
                # 檢查這個詞是否包含找到的產品代碼
                for product_match in product_matches:
                    if product_match.upper() in word.upper():
                        # VBA: 處理括號
                        # startpos = InStr(1, words(i), productMatch.Item(0))
                        # endpos = InStr(1, words(i), "(") - startpos
                        # If endpos > 0 Then
                        #     product = Mid(words(i), startpos, endpos)
                        
                        parentheses_pos = word.find("(")
                        if parentheses_pos > 0:
                            # 如果有括號，只取括號前的部分
                            start_pos = word.upper().find(product_match.upper())
                            if start_pos >= 0:
                                product = word[start_pos:parentheses_pos]
                        else:
                            # 如果沒有括號，取整個詞（清理標點符號）
                            start_pos = word.upper().find(product_match.upper())
                            if start_pos >= 0:
                                product = re.sub(r'[^\w]', '', word[start_pos:])
                        
                        method = "product_pattern_found"
                        break
                
                if product:  # 如果已找到產品，跳出外層循環
                    break
        
        # 尋找 MO 編號：C + 6-8位數字
        # VBA: moStringPattern = "C\d{6,8}"
        mo_match = re.search(self.mo_pattern, text, re.IGNORECASE)
        if mo_match:
            mo_number = mo_match.group(0)
            if method == "no_pattern":
                method = "mo_pattern_found"
            else:
                method += "_with_mo"
        
        # 尋找 LOT 編號：LOT：XXX 格式
        # VBA 中沒有明確的 LOT 解析，但從範例可以看出格式
        lot_patterns = [
            r'LOT[：:]\s*([A-Z0-9.]+)',  # LOT：RPM8A.G
            r'lot[：:]\s*([A-Z0-9.]+)',  # 小寫版本
            r'批[号號][：:]\s*([A-Z0-9.]+)'  # 批號：XXX
        ]
        
        for pattern in lot_patterns:
            lot_match = re.search(pattern, text, re.IGNORECASE)
            if lot_match:
                lot_number = lot_match.group(1)
                if method == "no_pattern":
                    method = "lot_pattern_found"
                else:
                    method += "_with_lot"
                break
        
        # 如果沒有找到標準模式，嘗試其他可能的組合
        if not product and not mo_number:
            # 尋找可能的產品-批次組合
            combined_pattern = r'\b([GM]\d{3,4}[A-Z]\d{1,2}[A-Z]).*?批次.*?(C\d{6,8})\b'
            combined_match = re.search(combined_pattern, text, re.IGNORECASE)
            
            if combined_match:
                product = combined_match.group(1)
                mo_number = combined_match.group(2)
                method = "combined_pattern_regex"
        
        return {
            "product": product,
            "mo_number": mo_number,
            "lot_number": lot_number,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'Product with parentheses format (G753T11U(金???))',
                'LOT colon format (LOT：RPM8A.G)',
                'C + 6-8 digits MO format (*********)',
                'Combined pattern with 批次 keyword'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'chinese_support': True,
            'based_on': 'VBA ChuzhouInfoFromStrings',
            'extraction_capabilities': [
                'Product code extraction using (G|M|AT)\\d+ pattern',
                'Parentheses handling for product codes',
                'C + digits MO number pattern matching',
                'LOT colon format extraction'
            ],
            'special_features': [
                'Parentheses-aware product parsing',
                'C-prefix MO number recognition',
                'LOT colon format support',
                'Chinese keyword recognition (批次, 低良反)'
            ]
        }