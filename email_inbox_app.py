#!/usr/bin/env python3
"""
郵件收件夾 Web 應用程式
整合所有組件的主啟動檔案
支援 Flask (郵件收件夾) + FastAPI (FT-EQC 處理) 雙服務
"""

import os
import sys
import asyncio
import threading
import time
from pathlib import Path
from typing import Dict, Any
from dotenv import load_dotenv

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 載入環境變數
load_dotenv(project_root / '.env')

from flask import Flask, render_template, jsonify, request, redirect, url_for, send_file
from werkzeug.serving import run_simple

# 導入專案組件
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.adapters.web_api.email_web_service import EmailWebService
from src.infrastructure.adapters.email_inbox.email_sync_service import EmailSyncService
from src.infrastructure.logging.logger_manager import LoggerManager
from src.presentation.web.api import parser_bp, attachment_bp


class FastAPIService:
    """
    FastAPI 服務管理類
    """
    
    def __init__(self, host='0.0.0.0', port=8010):
        self.host = host
        self.port = port
        self.logger = LoggerManager().get_logger("FastAPIService")
        self.process = None
        self.thread = None
        
    def start(self):
        """啟動 FastAPI 服務"""
        def run_fastapi():
            try:
                import uvicorn
                self.logger.info(f"啟動 FastAPI 服務: http://{self.host}:{self.port}")
                
                # 使用 uvicorn 啟動 FastAPI
                uvicorn.run(
                    "src.presentation.api.ft_eqc_api:app",
                    host=self.host,
                    port=self.port,
                    workers=1,
                    reload=False,
                    log_level="info"
                )
                
            except Exception as e:
                self.logger.error(f"FastAPI 服務啟動失敗: {e}")
        
        self.thread = threading.Thread(target=run_fastapi, daemon=True)
        self.thread.start()
        
        # 等待服務啟動
        time.sleep(2)
        self.logger.info("FastAPI 服務已在背景啟動")
    
    def stop(self):
        """停止 FastAPI 服務"""
        if self.thread and self.thread.is_alive():
            self.logger.info("停止 FastAPI 服務")
            # 在實際應用中可以使用更優雅的停止方式
        

class EmailInboxApp:
    """
    郵件收件夾應用程式主類
    整合 Flask (郵件收件夾) + FastAPI (FT-EQC 處理)
    """
    
    def __init__(self):
        """初始化應用程式"""
        self.logger = LoggerManager().get_logger("EmailInboxApp")
        self.app = Flask(__name__, 
                        template_folder='src/presentation/web/templates',
                        static_folder='src/presentation/web/static')
        
        # 初始化服務
        self.database = EmailDatabase()
        self.web_service = EmailWebService(self.database)
        self.sync_service = EmailSyncService(self.database)
        
        # 初始化 FastAPI 服務
        self.fastapi_service = FastAPIService()
        
        # 設定 Flask 應用
        self.app.secret_key = os.urandom(24)
        self.app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
        
        # 開發模式設定 - 禁用模板緩存
        self.app.config['TEMPLATES_AUTO_RELOAD'] = True
        self.app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
        
        # 註冊路由
        self._register_routes()
        
        # 註冊 API 藍圖
        self.app.register_blueprint(parser_bp)
        self.app.register_blueprint(attachment_bp)
        
        self.logger.info("郵件收件夾應用程式已初始化")
    
    def _register_routes(self):
        """註冊所有路由"""
        
        @self.app.route('/')
        def index():
            """首頁 - 郵件收件夾"""
            try:
                # 獲取統計資料
                stats = self.database.get_statistics()
                
                # 獲取寄件者列表
                senders = self.database.get_senders()
                
                return render_template('email_inbox.html',
                                     statistics=stats,
                                     senders=senders)
                
            except Exception as e:
                self.logger.error(f"載入首頁失敗: {e}")
                return jsonify({'error': '載入頁面失敗'}), 500
        
        @self.app.route('/email/<int:email_id>')
        def email_detail(email_id: int):
            """郵件詳情頁面"""
            try:
                email = self.database.get_email_by_id(email_id)
                if not email:
                    return redirect(url_for('index'))
                
                # 標記為已讀
                self.database.mark_email_read(email_id)
                
                return render_template('email_detail.html', email=email)
                
            except Exception as e:
                self.logger.error(f"載入郵件詳情失敗: {e}")
                return redirect(url_for('index'))
        
        # API 路由
        @self.app.route('/api/emails')
        def api_emails():
            """獲取郵件列表 API"""
            return jsonify(self.web_service.get_emails_api())
        
        @self.app.route('/api/emails/<int:email_id>')
        def api_email_detail(email_id: int):
            """獲取郵件詳情 API"""
            return jsonify(self.web_service.get_email_detail_api(email_id))
        
        @self.app.route('/api/emails/<int:email_id>/failed-analysis')
        def api_email_failed_analysis(email_id: int):
            """獲取解析失敗詳情 API"""
            return jsonify(self.web_service.get_email_failed_analysis_api(email_id))
        
        @self.app.route('/api/emails/<int:email_id>', methods=['DELETE'])
        def api_delete_email(email_id: int):
            """刪[EXCEPT_CHAR]郵件 API"""
            return jsonify(self.web_service.delete_email_api(email_id))
        
        @self.app.route('/api/emails/batch-delete', methods=['POST'])
        def api_batch_delete_emails():
            """批量刪[EXCEPT_CHAR]郵件 API"""
            try:
                data = request.get_json()
                if not data or 'email_ids' not in data:
                    return jsonify({'success': False, 'message': '缺少 email_ids 參數'}), 400
                
                email_ids = data['email_ids']
                if not isinstance(email_ids, list) or not email_ids:
                    return jsonify({'success': False, 'message': 'email_ids 必須是非空數組'}), 400
                
                # 使用新的批量刪除方法（帶級聯統計更新）
                result = self.database.delete_emails_batch(email_ids)

                return jsonify({
                    'success': result['success'],
                    'message': result['message'],
                    'deleted_count': result['deleted_count'],
                    'failed_count': result['failed_count'],
                    'updated_senders': result.get('updated_senders', 0)
                })
                
            except Exception as e:
                self.logger.error(f"批量刪[EXCEPT_CHAR]郵件 API 失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/emails/clear-all', methods=['POST'])
        def api_clear_all_emails():
            """清空所有郵件 API"""
            try:
                # 確認操作
                data = request.get_json() or {}
                confirm = data.get('confirm', False)

                if not confirm:
                    return jsonify({
                        'success': False,
                        'message': '需要確認操作：請在請求中包含 "confirm": true'
                    }), 400

                # 執行清空操作
                result = self.database.clear_all_emails()

                return jsonify({
                    'success': result['success'],
                    'message': result['message'],
                    'deleted_emails': result['deleted_emails'],
                    'deleted_senders': result['deleted_senders']
                })

            except Exception as e:
                self.logger.error(f"清空所有郵件 API 失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/emails/batch-mark-read', methods=['POST'])
        def api_batch_mark_read_emails():
            """批量標記已讀郵件 API"""
            try:
                data = request.get_json()
                if not data or 'email_ids' not in data:
                    return jsonify({'success': False, 'message': '缺少 email_ids 參數'}), 400
                
                email_ids = data['email_ids']
                if not isinstance(email_ids, list) or not email_ids:
                    return jsonify({'success': False, 'message': 'email_ids 必須是非空數組'}), 400
                
                # 執行批量標記已讀
                marked_count = 0
                failed_count = 0
                
                for email_id in email_ids:
                    try:
                        # 調用資料庫服務標記已讀
                        if self.database.mark_email_as_read(email_id):
                            marked_count += 1
                        else:
                            failed_count += 1
                            self.logger.warning(f"標記郵件 {email_id} 已讀失敗")
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"標記郵件 {email_id} 已讀時發生錯誤: {e}")
                
                return jsonify({
                    'success': True,
                    'message': f'成功標記 {marked_count} 封郵件為已讀，失敗 {failed_count} 封',
                    'marked_count': marked_count,
                    'failed_count': failed_count
                })
                
            except Exception as e:
                self.logger.error(f"批量標記已讀郵件 API 失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/emails/batch-process', methods=['POST'])
        def api_batch_process_emails():
            """批量處理郵件 API"""
            try:
                data = request.get_json()
                if not data or 'email_ids' not in data:
                    return jsonify({'success': False, 'message': '缺少 email_ids 參數'}), 400
                
                email_ids = data['email_ids']
                if not isinstance(email_ids, list) or not email_ids:
                    return jsonify({'success': False, 'message': 'email_ids 必須是非空數組'}), 400
                
                # 執行批量處理
                processed_count = 0
                failed_count = 0
                
                for email_id in email_ids:
                    try:
                        # 模擬處理邏輯 - 實際可以調用具體的處理服務
                        self.logger.info(f"正在處理郵件 {email_id}")
                        processed_count += 1
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"處理郵件 {email_id} 時發生錯誤: {e}")
                
                return jsonify({
                    'success': True,
                    'message': f'成功處理 {processed_count} 封郵件，失敗 {failed_count} 封',
                    'processed_count': processed_count,
                    'failed_count': failed_count
                })
                
            except Exception as e:
                self.logger.error(f"批量處理郵件 API 失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/emails/<int:email_id>/process', methods=['POST'])
        async def api_process_email(email_id: int):
            """處理單一郵件 API - 使用統一的 ALL IN ONE 處理流程"""
            try:
                from src.infrastructure.adapters.database.models import EmailDB
                from src.application.services.unified_email_processor import UnifiedEmailProcessor
                from src.data_models.email_models import EmailData

                with self.database.get_session() as session:
                    # 查詢郵件資料
                    email = session.query(EmailDB).filter(EmailDB.id == email_id).first()

                    if not email:
                        return jsonify({
                            'success': False,
                            'message': '郵件不存在'
                        }), 404

                    # 檢查是否已經處理過
                    if email.is_processed:
                        return jsonify({
                            'success': False,
                            'message': '此郵件已經處理過'
                        })

                    # 轉換為 EmailData 格式
                    email_data = EmailData(
                        message_id=email.message_id,
                        subject=email.subject,
                        sender=email.sender,
                        received_time=email.received_time,
                        body=email.body or "",
                        attachments=[]  # 附件會在處理過程中自動獲取
                    )

                    # 🔧 使用統一的 ALL IN ONE 處理流程
                    unified_processor = UnifiedEmailProcessor()

                    processing_result = await unified_processor.process_email_complete(
                        email_data=email_data,
                        email_id=str(email_id),
                        process_attachments=True,   # ✅ 處理附件
                        process_vendor_files=True,  # ✅ 處理廠商檔案
                        send_notifications=True,    # ✅ 發送LINE通知
                        update_database=True        # ✅ 更新資料庫
                    )

                    # 轉換為 API 回應格式
                    if processing_result.is_success:
                        return jsonify({
                            'success': True,
                            'message': f'ALL IN ONE 處理完成 (處理時間: {processing_result.processing_time:.2f}s)',
                            'email_id': email_id,
                            'vendor_code': processing_result.vendor_code,
                            'extraction_method': processing_result.parsing_result.extraction_method,
                            'attachment_processed': processing_result.attachment_result is not None,
                            'vendor_files_processed': processing_result.vendor_files_result is not None,
                            'notification_sent': processing_result.notification_sent,
                            'database_updated': processing_result.database_updated,
                            'processing_time': processing_result.processing_time
                        })
                    else:
                        return jsonify({
                            'success': False,
                            'message': f'處理失敗: {processing_result.error_message}',
                            'email_id': email_id,
                            'error': processing_result.error_message
                        })

            except Exception as e:
                self.logger.error(f"處理郵件 {email_id} 失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        
        @self.app.route('/api/emails/<int:email_id>/read', methods=['PUT'])
        def api_mark_email_read(email_id: int):
            """標記單一郵件已讀/未讀 API"""
            try:
                data = request.get_json()
                if not data or 'is_read' not in data:
                    return jsonify({'success': False, 'message': '缺少 is_read 參數'}), 400
                
                is_read = data['is_read']
                if not isinstance(is_read, bool):
                    return jsonify({'success': False, 'message': 'is_read 必須是布林值'}), 400
                
                # 調用資料庫服務標記已讀
                if is_read:
                    result = self.database.mark_email_as_read(email_id)
                else:
                    result = self.database.mark_email_as_unread(email_id)
                
                if result:
                    status_text = '已讀' if is_read else '未讀'
                    return jsonify({
                        'success': True,
                        'message': f'郵件已標記為{status_text}',
                        'email_id': email_id,
                        'is_read': is_read
                    })
                else:
                    return jsonify({'success': False, 'message': '更新失敗'}), 500
                
            except Exception as e:
                self.logger.error(f"標記郵件 {email_id} 狀態失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/sync', methods=['POST'])
        def api_sync():
            """同步郵件 API"""
            try:
                max_emails = request.json.get('max_emails', 100) if request.json else 100
                result = self.sync_service.sync_emails_background(max_emails)
                return jsonify(result)
            except Exception as e:
                self.logger.error(f"同步郵件 API 失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/sync/status')
        def api_sync_status():
            """獲取同步狀態 API"""
            return jsonify(self.sync_service.get_sync_status())
        
        @self.app.route('/api/sync/auto/start', methods=['POST'])
        def api_start_auto_sync():
            """啟動自動同步 API"""
            try:
                interval = request.json.get('interval', 60) if request.json else 60
                result = self.sync_service.start_auto_sync(interval)
                return jsonify(result)
            except Exception as e:
                self.logger.error(f"啟動自動同步失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/sync/auto/stop', methods=['POST'])
        def api_stop_auto_sync():
            """停止自動同步 API"""
            return jsonify(self.sync_service.stop_auto_sync())
        
        @self.app.route('/api/statistics')
        def api_statistics():
            """獲取統計資料 API"""
            return jsonify(self.web_service.get_statistics_api())
        
        @self.app.route('/api/senders')
        def api_senders():
            """獲取寄件者列表 API"""
            return jsonify(self.web_service.get_senders_api())
        
        @self.app.route('/api/search')
        def api_search():
            """搜尋郵件 API"""
            return jsonify(self.web_service.search_emails_api())
        
        @self.app.route('/api/connection/test')
        def api_test_connection():
            """測試郵件伺服器連接 API"""
            try:
                result = asyncio.run(self.sync_service.test_connection())
                return jsonify(result)
            except Exception as e:
                self.logger.error(f"測試連接失敗: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500
        
        @self.app.route('/api/connection/status')
        def api_connection_status():
            """獲取連接狀態 API"""
            return jsonify(self.sync_service.get_connection_status())
        
        # 附件下載 API 已移至 attachment_bp
        
        # FastAPI 服務整合路由
        @self.app.route('/ft-eqc')
        def ft_eqc_redirect():
            """重定向到 FT-EQC 處理介面"""
            return redirect(f"http://localhost:{self.fastapi_service.port}/ui")
        
        @self.app.route('/ft-eqc-api')
        def ft_eqc_api_redirect():
            """重定向到 FT-EQC API 文檔"""
            return redirect(f"http://localhost:{self.fastapi_service.port}/docs")
        
        # Favicon 路由
        @self.app.route('/favicon.ico')
        def favicon():
            """提供 favicon"""
            from flask import send_from_directory
            return send_from_directory(self.app.static_folder, 'favicon.ico', mimetype='image/vnd.microsoft.icon')
        
        # 測試頁面路由
        @self.app.route('/test')
        def test_page():
            """測試頁面"""
            from flask import send_from_directory
            return send_from_directory('.', 'test_sync_fix.html')

        # 寄件者顯示調試頁面
        @self.app.route('/debug_sender_display.html')
        def debug_sender_display():
            """寄件者顯示調試頁面"""
            from flask import send_from_directory
            return send_from_directory('.', 'debug_sender_display.html')
        
        # 資料庫管理頁面
        @self.app.route('/database-manager')
        def database_manager():
            """資料庫管理頁面"""
            try:
                import os
                db_path = os.path.abspath(getattr(self.database, 'database_url', 'email_inbox.db').replace('sqlite:///', ''))
                return render_template('database_manager.html', db_path=db_path)
            except Exception as e:
                self.logger.error(f"載入資料庫管理頁面失敗: {e}")
                return redirect(url_for('index'))
        
        # 資料庫管理 API
        @self.app.route('/api/database/info')
        def api_database_info():
            """獲取資料庫資訊"""
            try:
                import os
                from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB
                
                db_path = getattr(self.database, 'database_url', 'email_inbox.db').replace('sqlite:///', '')
                db_size = os.path.getsize(db_path) if os.path.exists(db_path) else 0
                
                with self.database.get_session() as session:
                    tables_info = {
                        'emails': session.query(EmailDB).count(),
                        'senders': session.query(SenderDB).count(),
                        'attachments': session.query(AttachmentDB).count(),
                        'email_process_status': session.query(EmailProcessStatusDB).count()
                    }
                
                return jsonify({
                    'success': True,
                    'data': {
                        'db_path': db_path,
                        'db_size': db_size,
                        'tables': tables_info
                    }
                })
            except Exception as e:
                self.logger.error(f"獲取資料庫資訊失敗: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/database/table/<table_name>')
        def api_get_table_data(table_name: str):
            """獲取表格資料"""
            try:
                from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB
                
                # 映射表名到模型
                table_models = {
                    'emails': EmailDB,
                    'senders': SenderDB,
                    'attachments': AttachmentDB,
                    'email_process_status': EmailProcessStatusDB
                }
                
                if table_name not in table_models:
                    return jsonify({'success': False, 'error': '無效的表名'})
                
                model = table_models[table_name]
                limit = int(request.args.get('limit', 100))  # 預設減少到100筆
                offset = int(request.args.get('offset', 0))
                
                # 針對 email_process_status 表格，進一步限制預設顯示數量
                if table_name == 'email_process_status':
                    limit = int(request.args.get('limit', 50))  # 更少的預設顯示
                
                with self.database.get_session() as session:
                    query = session.query(model)
                    
                    # 針對 email_process_status 表格，按時間倒序排列
                    if table_name == 'email_process_status' and hasattr(model, 'started_at'):
                        query = query.order_by(model.started_at.desc())
                    elif hasattr(model, 'created_at'):
                        query = query.order_by(model.created_at.desc())
                    elif hasattr(model, 'id'):
                        query = query.order_by(model.id.desc())
                    
                    total = query.count()
                    records = query.offset(offset).limit(limit).all()
                    
                    # 轉換為字典
                    data = []
                    for record in records:
                        row = {}
                        for column in model.__table__.columns:
                            value = getattr(record, column.name)
                            # 處理日期時間
                            if hasattr(value, 'isoformat'):
                                value = value.isoformat()
                            elif hasattr(value, '__str__') and not isinstance(value, (str, int, float, bool)):
                                value = str(value)
                            row[column.name] = value
                        data.append(row)
                    
                    # 獲取欄位資訊
                    columns = [{'name': col.name, 'type': str(col.type)} for col in model.__table__.columns]
                    
                return jsonify({
                    'success': True,
                    'data': {
                        'records': data,
                        'columns': columns,
                        'total': total,
                        'offset': offset,
                        'limit': limit
                    }
                })
                
            except Exception as e:
                self.logger.error(f"獲取表格資料失敗: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/database/execute', methods=['POST'])
        def api_execute_query():
            """執行 SQL 查詢（僅限 SELECT）"""
            try:
                data = request.get_json()
                query = data.get('query', '').strip()
                
                # 安全檢查：只允許 SELECT 語句
                if not query.upper().startswith('SELECT'):
                    return jsonify({'success': False, 'error': '只允許執行 SELECT 查詢'})
                
                # 禁止危險關鍵字
                dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'CREATE', 'ALTER', 'TRUNCATE']
                for keyword in dangerous_keywords:
                    if keyword in query.upper():
                        return jsonify({'success': False, 'error': f'不允許使用 {keyword} 關鍵字'})
                
                with self.database.get_session() as session:
                    from sqlalchemy import text
                    result = session.execute(text(query))

                    # 獲取欄位名稱
                    columns = list(result.keys()) if hasattr(result, 'keys') else []

                    # 獲取資料
                    rows = []
                    for row in result:
                        row_dict = {}
                        for i, value in enumerate(row):
                            column_name = columns[i] if i < len(columns) else f'column_{i}'
                            if value is not None:
                                # 處理日期時間格式
                                if hasattr(value, 'isoformat'):
                                    row_dict[column_name] = value.isoformat()
                                else:
                                    row_dict[column_name] = value
                            else:
                                row_dict[column_name] = None
                        rows.append(row_dict)

                return jsonify({
                    'success': True,
                    'data': {
                        'columns': columns,
                        'records': rows
                    }
                })
                
            except Exception as e:
                self.logger.error(f"執行查詢失敗: {e}")
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/database/<table_name>/<int:record_id>')
        def api_get_record_detail(table_name: str, record_id: int):
            """獲取單筆記錄詳情"""
            try:
                from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB

                # 映射表名到模型
                table_models = {
                    'emails': EmailDB,
                    'senders': SenderDB,
                    'attachments': AttachmentDB,
                    'email_process_status': EmailProcessStatusDB
                }

                if table_name not in table_models:
                    return jsonify({'success': False, 'error': '無效的表名'})

                model = table_models[table_name]

                with self.database.get_session() as session:
                    record = session.query(model).filter_by(id=record_id).first()

                    if not record:
                        return jsonify({'success': False, 'error': '記錄不存在'})

                    # 將記錄轉換為字典
                    record_dict = {}
                    for column in model.__table__.columns:
                        value = getattr(record, column.name)
                        if value is not None:
                            # 處理日期時間格式
                            if hasattr(value, 'isoformat'):
                                record_dict[column.name] = value.isoformat()
                            else:
                                record_dict[column.name] = value
                        else:
                            record_dict[column.name] = None

                    return jsonify({
                        'success': True,
                        'data': record_dict
                    })

            except Exception as e:
                self.logger.error(f"獲取記錄詳情失敗: {e}")
                return jsonify({'success': False, 'error': str(e)})



        @self.app.route('/api/database/search', methods=['POST'])
        def api_search_database():
            """搜尋資料庫內容（支援多欄位模糊搜尋）"""
            try:
                data = request.get_json()
                search_term = data.get('search_term', '').strip()
                table_name = data.get('table_name', 'emails')
                
                if not search_term:
                    return jsonify({'success': False, 'error': '請輸入搜尋關鍵字'})
                
                from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB
                from sqlalchemy import or_, and_
                
                # 映射表名到模型
                table_models = {
                    'emails': EmailDB,
                    'senders': SenderDB,
                    'attachments': AttachmentDB,
                    'email_process_status': EmailProcessStatusDB
                }
                
                if table_name not in table_models:
                    return jsonify({'success': False, 'error': '無效的表名'})
                
                model = table_models[table_name]
                search_pattern = f'%{search_term}%'
                
                with self.database.get_session() as session:
                    # 根據不同表格設定搜尋欄位
                    if table_name == 'emails':
                        query = session.query(model).filter(
                            or_(
                                model.sender.like(search_pattern),
                                model.sender_display_name.like(search_pattern),
                                model.subject.like(search_pattern),
                                model.body.like(search_pattern),
                                model.vendor_code.like(search_pattern),
                                model.pd.like(search_pattern),
                                model.lot.like(search_pattern)
                            )
                        )
                    elif table_name == 'senders':
                        query = session.query(model).filter(
                            or_(
                                model.email_address.like(search_pattern),
                                model.display_name.like(search_pattern)
                            )
                        )
                    elif table_name == 'attachments':
                        query = session.query(model).filter(
                            or_(
                                model.filename.like(search_pattern),
                                model.content_type.like(search_pattern)
                            )
                        )
                    elif table_name == 'email_process_status':
                        query = session.query(model).filter(
                            or_(
                                model.step_name.like(search_pattern),
                                model.status.like(search_pattern),
                                model.error_message.like(search_pattern)
                            )
                        )
                    
                    # 按時間倒序排列
                    if hasattr(model, 'created_at'):
                        query = query.order_by(model.created_at.desc())
                    elif hasattr(model, 'received_time'):
                        query = query.order_by(model.received_time.desc())
                    elif hasattr(model, 'id'):
                        query = query.order_by(model.id.desc())
                    
                    # 限制結果數量
                    limit = min(int(data.get('limit', 100)), 500)
                    total = query.count()
                    records = query.limit(limit).all()
                    
                    # 轉換為字典
                    result_data = []
                    for record in records:
                        row = {}
                        for column in model.__table__.columns:
                            value = getattr(record, column.name)
                            # 處理日期時間
                            if hasattr(value, 'isoformat'):
                                value = value.isoformat()
                            elif hasattr(value, '__str__') and not isinstance(value, (str, int, float, bool)):
                                value = str(value)
                            row[column.name] = value
                        result_data.append(row)
                    
                    # 獲取欄位資訊
                    columns = [{'name': col.name, 'type': str(col.type)} for col in model.__table__.columns]
                    
                return jsonify({
                    'success': True,
                    'data': {
                        'records': result_data,
                        'columns': columns,
                        'total': total,
                        'search_term': search_term,
                        'table_name': table_name,
                        'limit': limit
                    }
                })
                
            except Exception as e:
                self.logger.error(f"搜尋資料庫失敗: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/database/delete/<table_name>/<int:record_id>', methods=['DELETE'])
        def api_delete_record(table_name: str, record_id: int):
            """刪[EXCEPT_CHAR]單筆記錄"""
            try:
                from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB
                
                # 映射表名到模型
                table_models = {
                    'emails': EmailDB,
                    'senders': SenderDB,
                    'attachments': AttachmentDB,
                    'email_process_status': EmailProcessStatusDB
                }
                
                if table_name not in table_models:
                    return jsonify({'success': False, 'error': '無效的表名'})
                
                model = table_models[table_name]
                
                # 特殊處理郵件刪除（需要級聯更新寄件者統計）
                if table_name == 'emails':
                    success = self.database.delete_email(record_id)
                    if success:
                        return jsonify({'success': True, 'message': '郵件記錄已刪除（含級聯統計更新）'})
                    else:
                        return jsonify({'success': False, 'error': '郵件記錄不存在或刪除失敗'})

                # 其他表格的直接刪除
                with self.database.get_session() as session:
                    # 查找記錄
                    record = session.query(model).filter_by(id=record_id).first()
                    if not record:
                        return jsonify({'success': False, 'error': '記錄不存在'})

                    # 刪[EXCEPT_CHAR]記錄
                    session.delete(record)
                    session.commit()

                    self.logger.info(f"已刪[EXCEPT_CHAR] {table_name} 表格中的記錄 ID: {record_id}")

                return jsonify({'success': True, 'message': '記錄已刪[EXCEPT_CHAR]'})
                
            except Exception as e:
                self.logger.error(f"刪[EXCEPT_CHAR]記錄失敗: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/database/batch-delete/<table_name>', methods=['POST'])
        def api_batch_delete_records(table_name: str):
            """批量刪[EXCEPT_CHAR]記錄"""
            try:
                from src.infrastructure.adapters.database.models import EmailDB, SenderDB, AttachmentDB, EmailProcessStatusDB
                
                # 映射表名到模型
                table_models = {
                    'emails': EmailDB,
                    'senders': SenderDB,
                    'attachments': AttachmentDB,
                    'email_process_status': EmailProcessStatusDB
                }
                
                if table_name not in table_models:
                    return jsonify({'success': False, 'error': '無效的表名'})
                
                data = request.get_json()
                ids = data.get('ids', [])
                
                if not ids:
                    return jsonify({'success': False, 'error': '沒有提供要刪[EXCEPT_CHAR]的記錄 ID'})
                
                model = table_models[table_name]
                
                # 特殊處理郵件批量刪除（需要級聯更新寄件者統計）
                if table_name == 'emails':
                    result = self.database.delete_emails_batch(ids)
                    return jsonify({
                        'success': result['success'],
                        'message': result['message'],
                        'deleted_count': result['deleted_count'],
                        'updated_senders': result.get('updated_senders', 0)
                    })

                # 其他表格的直接批量刪除
                with self.database.get_session() as session:
                    # 查找要刪[EXCEPT_CHAR]的記錄
                    records = session.query(model).filter(model.id.in_(ids)).all()

                    if not records:
                        return jsonify({'success': False, 'error': '沒有找到要刪[EXCEPT_CHAR]的記錄'})

                    # 批量刪[EXCEPT_CHAR]
                    deleted_count = len(records)
                    for record in records:
                        session.delete(record)

                    session.commit()

                    self.logger.info(f"已批量刪[EXCEPT_CHAR] {table_name} 表格中的 {deleted_count} 筆記錄")

                return jsonify({'success': True, 'message': f'成功刪[EXCEPT_CHAR] {deleted_count} 筆記錄'})
                
            except Exception as e:
                self.logger.error(f"批量刪[EXCEPT_CHAR]記錄失敗: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/database/export/<table_name>')
        def api_export_table(table_name: str):
            """匯出表格為 CSV"""
            try:
                import csv
                import io
                from flask import Response
                
                # 獲取表格資料
                result = api_get_table_data(table_name)
                result_json = result.get_json()
                
                if not result_json['success']:
                    return result
                
                data = result_json['data']['records']
                if not data:
                    return jsonify({'success': False, 'error': '沒有資料可匯出'})
                
                # 創建 CSV
                output = io.StringIO()
                writer = csv.DictWriter(output, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
                
                # 返回 CSV 檔案
                output.seek(0)
                return Response(
                    output.getvalue(),
                    mimetype='text/csv',
                    headers={
                        'Content-Disposition': f'attachment; filename={table_name}.csv',
                        'Content-Type': 'text/csv; charset=utf-8-sig'
                    }
                )
                
            except Exception as e:
                self.logger.error(f"匯出表格失敗: {e}")
                return jsonify({'success': False, 'error': str(e)})
        
        # 錯誤處理
        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({'error': '頁面不存在'}), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            self.logger.error(f"內部伺服器錯誤: {error}")
            return jsonify({'error': '內部伺服器錯誤'}), 500
        
        @self.app.errorhandler(Exception)
        def handle_exception(e):
            self.logger.error(f"未處理的異常: {e}")
            return jsonify({'error': '伺服器錯誤'}), 500
    
    def initialize_database(self):
        """初始化資料庫"""
        try:
            # 資料庫已在 EmailDatabase 初始化時自動創建表格
            # 這裡只需要確認連接正常
            from src.infrastructure.adapters.database.models import db_engine
            db_engine.initialize()
            self.logger.info("資料庫已初始化")
            return True
        except Exception as e:
            self.logger.error(f"資料庫初始化失敗: {e}")
            return False
    
    def run(self, host='127.0.0.1', port=5555, debug=False, enable_fastapi=True):
        """啟動應用程式"""
        try:
            # 初始化資料庫
            if not self.initialize_database():
                self.logger.error("資料庫初始化失敗，無法啟動應用程式")
                return
            
            # 啟動 FastAPI 服務
            if enable_fastapi:
                try:
                    self.fastapi_service.start()
                    self.logger.info(f"FastAPI 服務已啟動: http://{self.fastapi_service.host}:{self.fastapi_service.port}")
                except Exception as e:
                    self.logger.error(f"FastAPI 服務啟動失敗: {e}")
                    self.logger.info("繼續啟動 Flask 服務...")
            
            self.logger.info(f"啟動郵件收件夾應用程式 http://{host}:{port}")
            self.logger.info("=== 服務端點資訊 ===")
            self.logger.info(f"[EMAIL] 郵件收件夾: http://{host}:{port}")
            if enable_fastapi:
                self.logger.info(f"[API] FT-EQC 處理: http://{self.fastapi_service.host}:{self.fastapi_service.port}/ui")
                self.logger.info(f"[DOCS] FT-EQC API 文檔: http://{self.fastapi_service.host}:{self.fastapi_service.port}/docs")
            
            # 啟動 Flask 應用
            if debug:
                self.app.run(host=host, port=port, debug=True, threaded=True)
            else:
                run_simple(host, port, self.app, 
                          use_reloader=False, 
                          use_debugger=False, 
                          threaded=True)
            
        except KeyboardInterrupt:
            self.logger.info("應用程式已停止")
        except Exception as e:
            self.logger.error(f"啟動應用程式失敗: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理資源"""
        try:
            # 停止 FastAPI 服務
            if hasattr(self, 'fastapi_service'):
                self.fastapi_service.stop()
            
            if hasattr(self, 'sync_service'):
                self.sync_service.cleanup()
            self.logger.info("應用程式資源已清理")
        except Exception as e:
            self.logger.error(f"清理資源失敗: {e}")


def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='郵件收件夾 Web 應用程式 (整合 FT-EQC 處理)')
    parser.add_argument('--host', default='127.0.0.1', help='Flask 服務主機地址')
    parser.add_argument('--port', type=int, default=5555, help='Flask 服務端口')
    parser.add_argument('--debug', action='store_true', help='啟用調試模式')
    parser.add_argument('--sync-on-start', action='store_true', help='啟動時自動同步郵件')
    parser.add_argument('--auto-sync', action='store_true', help='啟動自動同步')
    parser.add_argument('--sync-interval', type=int, default=60, help='自動同步間隔（秒）')
    parser.add_argument('--fastapi-port', type=int, default=8010, help='FastAPI 服務端口')
    parser.add_argument('--no-fastapi', action='store_true', help='不啟動 FastAPI 服務')
    
    args = parser.parse_args()
    
    # 創建應用程式
    app = EmailInboxApp()
    
    # 設定 FastAPI 服務端口
    if not args.no_fastapi:
        app.fastapi_service.port = args.fastapi_port
    
    # 如果需要，啟動時同步郵件
    if args.sync_on_start:
        print("啟動時同步郵件...")
        try:
            result = asyncio.run(app.sync_service.sync_emails_once(50))
            if result['success']:
                print(f"同步完成: {result['data']['sync_count']} 封郵件")
            else:
                print(f"同步失敗: {result['message']}")
        except Exception as e:
            print(f"啟動同步失敗: {e}")
    
    # 如果需要，啟動自動同步
    if args.auto_sync:
        print(f"啟動自動同步，間隔 {args.sync_interval} 秒...")
        result = app.sync_service.start_auto_sync(args.sync_interval)
        if result['success']:
            print("自動同步已啟動")
        else:
            print(f"自動同步啟動失敗: {result['message']}")
    
    # 啟動應用程式
    app.run(host=args.host, port=args.port, debug=args.debug, enable_fastapi=not args.no_fastapi)


def create_flask_app():
    """創建 Flask 應用實例（供整合服務使用）"""
    app_instance = EmailInboxApp()
    
    # 初始化資料庫
    if not app_instance.initialize_database():
        raise RuntimeError("資料庫初始化失敗")
    
    # 為整合服務添加健康檢查端點
    @app_instance.app.route('/api/health')
    def api_health_check():
        """健康檢查端點"""
        return jsonify({
            'status': 'healthy',
            'service': 'inbox',
            'version': '1.0.0',
            'timestamp': int(time.time())
        })
    
    return app_instance.app


if __name__ == '__main__':
    main()