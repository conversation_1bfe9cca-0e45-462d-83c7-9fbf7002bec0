# 需求文件：Outlook 摘要系統

## 系統概述

Outlook 摘要系統是一個企業級的半導體製造測試資料處理平台，專門設計用於自動化處理來自多個半導體測試廠商的郵件。系統從傳統的 VBA Excel 解決方案遷移至現代化的 Python 架構，提供高效、可靠且可擴展的資料處理能力。

### 系統規模
- **程式碼規模**：67,675 行 Python 代碼
- **支援廠商**：6 個半導體測試廠商
- **開發歷程**：112 次提交
- **測試覆蓋**：2,562 個測試檔案
- **架構模式**：六角架構設計

### 核心價值主張
- **自動化處理**：消除手動郵件處理工作流程，提高作業效率
- **多廠商整合**：統一處理來自不同廠商的多種資料格式
- **智能解析**：結合傳統解析和 AI 輔助解析，提高資料萃取準確性
- **即時監控**：提供系統健康狀態和處理效能的即時可視性
- **可擴展性**：支援新廠商和新資料格式的快速整合

## 功能需求

### 需求 1：自動化郵件監控與接收

**使用者故事：** 身為系統操作員，我希望系統能夠自動監控指定的郵件帳戶，即時接收來自半導體測試廠商的郵件，以確保測試資料的及時處理。

#### 驗收標準

1. **當系統啟動時**，系統應能夠自動連接到指定的郵件服務器
   - 支援 POP3 和 Outlook 整合兩種連接方式
   - 自動處理連接失敗和重連機制
   - 記錄連接狀態和錯誤資訊

2. **當有新郵件到達時**，系統應在 5 分鐘內檢測到新郵件
   - 支援即時郵件監控
   - 自動下載郵件內容和附件
   - 保持郵件的完整性和原始格式

3. **當處理大量郵件時**，系統應支援批量處理模式
   - 支援同時處理多封郵件
   - 提供處理進度和狀態回饋
   - 確保處理順序的一致性

4. **當郵件服務器不可用時**，系統應提供適當的錯誤處理
   - 自動重試機制，最多重試 3 次
   - 記錄詳細的錯誤日誌
   - 通知系統管理員連接問題

5. **當系統重啟後**，系統應能夠恢復之前的處理狀態
   - 避免重複處理已處理的郵件
   - 繼續處理中斷的任務
   - 保持資料的一致性

### 需求 2：智能廠商識別與路由

**使用者故事：** 身為資料分析師，我希望系統能夠自動識別郵件來源廠商，並將郵件路由到對應的解析器進行處理，以確保不同廠商資料格式的正確解析。

#### 驗收標準

1. **當收到 GTK 廠商郵件時**，系統應正確識別並路由
   - 識別關鍵字：`ft hold`、`ft lot`
   - 解析成功率應達到 98.5% 以上
   - 測試覆蓋率應達到 93% 以上
   - 支援複雜的測試資料格式解析

2. **當收到 ETD 廠商郵件時**，系統應正確識別並路由
   - 識別關鍵字：`anf`
   - 解析成功率應達到 97.8% 以上
   - 測試覆蓋率應達到 85% 以上
   - 支援斜線分隔格式的資料解析

3. **當收到 XAHT 廠商郵件時**，系統應正確識別並路由
   - 識別關鍵字：`tianshui`、`西安`
   - 解析成功率應達到 96.5% 以上
   - 測試覆蓋率應達到 79% 以上
   - 支援雙重解析機制

4. **當收到 JCET 廠商郵件時**，系統應正確識別並路由
   - 識別關鍵字：`jcet`
   - 解析成功率應達到 99.0% 以上
   - 測試覆蓋率應達到 93% 以上
   - 支援複雜的 KUI/GYC 模式解析

5. **當收到 LINGSEN 廠商郵件時**，系統應正確識別並路由
   - 識別關鍵字：`lingsen`
   - 解析成功率應達到 98.2% 以上
   - 測試覆蓋率應達到 90% 以上
   - 支援正則表達式產品代碼解析

6. **當無法識別廠商時**，系統應提供備用處理機制
   - 使用 LLM 輔助識別
   - 記錄未識別的郵件供人工審核
   - 提供手動分類功能

### 需求 3：多格式檔案處理與轉換

**使用者故事：** 身為品質工程師，我希望系統能夠自動處理各種格式的附件檔案，包括下載、解壓縮、格式轉換和資料萃取，以獲得標準化的測試資料。

#### 驗收標準

1. **當郵件包含壓縮檔案時**，系統應自動解壓縮
   - 支援 ZIP、RAR、7Z 等常見壓縮格式
   - 自動處理密碼保護的壓縮檔案
   - 保持檔案結構和檔案名稱的完整性
   - 處理中文檔案名稱和路徑

2. **當處理 CSV 檔案時**，系統應進行格式檢測和轉換
   - 自動檢測 CSV 檔案的編碼格式
   - 支援不同分隔符號的 CSV 檔案
   - 自動處理資料清洗和格式標準化
   - 檢測和處理異常資料

3. **當處理 Excel 檔案時**，系統應支援複雜的資料操作
   - 支援多工作表的 Excel 檔案處理
   - 自動識別資料區域和標題行
   - 支援公式計算和資料驗證
   - 生成標準化的輸出格式

4. **當進行檔案轉換時**，系統應保持資料完整性
   - CSV 到 Excel 的無損轉換
   - 保持原始資料的精度和格式
   - 自動生成轉換日誌和報告
   - 支援批量檔案轉換

5. **當檔案處理失敗時**，系統應提供詳細的錯誤資訊
   - 記錄具體的失敗原因和位置
   - 提供檔案修復建議
   - 支援手動干預和重新處理
   - 保留原始檔案供故障排除

### 需求 4：資料萃取與分析

**使用者故事：** 身為製造工程師，我希望系統能夠從處理後的檔案中萃取關鍵的測試資料，包括 MO（製造訂單）、LOT（批次號）、良率等資訊，並進行統計分析。

#### 驗收標準

1. **當萃取製造訂單（MO）資訊時**，系統應確保準確性
   - 自動識別 MO 號碼的格式和位置
   - 支援不同廠商的 MO 編號規則
   - 驗證 MO 號碼的有效性
   - 處理重複和異常的 MO 資料

2. **當萃取批次號（LOT）資訊時**，系統應支援多種格式
   - 識別標準和非標準的 LOT 格式
   - 支援批次號的層級結構
   - 自動關聯相關的測試資料
   - 檢測批次號的重複和衝突

3. **當計算良率資料時**，系統應提供準確的統計
   - 自動計算各種良率指標
   - 支援多維度的良率分析
   - 提供良率趨勢和異常檢測
   - 生成良率分佈圖表和報告

4. **當進行資料驗證時**，系統應確保資料品質
   - 檢測資料的完整性和一致性
   - 識別異常值和離群點
   - 提供資料品質評分
   - 自動標記可疑資料

5. **當生成統計報告時**，系統應提供豐富的分析功能
   - 支援多種統計指標的計算
   - 提供時間序列分析
   - 支援廠商間的比較分析
   - 生成可視化的統計圖表

### 需求 5：EQC 一鍵完成處理流程

**使用者故事：** 身為測試工程師，我希望系統提供一鍵完成的 EQC（設備品質控制）處理流程，自動完成從檔案整合到最終報告生成的所有步驟。

#### 驗收標準

1. **當執行第一階段檔案整合時**，系統應在 2.5 秒內完成
   - 自動掃描和分類相關檔案
   - 驗證檔案的完整性和格式
   - 整合多個來源的資料檔案
   - 生成整合狀態報告

2. **當執行第二階段 8 步驟處理時**，系統應按順序完成所有步驟
   - 步驟 1：EQC 總資料生成
   - 步驟 2：CODE 區間檢測
   - 步驟 3：雙重搜尋機制執行
   - 步驟 4：InsEqcRtData2 處理
   - 步驟 5：Excel 生成與標記
   - 步驟 6：檔案重命名
   - 步驟 7：CSV 到 Excel 轉換
   - 步驟 8：完整報告生成

3. **當執行 CODE 區間檢測時**，系統應支援自動和手動模式
   - 自動檢測主要區間和備用區間
   - 支援前端自訂區間設定
   - 提供區間檢測的準確性驗證
   - 記錄區間檢測的歷史和變更

4. **當執行雙重搜尋機制時**，系統應確保高匹配率
   - 主要區間精確匹配
   - 備用區間映射匹配
   - 提高 CODE 匹配成功率
   - 記錄搜尋結果和統計

5. **當生成最終報告時**，系統應包含完整的處理資訊
   - 詳細的處理步驟記錄
   - 各階段的處理時間統計
   - 資料品質和異常報告
   - 可追溯的處理歷史

### 需求 6：Excel 處理系統與 BIN1 保護

**使用者故事：** 身為品質保證工程師，我希望系統提供強大的 Excel 處理能力，包括 BIN1 保護機制，確保關鍵測試項目的資料安全和準確性。

#### 驗收標準

1. **當執行 8 步驟 Excel 處理流程時**，系統應確保每步驟的準確性
   - 檔案載入：支援大型 Excel 檔案的高效載入
   - 格式檢測：自動識別 Excel 檔案的結構和格式
   - 資料清洗：移除無效和重複資料
   - 資料轉換：標準化資料格式和單位
   - 資料計算：執行複雜的統計和分析計算
   - Excel 生成：創建格式化的輸出檔案
   - 樣式應用：應用專業的報表樣式
   - 檔案輸出：生成最終的報告檔案

2. **當執行 BIN1 保護機制時**，系統應達到 99.99% 的保護準確率
   - 自動識別關鍵測試項目的 BIN1 設備
   - 防止意外修改或刪除 BIN1 資料
   - 提供 BIN1 資料的完整性驗證
   - 記錄所有 BIN1 相關的操作

3. **當使用向量化處理時**，系統應顯著提升處理速度
   - 使用 Pandas 和 NumPy 的向量化操作
   - 支援大型資料集的高效處理
   - 提供處理進度和效能監控
   - 自動優化記憶體使用

4. **當處理大型檔案時**，系統應採用分塊處理策略
   - 預設 10,000 行/塊的分塊大小
   - 支援可配置的分塊參數
   - 提供分塊處理的進度追蹤
   - 確保分塊處理的資料一致性

5. **當生成 Summary 工作表時**，系統應包含完整的統計資訊
   - Site 統計：各測試站點的統計資料
   - 良率統計：詳細的良率分析
   - 趨勢分析：時間序列的趨勢圖表
   - 異常檢測：識別和標記異常資料

### 需求 7：LLM 整合與智能解析

**使用者故事：** 身為系統管理員，我希望系統整合大型語言模型（LLM）來輔助資料解析，特別是處理複雜或非標準格式的測試資料，提高解析的準確性和覆蓋率。

#### 驗收標準

1. **當使用 UnifiedLLMClient 時**，系統應支援多種 LLM 服務
   - 支援 Ollama 本地 LLM 服務
   - 支援 Grok API 雲端服務
   - 自動選擇最適合的 LLM 服務
   - 提供 LLM 服務的健康監控

2. **當傳統解析失敗時**，系統應自動切換到 LLM 輔助解析
   - 檢測傳統解析的失敗情況
   - 自動觸發 LLM 輔助解析
   - 提供解析信心分數評估
   - 記錄 LLM 解析的結果和效能

3. **當使用混合解析模式時**，系統應結合多種解析方法
   - 傳統規則解析 + LLM 輔助解析
   - 交叉驗證解析結果的準確性
   - 提供解析方法的效能比較
   - 支援解析策略的動態調整

4. **當評估解析品質時**，系統應提供詳細的品質指標
   - 解析信心分數（confidence_score）
   - 解析方法統計（extraction_method）
   - 解析時間戳記（llm_analysis_timestamp）
   - 使用的 LLM 服務記錄（llm_service_used）

5. **當 LLM 服務不可用時**，系統應提供降級處理
   - 自動切換到備用解析方法
   - 記錄服務不可用的情況
   - 提供手動解析的選項
   - 通知管理員服務狀態

### 需求 8：多服務架構與 API 整合

**使用者故事：** 身為開發人員，我希望系統提供完整的 API 服務架構，支援不同類型的客戶端存取，包括網頁介面、命令列工具和第三方整合。

#### 驗收標準

1. **當提供 Flask 郵件收件夾服務時**，系統應在埠 5000 上運行
   - 提供郵件管理的 RESTful API
   - 支援郵件的查詢、篩選和操作
   - 提供同步狀態的監控介面
   - 支援批量郵件操作

2. **當提供 FastAPI FT-EQC 處理服務時**，系統應在埠 8010 上運行
   - 提供高效能的資料處理 API
   - 支援非同步處理和任務佇列
   - 提供詳細的 API 文檔（Swagger UI）
   - 支援檔案上傳和處理狀態查詢

3. **當提供網路瀏覽器 API 時**，系統應在埠 8009 上運行
   - 提供網路檔案存取和下載功能
   - 支援遠端檔案的瀏覽和管理
   - 提供檔案上傳和同步功能
   - 支援網路連線狀態監控

4. **當服務間需要通訊時**，系統應提供可靠的整合機制
   - 支援服務間的資料同步
   - 提供統一的錯誤處理
   - 支援負載均衡和故障轉移
   - 記錄服務間的通訊日誌

5. **當提供 API 文檔時**，系統應包含完整的使用說明
   - 詳細的端點描述和參數說明
   - 提供 API 使用範例和程式碼片段
   - 支援 API 測試和除錯功能
   - 提供 API 版本管理

### 需求 9：即時監控與警報系統

**使用者故事：** 身為系統操作員，我希望系統提供即時的監控儀表板，顯示系統健康狀態、處理效能和錯誤資訊，並在出現問題時及時發出警報。

#### 驗收標準

1. **當監控系統健康狀態時**，系統應顯示所有關鍵指標
   - 三個 API 服務的運行狀態和回應時間
   - 系統資源使用率（CPU、記憶體、磁碟）
   - 資料庫連線狀態和查詢效能
   - 郵件同步服務的運行狀態

2. **當監控處理效能時**，系統應提供詳細的統計資料
   - 郵件處理速率和成功率
   - 各廠商解析器的效能統計
   - EQC 處理流程的執行狀態
   - Excel 處理系統的效能指標

3. **當檢測到異常情況時**，系統應及時發出警報
   - 服務離線或回應時間過長
   - 處理失敗率超過閾值
   - 系統資源使用率過高
   - 資料品質異常或錯誤率上升

4. **當提供監控儀表板時**，系統應支援即時更新
   - 使用 WebSocket 進行即時資料推送
   - 支援自訂重新整理間隔
   - 提供歷史資料的趨勢分析
   - 支援多使用者同時存取

5. **當配置警報規則時**，系統應支援靈活的設定
   - 支援多種警報條件和閾值
   - 提供不同嚴重程度的警報級別
   - 支援警報的確認和靜音功能
   - 記錄警報歷史和處理狀態

### 需求 10：資料庫管理與資料持久化

**使用者故事：** 身為資料庫管理員，我希望系統提供強大的資料庫管理功能，確保資料的完整性、一致性和高效能存取。

#### 驗收標準

1. **當管理郵件資料時**，系統應使用 EmailDB 表結構
   - 儲存完整的郵件元資料和內容
   - 支援郵件的狀態追蹤（已讀、已處理等）
   - 記錄解析狀態和結果
   - 支援附件資訊的關聯儲存

2. **當追蹤處理狀態時**，系統應使用 EmailProcessStatusDB 表
   - 記錄詳細的處理步驟和狀態
   - 支援處理時間和效能統計
   - 提供處理歷史的可追溯性
   - 支援並行處理的狀態管理

3. **當管理寄件者資訊時**，系統應使用 SenderDB 表
   - 儲存寄件者的統計資訊
   - 支援寄件者的分類和標記
   - 提供寄件者的歷史分析
   - 支援白名單和黑名單管理

4. **當執行資料庫操作時**，系統應確保高效能
   - 支援批量資料操作
   - 提供查詢效能優化
   - 支援資料庫連線池管理
   - 提供慢查詢檢測和優化建議

5. **當進行資料備份時**，系統應提供完整的備份策略
   - 支援自動定期備份
   - 提供增量備份功能
   - 支援備份資料的驗證
   - 提供災難恢復機制

## 非功能需求

### 效能需求

1. **郵件處理效能**
   - 郵件接收延遲：< 5 分鐘
   - 單封郵件處理時間：< 30 秒
   - 批量處理能力：> 100 封郵件/小時
   - 系統回應時間：< 3 秒

2. **資料處理效能**
   - EQC 第一階段處理：< 2.5 秒
   - Excel 檔案處理：< 60 秒/MB
   - 資料庫查詢回應：< 1 秒
   - API 端點回應：< 2 秒

3. **系統資源使用**
   - CPU 使用率：< 80%（正常負載）
   - 記憶體使用率：< 70%（正常負載）
   - 磁碟 I/O：< 80% 飽和度
   - 網路頻寬：< 50% 使用率

### 可靠性需求

1. **系統可用性**
   - 系統正常運行時間：> 99.5%
   - 服務恢復時間：< 5 分鐘
   - 資料完整性：100%
   - 備份成功率：> 99%

2. **錯誤處理**
   - 自動錯誤恢復：> 90% 的錯誤情況
   - 錯誤日誌完整性：100%
   - 異常檢測準確率：> 95%
   - 警報回應時間：< 1 分鐘

### 擴展性需求

1. **水平擴展**
   - 支援多實例部署
   - 支援負載均衡
   - 支援分散式處理
   - 支援雲端部署

2. **功能擴展**
   - 支援新廠商整合：< 1 週開發時間
   - 支援新檔案格式：< 3 天開發時間
   - 支援新 API 端點：< 1 天開發時間
   - 支援新監控指標：< 2 天開發時間

### 安全性需求

1. **資料安全**
   - 資料傳輸加密：TLS 1.3
   - 資料儲存加密：AES-256
   - 存取控制：基於角色的權限管理
   - 審計日誌：完整的操作記錄

2. **系統安全**
   - API 身份驗證：JWT 或 API Key
   - 輸入驗證：防止注入攻擊
   - 速率限制：防止 DoS 攻擊
   - 安全更新：定期安全補丁

### 維護性需求

1. **程式碼品質**
   - 測試覆蓋率：> 90%
   - 程式碼複雜度：< 10（循環複雜度）
   - 文檔覆蓋率：> 80%
   - 程式碼重複率：< 5%

2. **運維支援**
   - 日誌記錄：結構化日誌
   - 監控指標：完整的系統監控
   - 故障診斷：詳細的錯誤資訊
   - 效能調優：效能分析工具

## 技術約束

### 技術棧約束

1. **程式語言**：Python 3.11+
2. **Web 框架**：Flask 2.3.3、FastAPI 0.104.1
3. **資料庫**：SQLite（開發）、PostgreSQL（生產）
4. **資料處理**：Pandas 2.1.3、NumPy 1.24.3
5. **檔案處理**：OpenPyXL 3.1.2、XlsxWriter 3.1.9

### 架構約束

1. **設計模式**：六角架構（Ports and Adapters）
2. **開發方法**：測試驅動開發（TDD）
3. **程式碼風格**：Black、Flake8、MyPy
4. **容器化**：Docker 和 Docker Compose
5. **監控**：Prometheus、Grafana

### 整合約束

1. **郵件服務**：支援 POP3 和 Outlook 整合
2. **LLM 服務**：Ollama（本地）、Grok API（雲端）
3. **檔案格式**：CSV、Excel、ZIP、RAR、7Z
4. **編碼支援**：UTF-8、中英文混合內容
5. **平台支援**：Windows、Linux、Docker

## 驗收標準

### 功能驗收

1. **郵件處理**：所有廠商郵件解析成功率 > 95%
2. **資料萃取**：關鍵資料欄位萃取準確率 > 99%
3. **檔案處理**：支援的檔案格式處理成功率 > 98%
4. **API 服務**：所有 API 端點正常運作
5. **監控系統**：即時監控和警報功能正常

### 效能驗收

1. **處理速度**：滿足所有效能需求指標
2. **系統負載**：在預期負載下穩定運行
3. **資源使用**：不超過資源使用限制
4. **回應時間**：滿足所有回應時間要求

### 品質驗收

1. **測試覆蓋**：單元測試覆蓋率 > 90%
2. **程式碼品質**：通過所有程式碼品質檢查
3. **文檔完整**：提供完整的技術文檔
4. **使用者體驗**：使用者滿意度 > 4.0/5.0

---

**文件版本**：1.0  
**建立日期**：2025-01-30  
**最後更新**：2025-01-30  
**狀態**：待審核