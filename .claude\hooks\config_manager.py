#!/usr/bin/env python3
"""
配置管理器
處理 <PERSON>s 系統的所有配置
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, repo_root: Path):
        self.repo_root = Path(repo_root)
        self.logger = logging.getLogger('ConfigManager')
        
        # 配置檔案路徑
        self.config_file = self.repo_root / '.claude' / 'config.yaml'
        self.user_config_file = self.repo_root / '.claude' / 'user_config.yaml'
        
        # 載入配置
        self.config = self._load_config()
        
        # 檢測專案類型
        self.project_type = self._detect_project_type()
        
        # 應用專案特定配置
        self._apply_project_specific_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """載入配置檔案"""
        # 預設配置
        default_config = self._get_default_config()
        
        # 載入主配置檔案
        main_config = {}
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    main_config = yaml.safe_load(f) or {}
            except Exception as e:
                self.logger.warning(f"載入主配置檔案失敗: {e}")
        
        # 載入用戶自定義配置
        user_config = {}
        if self.user_config_file.exists():
            try:
                with open(self.user_config_file, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f) or {}
            except Exception as e:
                self.logger.warning(f"載入用戶配置檔案失敗: {e}")
        
        # 合併配置（用戶配置 > 主配置 > 預設配置）
        merged_config = self._deep_merge(default_config, main_config)
        merged_config = self._deep_merge(merged_config, user_config)
        
        return merged_config
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取預設配置"""
        return {
            'global': {
                'enabled': True,
                'log_level': 'INFO',
                'max_processing_time': 600,
                'ignore_patterns': ['*.pyc', '__pycache__', '.git', '*.log', '*.tmp'],
                'auto_detect_project_type': True
            },
            'file_size_analysis': {
                'enabled': True,
                'max_lines': 500,
                'warning_threshold': 400,
                'language_settings': {
                    'python': {'max_lines': 500, 'warning_threshold': 400, 'check_complexity': True},
                    'javascript': {'max_lines': 300, 'warning_threshold': 250, 'check_complexity': True},
                    'typescript': {'max_lines': 400, 'warning_threshold': 350, 'check_complexity': True},
                    'markdown': {'max_lines': 1000, 'warning_threshold': 800, 'check_complexity': False}
                }
            },
            'duplicate_detection': {
                'enabled': True,
                'similarity_threshold': 0.8,
                'min_block_size': 5,
                'detect_functions': True,
                'detect_classes': True,
                'detect_code_blocks': True
            },
            'import_analysis': {
                'enabled': True,
                'check_unused_imports': True,
                'check_circular_dependencies': True,
                'detect_optimization_opportunities': True
            },
            'quality_checking': {
                'enabled': True,
                'max_cyclomatic_complexity': 10,
                'max_nesting_depth': 4,
                'max_parameters': 5,
                'max_function_lines': 50,
                'max_class_lines': 200
            },
            'file_splitting': {
                'enabled': True,
                'strategies': ['class_split', 'function_split', 'mixed_split'],
                'min_split_size': 50,
                'preserve_imports': True
            },
            'duplicate_cleaning': {
                'enabled': True,
                'preserve_newest': True,
                'backup_before_clean': True,
                'safe_mode': True
            },
            'import_optimization': {
                'enabled': True,
                'auto_apply_safe_changes': False,
                'preserve_existing_imports': True
            },
            'validation': {
                'enabled': True,
                'run_syntax_check': True,
                'run_import_check': True,
                'run_tests': True,
                'auto_create_snapshots': True,
                'max_snapshots': 10
            }
        }
    
    def _detect_project_type(self) -> Optional[str]:
        """檢測專案類型"""
        if not self.config.get('global', {}).get('auto_detect_project_type', True):
            return None
        
        # 檢查常見的專案標識檔案
        project_indicators = {
            'django': ['manage.py', 'settings.py', 'wsgi.py'],
            'flask': ['app.py', 'run.py', 'wsgi.py'],
            'fastapi': ['main.py', 'app.py'],
            'data_science': ['requirements.txt', 'environment.yml', 'Pipfile'],
            'web_frontend': ['package.json', 'webpack.config.js', 'vite.config.js'],
            'react': ['package.json', 'src/App.js', 'src/App.tsx'],
            'vue': ['package.json', 'vue.config.js', 'src/App.vue'],
            'nodejs': ['package.json', 'index.js', 'server.js']
        }
        
        for project_type, indicators in project_indicators.items():
            matches = 0
            for indicator in indicators:
                if (self.repo_root / indicator).exists():
                    matches += 1
            
            # 如果匹配超過一半的指標，認為是該類型
            if matches >= len(indicators) // 2 + 1:
                self.logger.info(f"檢測到專案類型: {project_type}")
                return project_type
        
        # 檢查檔案擴展名分佈
        file_extensions = {}
        for file_path in self.repo_root.rglob('*'):
            if file_path.is_file() and not any(ignore in str(file_path) for ignore in ['.git', '__pycache__']):
                ext = file_path.suffix.lower()
                file_extensions[ext] = file_extensions.get(ext, 0) + 1
        
        # 根據主要檔案類型推斷
        if file_extensions.get('.py', 0) > file_extensions.get('.js', 0):
            return 'python'
        elif file_extensions.get('.js', 0) > 0 or file_extensions.get('.ts', 0) > 0:
            return 'javascript'
        
        return None
    
    def _apply_project_specific_config(self):
        """應用專案特定配置"""
        if not self.project_type:
            return
        
        project_configs = self.config.get('project_types', {})
        if self.project_type in project_configs:
            project_config = project_configs[self.project_type]
            self.config = self._deep_merge(self.config, project_config)
            self.logger.info(f"應用 {self.project_type} 專案配置")
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合併字典"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """獲取配置值（支援點號路徑）"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_analyzer_config(self, analyzer_name: str) -> Dict[str, Any]:
        """獲取分析器配置"""
        config_mapping = {
            'file_size': 'file_size_analysis',
            'duplicate': 'duplicate_detection',
            'import': 'import_analysis',
            'quality': 'quality_checking'
        }
        
        config_key = config_mapping.get(analyzer_name, analyzer_name)
        return self.get(config_key, {})
    
    def get_refactor_config(self, refactor_name: str) -> Dict[str, Any]:
        """獲取重構器配置"""
        config_mapping = {
            'file_splitter': 'file_splitting',
            'duplicate_cleaner': 'duplicate_cleaning',
            'import_optimizer': 'import_optimization'
        }
        
        config_key = config_mapping.get(refactor_name, refactor_name)
        return self.get(config_key, {})
    
    def get_language_config(self, language: str) -> Dict[str, Any]:
        """獲取語言特定配置"""
        language_rules = self.get('language_rules', {})
        return language_rules.get(language, {})
    
    def get_file_language(self, file_path: str) -> Optional[str]:
        """根據檔案路徑判斷語言"""
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        # 語言映射
        extension_mapping = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.md': 'markdown',
            '.txt': 'text'
        }
        
        return extension_mapping.get(extension)
    
    def is_file_ignored(self, file_path: str) -> bool:
        """檢查檔案是否應該被忽略"""
        ignore_patterns = self.get('global.ignore_patterns', [])
        file_path = Path(file_path)
        
        for pattern in ignore_patterns:
            # 簡單的 glob 模式匹配
            if pattern.startswith('*') and str(file_path).endswith(pattern[1:]):
                return True
            elif pattern in str(file_path):
                return True
        
        return False
    
    def get_max_lines_for_file(self, file_path: str) -> int:
        """獲取檔案的最大行數限制"""
        language = self.get_file_language(file_path)
        
        if language:
            # 檢查語言特定設定
            language_config = self.get_analyzer_config('file_size').get('language_settings', {})
            if language in language_config:
                return language_config[language].get('max_lines', 500)
        
        # 返回預設值
        return self.get('file_size_analysis.max_lines', 500)
    
    def should_check_complexity(self, file_path: str) -> bool:
        """檢查是否應該檢查檔案複雜度"""
        language = self.get_file_language(file_path)
        
        if language:
            language_config = self.get_analyzer_config('file_size').get('language_settings', {})
            if language in language_config:
                return language_config[language].get('check_complexity', True)
        
        return True
    
    def get_validation_config(self) -> Dict[str, Any]:
        """獲取驗證配置"""
        return self.get('validation', {})
    
    def get_test_command(self) -> Optional[str]:
        """獲取測試命令"""
        # 檢查專案特定的測試命令
        if self.project_type:
            project_test_commands = {
                'django': 'python manage.py test',
                'flask': 'pytest tests/',
                'fastapi': 'pytest tests/',
                'python': 'pytest tests/ -v',
                'javascript': 'npm test',
                'nodejs': 'npm test'
            }
            
            if self.project_type in project_test_commands:
                return project_test_commands[self.project_type]
        
        # 檢查配置檔案
        return self.get('validation.test_command')
    
    def create_user_config_template(self):
        """創建用戶配置模板"""
        template = {
            '# 用戶自定義配置': '覆蓋主配置檔案中的設定',
            'global': {
                'log_level': 'DEBUG',  # 調整日誌級別
            },
            'file_size_analysis': {
                'max_lines': 600,  # 自定義行數限制
            },
            'validation': {
                'test_command': 'pytest tests/ -v --tb=short',  # 自定義測試命令
                'auto_create_snapshots': True
            },
            '# 範例': '取消註釋並修改下面的設定',
            '# notifications': {
            '    enabled': True,
            '    report_format': 'detailed'
            '# }'
        }
        
        if not self.user_config_file.exists():
            with open(self.user_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(template, f, default_flow_style=False, allow_unicode=True)
            self.logger.info(f"創建用戶配置模板: {self.user_config_file}")
    
    def validate_config(self) -> List[str]:
        """驗證配置"""
        errors = []
        
        # 檢查必要的配置
        required_configs = [
            'global.enabled',
            'file_size_analysis.enabled',
            'duplicate_detection.enabled'
        ]
        
        for config_path in required_configs:
            if self.get(config_path) is None:
                errors.append(f"缺少必要配置: {config_path}")
        
        # 檢查數值範圍
        numeric_ranges = {
            'file_size_analysis.max_lines': (50, 10000),
            'duplicate_detection.similarity_threshold': (0.1, 1.0),
            'quality_checking.max_cyclomatic_complexity': (1, 100)
        }
        
        for config_path, (min_val, max_val) in numeric_ranges.items():
            value = self.get(config_path)
            if value is not None and not (min_val <= value <= max_val):
                errors.append(f"配置值超出範圍 {config_path}: {value} (應在 {min_val}-{max_val})")
        
        return errors
    
    def save_config(self, config_updates: Dict[str, Any], to_user_config: bool = True):
        """保存配置更新"""
        target_file = self.user_config_file if to_user_config else self.config_file
        
        # 載入現有配置
        existing_config = {}
        if target_file.exists():
            with open(target_file, 'r', encoding='utf-8') as f:
                existing_config = yaml.safe_load(f) or {}
        
        # 合併更新
        updated_config = self._deep_merge(existing_config, config_updates)
        
        # 保存
        with open(target_file, 'w', encoding='utf-8') as f:
            yaml.dump(updated_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"配置已保存到: {target_file}")
        
        # 重新載入配置
        self.config = self._load_config()
    
    def export_current_config(self) -> str:
        """匯出當前配置為 YAML 字符串"""
        return yaml.dump(self.config, default_flow_style=False, allow_unicode=True)
    
    def get_performance_config(self) -> Dict[str, Any]:
        """獲取性能配置"""
        return self.get('performance', {
            'max_concurrent_files': 4,
            'enable_caching': True,
            'cache_duration': 3600,
            'max_memory_usage': '512MB',
            'max_file_size': '10MB',
            'skip_large_files': True
        })
    
    def is_experimental_enabled(self, feature_name: str) -> bool:
        """檢查實驗性功能是否啟用"""
        if not self.get('experimental.enabled', False):
            return False
        
        experimental_features = self.get('experimental.features', [])
        return feature_name in experimental_features
    
    def get_integration_config(self, integration_name: str) -> Dict[str, Any]:
        """獲取整合配置"""
        integrations = self.get('integrations', {})
        return integrations.get(integration_name, {})