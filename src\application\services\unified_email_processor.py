"""
統一郵件處理服務 - ALL IN ONE 完整流程
整合所有郵件處理功能為單一服務，避免重複代碼
"""

import asyncio
import os
from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime

from src.data_models.email_models import EmailData
from src.infrastructure.parsers.base_parser import ParserFactory, EmailParsingResult, VendorIdentificationResult
from src.infrastructure.adapters.notification.line_notification_service import LineNotificationService
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.adapters.email.email_filter import EmailFilter
from src.infrastructure.logging.logger_manager import LoggerManager


@dataclass
class EmailProcessingResult:
    """統一郵件處理結果"""
    is_success: bool
    vendor_code: Optional[str]
    vendor_name: Optional[str]
    parsing_result: EmailParsingResult
    vendor_result: VendorIdentificationResult
    attachment_result: Optional[Dict[str, Any]] = None
    vendor_files_result: Optional[Dict[str, Any]] = None
    notification_sent: bool = False
    database_updated: bool = False
    error_message: Optional[str] = None
    processing_time: float = 0.0
    whitelist_result: Optional[Dict[str, Any]] = None  # 新增：白名單檢查結果


class UnifiedEmailProcessor:
    """
    統一郵件處理服務 - ALL IN ONE 完整流程
    
    包含完整的郵件處理流程：
    1. 解析郵件 (遵循 LLM_PARSING_MODE)
    2. 處理附件
    3. 處理廠商檔案
    4. 發送LINE通知
    5. 更新資料庫
    """
    
    def __init__(self):
        """初始化統一郵件處理服務"""
        self.logger = LoggerManager().get_logger("UnifiedEmailProcessor")
        
        # 初始化所有組件
        self.parser_factory = ParserFactory()
        self.line_service = LineNotificationService()
        self.database = EmailDatabase()  # 修正：使用 database 而不是 email_database

        # 初始化郵件過濾器
        try:
            self.email_filter = EmailFilter()
            self.logger.info("✅ 郵件過濾器初始化成功")
        except Exception as e:
            self.logger.warning(f"郵件過濾器初始化失敗，白名單功能將被跳過: {e}")
            self.email_filter = None

        # 初始化附件處理器
        try:
            from src.infrastructure.adapters.email_inbox.sync_attachment_handler import SyncAttachmentHandler
            from src.infrastructure.adapters.attachments.attachment_manager import AttachmentManager

            # 創建附件管理器和處理器
            attachment_manager = AttachmentManager()
            self.attachment_handler = SyncAttachmentHandler(self.database, attachment_manager)
            self.logger.info("✅ 附件處理器初始化成功")
        except ImportError as e:
            self.logger.warning(f"AttachmentHandler 不可用，附件處理將被跳過: {e}")
            self.attachment_handler = None
        
        self.logger.info("統一郵件處理服務已初始化")
    
    async def process_email_complete(
        self,
        email_data: EmailData,
        email_id: Optional[str] = None,
        process_attachments: bool = True,
        process_vendor_files: bool = True,
        send_notifications: bool = True,
        update_database: bool = True
    ) -> EmailProcessingResult:
        """
        完整的 ALL IN ONE 郵件處理流程
        
        Args:
            email_data: 郵件數據
            email_id: 郵件ID (可選)
            process_attachments: 是否處理附件
            process_vendor_files: 是否處理廠商檔案
            send_notifications: 是否發送通知
            update_database: 是否更新資料庫
            
        Returns:
            EmailProcessingResult: 完整處理結果
        """
        start_time = datetime.now()
        
        self.logger.info(f"🚀 開始 ALL IN ONE 郵件處理: {email_data.message_id}")
        self.logger.info(f"   主旨: {email_data.subject}")
        self.logger.info(f"   寄件者: {email_data.sender}")
        
        try:
            # ========================================
            # 步驟0: 白名單檢查
            # ========================================
            whitelist_result = None
            if self.email_filter:
                self.logger.info("🔍 步驟0: 白名單檢查...")
                filter_result = self.email_filter.filter_email(email_data)
                whitelist_result = filter_result
                
                if not filter_result['is_allowed']:
                    self.logger.warning(f"❌ 郵件被白名單過濾: {email_data.sender}")
                    self.logger.warning(f"   原因: {filter_result['reason']}")
                    
                    # 建立失敗的處理結果
                    result = EmailProcessingResult(
                        is_success=False,
                        vendor_code=None,
                        vendor_name=None,
                        parsing_result=EmailParsingResult(
                            is_success=False,
                            error_message=f"郵件被白名單過濾: {filter_result['reason']}",
                            vendor_code=None,
                            extraction_method="whitelist_filtered"
                        ),
                        vendor_result=VendorIdentificationResult(
                            vendor_code=None,
                            vendor_name=None,
                            confidence_score=0.0,
                            is_identified=False,
                            identification_method="whitelist_filtered"
                        ),
                        error_message=f"郵件被白名單過濾: {filter_result['reason']}",
                        whitelist_result=whitelist_result
                    )
                    
                    # 發送過濾通知
                    if send_notifications:
                        await self._send_whitelist_filtered_notification(email_data, filter_result)
                        result.notification_sent = True
                    
                    return result
                else:
                    self.logger.info(f"✅ 白名單檢查通過: {email_data.sender}")
                    self.logger.info(f"   原因: {filter_result['reason']}")
            else:
                self.logger.debug("⏭️ 跳過白名單檢查 (過濾器不可用)")

            # ========================================
            # 步驟1: 解析郵件 (遵循 LLM_PARSING_MODE)
            # ========================================
            self.logger.info("📧 步驟1: 解析郵件...")
            vendor_result, parsing_result = self.parser_factory.parse_email(email_data)

            # 詳細記錄廠商識別結果
            if vendor_result and vendor_result.is_identified:
                self.logger.info(f"✅ 廠商識別成功: {vendor_result.vendor_code} (分數: {vendor_result.confidence_score})")
                self.logger.info(f"   匹配模式: {vendor_result.matching_patterns}")
            else:
                self.logger.warning(f"❌ 廠商識別失敗")
                if vendor_result:
                    self.logger.warning(f"   最高分數: {vendor_result.confidence_score}")
                    self.logger.warning(f"   匹配模式: {vendor_result.matching_patterns}")

            # 詳細記錄解析結果
            if parsing_result.is_success:
                self.logger.info(f"✅ 初步解析成功:")
                self.logger.info(f"   產品代碼: {parsing_result.product_code}")
                self.logger.info(f"   MO編號: {parsing_result.mo_number}")
                self.logger.info(f"   LOT編號: {parsing_result.lot_number}")
                self.logger.info(f"   解析方法: {parsing_result.extraction_method}")
            else:
                self.logger.warning(f"❌ 初步解析失敗: {parsing_result.error_message}")

            # 驗證解析結果
            if parsing_result.is_success:
                validation_success = self._validate_parsing_result(parsing_result, vendor_result.vendor_code)
                if not validation_success:
                    parsing_result.is_success = False
                    self.logger.warning(f"❌ 解析結果驗證失敗")
                else:
                    self.logger.info(f"✅ 解析結果驗證通過")
            
            # 初始化處理結果
            result = EmailProcessingResult(
                is_success=parsing_result.is_success,
                vendor_code=vendor_result.vendor_code,
                vendor_name=vendor_result.vendor_name,
                parsing_result=parsing_result,
                vendor_result=vendor_result,
                whitelist_result=whitelist_result
            )
            
            if not parsing_result.is_success:
                self.logger.warning(f"❌ 郵件解析最終失敗: {parsing_result.error_message}")
                
                # 解析失敗也要發送通知
                if send_notifications:
                    await self._send_failure_notification(email_data, parsing_result)
                    result.notification_sent = True
                
                result.error_message = parsing_result.error_message
                return result
            
            self.logger.info(f"✅ 郵件解析成功: {vendor_result.vendor_code}")
            
            # ========================================
            # 步驟2: 處理附件
            # ========================================
            if process_attachments and email_data.attachments and self.attachment_handler:
                self.logger.info("📎 步驟2: 處理附件...")
                try:
                    attachment_result = await self._process_attachments(
                        email_data, parsing_result, email_id
                    )
                    result.attachment_result = attachment_result
                    
                    if attachment_result.get('success'):
                        self.logger.info(f"✅ 附件處理成功: {attachment_result.get('saved_count', 0)} 個")
                    else:
                        self.logger.warning(f"⚠️ 附件處理部分失敗: {attachment_result.get('failed_count', 0)} 個")
                        
                except Exception as e:
                    self.logger.error(f"❌ 附件處理失敗: {e}")
                    result.attachment_result = {'success': False, 'error': str(e)}
            else:
                self.logger.debug("⏭️ 跳過附件處理")
            
            # ========================================
            # 步驟3: 處理廠商檔案
            # ========================================
            if process_vendor_files:
                self.logger.info("📁 步驟3: 處理廠商檔案...")
                try:
                    vendor_files_result = await self._process_vendor_files(
                        email_data, parsing_result, email_id
                    )
                    result.vendor_files_result = vendor_files_result
                    self.logger.info("✅ 廠商檔案處理完成")
                except Exception as e:
                    self.logger.error(f"❌ 廠商檔案處理失敗: {e}")
                    result.vendor_files_result = {'success': False, 'error': str(e)}
            else:
                self.logger.debug("⏭️ 跳過廠商檔案處理")
            
            # ========================================
            # 步驟4: 更新資料庫
            # ========================================
            if update_database:
                self.logger.info("💾 步驟4: 更新資料庫...")
                try:
                    await self._update_database(email_data, parsing_result, email_id)
                    result.database_updated = True
                    self.logger.info("✅ 資料庫更新成功")
                except Exception as e:
                    self.logger.error(f"❌ 資料庫更新失敗: {e}")
                    result.database_updated = False
            else:
                self.logger.debug("⏭️ 跳過資料庫更新")
            
            # ========================================
            # 步驟5: 發送LINE通知
            # ========================================
            if send_notifications:
                self.logger.info("📱 步驟5: 發送LINE通知...")
                try:
                    await self._send_success_notification(email_data, parsing_result, result)
                    result.notification_sent = True
                    self.logger.info("✅ LINE通知發送成功")
                except Exception as e:
                    self.logger.error(f"❌ LINE通知發送失敗: {e}")
                    result.notification_sent = False
            else:
                self.logger.debug("⏭️ 跳過LINE通知")
            
            # 計算處理時間
            processing_time = (datetime.now() - start_time).total_seconds()
            result.processing_time = processing_time
            
            self.logger.info(f"🎉 ALL IN ONE 處理完成: {email_data.message_id}")
            self.logger.info(f"   處理時間: {processing_time:.2f}s")
            self.logger.info(f"   廠商: {vendor_result.vendor_code}")
            self.logger.info(f"   解析方法: {parsing_result.extraction_method}")
            
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"ALL IN ONE 處理發生異常: {str(e)}"
            self.logger.error(error_msg)
            
            # 異常情況也要發送通知
            if send_notifications:
                try:
                    await self._send_error_notification(email_data, str(e))
                except:
                    pass
            
            return EmailProcessingResult(
                is_success=False,
                vendor_code=None,
                vendor_name=None,
                parsing_result=EmailParsingResult(
                    is_success=False,
                    error_message=error_msg,
                    vendor_code=None,
                    extraction_method="error"
                ),
                vendor_result=VendorIdentificationResult(
                    vendor_code=None,
                    vendor_name=None,
                    confidence_score=0.0,
                    is_identified=False,
                    identification_method="error"
                ),
                error_message=error_msg,
                processing_time=processing_time
            )

    def _validate_parsing_result(self, parsing_result: EmailParsingResult, vendor_code: str = None) -> bool:
        """
        驗證解析結果
        根據廠商特定的必填欄位要求進行驗證
        """
        try:
            # 獲取廠商特定的必填欄位
            if vendor_code:
                vendor_required_fields_key = f'{vendor_code.upper()}_REQUIRED_FIELDS'
                required_fields_str = os.getenv(vendor_required_fields_key, 'PD,MO')
            else:
                # 回退到通用設定
                required_fields_str = os.getenv('REQUIRED_FIELDS', 'PD,MO')

            required_fields = required_fields_str.split(',')
            self.logger.debug(f"廠商 {vendor_code} 必填欄位: {required_fields}")

            for field in required_fields:
                field = field.strip().upper()
                if field == 'PD' and not parsing_result.product_code:
                    self.logger.warning(f"必填欄位缺失: 產品代碼")
                    return False
                elif field == 'LOT' and not parsing_result.lot_number:
                    self.logger.warning(f"必填欄位缺失: LOT編號")
                    return False
                elif field == 'MO' and not parsing_result.mo_number:
                    self.logger.warning(f"必填欄位缺失: MO編號")
                    return False

            self.logger.debug(f"廠商 {vendor_code} 必填欄位驗證通過")
            return True

        except Exception as e:
            self.logger.error(f"驗證解析結果時發生錯誤: {e}")
            return True  # 驗證失敗時不影響解析結果

    async def _process_attachments(
        self,
        email_data: EmailData,
        parsing_result: EmailParsingResult,
        email_id: Optional[str]
    ) -> Dict[str, Any]:
        """
        處理附件
        將附件存入 d:\temp\{pd}\{mo} 目錄，支援多個MO的情況
        """
        # 詳細記錄附件處理開始
        self.logger.info(f"📎 開始處理附件...")
        self.logger.info(f"   附件處理器: {'✅ 可用' if self.attachment_handler else '❌ 不可用'}")
        self.logger.info(f"   附件數量: {len(email_data.attachments) if email_data.attachments else 0}")

        if not self.attachment_handler:
            self.logger.warning("❌ 附件處理器不可用")
            return {'success': False, 'saved_count': 0, 'message': '附件處理器不可用'}

        if not email_data.attachments:
            self.logger.info("ℹ️ 無附件需要處理")
            return {'success': True, 'saved_count': 0, 'message': '無附件需要處理'}

        # 記錄附件詳情
        for i, attachment in enumerate(email_data.attachments):
            self.logger.info(f"   附件 {i+1}: {attachment.filename} ({attachment.size_bytes} bytes)")

        try:
            mo = parsing_result.mo_number
            pd = parsing_result.product_code or parsing_result.extracted_data.get('product_name', 'NO_PD')

            self.logger.info(f"📁 附件存放路徑計算:")
            self.logger.info(f"   產品代碼: {pd}")
            self.logger.info(f"   MO編號: {mo}")

            # 處理多個MO的情況
            if mo and ',' in mo:
                self.logger.info("🔄 多個MO情況，附件存放在產品層級")
                mo_list = [m.strip() for m in mo.split(',')]
                base_path = os.getenv('FILE_TEMP_BASE_PATH', 'D:\\temp')
                sanitized_pd = self._sanitize_filename(pd)

                # 附件存放在產品目錄: d:\temp\{pd}
                pd_path = os.path.join(base_path, sanitized_pd)

                self.logger.info(f"   MO列表: {mo_list}")
                self.logger.info(f"   目標路徑: {pd_path}")
                self.logger.info(f"   策略: 附件只下載一次到產品目錄")

                # 只處理一次附件，存放在產品目錄下
                self.logger.info("🚀 開始附件下載...")
                result = await self.attachment_handler.process_email_attachments(
                    email_id or email_data.message_id,
                    email_data,
                    pd_path  # 多MO時附件存入 d:\temp\{pd}
                )

                self.logger.info(f"📊 附件處理結果:")
                self.logger.info(f"   成功: {'✅' if result.get('success') else '❌'} {result.get('success')}")
                self.logger.info(f"   處理數量: {result.get('processed_count', 0)}")
                self.logger.info(f"   失敗數量: {result.get('failed_count', 0)}")

                if result.get('errors'):
                    self.logger.warning(f"   錯誤詳情: {result.get('errors')}")

                if result.get('success'):
                    self.logger.info(f"✅ 多MO附件已存入產品目錄: {pd_path}")
                else:
                    self.logger.warning(f"❌ 多MO附件處理失敗: {result.get('errors', ['Unknown error'])}")

                return {
                    'success': result.get('success', False),
                    'saved_count': result.get('saved_count', 0),
                    'message': f'多MO附件存放在產品目錄: {sanitized_pd}',
                    'path': pd_path,
                    'mo_list': mo_list,
                    'attachment_strategy': 'single_pd_directory'
                }
            else:
                # 單個MO或無MO：直接存入 d:\temp\{pd}\{mo} 目錄
                self.logger.info("📁 單個MO情況，附件存放在MO目錄")
                mo_path = self._build_attachment_path(parsing_result)

                self.logger.info(f"   目標路徑: {mo_path}")
                self.logger.info(f"   策略: 附件存放在MO專用目錄")

                self.logger.info("🚀 開始附件下載...")
                result = await self.attachment_handler.process_email_attachments(
                    email_id or email_data.message_id,
                    email_data,
                    mo_path  # 附件直接存入 d:\temp\{pd}\{mo}
                )

                self.logger.info(f"📊 附件處理結果:")
                self.logger.info(f"   成功: {'✅' if result.get('success') else '❌'} {result.get('success')}")
                self.logger.info(f"   處理數量: {result.get('processed_count', 0)}")
                self.logger.info(f"   失敗數量: {result.get('failed_count', 0)}")

                if result.get('errors'):
                    self.logger.warning(f"   錯誤詳情: {result.get('errors')}")

                if result.get('success'):
                    self.logger.info(f"✅ 附件已存入目錄: {mo_path}")
                else:
                    self.logger.warning(f"❌ 附件處理失敗: {result.get('errors', ['Unknown error'])}")

                return result

        except Exception as e:
            self.logger.error(f"處理附件時發生錯誤: {e}")
            return {'success': False, 'error': str(e)}

    def _build_attachment_path(self, parsing_result: EmailParsingResult) -> str:
        """
        構建附件儲存路徑
        - 單個MO: D:\temp\{pd}\{mo} (與vendor_files同層)
        - 多個MO: D:\temp\{pd} (產品層級，只下載一次)
        """
        try:
            base_path = os.getenv('FILE_TEMP_BASE_PATH', 'D:\\temp')
            pd = parsing_result.product_code or 'NO_PD'
            mo = parsing_result.mo_number
            lot = parsing_result.lot_number

            # 清理檔案名稱
            pd = self._sanitize_filename(pd)

            if mo and ',' in mo:
                # 多個MO：附件放在產品層級，只下載一次
                attachment_path = os.path.join(base_path, pd)
                self.logger.debug(f"多MO附件存放在產品層級: {attachment_path}")
            else:
                # 單個MO：附件與vendor_files同層
                if mo:
                    mo = self._sanitize_filename(mo)
                else:
                    mo = self._sanitize_filename(lot) if lot else 'NO_MO'

                attachment_path = os.path.join(base_path, pd, mo)
                self.logger.debug(f"單MO附件存放在: {attachment_path}")

            return attachment_path

        except Exception as e:
            self.logger.error(f"構建附件路徑時發生錯誤: {e}")
            return os.path.join(os.getenv('FILE_TEMP_BASE_PATH', 'D:\\temp'), 'NO_PD')

    def _sanitize_filename(self, filename: str) -> str:
        """清理檔案名稱中的特殊字符"""
        if not filename:
            return "UNKNOWN"

        # 移除或替換特殊字符
        import re
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        sanitized = sanitized.replace('~', '_')
        return sanitized[:100]  # 限制長度

    async def _process_vendor_files(
        self,
        email_data: EmailData,
        parsing_result: EmailParsingResult,
        email_id: Optional[str]
    ) -> Dict[str, Any]:
        """
        處理廠商相關檔案
        在郵件解析成功後，根據廠商複製相關檔案到 d:\temp\{pd}\{mo} 目錄

        Args:
            email_data: 郵件數據
            parsing_result: 解析結果
            email_id: 郵件ID

        Returns:
            Dict[str, Any]: 處理結果
        """
        try:
            # 檢查是否有必要資訊
            vendor = parsing_result.vendor_code
            mo = parsing_result.mo_number
            lot = parsing_result.lot_number
            pd = parsing_result.product_code or parsing_result.extracted_data.get('product_name', 'default')

            if not vendor:
                self.logger.debug("無廠商資訊，跳過檔案處理")
                return {'success': True, 'message': '無廠商資訊，跳過檔案處理'}

            if not mo and not lot:
                self.logger.debug("無 MO 或 LOT 資訊，跳過檔案處理")
                return {'success': True, 'message': '無 MO 或 LOT 資訊，跳過檔案處理'}

            # 導入檔案處理器工廠
            try:
                from src.infrastructure.adapters.file_handlers.file_handler_factory import FileHandlerFactory
            except ImportError:
                self.logger.warning("FileHandlerFactory 不可用，跳過檔案處理")
                return {'success': True, 'message': 'FileHandlerFactory 不可用，跳過檔案處理'}

            # 建立工廠（使用環境變數或預設路徑）
            import os
            from pathlib import Path

            source_base_path = os.getenv('FILE_SOURCE_BASE_PATH', '/mnt/share')
            factory = FileHandlerFactory(source_base_path)

            # 建立暫存目錄 - 使用 PD/MO 結構
            temp_base_path = os.getenv('FILE_TEMP_BASE_PATH', 'D:\\temp')

            # 處理多個MO的情況
            if mo and ',' in mo:
                # 多個MO的情況，為每個MO創建目錄
                mo_list = [m.strip() for m in mo.split(',')]
                results = []

                for single_mo in mo_list:
                    # 目標路徑：D:\temp\{pd}\{mo} (直接放遠端檔案和附件)
                    target_dir = Path(temp_base_path) / pd / single_mo
                    target_dir.mkdir(parents=True, exist_ok=True)

                    # 執行檔案處理 - 直接傳入MO目錄
                    result = factory.process_vendor_files(
                        vendor_code=vendor,
                        mo=single_mo,
                        temp_path=str(target_dir),  # 直接傳入MO目錄
                        pd=pd,
                        lot=lot or single_mo
                    )

                    results.append({
                        'mo': single_mo,
                        'target_dir': str(target_dir),
                        'success': result['success'],
                        'message': result.get('message', ''),
                        'error': result.get('error', '')
                    })

                    if result['success']:
                        self.logger.info(f"成功處理 {vendor} 檔案 (MO: {single_mo}): {target_dir}")
                    else:
                        self.logger.warning(f"處理 {vendor} 檔案失敗 (MO: {single_mo}): {result.get('error')}")

                # 統計結果
                success_count = sum(1 for r in results if r['success'])
                total_count = len(results)

                return {
                    'success': success_count > 0,
                    'message': f'多MO處理完成: {success_count}/{total_count} 成功',
                    'results': results,
                    'vendor': vendor,
                    'pd': pd,
                    'mo_list': mo_list
                }
            else:
                # 單個MO的情況 - 目標路徑：D:\temp\{pd}\{mo} (直接放遠端檔案和附件)
                target_dir = Path(temp_base_path) / pd / (mo or lot)
                target_dir.mkdir(parents=True, exist_ok=True)

                # 執行檔案處理 - 直接傳入MO目錄
                result = factory.process_vendor_files(
                    vendor_code=vendor,
                    mo=mo or lot,
                    temp_path=str(target_dir),  # 直接傳入MO目錄
                    pd=pd,
                    lot=lot or mo
                )

                if result['success']:
                    self.logger.info(f"成功處理 {vendor} 檔案: {target_dir}")
                    return {
                        'success': True,
                        'message': f'成功處理 {vendor} 檔案',
                        'target_dir': str(target_dir),
                        'vendor': vendor,
                        'pd': pd,
                        'mo': mo or lot
                    }
                else:
                    self.logger.warning(f"處理 {vendor} 檔案失敗: {result.get('error')}")
                    return {
                        'success': False,
                        'message': f'處理 {vendor} 檔案失敗',
                        'error': result.get('error'),
                        'vendor': vendor,
                        'pd': pd,
                        'mo': mo or lot
                    }

        except Exception as e:
            self.logger.error(f"處理廠商檔案時發生錯誤: {e}")
            return {'success': False, 'error': str(e)}

    # 🗑️ 已刪除：廠商特定的檔案處理方法
    # 現在使用統一的 _process_vendor_files 方法處理所有廠商

    async def _update_database(
        self,
        email_data: EmailData,
        parsing_result: EmailParsingResult,
        email_id: Optional[str]
    ) -> None:
        """更新資料庫"""
        try:
            # 構建更新資料
            update_data = {
                'vendor_code': parsing_result.vendor_code,
                'pd': parsing_result.product_code,
                'lot': parsing_result.lot_number,
                'mo': parsing_result.mo_number,
                'yield_value': parsing_result.extracted_data.get('yield_value') if parsing_result.extracted_data else None,
                'extraction_method': parsing_result.extraction_method,
                'parse_status': 'parsed' if parsing_result.is_success else 'failed',
                'parse_error': parsing_result.error_message,
                'updated_at': datetime.now()
            }

            # 更新資料庫
            if email_id:
                await self.database.update_email_parsing_result(email_id, update_data)
            else:
                # 如果沒有email_id，嘗試根據message_id查找
                await self.database.update_email_by_message_id(email_data.message_id, update_data)

        except Exception as e:
            self.logger.error(f"更新資料庫時發生錯誤: {e}")
            raise

    async def _send_success_notification(
        self,
        email_data: EmailData,
        parsing_result: EmailParsingResult,
        processing_result: EmailProcessingResult
    ) -> None:
        """發送成功通知"""
        try:
            notification_data = {
                'id': email_data.message_id,
                'subject': email_data.subject,
                'sender': email_data.sender,
                'received_time': email_data.received_time.isoformat() if email_data.received_time else None,
                'vendor_code': parsing_result.vendor_code,
                'extraction_method': parsing_result.extraction_method,
                'pd': parsing_result.product_code,
                'lot': parsing_result.lot_number,
                'mo_number': parsing_result.mo_number,
                'yield_value': parsing_result.extracted_data.get('yield_value') if parsing_result.extracted_data else None,
                'attachment_count': len(email_data.attachments) if email_data.attachments else 0,
                'processing_time': processing_result.processing_time
            }

            self.line_service.send_parsing_success_notification(notification_data)

        except Exception as e:
            self.logger.error(f"發送成功通知時發生錯誤: {e}")
            raise

    async def _send_failure_notification(
        self,
        email_data: EmailData,
        parsing_result: EmailParsingResult
    ) -> None:
        """發送失敗通知"""
        try:
            notification_data = {
                'id': email_data.message_id,
                'subject': email_data.subject,
                'sender': email_data.sender,
                'received_time': email_data.received_time.isoformat() if email_data.received_time else None,
                'vendor_code': 'UNKNOWN',
                'extraction_method': parsing_result.extraction_method or 'failed',
                'error_message': parsing_result.error_message,
                'pd': 'N/A',
                'lot': 'N/A',
                'mo_number': 'N/A',
                'yield_value': 'N/A'
            }

            self.line_service.send_parsing_failure_notification(notification_data)

        except Exception as e:
            self.logger.error(f"發送失敗通知時發生錯誤: {e}")
            raise

    async def _send_error_notification(
        self,
        email_data: EmailData,
        error_message: str
    ) -> None:
        """發送錯誤通知"""
        try:
            notification_data = {
                'id': email_data.message_id,
                'subject': email_data.subject,
                'sender': email_data.sender,
                'received_time': email_data.received_time.isoformat() if email_data.received_time else None,
                'vendor_code': 'ERROR',
                'extraction_method': 'error',
                'error_message': error_message,
                'pd': 'N/A',
                'lot': 'N/A',
                'mo_number': 'N/A',
                'yield_value': 'N/A'
            }

            self.line_service.send_parsing_failure_notification(notification_data)

        except Exception as e:
            self.logger.error(f"發送錯誤通知時發生錯誤: {e}")
            # 錯誤通知失敗不拋出異常，避免無限循環

    async def _send_whitelist_filtered_notification(
        self,
        email_data: EmailData,
        filter_result: Dict[str, Any]
    ) -> None:
        """發送白名單過濾通知"""
        try:
            notification_data = {
                'id': email_data.message_id,
                'subject': email_data.subject,
                'sender': email_data.sender,
                'received_time': email_data.received_time.isoformat() if email_data.received_time else None,
                'vendor_code': 'FILTERED',
                'extraction_method': 'whitelist_filtered',
                'filter_reason': filter_result.get('reason', '未知原因'),
                'filter_action': filter_result.get('action', 'block'),
                'pd': 'N/A',
                'lot': 'N/A',
                'mo_number': 'N/A',
                'yield_value': 'N/A'
            }

            self.line_service.send_parsing_failure_notification(notification_data)

        except Exception as e:
            self.logger.error(f"發送白名單過濾通知時發生錯誤: {e}")
            raise
    
    def get_whitelist_stats(self) -> Optional[Dict[str, Any]]:
        """
        取得白名單統計資訊
        
        Returns:
            Optional[Dict[str, Any]]: 白名單統計資訊，如果過濾器不可用則返回 None
        """
        if self.email_filter:
            return self.email_filter.get_filter_stats()
        return None
    
    def reload_whitelist(self) -> bool:
        """
        重新載入白名單
        
        Returns:
            bool: 是否成功
        """
        if self.email_filter:
            return self.email_filter.reload_whitelist()
        return False
