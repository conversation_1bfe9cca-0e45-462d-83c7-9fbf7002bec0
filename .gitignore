# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env
venv_win_3_11_12/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/
old_logs/

# Database
*.db
*.sqlite3

# Test Coverage
htmlcov/
.coverage
.pytest_cache/
.tox/

# Excel Files (原始 VBA 檔案，暫時保留但不追蹤變更)
*.xlsm
*.xlsx

# Temporary Files
*.tmp
*.temp
temp/
tmp/

# Node modules (如果有前端)
node_modules/

# Docker
.dockerignore

# Secrets
.env.local
.env.production
.env_key
secrets/
*.key
*.pem

# Cache
.cache/
.claude/settings.local.json

# Claude 工作目錄 - 不上傳的資料夾
.claude/reports/
.claude/logs/
.claude/backup/

# 整理目錄
bak_del/

# 測試資料目錄（包含敏感或大型測試檔案）
doc/

# 檔案上傳暫存目錄
D:/temp/
temp/uploads/
temp/extracted/

# 測試檔案和目錄（避免上傳測試檔案）
test_spd_conversion/
test_*.py
simple_test_*.py
*_test_*/
test_data/
sample_data/

# 解壓縮和上傳的檔案
*.zip
*.7z
*.rar
*.tar
*.gz
uploaded_*/
extracted_*/

# 處理結果暫存檔案
result_*/
output_*/
processed_*/

# 郵件附件目錄（可能包含敏感資料）
attachments/

logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/

# Tool configuration directories
.kiro/ n u l 
 
 
nul
