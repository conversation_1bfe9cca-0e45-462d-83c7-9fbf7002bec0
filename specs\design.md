# 技術設計文件：即時監控儀表板系統

## 1. 系統概述

本設計文件描述了為 Outlook Summary System 開發即時監控儀表板的技術實現方案。該儀表板將深度整合現有的監控資料來源，提供系統健康狀態、效能指標、錯誤追蹤和業務指標的即時視覺化展示。

### 1.1 系統現況分析

**完整系統架構（基於52.2%完成進度）：**

系統採用六角架構設計，包含四個主要層次：
- **展示層**：Flask Web服務(5000)、FastAPI服務(8010)、網路瀏覽器API(8009)、命令列介面
- **應用層**：郵件處理用例、EQC處理用例、Excel處理用例、檔案處理用例
- **領域層**：郵件實體、檔案實體、處理服務、值物件
- **基礎設施層**：郵件適配器、資料庫、檔案系統、LLM客戶端

**核心處理系統分析：**

1. **EQC一鍵完成流程系統**：
   - **兩階段處理架構**：第一階段檔案整合（2.3秒平均處理時間），第二階段8步驟完整處理
   - **8個核心處理步驟**：EQC總資料生成→CODE區間檢測→雙重搜尋機制→InsEqcRtData2處理→Excel生成與標記→檔案重命名→CSV到Excel轉換→完整報告生成
   - **雙重搜尋機制**：主要區間精確匹配 + 備用區間映射匹配
   - **CODE區間檢測**：自動檢測主要區間和備用區間，支援前端自訂區間設定

2. **Excel處理系統**：
   - **8步驟處理流程**：檔案載入→格式檢測→資料清洗→資料轉換→資料計算→Excel生成→樣式應用→檔案輸出
   - **BIN1保護機制**：保護關鍵測試項目的BIN1設備，確保99.99%保護準確率
   - **向量化處理**：使用Pandas和NumPy的向量化操作
   - **分塊處理**：對大型檔案採用分塊處理策略，預設10000行/塊

3. **廠商解析器系統（100%完成）**：
   - **GTK解析器**：識別條件`ft hold`、`ft lot`，93%測試覆蓋率，98.5%成功率
   - **ETD解析器**：識別條件`anf`，85%測試覆蓋率，97.8%成功率
   - **XAHT解析器**：識別條件`tianshui`、`西安`，79%測試覆蓋率，96.5%成功率
   - **JCET解析器**：識別條件`jcet`，93%測試覆蓋率，99.0%成功率
   - **LINGSEN解析器**：識別條件`lingsen`，90%測試覆蓋率，98.2%成功率

### 1.2 設計目標

- **深度整合現有系統**：整合52.2%完成進度系統的24/46已完成任務
- **三層API服務監控**：Flask(5000)、FastAPI(8010)、網路瀏覽器API(8009)的統一監控
- **廠商解析器效能監控**：5個廠商解析器的詳細效能和成功率監控
- **EQC處理流程監控**：兩階段處理架構和8步驟處理流程的即時監控
- **Excel處理系統監控**：8步驟處理流程、BIN1保護機制、效能瓶頸的全面監控
- **LLM整合監控**：UnifiedLLMClient的Ollama和Grok服務監控
- **即時警報系統**：基於閾值的智能警報和歷史趨勢分析

### 1.3 技術約束

- **六角架構相容性**：必須與現有六角架構無縫整合，不破壞領域邊界
- **效能無影響原則**：監控系統不能影響現有系統的處理效能
- **彩色日誌系統整合**：支援現有的LoggerManager彩色日誌系統
- **現有API端點利用**：最大化利用現有的API端點和資料庫結構
- **TDD開發方法**：支援210+個測試的TDD開發方法，確保程式碼品質

## 2. 系統架構設計

### 2.1 整體架構

監控儀表板採用分層架構，深度整合現有系統：

```
┌─────────────────────────────────────────────────────────────────┐
│                    監控儀表板前端 (Vue.js + ECharts)              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ 系統健康面板 │  │ 郵件處理面板 │  │ 廠商統計面板 │  │ EQC面板  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │ HTTP/WebSocket
┌───────────────────────────▼─────────────────────────────────────┐
│                    監控資料收集層 (Python)                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ 系統監控器   │  │ 處理統計器   │  │ 廠商分析器   │  │ 錯誤追蹤 │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└───────────────────────────┬─────────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────────┐
│                      現有系統整合層                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ Flask(5000) │  │FastAPI(8010)│  │網路API(8009)│  │ 資料庫  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 技術選型

基於現有系統架構，選擇相容的技術棧：

**後端框架：**
- Flask (擴展現有服務)
- FastAPI (擴展現有服務)
- SQLAlchemy (利用現有資料庫)
- APScheduler (定時監控任務)

**前端框架：**
- Vue.js 3 (響應式界面)
- ECharts (資料視覺化)
- Axios (API 請求)
- Element Plus (UI 元件)

**通訊協議：**
- RESTful API (資料查詢)
- WebSocket (即時更新)

**資料存儲：**
- SQLite (與現有系統共用)
- Redis (可選，用於快取)

### 2.3 六角架構整合

監控系統將遵循現有的六角架構模式：

```
監控系統六角架構
├── 領域層 (Domain Layer)
│   ├── 監控實體 (Monitoring Entities)
│   ├── 監控服務 (Monitoring Services)
│   └── 監控值物件 (Monitoring Value Objects)
├── 應用層 (Application Layer)
│   ├── 監控用例 (Monitoring Use Cases)
│   └── 監控介面 (Monitoring Interfaces)
├── 基礎設施層 (Infrastructure Layer)
│   ├── 監控適配器 (Monitoring Adapters)
│   ├── 資料收集器 (Data Collectors)
│   └── 外部服務整合 (External Service Integration)
└── 展示層 (Presentation Layer)
    ├── 監控API (Monitoring API)
    └── 監控UI (Monitoring UI)
```

## 3. 資料模型設計

### 3.1 核心監控資料模型

```python
# 系統狀態模型
@dataclass
class SystemStatus:
    services: Dict[str, ServiceStatus]  # 服務狀態
    resources: SystemResources          # 系統資源
    uptime: Dict[str, int]             # 運行時間（秒）
    last_updated: datetime             # 最後更新時間

# 服務狀態模型
@dataclass
class ServiceStatus:
    name: str                          # 服務名稱
    port: int                          # 服務端口
    status: str                        # 狀態 (running, stopped, error)
    response_time: float               # 回應時間（毫秒）
    error_message: Optional[str]       # 錯誤訊息
    last_checked: datetime             # 最後檢查時間

# 系統資源模型
@dataclass
class SystemResources:
    cpu_usage: float                   # CPU 使用率 (%)
    memory_usage: float                # 記憶體使用率 (%)
    disk_usage: float                  # 磁碟使用率 (%)
    network_io: Dict[str, float]       # 網路 IO (bytes/s)

# 郵件處理統計模型
@dataclass
class EmailProcessingStats:
    total_emails: int                  # 總郵件數
    processed_emails: int              # 已處理郵件數
    success_rate: float                # 成功率 (%)
    processing_rate: float             # 處理速率 (郵件/分鐘)
    avg_processing_time: float         # 平均處理時間（秒）
    queue_length: int                  # 佇列長度
    estimated_time: float              # 預估處理時間（秒）

# 廠商統計模型
@dataclass
class VendorStats:
    vendor_name: str                   # 廠商名稱
    identification_pattern: str        # 識別模式
    total_processed: int               # 總處理數
    success_count: int                 # 成功數
    error_count: int                   # 錯誤數
    success_rate: float                # 成功率 (%)
    avg_processing_time: float         # 平均處理時間（秒）
    confidence_score_avg: float        # 平均信心分數
    test_coverage: float               # 測試覆蓋率
    trend: List[DailyStats]            # 趨勢資料

# EQC處理狀態模型
@dataclass
class EQCProcessingStatus:
    stage: str                         # 處理階段 (stage1, stage2)
    current_step: int                  # 當前步驟 (1-8)
    step_name: str                     # 步驟名稱
    progress_percentage: float         # 進度百分比
    files_processed: int               # 已處理檔案數
    estimated_completion: datetime     # 預估完成時間
    code_region_settings: Dict         # CODE區間設定

# Excel處理統計模型
@dataclass
class ExcelProcessingStats:
    csv_files_processed: int           # 處理的CSV檔案數
    excel_files_generated: int         # 生成的Excel檔案數
    bin1_protections: int              # BIN1保護次數
    processing_time_avg: float         # 平均處理時間
    performance_bottleneck: str        # 效能瓶頸
    summary_generation_time: float     # Summary生成時間
```

### 3.2 資料庫擴展設計

在現有的 SQLite 資料庫中新增監控相關表：

```sql
-- 系統狀態表
CREATE TABLE IF NOT EXISTS system_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    status_type TEXT NOT NULL,      -- 狀態類型 (service, resource)
    status_name TEXT NOT NULL,      -- 狀態名稱
    status_value TEXT NOT NULL,     -- 狀態值
    details TEXT,                   -- 詳細資訊 (JSON)
    created_at TEXT NOT NULL        -- 創建時間
);

-- 效能指標表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_type TEXT NOT NULL,      -- 指標類型
    metric_name TEXT NOT NULL,      -- 指標名稱
    metric_value REAL NOT NULL,     -- 指標值
    details TEXT,                   -- 詳細資訊 (JSON)
    created_at TEXT NOT NULL        -- 創建時間
);

-- 警報記錄表
CREATE TABLE IF NOT EXISTS alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    alert_type TEXT NOT NULL,       -- 警報類型
    severity TEXT NOT NULL,         -- 嚴重程度 (critical, warning, info)
    message TEXT NOT NULL,          -- 警報訊息
    details TEXT,                   -- 詳細資訊 (JSON)
    acknowledged INTEGER DEFAULT 0, -- 是否已確認
    created_at TEXT NOT NULL        -- 創建時間
);

-- 儀表板配置表
CREATE TABLE IF NOT EXISTS dashboard_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT UNIQUE NOT NULL, -- 配置鍵
    config_value TEXT NOT NULL,      -- 配置值 (JSON)
    updated_at TEXT NOT NULL         -- 更新時間
);

-- 監控會話表
CREATE TABLE IF NOT EXISTS monitoring_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT UNIQUE NOT NULL, -- 會話ID
    start_time TEXT NOT NULL,        -- 開始時間
    end_time TEXT,                   -- 結束時間
    session_data TEXT,               -- 會話資料 (JSON)
    created_at TEXT NOT NULL         -- 創建時間
);
```

## 4. API 設計

### 4.1 RESTful API 端點

#### 系統狀態 API
```
GET /api/monitor/system/status
返回所有系統服務的狀態和系統資源使用情況

GET /api/monitor/system/services
返回所有服務的狀態

GET /api/monitor/system/resources
返回系統資源使用情況

GET /api/monitor/system/uptime
返回各服務的運行時間統計
```

#### 處理統計 API
```
GET /api/monitor/processing/stats
返回郵件處理統計資訊

GET /api/monitor/processing/queue
返回處理佇列狀態

GET /api/monitor/processing/history?days=7
返回指定天數的處理歷史資料

GET /api/monitor/processing/performance
返回處理效能分析
```

#### 廠商統計 API
```
GET /api/monitor/vendors/stats
返回所有廠商的統計資訊

GET /api/monitor/vendors/{vendor_name}/stats
返回指定廠商的統計資訊

GET /api/monitor/vendors/{vendor_name}/history?days=7
返回指定廠商指定天數的歷史資料

GET /api/monitor/vendors/comparison
返回廠商之間的比較分析
```

#### EQC處理監控 API
```
GET /api/monitor/eqc/status
返回EQC處理流程的當前狀態

GET /api/monitor/eqc/stage/{stage_id}/progress
返回指定階段的處理進度

GET /api/monitor/eqc/performance
返回EQC處理效能統計

GET /api/monitor/eqc/dual-search/stats
返回雙重搜尋機制統計
```

#### Excel處理監控 API
```
GET /api/monitor/excel/stats
返回Excel處理統計資訊

GET /api/monitor/excel/performance
返回Excel處理效能分析

GET /api/monitor/excel/bin1-protection
返回BIN1保護機制統計

GET /api/monitor/excel/bottlenecks
返回處理瓶頸分析
```

#### 錯誤日誌 API
```
GET /api/monitor/errors?level=ERROR&limit=50
返回錯誤日誌，可按等級過濾

GET /api/monitor/errors/{error_id}
返回指定錯誤的詳細資訊

GET /api/monitor/errors/summary
返回錯誤統計摘要

POST /api/monitor/errors/{error_id}/acknowledge
確認錯誤處理
```

#### 警報管理 API
```
GET /api/monitor/alerts
返回活動警報列表

POST /api/monitor/alerts/{alert_id}/acknowledge
確認警報

GET /api/monitor/alerts/history
返回警報歷史記錄

GET /api/monitor/alerts/config
返回警報配置
```

#### 配置管理 API
```
GET /api/monitor/config
返回儀表板配置

PUT /api/monitor/config
更新儀表板配置

GET /api/monitor/config/thresholds
返回警報閾值配置

PUT /api/monitor/config/thresholds
更新警報閾值配置
```

### 4.2 WebSocket API

```
WebSocket /api/monitor/ws
即時推送系統狀態更新、新錯誤和警報

消息格式：
{
    "type": "system_status|alert|error|metric_update",
    "timestamp": "2025-07-26T10:30:00Z",
    "data": { ... }
}
```

### 4.3 API 安全設計

```python
# API 認證和授權
class MonitoringAPIAuth:
    def __init__(self):
        self.api_keys = {}
        self.rate_limits = {}
    
    def authenticate_request(self, request):
        # API 金鑰驗證
        api_key = request.headers.get('X-API-Key')
        return self.validate_api_key(api_key)
    
    def check_rate_limit(self, client_id):
        # 速率限制檢查
        return self.is_within_rate_limit(client_id)
```

## 5. 核心組件設計

### 5.1 監控資料收集器

```python
class MonitorDataCollector:
    """監控資料收集器，負責收集各種監控資料"""
    
    def __init__(self, collection_interval: int = 60):
        self.collection_interval = collection_interval
        self.scheduler = None
        self.db = EmailDatabase()
        self.collectors = {
            'system': SystemStatusCollector(),
            'email': EmailProcessingCollector(),
            'vendor': VendorStatsCollector(),
            'eqc': EQCProcessingCollector(),
            'excel': ExcelProcessingCollector(),
            'llm': LLMServiceCollector(),
            'network': NetworkStatusCollector()
        }
    
    def start(self):
        """啟動定時收集任務"""
        self.scheduler = BackgroundScheduler()
        for name, collector in self.collectors.items():
            self.scheduler.add_job(
                collector.collect,
                'interval',
                seconds=self.collection_interval,
                id=f"collect_{name}",
                replace_existing=True
            )
        self.scheduler.start()
    
    def stop(self):
        """停止定時收集任務"""
        if self.scheduler:
            self.scheduler.shutdown()
    
    def collect_all(self):
        """手動觸發所有資料收集"""
        for collector in self.collectors.values():
            collector.collect()
```

### 5.2 系統狀態監控器

```python
class SystemStatusMonitor:
    """系統狀態監控器"""
    
    def __init__(self):
        self.services = {
            'flask': {'port': 5000, 'name': 'Flask郵件服務'},
            'fastapi': {'port': 8010, 'name': 'FastAPI處理服務'},
            'network_api': {'port': 8009, 'name': '網路瀏覽器API'}
        }
    
    def check_services_status(self) -> Dict[str, ServiceStatus]:
        """檢查所有服務狀態"""
        status_dict = {}
        for service_id, config in self.services.items():
            status = self._check_single_service(config['port'])
            status_dict[service_id] = ServiceStatus(
                name=config['name'],
                port=config['port'],
                status=status['status'],
                response_time=status['response_time'],
                error_message=status.get('error'),
                last_checked=datetime.now()
            )
        return status_dict
    
    def _check_single_service(self, port: int) -> Dict:
        """檢查單一服務狀態"""
        try:
            start_time = time.time()
            response = requests.get(
                f"http://localhost:{port}/health",
                timeout=5
            )
            response_time = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                return {
                    'status': 'running',
                    'response_time': response_time
                }
            else:
                return {
                    'status': 'error',
                    'response_time': response_time,
                    'error': f"HTTP {response.status_code}"
                }
        except Exception as e:
            return {
                'status': 'stopped',
                'response_time': 0,
                'error': str(e)
            }
    
    def get_system_resources(self) -> SystemResources:
        """獲取系統資源使用情況"""
        import psutil
        
        return SystemResources(
            cpu_usage=psutil.cpu_percent(interval=1),
            memory_usage=psutil.virtual_memory().percent,
            disk_usage=psutil.disk_usage('/').percent,
            network_io={
                'bytes_sent': psutil.net_io_counters().bytes_sent,
                'bytes_recv': psutil.net_io_counters().bytes_recv
            }
        )
```

### 5.3 廠商監控器

```python
class VendorMonitor:
    """廠商解析器監控器"""
    
    def __init__(self):
        self.vendors = {
            'GTK': {
                'patterns': ['ft hold', 'ft lot'],
                'coverage': 93,
                'expected_success_rate': 98.5
            },
            'ETD': {
                'patterns': ['anf'],
                'coverage': 85,
                'expected_success_rate': 97.8
            },
            'XAHT': {
                'patterns': ['tianshui', '西安'],
                'coverage': 79,
                'expected_success_rate': 96.5
            },
            'JCET': {
                'patterns': ['jcet'],
                'coverage': 93,
                'expected_success_rate': 99.0
            },
            'LINGSEN': {
                'patterns': ['lingsen'],
                'coverage': 90,
                'expected_success_rate': 98.2
            }
        }
    
    def get_vendor_stats(self) -> Dict[str, VendorStats]:
        """獲取所有廠商統計資料"""
        stats = {}
        for vendor_name, config in self.vendors.items():
            stats[vendor_name] = self._calculate_vendor_stats(vendor_name, config)
        return stats
    
    def _calculate_vendor_stats(self, vendor_name: str, config: Dict) -> VendorStats:
        """計算單一廠商統計資料"""
        # 從資料庫查詢廠商處理記錄
        records = self._get_vendor_records(vendor_name)
        
        total_processed = len(records)
        success_count = len([r for r in records if r.parse_status == 'parsed'])
        error_count = total_processed - success_count
        success_rate = success_count / total_processed if total_processed > 0 else 0
        
        # 計算平均處理時間
        processing_times = [
            (r.parsed_at - r.received_time).total_seconds()
            for r in records
            if r.parsed_at and r.received_time
        ]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # 計算平均信心分數
        confidence_scores = [
            r.confidence_score for r in records 
            if r.confidence_score is not None
        ]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        return VendorStats(
            vendor_name=vendor_name,
            identification_pattern=str(config['patterns']),
            total_processed=total_processed,
            success_count=success_count,
            error_count=error_count,
            success_rate=success_rate,
            avg_processing_time=avg_processing_time,
            confidence_score_avg=avg_confidence,
            test_coverage=config['coverage'],
            trend=self._calculate_trend(vendor_name)
        )
    
    def _get_vendor_records(self, vendor_name: str):
        """從資料庫獲取廠商記錄"""
        # 實現資料庫查詢邏輯
        pass
    
    def _calculate_trend(self, vendor_name: str):
        """計算廠商處理趨勢"""
        # 實現趨勢計算邏輯
        pass
```

### 5.4 EQC處理監控器

```python
class EQCProcessMonitor:
    """EQC處理流程監控器"""
    
    def __init__(self):
        self.stages = {
            'stage1': '檔案整合階段',
            'stage2': '完整處理階段'
        }
        self.stage2_steps = [
            'EQC總資料生成',
            'CODE區間檢測',
            '雙重搜尋機制',
            'InsEqcRtData2處理',
            'Excel生成與標記',
            '檔案重命名',
            'CSV到Excel轉換',
            '完整報告生成'
        ]
    
    def get_current_status(self) -> EQCProcessingStatus:
        """獲取當前EQC處理狀態"""
        # 從API或日誌檔案獲取當前處理狀態
        current_process = self._get_current_eqc_process()
        
        if current_process:
            return EQCProcessingStatus(
                stage=current_process.get('stage', 'idle'),
                current_step=current_process.get('step', 0),
                step_name=self._get_step_name(current_process.get('step', 0)),
                progress_percentage=current_process.get('progress', 0),
                files_processed=current_process.get('files_processed', 0),
                estimated_completion=current_process.get('estimated_completion'),
                code_region_settings=current_process.get('code_regions', {})
            )
        
        return EQCProcessingStatus(
            stage='idle',
            current_step=0,
            step_name='待機中',
            progress_percentage=0,
            files_processed=0,
            estimated_completion=None,
            code_region_settings={}
        )
    
    def monitor_dual_search_mechanism(self) -> Dict:
        """監控雙重搜尋機制"""
        return {
            'main_region_matches': self._get_main_region_stats(),
            'backup_region_matches': self._get_backup_region_stats(),
            'mapping_success_rate': self._get_mapping_success_rate(),
            'code_region_override_usage': self._get_override_usage()
        }
    
    def _get_current_eqc_process(self):
        """從系統獲取當前EQC處理狀態"""
        # 實現狀態獲取邏輯
        pass
    
    def _get_step_name(self, step: int) -> str:
        """獲取步驟名稱"""
        if 0 < step <= len(self.stage2_steps):
            return self.stage2_steps[step - 1]
        return '未知步驟'
```

### 5.5 警報管理器

```python
class AlertManager:
    """警報管理器"""
    
    def __init__(self):
        self.alert_rules = {}
        self.active_alerts = {}
        self.notification_channels = []
    
    def register_alert_rule(self, rule_id: str, rule: AlertRule):
        """註冊警報規則"""
        self.alert_rules[rule_id] = rule
    
    def check_alerts(self, metrics: Dict):
        """檢查警報條件"""
        for rule_id, rule in self.alert_rules.items():
            if rule.should_trigger(metrics):
                self._trigger_alert(rule_id, rule, metrics)
            elif rule_id in self.active_alerts:
                self._resolve_alert(rule_id)
    
    def _trigger_alert(self, rule_id: str, rule: AlertRule, metrics: Dict):
        """觸發警報"""
        alert = Alert(
            rule_id=rule_id,
            severity=rule.severity,
            message=rule.generate_message(metrics),
            timestamp=datetime.now(),
            metadata=metrics
        )
        
        self.active_alerts[rule_id] = alert
        self._send_notifications(alert)
    
    def _resolve_alert(self, rule_id: str):
        """解決警報"""
        if rule_id in self.active_alerts:
            del self.active_alerts[rule_id]
    
    def _send_notifications(self, alert: Alert):
        """發送通知"""
        for channel in self.notification_channels:
            channel.send(alert)

class AlertRule:
    """警報規則"""
    
    def __init__(self, condition, severity, message_template):
        self.condition = condition
        self.severity = severity
        self.message_template = message_template
    
    def should_trigger(self, metrics: Dict) -> bool:
        """檢查是否應該觸發警報"""
        return self.condition(metrics)
    
    def generate_message(self, metrics: Dict) -> str:
        """生成警報訊息"""
        return self.message_template.format(**metrics)
```

## 6. 前端設計

### 6.1 Vue.js 3 架構

```
frontend/
├── src/
│   ├── components/
│   │   ├── charts/
│   │   │   ├── SystemHealthChart.vue
│   │   │   ├── VendorStatsChart.vue
│   │   │   ├── ProcessingTrendChart.vue
│   │   │   └── AlertsChart.vue
│   │   ├── widgets/
│   │   │   ├── MetricCard.vue
│   │   │   ├── ServiceStatus.vue
│   │   │   ├── AlertPanel.vue
│   │   │   └── ConfigPanel.vue
│   │   └── layout/
│   │       ├── Header.vue
│   │       ├── Sidebar.vue
│   │       └── Footer.vue
│   ├── views/
│   │   ├── Dashboard.vue
│   │   ├── SystemMonitoring.vue
│   │   ├── VendorAnalysis.vue
│   │   ├── ProcessingStatus.vue
│   │   ├── ErrorLogs.vue
│   │   └── Settings.vue
│   ├── stores/
│   │   ├── monitoring.js
│   │   ├── alerts.js
│   │   └── config.js
│   ├── services/
│   │   ├── api.js
│   │   ├── websocket.js
│   │   └── charts.js
│   └── utils/
│       ├── date.js
│       ├── format.js
│       └── constants.js
```

### 6.2 核心 Vue 元件

```vue
<!-- SystemHealthChart.vue -->
<template>
  <div class="system-health-chart">
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import { useMonitoringStore } from '@/stores/monitoring'

const chartContainer = ref(null)
const monitoringStore = useMonitoringStore()
let chart = null

onMounted(() => {
  chart = echarts.init(chartContainer.value)
  updateChart()
})

watch(() => monitoringStore.systemStatus, updateChart)

function updateChart() {
  const option = {
    title: { text: '系統健康狀態' },
    xAxis: { type: 'category', data: ['CPU', '記憶體', '磁碟'] },
    yAxis: { type: 'value', max: 100 },
    series: [{
      type: 'bar',
      data: [
        monitoringStore.systemStatus.cpu_usage,
        monitoringStore.systemStatus.memory_usage,
        monitoringStore.systemStatus.disk_usage
      ]
    }]
  }
  chart.setOption(option)
}
</script>
```

### 6.3 WebSocket 整合

```javascript
// websocket.js
class MonitoringWebSocket {
  constructor() {
    this.ws = null
    this.reconnectInterval = 5000
    this.callbacks = new Map()
  }
  
  connect() {
    this.ws = new WebSocket('ws://localhost:8010/api/monitor/ws')
    
    this.ws.onopen = () => {
      console.log('監控 WebSocket 已連接')
    }
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }
    
    this.ws.onclose = () => {
      console.log('監控 WebSocket 已斷線，嘗試重連...')
      setTimeout(() => this.connect(), this.reconnectInterval)
    }
  }
  
  handleMessage(data) {
    const { type, data: payload } = data
    if (this.callbacks.has(type)) {
      this.callbacks.get(type).forEach(callback => callback(payload))
    }
  }
  
  subscribe(type, callback) {
    if (!this.callbacks.has(type)) {
      this.callbacks.set(type, [])
    }
    this.callbacks.get(type).push(callback)
  }
}

export default new MonitoringWebSocket()
```

## 7. 部署和運維

### 7.1 Docker 容器化

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 複製依賴文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 複製應用代碼
COPY src/ ./src/
COPY monitoring/ ./monitoring/

# 設置環境變數
ENV PYTHONPATH=/app
ENV MONITORING_CONFIG=/app/config/monitoring.json

# 暴露端口
EXPOSE 8011

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8011/health || exit 1

# 啟動命令
CMD ["python", "-m", "monitoring.main"]
```

### 7.2 監控服務配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  monitoring:
    build: .
    ports:
      - "8011:8011"
    environment:
      - MONITORING_DB_PATH=/data/monitoring.db
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - monitoring
    restart: unless-stopped

volumes:
  redis_data:
```

### 7.3 效能優化

```python
# 效能優化配置
class MonitoringPerformanceConfig:
    # 資料收集間隔設定
    COLLECTION_INTERVALS = {
        'system_status': 30,      # 系統狀態每30秒
        'processing_stats': 60,   # 處理統計每60秒
        'vendor_analysis': 300,   # 廠商分析每5分鐘
        'llm_monitoring': 120,    # LLM監控每2分鐘
    }
    
    # 資料保留策略
    DATA_RETENTION = {
        'realtime': '1 day',      # 即時資料保留1天
        'hourly': '7 days',       # 小時統計保留7天
        'daily': '30 days',       # 日統計保留30天
        'monthly': '365 days',    # 月統計保留1年
    }
    
    # 快取配置
    CACHE_CONFIG = {
        'redis_url': 'redis://localhost:6379',
        'default_ttl': 300,       # 預設5分鐘過期
        'max_connections': 20,    # 最大連接數
    }
```

## 8. 安全性設計

### 8.1 認證授權

```python
class MonitoringAuth:
    """監控系統認證授權"""
    
    def __init__(self):
        self.jwt_secret = os.getenv('JWT_SECRET')
        self.api_keys = self._load_api_keys()
    
    def authenticate_user(self, token: str) -> Optional[User]:
        """使用者認證"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            return User.from_payload(payload)
        except jwt.InvalidTokenError:
            return None
    
    def check_permission(self, user: User, resource: str, action: str) -> bool:
        """權限檢查"""
        return user.has_permission(resource, action)
    
    def validate_api_key(self, api_key: str) -> bool:
        """API金鑰驗證"""
        return api_key in self.api_keys
```

### 8.2 資料保護

```python
class DataProtection:
    """資料保護機制"""
    
    @staticmethod
    def sanitize_email_data(email_data: Dict) -> Dict:
        """郵件資料脫敏"""
        sanitized = email_data.copy()
        # 移除敏感資訊
        sanitized.pop('email_content', None)
        sanitized.pop('personal_info', None)
        # 遮蔽郵件地址
        if 'sender' in sanitized:
            sanitized['sender'] = DataProtection.mask_email(sanitized['sender'])
        return sanitized
    
    @staticmethod
    def mask_email(email: str) -> str:
        """遮蔽郵件地址"""
        local, domain = email.split('@')
        masked_local = local[:2] + '*' * (len(local) - 2)
        return f"{masked_local}@{domain}"
```

## 9. 測試策略

### 9.1 單元測試

```python
# tests/test_system_monitor.py
import pytest
from monitoring.core.system_monitor import SystemStatusMonitor

class TestSystemStatusMonitor:
    
    def setup_method(self):
        self.monitor = SystemStatusMonitor()
    
    def test_check_services_status(self):
        """測試服務狀態檢查"""
        status = self.monitor.check_services_status()
        assert isinstance(status, dict)
        assert 'flask' in status
        assert 'fastapi' in status
        assert 'network_api' in status
    
    def test_get_system_resources(self):
        """測試系統資源獲取"""
        resources = self.monitor.get_system_resources()
        assert resources.cpu_usage >= 0
        assert resources.memory_usage >= 0
        assert resources.disk_usage >= 0
```

### 9.2 整合測試

```python
# tests/integration/test_monitoring_api.py
import pytest
from fastapi.testclient import TestClient
from monitoring.main import app

class TestMonitoringAPI:
    
    def setup_method(self):
        self.client = TestClient(app)
    
    def test_system_status_endpoint(self):
        """測試系統狀態API"""
        response = self.client.get("/api/monitor/system/status")
        assert response.status_code == 200
        data = response.json()
        assert 'services' in data
        assert 'resources' in data
    
    def test_vendor_stats_endpoint(self):
        """測試廠商統計API"""
        response = self.client.get("/api/monitor/vendors/stats")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, dict)
```

### 9.3 效能測試

```python
# tests/performance/test_monitoring_performance.py
import time
import pytest
from monitoring.core.data_collector import MonitorDataCollector

class TestMonitoringPerformance:
    
    def test_data_collection_performance(self):
        """測試資料收集效能"""
        collector = MonitorDataCollector()
        
        start_time = time.time()
        collector.collect_all()
        end_time = time.time()
        
        collection_time = end_time - start_time
        assert collection_time < 5.0  # 5秒內完成
    
    def test_api_response_time(self):
        """測試API回應時間"""
        from fastapi.testclient import TestClient
        from monitoring.main import app
        
        client = TestClient(app)
        
        start_time = time.time()
        response = client.get("/api/monitor/system/status")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response_time < 1.0  # 1秒內回應
        assert response.status_code == 200
```

## 10. 監控和維護

### 10.1 自我監控

```python
class SelfMonitoring:
    """監控系統自我監控"""
    
    def __init__(self):
        self.metrics = {}
        self.health_checks = []
    
    def register_health_check(self, name: str, check_func):
        """註冊健康檢查"""
        self.health_checks.append((name, check_func))
    
    def perform_health_checks(self) -> Dict:
        """執行健康檢查"""
        results = {}
        for name, check_func in self.health_checks:
            try:
                results[name] = check_func()
            except Exception as e:
                results[name] = {'status': 'error', 'error': str(e)}
        return results
    
    def get_self_metrics(self) -> Dict:
        """獲取自身指標"""
        return {
            'uptime': self._get_uptime(),
            'memory_usage': self._get_memory_usage(),
            'active_connections': self._get_active_connections(),
            'data_collection_rate': self._get_collection_rate()
        }
```

### 10.2 日誌管理

```python
class MonitoringLogger:
    """監控系統日誌管理"""
    
    def __init__(self):
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """設置日誌記錄器"""
        logger = logging.getLogger('monitoring')
        logger.setLevel(logging.INFO)
        
        # 檔案處理器
        file_handler = RotatingFileHandler(
            'logs/monitoring.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        
        # 控制台處理器
        console_handler = logging.StreamHandler()
        
        # 格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
```

---

**文件版本：** 1.0  
**最後更新：** 2025-07-26  
**核准狀態：** 待核准