# 郵件白名單功能配置範例
# 請複製此檔案並重新命名為 .env 或在環境變數中設定

# ===== 白名單功能控制 =====
# 啟用或停用白名單功能
EMAIL_WHITELIST_ENABLED=true

# 不在白名單時的預設行為 (allow/deny)
# allow: 允許處理不在白名單中的郵件 (較寬鬆)
# deny: 拒絕處理不在白名單中的郵件 (較嚴格)
EMAIL_WHITELIST_DEFAULT_ACTION=deny

# 白名單檢查失敗時的預設行為 (allow/deny)
# 當白名單檔案載入失敗或檢查過程發生錯誤時的處理方式
EMAIL_WHITELIST_DEFAULT_ON_ERROR=allow

# ===== 白名單檔案管理 =====
# 配置檔案目錄
EMAIL_CONFIG_DIR=./config

# 自動重新載入白名單檔案
EMAIL_WHITELIST_AUTO_RELOAD=true

# 檢查白名單檔案修改的間隔時間 (秒)
EMAIL_WHITELIST_CHECK_INTERVAL=300

# ===== 郵件處理配置 =====
# 郵件讀取相關配置
EMAIL_DELETE_AFTER_READ=false
EMAIL_MAX_FETCH_COUNT=100

# ===== 使用範例 =====
# 1. 複製 config/.emaillist.example 為 config/.emaillist
# 2. 編輯 config/.emaillist 添加信任的郵件地址和網域
# 3. 設定上述環境變數
# 4. 重新啟動郵件處理服務

# ===== 白名單檔案格式範例 =====
# <EMAIL>              # 允許特定郵件地址
# @trusted-vendor.com            # 允許整個網域
# <EMAIL>           # 技術支援郵件

# ===== 注意事項 =====
# - 建議先設定 EMAIL_WHITELIST_DEFAULT_ACTION=allow 進行測試
# - 確認白名單運作正常後再改為 deny 以提高安全性
# - 定期檢查和更新白名單內容
# - 監控郵件處理日誌確認過濾效果