"""
.emaillist 檔案解析器
負責解析和驗證 .emaillist 檔案格式
"""

import os
import re
from pathlib import Path
from typing import List, Optional, Union, Set
from datetime import datetime

from .models import EmailWhitelistEntry, WhitelistEntryType
from .exceptions import (
    EmaillistFileError, 
    EmaillistParseError, 
    EmaillistValidationError
)
from src.infrastructure.logging.logger_manager import LoggerManager


class EmaillistParser:
    """
    .emaillist 檔案解析器
    
    支援格式：
    - # 註解行
    - <EMAIL>         # 完整郵件地址
    - @domain.com            # 網域匹配  
    - <EMAIL>    # 另一個地址
    - 空行（會被忽略）
    """
    
    def __init__(self):
        """初始化解析器"""
        self.logger = LoggerManager().get_logger("EmaillistParser")
        
        # 郵件地址正則表達式
        self.email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
        
        # 網域模式正則表達式 (@domain.com)
        self.domain_pattern = re.compile(
            r'^@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
        
        # 註解模式
        self.comment_pattern = re.compile(r'^\s*#')
        
        # 空行模式
        self.empty_line_pattern = re.compile(r'^\s*$')
    
    def parse_file(self, file_path: Union[str, Path]) -> List[EmailWhitelistEntry]:
        """
        解析 .emaillist 檔案
        
        Args:
            file_path: .emaillist 檔案路徑
            
        Returns:
            List[EmailWhitelistEntry]: 解析出的白名單條目列表
            
        Raises:
            EmaillistFileError: 檔案處理錯誤
            EmaillistParseError: 解析錯誤
        """
        file_path = Path(file_path)
        
        # 檢查檔案是否存在
        if not file_path.exists():
            raise EmaillistFileError(
                str(file_path), 
                "檔案不存在"
            )
        
        # 檢查檔案是否可讀
        if not file_path.is_file():
            raise EmaillistFileError(
                str(file_path), 
                "不是有效的檔案"
            )
        
        self.logger.info(f"開始解析 .emaillist 檔案: {file_path}")
        
        entries = []
        errors = []
        line_number = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line_number += 1
                    
                    try:
                        entry = self._parse_line(line, line_number)
                        if entry:
                            entries.append(entry)
                    except EmaillistParseError as e:
                        errors.append(e)
                        self.logger.warning(f"解析第 {line_number} 行失敗: {e.message}")
                        
        except UnicodeDecodeError as e:
            raise EmaillistFileError(
                str(file_path),
                f"檔案編碼錯誤: {str(e)}"
            )
        except IOError as e:
            raise EmaillistFileError(
                str(file_path),
                f"讀取檔案失敗: {str(e)}"
            )
        
        # 檢查重複條目
        self._check_duplicates(entries)
        
        self.logger.info(f"解析完成: 共 {len(entries)} 個有效條目，{len(errors)} 個錯誤")
        
        # 如果有解析錯誤但仍有有效條目，記錄警告
        if errors and entries:
            self.logger.warning(f"檔案解析完成但存在 {len(errors)} 個錯誤行")
        
        # 如果沒有任何有效條目，拋出異常
        if not entries and errors:
            raise EmaillistFileError(
                str(file_path),
                f"檔案解析失敗，無有效條目 (共 {len(errors)} 個錯誤)"
            )
        
        return entries
    
    def _parse_line(self, line: str, line_number: int) -> Optional[EmailWhitelistEntry]:
        """
        解析單行內容
        
        Args:
            line: 行內容
            line_number: 行號
            
        Returns:
            Optional[EmailWhitelistEntry]: 解析結果，None 表示跳過該行
            
        Raises:
            EmaillistParseError: 解析錯誤
        """
        original_line = line
        
        # 移除行末註解
        if '#' in line:
            line_parts = line.split('#', 1)
            line = line_parts[0]
            comment = line_parts[1].strip() if len(line_parts) > 1 else None
        else:
            comment = None
        
        # 清理空白字符
        line = line.strip()
        
        # 跳過空行
        if self.empty_line_pattern.match(line):
            return None
        
        # 跳過純註解行
        if self.comment_pattern.match(original_line):
            return None
        
        # 驗證和分類模式
        try:
            entry_type, validated_pattern = self._validate_pattern(line)
            
            return EmailWhitelistEntry(
                pattern=validated_pattern,
                entry_type=entry_type,
                description=comment,
                is_active=True,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
        except EmaillistValidationError as e:
            raise EmaillistParseError(
                line=original_line,
                line_number=line_number,
                reason=e.reason
            )
    
    def _validate_pattern(self, pattern: str) -> tuple[WhitelistEntryType, str]:
        """
        驗證和分類白名單模式
        
        Args:
            pattern: 待驗證的模式
            
        Returns:
            tuple[WhitelistEntryType, str]: (條目類型, 標準化的模式)
            
        Raises:
            EmaillistValidationError: 驗證失敗
        """
        if not pattern:
            raise EmaillistValidationError(pattern, "模式不能為空")
        
        # 檢查是否為網域模式 (@domain.com)
        if pattern.startswith('@'):
            if self.domain_pattern.match(pattern):
                return WhitelistEntryType.DOMAIN, pattern.lower()
            else:
                raise EmaillistValidationError(
                    pattern, 
                    "無效的網域格式，應為 @domain.com"
                )
        
        # 檢查是否為完整郵件地址
        elif self.email_pattern.match(pattern):
            return WhitelistEntryType.EMAIL, pattern.lower()
        
        # 檢查是否包含通配符
        elif '*' in pattern or '?' in pattern:
            # 未來可擴展支援通配符
            return WhitelistEntryType.WILDCARD, pattern.lower()
        
        else:
            raise EmaillistValidationError(
                pattern,
                "無效的模式格式，支援的格式: <EMAIL> 或 @domain.com"
            )
    
    def _check_duplicates(self, entries: List[EmailWhitelistEntry]) -> None:
        """
        檢查重複條目
        
        Args:
            entries: 白名單條目列表
        """
        seen_patterns: Set[str] = set()
        duplicates = []
        
        for entry in entries:
            if entry.pattern in seen_patterns:
                duplicates.append(entry.pattern)
            else:
                seen_patterns.add(entry.pattern)
        
        if duplicates:
            self.logger.warning(f"發現重複的白名單條目: {duplicates}")
    
    def validate_file_format(self, file_path: Union[str, Path]) -> dict:
        """
        驗證 .emaillist 檔案格式
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            dict: 驗證結果報告
        """
        result = {
            'is_valid': True,
            'total_lines': 0,
            'valid_entries': 0,
            'comment_lines': 0,
            'empty_lines': 0,
            'error_lines': 0,
            'errors': [],
            'warnings': []
        }
        
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                result['is_valid'] = False
                result['errors'].append("檔案不存在")
                return result
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_number, line in enumerate(f, 1):
                    result['total_lines'] += 1
                    
                    # 檢查空行
                    if self.empty_line_pattern.match(line):
                        result['empty_lines'] += 1
                        continue
                    
                    # 檢查註解行
                    if self.comment_pattern.match(line):
                        result['comment_lines'] += 1
                        continue
                    
                    # 嘗試解析條目
                    try:
                        entry = self._parse_line(line, line_number)
                        if entry:
                            result['valid_entries'] += 1
                    except EmaillistParseError as e:
                        result['error_lines'] += 1
                        result['errors'].append(f"第 {line_number} 行: {e.reason}")
                        result['is_valid'] = False
            
            # 生成警告
            if result['error_lines'] > 0:
                result['warnings'].append(f"檔案包含 {result['error_lines']} 個錯誤行")
            
            if result['valid_entries'] == 0:
                result['warnings'].append("檔案中沒有有效的白名單條目")
                
        except Exception as e:
            result['is_valid'] = False
            result['errors'].append(f"檔案處理異常: {str(e)}")
        
        return result
    
    def generate_sample_file(self, file_path: Union[str, Path]) -> None:
        """
        生成範例 .emaillist 檔案
        
        Args:
            file_path: 目標檔案路徑
        """
        sample_content = """# .emaillist 檔案格式範例
# 此檔案定義郵件白名單，用於過濾允許處理的郵件寄件者
# 
# 支援的格式：
# 1. 完整郵件地址: <EMAIL>
# 2. 網域匹配: @example.com (匹配該網域下的所有郵件)
# 3. 註解: 以 # 開頭的行或行末註解
#
# 範例:

# 特定的郵件地址
<EMAIL>              # 允許 John Doe 的郵件
<EMAIL>                 # 測試用管理員帳號

# 允許整個網域
@trusted-vendor.com               # 信任的供應商網域
@company.com                      # 公司內部郵件
@gmail.com                        # Gmail 用戶 (謹慎使用)

# 更多範例
<EMAIL>
<EMAIL>
@secure-partner.net               # 安全合作夥伴

# 注意事項：
# - 模式匹配不區分大小寫
# - 重複的條目會被警告但不會阻止載入
# - 空行會被忽略
# - 無效的格式會被記錄為錯誤"""

        file_path = Path(file_path)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(sample_content)
            
            self.logger.info(f"已生成範例 .emaillist 檔案: {file_path}")
            
        except IOError as e:
            raise EmaillistFileError(
                str(file_path),
                f"生成範例檔案失敗: {str(e)}"
            )