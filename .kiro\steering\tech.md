# 技術堆疊與建置系統

## 核心技術

### 後端框架
- **Python >=3.9**：主要開發語言
- **Flask 2.3.3**：郵件收件夾管理服務（埠號 5000）
- **FastAPI 0.104.1**：FT-EQC 處理服務（埠號 8010）
- **SQLAlchemy 2.0.23**：資料庫 ORM 操作
- **Pydantic 2.5.0**：資料驗證與序列化

### 資料處理
- **Pandas 2.1.3**：資料操作與分析
- **OpenPyXL 3.1.2**：Excel 檔案處理
- **XlsxWriter 3.1.9**：Excel 報表生成
- **NumPy 1.24.3**：數值計算

### 資料庫
- **SQLite**：開發與測試環境
- **PostgreSQL**：生產環境（透過 Docker）

### 測試與品質
- **Pytest**：測試框架，要求 90%+ 覆蓋率
- **Playwright**：端對端測試
- **Black**：程式碼格式化
- **Flake8**：程式碼檢查
- **MyPy**：型別檢查
- **Coverage**：測試覆蓋率報告

### 基礎設施
- **Docker & Docker Compose**：容器化部署
- **Prometheus**：指標收集
- **Grafana**：監控儀表板
- **Loguru**：結構化日誌

### AI/LLM 整合
- **Ollama**：本地 LLM 提供者（預設）
- **Grok API**：替代 LLM 提供者
- **Sentence Transformers**：文字嵌入

## 建置系統

### 環境設定
```bash
# 建立虛擬環境（必要）
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# 安裝相依套件
pip install -r requirements.txt
```

### 開發指令
```bash
# 執行測試並產生覆蓋率報告
pytest --cov=src --cov-report=html

# 程式碼品質檢查
black src/ tests/
flake8 src/ tests/
mypy src/

# 啟動開發服務
python start_integrated_services.py
```

### Makefile 指令
```bash
make help              # 顯示可用指令
make dev-setup         # 設定開發環境
make test              # 執行所有測試
make test-unit         # 僅執行單元測試
make quality-check     # 執行所有程式碼品質檢查
make tdd-red          # TDD 紅燈階段
make tdd-green        # TDD 綠燈階段
make clean            # 清理暫存檔案
```

### 服務管理
```bash
# 啟動整合服務
python start_integrated_services.py

# 個別服務端點
http://localhost:5000     # Flask 郵件收件夾服務
http://localhost:8010/ui  # FastAPI FT-EQC 服務
http://localhost:8010/docs # API 文件
```

### Docker 部署
```bash
# 啟動所有服務
docker-compose up -d

# 檢視日誌
docker-compose logs -f

# 停止服務
docker-compose down
```

### Windows 專用設定
```bash
# UTF-8 編碼（Windows 必要）
chcp 65001
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

# 使用 UV 虛擬環境（建議）
venv_win_3_11_12\Scripts\activate
```

## 開發標準

### 強制要求
- **虛擬環境**：開發前必須啟動虛擬環境
- **TDD 方法**：實作前先撰寫測試
- **測試覆蓋率**：核心業務邏輯最低 90%
- **型別提示**：所有函數必須有型別註解
- **程式碼格式**：必須符合 Black + Flake8 規範

### 品質門檻
- 所有測試必須通過
- MyPy 型別檢查：100% 符合
- 測試覆蓋率：整體 >90%，領域邏輯 >95%
- 無高風險安全問題（Bandit 掃描）

### 架構合規性
- 遵循六角架構模式
- 維持清晰的層級分離
- 使用依賴注入
- 實作適當的錯誤處理
- 包含完整的日誌記錄

<!-- 最後更新: 2025-07-21 23:36:29 -->