"""
TSHT 廠商解析器實作
基於 VBA FindTw083 邏輯和範例分析

VBA 邏輯參考：
- 識別條件：包含 "TW083" 關鍵字和相關格式
- 解析規則：基於 FindTw083 函數，解析 TW083_產品代碼_MO編號 格式
- 範例：FW: TW083_G529A1TB1U(BP)_SOT236230419299   低良率/OS超?
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


class TSHTParser(VendorParser):
    """
    TSHT 廠商郵件解析器
    
    識別條件：
    - 郵件主旨或內文包含 "TW083" 關鍵字
    - 包含 "tsht" 關鍵字
    - 包含 "低良率" 或 "OS超" 等特徵詞
    
    解析機制：
    1. 尋找 "TW083" 關鍵字位置
    2. 從 TW083 到第一個空格的部分作為解析目標
    3. 使用 "_" 分隔符解析：TW083_產品代碼_MO編號
    """
    
    def __init__(self):
        """初始化 TSHT 解析器"""
        super().__init__()
        self._vendor_code = "TSHT"
        self._vendor_name = "TSHT"
        self._identification_patterns = [
            "tsht",           # 主要識別關鍵字
            "TW083",          # 特殊格式標識
            "低良率",         # 常見關鍵字
            "OS超"           # 常見關鍵字
        ]
        self.set_confidence_threshold(0.7)
        
        # 初始化 logger
        self.logger = LoggerManager().get_logger("TSHTParser")
        
        # TSHT 特有的模式
        self.tw083_pattern = r'TW083_([^_\s]+)_([^_\s]+)'  # TW083_產品_MO格式

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        content = (subject + " " + body).lower()
        
        # 檢查 TSHT 關鍵字
        if "tsht" in content:
            matched_patterns.append("tsht")
            confidence_score += 0.8
            
        # 檢查寄件者是否包含 TSHT
        if "tsht" in sender_lower:
            if "tsht" not in matched_patterns:
                matched_patterns.append("tsht")
            confidence_score += 0.3
        
        # 檢查 TW083 格式（TSHT 特有的強識別特徵）
        if "tw083" in content:
            matched_patterns.append("TW083")
            confidence_score += 0.8
            
        # 檢查常見的 TSHT 特徵詞
        if "低良率" in content:
            matched_patterns.append("低良率")
            confidence_score += 0.4
            
        if "os超" in content:
            matched_patterns.append("OS超")
            confidence_score += 0.4
            
        # 檢查是否有完整的 TW083 格式
        if re.search(self.tw083_pattern, subject, re.IGNORECASE):
            confidence_score += 0.6
            matched_patterns.append("tw083_full_pattern")
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="tsht_tw083_pattern_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        try:
            # 基於 VBA FindTw083 邏輯進行解析
            subject = email_data.subject or ""
            body = email_data.body or ""
            
            # 首先嘗試從主旨解析
            tsht_result = self.parse_tsht_pattern(subject)
            
            # 如果主旨解析不完整，嘗試從郵件內文解析補充信息
            if not tsht_result["product"] or not tsht_result["mo_number"]:
                body_result = self.parse_tsht_pattern(body)
                
                # 合併解析結果
                if body_result["product"] and not tsht_result["product"]:
                    tsht_result["product"] = body_result["product"]
                if body_result["mo_number"] and not tsht_result["mo_number"]:
                    tsht_result["mo_number"] = body_result["mo_number"]
                if body_result["lot_number"] and not tsht_result["lot_number"]:
                    tsht_result["lot_number"] = body_result["lot_number"]
                if body_result["method"] != "no_pattern":
                    tsht_result["method"] = body_result["method"] + "_from_body"
            
            # 提取結果
            product_code = tsht_result["product"] if tsht_result["product"] else None
            mo_number = tsht_result["mo_number"] if tsht_result["mo_number"] else None
            lot_number = tsht_result["lot_number"] if tsht_result["lot_number"] else None
            
            # 檢查是否成功解析
            missing_fields = []
            if not product_code:
                missing_fields.append("產品代碼")
            if not mo_number:
                missing_fields.append("MO編號")

            is_success = len(missing_fields) == 0
            error_message = None

            if not is_success:
                error_message = f"TSHT 傳統解析失敗：缺少 {', '.join(missing_fields)}"
                self.logger.warning(f"TSHT 傳統解析失敗詳情:")
                self.logger.warning(f"  主旨: {email_data.subject}")
                self.logger.warning(f"  解析方法: {tsht_result['method']}")
                self.logger.warning(f"  產品代碼: {product_code or '未找到'}")
                self.logger.warning(f"  MO編號: {mo_number or '未找到'}")
                self.logger.warning(f"  LOT編號: {lot_number or '未找到'}")
                self.logger.warning(f"  缺少欄位: {', '.join(missing_fields)}")
            else:
                self.logger.info(f"TSHT 傳統解析成功:")
                self.logger.info(f"  產品代碼: {product_code}")
                self.logger.info(f"  MO編號: {mo_number}")
                self.logger.info(f"  LOT編號: {lot_number}")
                self.logger.info(f"  解析方法: {tsht_result['method']}")

            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=product_code,
                mo_number=mo_number,
                lot_number=lot_number,
                is_success=is_success,
                error_message=error_message,
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': tsht_result["product"],
                    'mo_number': tsht_result["mo_number"],
                    'lot_number': tsht_result["lot_number"],
                    'parsing_method': tsht_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'tsht_tw083_pattern_parsing'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                extraction_method="traditional",
                is_success=False,
                error_message=f"TSHT parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_tsht_pattern(self, text: str) -> Dict[str, Any]:
        """
        解析 TSHT 模式：基於 VBA FindTw083 邏輯
        
        VBA 邏輯：
        Function FindTw083(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String)
            tw083Pos = InStr(inputString, "TW083")
            If tw083Pos > 0 Then
                spacePos = InStr(tw083Pos, inputString, " ")
                If spacePos > 0 Then
                    inputString = Mid(inputString, tw083Pos, spacePos - tw083Pos)
                    words = Split(inputString, "_")
                    If Len(words(1)) > 0 Then
                        product = words(1)
                    End If
                    If Len(words(2)) > 0 Then
                        ...
        
        範例：TW083_G529A1TB1U(BP)_SOT236230419299
        解析為：產品: G529A1TB1U(BP), MO: SOT236230419299
        """
        if not text:
            return {
                "product": "",
                "mo_number": "",
                "lot_number": "",
                "method": "no_pattern"
            }
        
        product = ""
        mo_number = ""
        lot_number = ""
        method = "no_pattern"
        
        # VBA: tw083Pos = InStr(inputString, "TW083")
        tw083_pos = text.upper().find("TW083")
        
        if tw083_pos >= 0:
            # VBA: spacePos = InStr(tw083Pos, inputString, " ")
            # 從 TW083 位置開始尋找第一個空格
            search_start = tw083_pos
            space_pos = text.find(" ", search_start)
            
            if space_pos > 0:
                # VBA: inputString = Mid(inputString, tw083Pos, spacePos - tw083Pos)
                # 提取從 TW083 到空格之間的部分
                tw083_segment = text[tw083_pos:space_pos]
            else:
                # 如果沒有找到空格，取到字串結尾
                tw083_segment = text[tw083_pos:]
            
            # VBA: words = Split(inputString, "_")
            # 使用 "_" 分隔
            words = tw083_segment.split("_")
            
            if len(words) >= 2:
                # VBA: If Len(words(1)) > 0 Then product = words(1)
                if len(words) > 1 and words[1]:
                    product = words[1]
                    # 清理產品代碼中的括號內容（保留格式）
                    # G529A1TB1U(BP) 保持原樣
                    method = "tw083_pattern_found"
                
                # VBA: If Len(words(2)) > 0 Then ...
                if len(words) > 2 and words[2]:
                    mo_number = words[2]
                    method = "tw083_full_pattern"
        
        # 如果沒有找到 TW083 格式，嘗試尋找其他 TSHT 特徵
        if not product and not mo_number:
            # 尋找可能的產品代碼模式
            product_pattern = r'\b(G\d{3,4}[A-Z]\d{1,2}[A-Z]\d{1,2}[A-Z](?:\([A-Z]+\))?)\b'
            product_match = re.search(product_pattern, text, re.IGNORECASE)
            
            if product_match:
                product = product_match.group(1)
                method = "general_product_pattern"
                
                # 嘗試在產品代碼附近尋找 MO 編號
                # SOT + 數字格式
                mo_pattern = r'\b(SOT\d{9,15})\b'
                mo_match = re.search(mo_pattern, text, re.IGNORECASE)
                if mo_match:
                    mo_number = mo_match.group(1)
                    method = "product_with_sot_mo"
        
        # 尋找可能的 LOT 編號
        # TSHT 通常沒有明確的 LOT 格式，但可能包含在 MO 中
        if mo_number and not lot_number:
            lot_number = mo_number  # 使用 MO 作為 LOT
        
        return {
            "product": product,
            "mo_number": mo_number,
            "lot_number": lot_number,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'TW083 underscore separated format (TW083_G529A1TB1U(BP)_SOT236230419299)',
                'General product code pattern with SOT MO format',
                'Low yield and OS excess pattern recognition'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'chinese_support': True,
            'based_on': 'VBA FindTw083',
            'extraction_capabilities': [
                'TW083 position-based parsing',
                'Underscore-separated field extraction',
                'Product code with parentheses format support',
                'SOT MO number pattern matching'
            ],
            'special_features': [
                'TW083 segment extraction to first space',
                'Underscore-based field separation',
                'Product parentheses format preservation',
                'Fallback to general patterns'
            ]
        }