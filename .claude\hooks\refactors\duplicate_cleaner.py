#!/usr/bin/env python3
"""
重複代碼清理器
自動清理重複和相似代碼，保留最新版本
"""

import ast
import os
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
import logging
import hashlib

class DuplicateCleaner:
    """重複代碼清理器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('DuplicateCleaner')
        
        # 清理配置
        self.similarity_threshold = config.get('similarity_threshold', 0.8)
        self.preserve_newest = config.get('preserve_newest', True)
        self.backup_before_clean = config.get('backup_before_clean', True)
        self.safe_mode = config.get('safe_mode', True)
        
    async def clean_duplicates(self, duplicate_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """清理重複代碼"""
        self.logger.info("🧹 開始清理重複代碼...")
        
        if not duplicate_analysis.get('duplicates'):
            return {
                'status': 'no_duplicates',
                'message': '未發現需要清理的重複代碼',
                'cleaned_items': []
            }
        
        cleaned_items = []
        errors = []
        
        # 準備備份
        if self.backup_before_clean:
            backup_info = await self._create_backup()
        else:
            backup_info = None
        
        try:
            # 清理重複函數
            if duplicate_analysis.get('duplicate_functions'):
                func_results = await self._clean_duplicate_functions(
                    duplicate_analysis['duplicate_functions']
                )
                cleaned_items.extend(func_results['cleaned'])
                errors.extend(func_results['errors'])
            
            # 清理重複類別
            if duplicate_analysis.get('duplicate_classes'):
                class_results = await self._clean_duplicate_classes(
                    duplicate_analysis['duplicate_classes']
                )
                cleaned_items.extend(class_results['cleaned'])
                errors.extend(class_results['errors'])
            
            # 清理相似代碼塊
            if duplicate_analysis.get('similar_blocks'):
                similar_results = await self._clean_similar_blocks(
                    duplicate_analysis['similar_blocks']
                )
                cleaned_items.extend(similar_results['cleaned'])
                errors.extend(similar_results['errors'])
            
            # 更新導入引用
            import_updates = await self._update_import_references(cleaned_items)
            
            result = {
                'status': 'success',
                'cleaned_items': cleaned_items,
                'import_updates': import_updates,
                'backup_info': backup_info,
                'errors': errors,
                'total_cleaned': len(cleaned_items),
                'space_saved_lines': sum(item.get('lines_removed', 0) for item in cleaned_items)
            }
            
            self.logger.info(f"✅ 清理完成: 清理了 {len(cleaned_items)} 個重複項目")
            return result
            
        except Exception as e:
            self.logger.error(f"清理失敗: {e}")
            
            # 如果有備份，詢問是否回滾
            if backup_info:
                self.logger.warning("清理過程中發生錯誤，建議檢查備份並考慮回滾")
            
            return {
                'status': 'error',
                'message': f'清理失敗: {str(e)}',
                'backup_info': backup_info,
                'errors': errors + [str(e)]
            }
    
    async def _create_backup(self) -> Dict[str, Any]:
        """創建備份"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.repo_root / '.claude' / 'backups' / f'duplicate_clean_{timestamp}'
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 備份所有 Python 檔案
        backed_up_files = []
        for py_file in self.repo_root.rglob('*.py'):
            if any(ignore_dir in py_file.parts for ignore_dir in ['.git', '__pycache__', '.claude']):
                continue
            
            relative_path = py_file.relative_to(self.repo_root)
            backup_file = backup_dir / relative_path
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(py_file, backup_file)
            backed_up_files.append(str(relative_path))
        
        backup_info = {
            'backup_dir': str(backup_dir.relative_to(self.repo_root)),
            'timestamp': timestamp,
            'backed_up_files': len(backed_up_files),
            'backup_size_mb': sum(f.stat().st_size for f in backup_dir.rglob('*') if f.is_file()) / (1024 * 1024)
        }
        
        self.logger.info(f"🔒 備份創建: {backup_info['backed_up_files']} 個檔案")
        return backup_info
    
    async def _clean_duplicate_functions(self, duplicate_functions: List[Dict[str, Any]]) -> Dict[str, List]:
        """清理重複函數"""
        cleaned = []
        errors = []
        
        for func_group in duplicate_functions:
            try:
                if len(func_group['locations']) < 2:
                    continue
                
                # 決定保留哪個版本
                keep_location = await self._choose_version_to_keep(func_group['locations'])
                remove_locations = [loc for loc in func_group['locations'] if loc != keep_location]
                
                # 移除重複版本
                for location in remove_locations:
                    success = await self._remove_function_from_file(
                        location['file'], 
                        location['function_name'],
                        location['start_line'],
                        location['end_line']
                    )
                    
                    if success:
                        cleaned.append({
                            'type': 'duplicate_function',
                            'name': func_group['function_name'],
                            'removed_from': location['file'],
                            'kept_in': keep_location['file'],
                            'lines_removed': location['end_line'] - location['start_line'] + 1,
                            'similarity_score': func_group.get('similarity_score', 1.0)
                        })
                    else:
                        errors.append(f"無法移除函數 {func_group['function_name']} 從 {location['file']}")
                
            except Exception as e:
                errors.append(f"清理函數組 {func_group.get('function_name', 'unknown')} 失敗: {str(e)}")
        
        return {'cleaned': cleaned, 'errors': errors}
    
    async def _clean_duplicate_classes(self, duplicate_classes: List[Dict[str, Any]]) -> Dict[str, List]:
        """清理重複類別"""
        cleaned = []
        errors = []
        
        for class_group in duplicate_classes:
            try:
                if len(class_group['locations']) < 2:
                    continue
                
                # 決定保留哪個版本
                keep_location = await self._choose_version_to_keep(class_group['locations'])
                remove_locations = [loc for loc in class_group['locations'] if loc != keep_location]
                
                # 移除重複版本
                for location in remove_locations:
                    success = await self._remove_class_from_file(
                        location['file'], 
                        location['class_name'],
                        location['start_line'],
                        location['end_line']
                    )
                    
                    if success:
                        cleaned.append({
                            'type': 'duplicate_class',
                            'name': class_group['class_name'],
                            'removed_from': location['file'],
                            'kept_in': keep_location['file'],
                            'lines_removed': location['end_line'] - location['start_line'] + 1,
                            'similarity_score': class_group.get('similarity_score', 1.0)
                        })
                    else:
                        errors.append(f"無法移除類別 {class_group['class_name']} 從 {location['file']}")
                
            except Exception as e:
                errors.append(f"清理類別組 {class_group.get('class_name', 'unknown')} 失敗: {str(e)}")
        
        return {'cleaned': cleaned, 'errors': errors}
    
    async def _clean_similar_blocks(self, similar_blocks: List[Dict[str, Any]]) -> Dict[str, List]:
        """清理相似代碼塊"""
        cleaned = []
        errors = []
        
        for block_group in similar_blocks:
            try:
                if len(block_group['locations']) < 2:
                    continue
                
                similarity = block_group.get('similarity_score', 0)
                if similarity < self.similarity_threshold:
                    continue
                
                # 決定保留哪個版本
                keep_location = await self._choose_version_to_keep(block_group['locations'])
                remove_locations = [loc for loc in block_group['locations'] if loc != keep_location]
                
                # 對於相似代碼塊，可以選擇提取為共用函數
                if similarity > 0.9 and len(remove_locations) > 1:
                    extract_result = await self._extract_common_function(block_group, keep_location)
                    if extract_result['success']:
                        cleaned.append({
                            'type': 'extracted_function',
                            'name': extract_result['function_name'],
                            'extracted_to': extract_result['target_file'],
                            'replaced_in': [loc['file'] for loc in block_group['locations']],
                            'lines_saved': extract_result['lines_saved'],
                            'similarity_score': similarity
                        })
                        continue
                
                # 否則直接移除重複代碼塊
                for location in remove_locations:
                    success = await self._remove_code_block(
                        location['file'],
                        location['start_line'],
                        location['end_line']
                    )
                    
                    if success:
                        cleaned.append({
                            'type': 'similar_block',
                            'removed_from': location['file'],
                            'kept_in': keep_location['file'],
                            'lines_removed': location['end_line'] - location['start_line'] + 1,
                            'similarity_score': similarity
                        })
                    else:
                        errors.append(f"無法移除相似代碼塊從 {location['file']} 行 {location['start_line']}-{location['end_line']}")
                
            except Exception as e:
                errors.append(f"清理相似代碼塊失敗: {str(e)}")
        
        return {'cleaned': cleaned, 'errors': errors}
    
    async def _choose_version_to_keep(self, locations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """選擇要保留的版本"""
        if self.preserve_newest:
            # 按檔案修改時間選擇最新的
            newest_location = None
            newest_time = 0
            
            for location in locations:
                file_path = self.repo_root / location['file']
                if file_path.exists():
                    mtime = file_path.stat().st_mtime
                    if mtime > newest_time:
                        newest_time = mtime
                        newest_location = location
            
            if newest_location:
                return newest_location
        
        # 其他選擇策略
        
        # 1. 選擇行數最多的版本（可能功能更完整）
        longest_location = max(locations, key=lambda x: x.get('end_line', 0) - x.get('start_line', 0))
        
        # 2. 選擇有文檔字符串的版本
        documented_locations = [loc for loc in locations if loc.get('has_docstring', False)]
        if documented_locations:
            return documented_locations[0]
        
        # 3. 選擇在主要模組中的版本
        main_module_locations = [
            loc for loc in locations 
            if not any(keyword in loc['file'].lower() for keyword in ['test', 'backup', 'old', 'temp'])
        ]
        if main_module_locations:
            return main_module_locations[0]
        
        # 默認返回最長的版本
        return longest_location
    
    async def _remove_function_from_file(self, file_path: str, function_name: str, 
                                       start_line: int, end_line: int) -> bool:
        """從檔案中移除函數"""
        try:
            full_path = self.repo_root / file_path
            with open(full_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 安全檢查
            if self.safe_mode:
                # 驗證要移除的確實是目標函數
                if start_line > 0 and start_line <= len(lines):
                    line_content = lines[start_line - 1].strip()
                    if not (line_content.startswith(f'def {function_name}') or 
                           line_content.startswith(f'async def {function_name}')):
                        self.logger.warning(f"安全檢查失敗: 第 {start_line} 行不是函數 {function_name} 的定義")
                        return False
            
            # 移除函數（包括前後的空行）
            remove_start = max(0, start_line - 1)
            remove_end = min(len(lines), end_line)
            
            # 清理多餘的空行
            while remove_start > 0 and not lines[remove_start - 1].strip():
                remove_start -= 1
            while remove_end < len(lines) and not lines[remove_end].strip():
                remove_end += 1
            
            # 更新檔案
            new_lines = lines[:remove_start] + lines[remove_end:]
            
            with open(full_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            self.logger.debug(f"已移除函數 {function_name} 從 {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除函數 {function_name} 失敗: {e}")
            return False
    
    async def _remove_class_from_file(self, file_path: str, class_name: str, 
                                    start_line: int, end_line: int) -> bool:
        """從檔案中移除類別"""
        try:
            full_path = self.repo_root / file_path
            with open(full_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 安全檢查
            if self.safe_mode:
                if start_line > 0 and start_line <= len(lines):
                    line_content = lines[start_line - 1].strip()
                    if not line_content.startswith(f'class {class_name}'):
                        self.logger.warning(f"安全檢查失敗: 第 {start_line} 行不是類別 {class_name} 的定義")
                        return False
            
            # 移除類別
            remove_start = max(0, start_line - 1)
            remove_end = min(len(lines), end_line)
            
            # 清理多餘的空行
            while remove_start > 0 and not lines[remove_start - 1].strip():
                remove_start -= 1
            while remove_end < len(lines) and not lines[remove_end].strip():
                remove_end += 1
            
            new_lines = lines[:remove_start] + lines[remove_end:]
            
            with open(full_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            self.logger.debug(f"已移除類別 {class_name} 從 {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除類別 {class_name} 失敗: {e}")
            return False
    
    async def _remove_code_block(self, file_path: str, start_line: int, end_line: int) -> bool:
        """移除代碼塊"""
        try:
            full_path = self.repo_root / file_path
            with open(full_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 移除代碼塊
            remove_start = max(0, start_line - 1)
            remove_end = min(len(lines), end_line)
            
            new_lines = lines[:remove_start] + lines[remove_end:]
            
            with open(full_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            self.logger.debug(f"已移除代碼塊從 {file_path} 行 {start_line}-{end_line}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除代碼塊失敗: {e}")
            return False
    
    async def _extract_common_function(self, block_group: Dict[str, Any], 
                                     keep_location: Dict[str, Any]) -> Dict[str, Any]:
        """提取共用函數"""
        try:
            # 生成函數名
            function_name = self._generate_extracted_function_name(block_group)
            
            # 分析代碼塊內容
            target_file = self.repo_root / keep_location['file']
            with open(target_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 提取代碼塊
            start_line = keep_location['start_line'] - 1
            end_line = keep_location['end_line']
            block_lines = lines[start_line:end_line]
            
            # 分析參數和返回值
            params = await self._analyze_block_parameters(block_lines)
            
            # 生成函數定義
            function_def = self._generate_function_definition(function_name, params, block_lines)
            
            # 在目標檔案中添加函數
            insert_result = await self._insert_function_in_file(target_file, function_def)
            
            if insert_result:
                # 在所有位置替換為函數調用
                total_lines_saved = 0
                for location in block_group['locations']:
                    call_result = await self._replace_block_with_function_call(
                        location, function_name, params
                    )
                    if call_result:
                        total_lines_saved += location['end_line'] - location['start_line'] + 1 - 1  # -1 for function call
                
                return {
                    'success': True,
                    'function_name': function_name,
                    'target_file': str(target_file.relative_to(self.repo_root)),
                    'lines_saved': total_lines_saved
                }
            
        except Exception as e:
            self.logger.error(f"提取共用函數失敗: {e}")
        
        return {'success': False}
    
    def _generate_extracted_function_name(self, block_group: Dict[str, Any]) -> str:
        """生成提取函數的名稱"""
        # 基於代碼塊內容生成名稱
        if 'hash' in block_group:
            short_hash = block_group['hash'][:8]
            return f"extracted_function_{short_hash}"
        
        # 基於行數生成
        first_location = block_group['locations'][0]
        return f"extracted_function_{first_location['start_line']}_{first_location['end_line']}"
    
    async def _analyze_block_parameters(self, block_lines: List[str]) -> List[str]:
        """分析代碼塊的參數需求"""
        # 簡化實現：查找使用的變數
        block_content = ''.join(block_lines)
        
        try:
            # 嘗試解析為 AST
            tree = ast.parse(block_content)
            
            used_names = set()
            defined_names = set()
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Name):
                    if isinstance(node.ctx, ast.Load):
                        used_names.add(node.id)
                    elif isinstance(node.ctx, ast.Store):
                        defined_names.add(node.id)
            
            # 參數是使用但未定義的變數
            params = used_names - defined_names
            
            # 過濾內建函數和關鍵字
            builtin_names = {'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple'}
            params = params - builtin_names
            
            return sorted(list(params))
            
        except:
            # 如果解析失敗，返回空參數列表
            return []
    
    def _generate_function_definition(self, function_name: str, params: List[str], 
                                    block_lines: List[str]) -> str:
        """生成函數定義"""
        param_str = ', '.join(params) if params else ''
        
        function_lines = [
            f"def {function_name}({param_str}):",
            '    """自動提取的共用函數"""'
        ]
        
        # 添加縮進的代碼塊
        for line in block_lines:
            if line.strip():
                function_lines.append('    ' + line.rstrip())
            else:
                function_lines.append('')
        
        function_lines.append('')  # 函數後的空行
        
        return '\n'.join(function_lines)
    
    async def _insert_function_in_file(self, file_path: Path, function_def: str) -> bool:
        """在檔案中插入函數"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # 找到插入位置（通常在導入之後）
            insert_line = 0
            for i, line in enumerate(lines):
                if line.strip().startswith(('import ', 'from ')) or line.strip().startswith('#'):
                    insert_line = i + 1
                elif line.strip():
                    break
            
            # 插入函數
            lines.insert(insert_line, function_def)
            
            new_content = '\n'.join(lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return True
            
        except Exception as e:
            self.logger.error(f"插入函數失敗: {e}")
            return False
    
    async def _replace_block_with_function_call(self, location: Dict[str, Any], 
                                              function_name: str, params: List[str]) -> bool:
        """用函數調用替換代碼塊"""
        try:
            file_path = self.repo_root / location['file']
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 生成函數調用
            param_str = ', '.join(params) if params else ''
            function_call = f"{function_name}({param_str})\n"
            
            # 替換代碼塊
            start_line = location['start_line'] - 1
            end_line = location['end_line']
            
            # 保持原有縮進
            original_indent = ''
            if start_line < len(lines):
                original_line = lines[start_line]
                original_indent = original_line[:len(original_line) - len(original_line.lstrip())]
            
            indented_call = original_indent + function_call
            
            new_lines = lines[:start_line] + [indented_call] + lines[end_line:]
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            return True
            
        except Exception as e:
            self.logger.error(f"替換為函數調用失敗: {e}")
            return False
    
    async def _update_import_references(self, cleaned_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """更新導入引用"""
        import_updates = []
        
        # 收集需要更新導入的情況
        moved_items = {}
        for item in cleaned_items:
            if item['type'] in ['duplicate_function', 'duplicate_class']:
                moved_items[item['name']] = {
                    'from_file': item['removed_from'],
                    'to_file': item['kept_in'],
                    'type': item['type']
                }
        
        if not moved_items:
            return import_updates
        
        # 掃描所有檔案查找需要更新的導入
        for py_file in self.repo_root.rglob('*.py'):
            if any(ignore_dir in py_file.parts for ignore_dir in ['.git', '__pycache__', '.claude']):
                continue
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                updated_content = content
                file_updated = False
                
                # 檢查每個移動的項目
                for item_name, move_info in moved_items.items():
                    old_module = Path(move_info['from_file']).stem
                    new_module = Path(move_info['to_file']).stem
                    
                    # 更新 from import 語句
                    import_pattern = f"from {old_module} import"
                    if import_pattern in content:
                        updated_content = updated_content.replace(
                            f"from {old_module} import {item_name}",
                            f"from {new_module} import {item_name}"
                        )
                        file_updated = True
                
                if file_updated:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(updated_content)
                    
                    import_updates.append({
                        'file': str(py_file.relative_to(self.repo_root)),
                        'updates': list(moved_items.keys())
                    })
                    
            except Exception as e:
                self.logger.warning(f"更新導入失敗 {py_file}: {e}")
        
        return import_updates