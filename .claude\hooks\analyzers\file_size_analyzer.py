#!/usr/bin/env python3
"""
檔案大小分析器
檢測超過指定行數限制的檔案，並提供智能拆分建議
"""

import ast
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

class FileSizeAnalyzer:
    """檔案大小分析器"""
    
    def __init__(self, repo_root: Path, config: Dict[str, Any]):
        self.repo_root = Path(repo_root)
        self.config = config
        self.logger = logging.getLogger('FileSizeAnalyzer')
        self.line_limit = config.get('file_size_limit', 500)
        self.supported_languages = config.get('languages', ['python', 'javascript', 'typescript'])
    
    async def analyze(self, files: Optional[List[str]] = None) -> Dict[str, Any]:
        """分析檔案大小，找出超過限制的檔案"""
        self.logger.info(f"🔍 開始檔案大小分析 (限制: {self.line_limit} 行)")
        
        if files:
            # 只分析指定檔案
            target_files = [self.repo_root / f for f in files if self._is_supported_file(f)]
        else:
            # 分析所有支援的檔案
            target_files = self._find_all_supported_files()
        
        oversized_files = []
        total_files_checked = 0
        
        for file_path in target_files:
            try:
                analysis = await self._analyze_file(file_path)
                total_files_checked += 1
                
                if analysis['lines'] > self.line_limit:
                    oversized_files.append(analysis)
                    
            except Exception as e:
                self.logger.warning(f"分析檔案失敗 {file_path}: {e}")
        
        # 按行數排序，最大的在前
        oversized_files.sort(key=lambda x: x['lines'], reverse=True)
        
        result = {
            'total_files_checked': total_files_checked,
            'line_limit': self.line_limit,
            'oversized_files': oversized_files,
            'statistics': {
                'total_oversized': len(oversized_files),
                'avg_lines_oversized': sum(f['lines'] for f in oversized_files) / max(len(oversized_files), 1),
                'largest_file': oversized_files[0] if oversized_files else None
            }
        }
        
        self.logger.info(f"✅ 檔案大小分析完成: {len(oversized_files)} 個檔案超過限制")
        return result
    
    async def _analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """分析單一檔案"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.split('\n')
            total_lines = len(lines)
            
            # 基本資訊
            analysis = {
                'path': str(file_path.relative_to(self.repo_root)),
                'lines': total_lines,
                'file_type': file_path.suffix[1:] if file_path.suffix else 'unknown',
                'size_bytes': file_path.stat().st_size,
                'split_suggestions': []
            }
            
            # 根據檔案類型進行特定分析
            if file_path.suffix == '.py':
                analysis.update(await self._analyze_python_file(file_path, content, lines))
            elif file_path.suffix in ['.js', '.ts']:
                analysis.update(await self._analyze_javascript_file(file_path, content, lines))
            elif file_path.suffix == '.md':
                analysis.update(await self._analyze_markdown_file(file_path, content, lines))
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析檔案內容失敗 {file_path}: {e}")
            raise
    
    async def _analyze_python_file(self, file_path: Path, content: str, lines: List[str]) -> Dict[str, Any]:
        """分析 Python 檔案結構"""
        analysis = {
            'classes': [],
            'functions': [],
            'imports': [],
            'comments': 0,
            'blank_lines': 0,
            'code_complexity': 'medium'
        }
        
        try:
            # 使用 AST 分析
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'start_line': node.lineno,
                        'end_line': self._get_end_line(node, lines),
                        'methods': []
                    }
                    
                    # 分析類別中的方法
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_info = {
                                'name': item.name,
                                'start_line': item.lineno,
                                'end_line': self._get_end_line(item, lines),
                                'lines': self._get_end_line(item, lines) - item.lineno + 1
                            }
                            class_info['methods'].append(method_info)
                    
                    class_info['lines'] = class_info['end_line'] - class_info['start_line'] + 1
                    analysis['classes'].append(class_info)
                
                elif isinstance(node, ast.FunctionDef) and not self._is_inside_class(node, tree):
                    func_info = {
                        'name': node.name,
                        'start_line': node.lineno,
                        'end_line': self._get_end_line(node, lines),
                        'lines': self._get_end_line(node, lines) - node.lineno + 1
                    }
                    analysis['functions'].append(func_info)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    analysis['imports'].append({
                        'line': node.lineno,
                        'type': 'import' if isinstance(node, ast.Import) else 'from_import'
                    })
        
        except SyntaxError:
            self.logger.warning(f"無法解析 Python 檔案的語法: {file_path}")
        
        # 計算註釋和空行
        for line in lines:
            stripped = line.strip()
            if not stripped:
                analysis['blank_lines'] += 1
            elif stripped.startswith('#'):
                analysis['comments'] += 1
        
        # 生成拆分建議
        analysis['split_suggestions'] = await self._generate_python_split_suggestions(analysis, len(lines))
        
        return analysis
    
    async def _analyze_javascript_file(self, file_path: Path, content: str, lines: List[str]) -> Dict[str, Any]:
        """分析 JavaScript/TypeScript 檔案結構"""
        analysis = {
            'functions': [],
            'classes': [],
            'exports': [],
            'imports': [],
            'comments': 0,
            'blank_lines': 0
        }
        
        # 簡單的正則表達式分析（可以後續整合更強大的 JS 解析器）
        function_pattern = re.compile(r'^\s*(async\s+)?function\s+(\w+)|^\s*(\w+)\s*[:=]\s*(async\s+)?\(.*\)\s*=>|^\s*(\w+)\s*[:=]\s*function')
        class_pattern = re.compile(r'^\s*class\s+(\w+)')
        import_pattern = re.compile(r'^\s*import\s+')
        export_pattern = re.compile(r'^\s*export\s+')
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            
            if not stripped:
                analysis['blank_lines'] += 1
            elif stripped.startswith('//') or stripped.startswith('/*'):
                analysis['comments'] += 1
            
            # 檢測函數
            func_match = function_pattern.search(line)
            if func_match:
                func_name = func_match.group(2) or func_match.group(3) or func_match.group(5)
                if func_name:
                    analysis['functions'].append({
                        'name': func_name,
                        'line': i,
                        'type': 'function'
                    })
            
            # 檢測類別
            class_match = class_pattern.search(line)
            if class_match:
                analysis['classes'].append({
                    'name': class_match.group(1),
                    'line': i
                })
            
            # 檢測 import 和 export
            if import_pattern.search(line):
                analysis['imports'].append(i)
            elif export_pattern.search(line):
                analysis['exports'].append(i)
        
        # 生成拆分建議
        analysis['split_suggestions'] = await self._generate_javascript_split_suggestions(analysis, len(lines))
        
        return analysis
    
    async def _analyze_markdown_file(self, file_path: Path, content: str, lines: List[str]) -> Dict[str, Any]:
        """分析 Markdown 檔案結構"""
        analysis = {
            'sections': [],
            'headers': [],
            'code_blocks': 0,
            'images': 0,
            'links': 0
        }
        
        header_pattern = re.compile(r'^(#{1,6})\s+(.*)')
        code_block_pattern = re.compile(r'^```')
        image_pattern = re.compile(r'!\[.*\]\(.*\)')
        link_pattern = re.compile(r'\[.*\]\(.*\)')
        
        current_section = None
        in_code_block = False
        
        for i, line in enumerate(lines, 1):
            # 檢測標題
            header_match = header_pattern.match(line)
            if header_match:
                level = len(header_match.group(1))
                title = header_match.group(2)
                
                header_info = {
                    'level': level,
                    'title': title,
                    'line': i
                }
                analysis['headers'].append(header_info)
                
                # 結束前一個區段
                if current_section:
                    current_section['end_line'] = i - 1
                    current_section['lines'] = current_section['end_line'] - current_section['start_line'] + 1
                
                # 開始新區段
                if level <= 2:  # 只有 H1 和 H2 算主要區段
                    current_section = {
                        'title': title,
                        'level': level,
                        'start_line': i,
                        'end_line': None
                    }
                    analysis['sections'].append(current_section)
            
            # 檢測程式碼區塊
            if code_block_pattern.match(line):
                in_code_block = not in_code_block
                if in_code_block:
                    analysis['code_blocks'] += 1
            
            # 檢測圖片和連結
            if image_pattern.search(line):
                analysis['images'] += 1
            if link_pattern.search(line):
                analysis['links'] += 1
        
        # 結束最後一個區段
        if current_section and not current_section['end_line']:
            current_section['end_line'] = len(lines)
            current_section['lines'] = current_section['end_line'] - current_section['start_line'] + 1
        
        # 生成拆分建議
        analysis['split_suggestions'] = await self._generate_markdown_split_suggestions(analysis, len(lines))
        
        return analysis
    
    async def _generate_python_split_suggestions(self, analysis: Dict[str, Any], total_lines: int) -> List[Dict[str, Any]]:
        """為 Python 檔案生成拆分建議"""
        suggestions = []
        
        # 按類別拆分
        large_classes = [c for c in analysis['classes'] if c['lines'] > 100]
        if large_classes:
            for cls in large_classes:
                suggestions.append({
                    'strategy': 'extract_class',
                    'description': f"將類別 '{cls['name']}' ({cls['lines']} 行) 提取到獨立檔案",
                    'target': cls['name'],
                    'lines_to_move': cls['lines'],
                    'priority': 'high' if cls['lines'] > 200 else 'medium'
                })
        
        # 按功能模組拆分
        if len(analysis['functions']) > 10:
            suggestions.append({
                'strategy': 'group_functions',
                'description': f"將 {len(analysis['functions'])} 個函數按功能分組到不同模組",
                'target': 'functions',
                'estimated_splits': min(3, len(analysis['functions']) // 5),
                'priority': 'medium'
            })
        
        # 如果有很多 import，建議建立 __init__.py
        if len(analysis['imports']) > 20:
            suggestions.append({
                'strategy': 'create_package',
                'description': "建立 package 結構，使用 __init__.py 管理 imports",
                'target': 'imports',
                'priority': 'low'
            })
        
        return suggestions
    
    async def _generate_javascript_split_suggestions(self, analysis: Dict[str, Any], total_lines: int) -> List[Dict[str, Any]]:
        """為 JavaScript 檔案生成拆分建議"""
        suggestions = []
        
        # 按功能拆分
        if len(analysis['functions']) > 8:
            suggestions.append({
                'strategy': 'split_by_feature',
                'description': f"將 {len(analysis['functions'])} 個函數按功能分組",
                'target': 'functions',
                'priority': 'medium'
            })
        
        # 按類別拆分
        if analysis['classes']:
            for cls in analysis['classes']:
                suggestions.append({
                    'strategy': 'extract_class',
                    'description': f"將類別 '{cls['name']}' 提取到獨立檔案",
                    'target': cls['name'],
                    'priority': 'high'
                })
        
        return suggestions
    
    async def _generate_markdown_split_suggestions(self, analysis: Dict[str, Any], total_lines: int) -> List[Dict[str, Any]]:
        """為 Markdown 檔案生成拆分建議"""
        suggestions = []
        
        # 按主要區段拆分
        large_sections = [s for s in analysis['sections'] if s.get('lines', 0) > 200]
        if large_sections:
            for section in large_sections:
                suggestions.append({
                    'strategy': 'split_by_section',
                    'description': f"將區段 '{section['title']}' ({section.get('lines', 0)} 行) 提取到獨立檔案",
                    'target': section['title'],
                    'lines_to_move': section.get('lines', 0),
                    'priority': 'high'
                })
        
        # 如果有很多小區段，建議建立目錄結構
        if len(analysis['sections']) > 10:
            suggestions.append({
                'strategy': 'create_directory_structure',
                'description': f"建立目錄結構，將 {len(analysis['sections'])} 個區段組織到不同檔案",
                'target': 'sections',
                'priority': 'medium'
            })
        
        return suggestions
    
    def _find_all_supported_files(self) -> List[Path]:
        """找出所有支援的檔案"""
        supported_extensions = {
            'python': ['.py'],
            'javascript': ['.js', '.jsx'],
            'typescript': ['.ts', '.tsx'],
            'markdown': ['.md']
        }
        
        files = []
        ignore_dirs = {'.git', '__pycache__', 'node_modules', 'venv', 'venv_win_3_11_12', '.claude'}
        
        for language in self.supported_languages:
            extensions = supported_extensions.get(language, [])
            for ext in extensions:
                for file_path in self.repo_root.rglob(f'*{ext}'):
                    # 跳過忽略的目錄
                    if any(ignore_dir in file_path.parts for ignore_dir in ignore_dirs):
                        continue
                    files.append(file_path)
        
        return files
    
    def _is_supported_file(self, file_path: str) -> bool:
        """檢查檔案是否為支援的類型"""
        path = Path(file_path)
        supported_extensions = ['.py', '.js', '.jsx', '.ts', '.tsx', '.md']
        return path.suffix in supported_extensions
    
    def _get_end_line(self, node: ast.AST, lines: List[str]) -> int:
        """取得 AST 節點的結束行號"""
        if hasattr(node, 'end_lineno') and node.end_lineno:
            return node.end_lineno
        
        # 簡單估算：找到下一個相同縮進級別的行
        start_line = node.lineno - 1
        if start_line >= len(lines):
            return len(lines)
        
        start_indent = len(lines[start_line]) - len(lines[start_line].lstrip())
        
        for i in range(start_line + 1, len(lines)):
            line = lines[i]
            if line.strip():  # 非空行
                current_indent = len(line) - len(line.lstrip())
                if current_indent <= start_indent:
                    return i
        
        return len(lines)
    
    def _is_inside_class(self, func_node: ast.FunctionDef, tree: ast.AST) -> bool:
        """檢查函數是否在類別內部"""
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                for item in node.body:
                    if item is func_node:
                        return True
        return False