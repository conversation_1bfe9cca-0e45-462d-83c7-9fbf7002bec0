// 網路共享瀏覽器 - JavaScript功能
const API = '/network/api';

// 全域變數
let currentPath = '\\\\************\\test_log';
let isConnected = false;
let allFiles = []; // 儲存所有檔案以供過濾
let filteredFiles = []; // 當前過濾後的檔案
let isAuthenticated = false;

function showStatus(msg, type = 'loading') {
    const s = document.getElementById('status');
    const icons = {
        success: 'check-circle',
        error: 'exclamation-triangle',
        loading: 'spinner fa-spin'
    };
    s.innerHTML = `<div class="status ${type}"><i class="fas fa-${icons[type]}"></i> ${msg}</div>`;
}

function getIcon(type, isDir) {
    if (isDir) return {class: 'folder', icon: 'fas fa-folder'};
    const map = {
        'Excel 檔案': {class: 'excel', icon: 'fas fa-file-excel'},
        '文字檔案': {class: 'text', icon: 'fas fa-file-alt'},
        '壓縮檔案': {class: 'archive', icon: 'fas fa-file-archive'}
    };
    return map[type] || {class: 'other', icon: 'fas fa-file'};
}

function formatSize(mb) {
    if (mb >= 1024) return `${(mb/1024).toFixed(2)} GB`;
    if (mb >= 1) return `${mb.toFixed(2)} MB`;
    return `${(mb*1024).toFixed(0)} KB`;
}

function formatTime(iso) {
    return new Date(iso).toLocaleString('zh-TW', {
        year: 'numeric', month: '2-digit', day: '2-digit',
        hour: '2-digit', minute: '2-digit'
    });
}

function debugLog(msg) {
    console.log('[DEBUG]', msg);
    showStatus(msg, 'loading');
}

async function connectToShare() {
    showStatus('正在自動連接...', 'loading');

    try {
        // 首先嘗試使用當前 Windows 用戶認證
        showStatus('嘗試使用當前 Windows 用戶認證連接...', 'loading');
        let r = await fetch(`${API}/connect`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                path: currentPath,
                username: 'current_user',
                password: '',
                domain: 'gmt'
            })
        });

        let result = await r.json();

        // 如果當前用戶認證失敗，嘗試使用 .env 檔案中的認證
        if (result.status !== 'success' || !result.connected) {
            showStatus('當前用戶認證失敗，嘗試使用配置的認證資訊...', 'loading');

            // 獲取 .env 檔案中的認證資訊
            const credResponse = await fetch(`${API}/credentials`);
            const credResult = await credResponse.json();

            if (credResult.status !== 'success') {
                throw new Error('無法獲取認證資訊');
            }

            const credentials = credResult.credentials;

            // 使用從 .env 獲取的認證資訊連接
            r = await fetch(`${API}/connect`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    path: currentPath,
                    username: credentials.username,
                    password: credentials.password,
                    domain: credentials.domain
                })
            });

            result = await r.json();
        }

        // 處理最終連接結果
        if (result.status === 'success' && result.connected) {
            showStatus('連接成功！正在載入檔案...', 'success');
            isConnected = true;

            // 顯示導航列和過濾工具列
            document.getElementById('navigationBar').style.display = 'block';
            document.getElementById('filterBar').style.display = 'block';

            // 設定日期預設值：昨天到今天
            setDefaultDateRange();

            // 顯示當前用戶歡迎訊息
            showWelcomeMessage();

            // 自動載入檔案
            setTimeout(() => loadFiles(), 1000);
        } else {
            showStatus(result.message || '連接失敗', 'error');
        }
    } catch (e) {
        showStatus(`連接失敗: ${e.message}`, 'error');
        console.error('連接錯誤:', e);
    }
}

async function showWelcomeMessage() {
    try {
        const response = await fetch(`${API}/current-user`);
        const result = await response.json();

        if (result.status === 'success') {
            const userInfo = result.user_info;
            const welcomeElement = document.getElementById('welcomeMessage');
            const welcomeText = document.getElementById('welcomeText');

            if (welcomeElement && welcomeText) {
                welcomeText.innerHTML = `歡迎，${userInfo.username}！已使用當前 Windows 用戶認證成功連接網路共享`;
                welcomeElement.style.display = 'block';

                // 3秒後淡出歡迎訊息
                setTimeout(() => {
                    welcomeElement.style.transition = 'opacity 1s ease-out';
                    welcomeElement.style.opacity = '0.7';
                }, 3000);
            }
        }
    } catch (error) {
        console.log('無法獲取用戶資訊:', error);
    }
}

// 路徑正規化函數
function normalizePath(path) {
    // 先移除所有多餘的反斜線
    let normalized = path.replace(/\\+/g, '\\');
    // 確保 UNC 路徑以 \\ 開頭
    if (normalized.startsWith('\\') && !normalized.startsWith('\\\\')) {
        normalized = '\\' + normalized;
    }
    return normalized;
}

async function navigateUp() {
    if (!isConnected) return;

    // 正規化當前路徑
    currentPath = normalizePath(currentPath);

    // 分割路徑，移除空字串
    const pathParts = currentPath.split('\\').filter(p => p);
    debugLog(`當前路徑部分: ${JSON.stringify(pathParts)}`);

    if (pathParts.length > 2) { // 至少保留 server\share
        pathParts.pop();
        currentPath = '\\\\' + pathParts.join('\\');
        debugLog(`上一層路徑: ${currentPath}`);
        await loadFiles();
    } else {
        debugLog('已在根目錄，無法再上一層');
    }
}

async function navigateToFolder(folderName) {
    if (!isConnected) {
        debugLog('未連接，無法導航');
        return;
    }

    debugLog(`導航前路徑: ${currentPath}`);
    debugLog(`要進入的資料夾: ${folderName}`);

    // 正規化當前路徑
    currentPath = normalizePath(currentPath);

    // 構建新路徑
    let newPath = currentPath;
    if (!newPath.endsWith('\\')) {
        newPath += '\\';
    }
    newPath += folderName;

    // 正規化新路徑
    newPath = normalizePath(newPath);

    debugLog(`準備載入新路徑: ${newPath}`);

    // 先更新路徑，再載入檔案
    currentPath = newPath;
    debugLog(`已更新currentPath: ${currentPath}`);

    // 重新載入檔案列表
    await loadFiles();
}

async function loadFiles() {
    if (!isConnected) {
        showStatus('請先連接網路共享', 'error');
        return;
    }
    showStatus('載入中...', 'loading');
    // 正規化並顯示路徑
    currentPath = normalizePath(currentPath);
    document.getElementById('currentPath').textContent = currentPath;
    document.getElementById('fileCount').textContent = '載入中...';
    const list = document.getElementById('fileList');
    list.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i><p>載入中...</p></div>';
    
    try {
        const r = await fetch(`${API}/list?path=${encodeURIComponent(currentPath)}`);
        const result = await r.json();
        if (r.ok && result.status === 'success') {
            // 儲存檔案列表以供過濾
            allFiles = result.files;
            filteredFiles = [...allFiles]; // 預設顯示所有檔案
            
            showStatus(`載入 ${result.total_count} 項目 (${formatSize(result.total_size_mb)})`, 'success');
            updateFileDisplay();
        } else {
            showStatus(result.error || '載入失敗', 'error');
            list.innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><h3>載入失敗</h3></div>';
        }
    } catch (e) {
        showStatus('連線失敗', 'error');
        list.innerHTML = '<div class="empty-state"><i class="fas fa-wifi"></i><h3>連線失敗</h3></div>';
    }
}

async function getInfo(filename) {
    showStatus(`取得 "${decodeURIComponent(filename)}" 資訊...`, 'loading');
    try {
        const r = await fetch(`${API}/info?path=${encodeURIComponent(currentPath)}&filename=${filename}`);
        const result = await r.json();
        if (r.ok && result.status === 'success') {
            showStatus(`${result.filename} | ${result.file_type} | ${formatSize(result.size_mb)} | ${formatTime(result.modified_time)}`, 'success');
        } else {
            showStatus('取得資訊失敗', 'error');
        }
    } catch (e) {
        showStatus('取得資訊失敗', 'error');
    }
}

// 過濾功能
function updateFileDisplay() {
    const list = document.getElementById('fileList');
    document.getElementById('fileCount').textContent = `${filteredFiles.length} 項目`;
    
    if (filteredFiles.length === 0) {
        list.innerHTML = '<div class="empty-state"><i class="fas fa-folder-open"></i><h3>沒有符合條件的檔案</h3></div>';
    } else {
        list.innerHTML = filteredFiles.map(f => {
            const icon = getIcon(f.file_type, f.is_directory);
            const escapedFilename = f.filename.replace(/'/g, "\\'").replace(/"/g, '&quot;');
            const clickAction = f.is_directory ? `onclick="navigateToFolder('${escapedFilename}')" style="cursor:pointer;"` : '';
            const dl = f.is_directory ? '' : `<a href="${API}/download?path=${encodeURIComponent(currentPath)}&filename=${encodeURIComponent(f.filename)}" download class="btn btn-success"><i class="fas fa-download"></i> 下載</a>`;
            const openBtn = f.is_directory ? `<button class="btn btn-primary" onclick="event.stopPropagation(); navigateToFolder('${escapedFilename}');"><i class="fas fa-folder-open"></i> 開啟</button>` : '';
            
            // 高亮搜尋關鍵字
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            let displayName = f.filename;
            if (searchTerm && f.filename.toLowerCase().includes(searchTerm)) {
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                displayName = f.filename.replace(regex, '<mark>$1</mark>');
            }
            
            return `<div class="file-item" ${clickAction}>
                <div class="file-info">
                    <div class="file-icon ${icon.class}"><i class="${icon.icon}"></i></div>
                    <div class="file-details">
                        <h4>${displayName}</h4>
                        <div class="file-meta">${f.file_type} • ${f.is_directory ? '資料夾' : formatSize(f.size_mb)} • ${formatTime(f.modified_time)}</div>
                    </div>
                </div>
                <div class="file-actions">
                    ${openBtn}
                    ${dl}
                </div>
            </div>`;
        }).join('');
    }
    
    // 更新統計
    document.getElementById('filterStats').textContent = 
        `找到 ${filteredFiles.length} 個項目 (共 ${allFiles.length} 個)`;
}

function filterFiles() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    filteredFiles = allFiles.filter(file => {
        // 檔案名稱過濾
        const nameMatch = !searchTerm || file.filename.toLowerCase().includes(searchTerm);
        
        // 日期過濾
        let dateMatch = true;
        if (startDate || endDate) {
            const fileDate = new Date(file.modified_time);
            if (startDate) {
                const start = new Date(startDate);
                dateMatch = dateMatch && fileDate >= start;
            }
            if (endDate) {
                const end = new Date(endDate);
                end.setHours(23, 59, 59, 999); // 包含整天
                dateMatch = dateMatch && fileDate <= end;
            }
        }
        
        return nameMatch && dateMatch;
    });
    
    updateFileDisplay();
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    filteredFiles = [...allFiles];
    updateFileDisplay();
}

async function smartSearch() {
    const query = document.getElementById('smartSearchInput').value.trim();
    if (!query) {
        showStatus('請輸入搜尋查詢', 'error');
        return;
    }
    
    try {
        const r = await fetch(`${API}/smart-search?query=${encodeURIComponent(query)}&path=${encodeURIComponent(currentPath)}`);
        const result = await r.json();
        showStatus(result.message, result.status === 'not_implemented' ? 'loading' : 'error');
    } catch (e) {
        showStatus('智能搜尋請求失敗', 'error');
    }
}

// 設定日期預設值：昨天到今天
function setDefaultDateRange() {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // 格式化為 YYYY-MM-DD
    const formatDate = (date) => {
        return date.getFullYear() + '-' + 
               String(date.getMonth() + 1).padStart(2, '0') + '-' + 
               String(date.getDate()).padStart(2, '0');
    };
    
    document.getElementById('startDate').value = formatDate(yesterday);
    document.getElementById('endDate').value = formatDate(today);
    
    // 更新過濾統計顯示
    document.getElementById('filterStats').textContent = '日期範圍已預設為昨天到今天';
}

// 登入驗證功能
function showLoginStatus(msg, type = 'loading') {
    const s = document.getElementById('loginStatus');
    const icons = {
        success: 'check-circle',
        error: 'exclamation-triangle',
        loading: 'spinner fa-spin'
    };
    s.innerHTML = `<div class="status ${type}" style="margin-top:15px;"><i class="fas fa-${icons[type]}"></i> ${msg}</div>`;
}

async function performLogin() {
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;
    
    if (!username || !password) {
        showLoginStatus('請輸入使用者名稱和密碼', 'error');
        return;
    }
    
    showLoginStatus('驗證中...正在測試網路連接到 \\\\************\\test_log', 'loading');
    document.getElementById('loginBtn').disabled = true;
    
    try {
        // 使用實際網路連接來驗證登入
        const testPath = '\\\\************\\test_log';
        const response = await fetch(`${API}/connect`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                path: testPath,
                username: username,
                password: password,
                domain: 'gmt'
            })
        });
        
        const result = await response.json();
        
        if (result.status === 'success' && result.connected) {
            showLoginStatus('登入成功！網路連接驗證通過', 'success');
            isAuthenticated = true;
            
            // 儲存登入狀態和認證資訊到 sessionStorage
            sessionStorage.setItem('networkBrowserAuth', 'true');
            sessionStorage.setItem('networkBrowserUser', username);
            sessionStorage.setItem('networkBrowserPassword', password);
            
            // 延遲顯示主介面
            setTimeout(() => {
                document.getElementById('loginModal').style.display = 'none';
                document.getElementById('mainContainer').style.display = 'block';
                
                // 自動填入網路共享帳號
                document.getElementById('usernameInput').value = username;
                document.getElementById('passwordInput').value = password;
                
                // 自動連接並載入檔案
                setTimeout(() => {
                    connectToShare();
                }, 500);
            }, 1000);
            
        } else {
            showLoginStatus(`登入失敗：${result.message || '無法連接到 \\\\************\\test_log，請檢查帳號密碼'}`, 'error');
            document.getElementById('loginBtn').disabled = false;
        }
    } catch (error) {
        showLoginStatus('網路連接失敗，請檢查網路狀態和認證資訊', 'error');
        document.getElementById('loginBtn').disabled = false;
    }
}

function checkAuthStatus() {
    // 檢查是否已登入
    const authStatus = sessionStorage.getItem('networkBrowserAuth');
    if (authStatus === 'true') {
        isAuthenticated = true;
        document.getElementById('loginModal').style.display = 'none';
        document.getElementById('mainContainer').style.display = 'block';
        
        // 顯示歡迎訊息
        const username = sessionStorage.getItem('networkBrowserUser');
        if (username) {
            showStatus(`歡迎回來，${username}！請連接網路共享。`, 'success');
            if (username === 'tong.wu') {
                document.getElementById('usernameInput').value = username;
            }
        }
    }
}

// 登出功能
function logout() {
    sessionStorage.removeItem('networkBrowserAuth');
    sessionStorage.removeItem('networkBrowserUser');
    isAuthenticated = false;
    location.reload();
}

// 事件監聽器
window.addEventListener('load', function() {
    // 直接顯示主要內容，跳過登入檢查
    isAuthenticated = true;
    document.getElementById('loginModal').style.display = 'none';
    document.getElementById('mainContainer').style.display = 'block';

    // 自動連接
    showStatus('網路共享瀏覽器正在自動連接...', 'loading');
    setTimeout(() => {
        connectToShare();
    }, 1000);
});

// DOM 載入完成後的初始化
document.addEventListener('DOMContentLoaded', function() {
    
    // 即時搜尋功能
    document.getElementById('searchInput').addEventListener('input', function() {
        if (this.value.trim() === '') {
            clearFilters();
        } else {
            filterFiles();
        }
    });
    
    // Enter鍵登入 - 綁定到登入模態框的輸入欄位
    document.getElementById('loginUsername').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') performLogin();
    });
    document.getElementById('loginPassword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') performLogin();
    });
});