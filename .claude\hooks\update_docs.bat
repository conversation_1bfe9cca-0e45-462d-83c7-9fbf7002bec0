@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 開始自動更新專案文檔...

REM 取得當前時間
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set current_date=%datetime:~0,4%-%datetime:~4,2%-%datetime:~6,2%
set current_time=%datetime:~8,2%:%datetime:~10,2%:%datetime:~12,2%
set current_datetime=%current_date% %current_time%

echo 📊 收集專案統計資料...

REM 計算檔案數量
set total_files=0
set python_files=0
set test_files=0
set doc_files=0
set function_count=0
set class_count=0

REM 計算總檔案數（排除特定目錄）
for /f %%i in ('dir /s /b *.* ^| findstr /v ".git" ^| findstr /v ".venv" ^| findstr /v "__pycache__" ^| find /c /v ""') do set total_files=%%i

REM 計算 Python 檔案數
for /f %%i in ('dir /s /b *.py ^| findstr /v ".venv" ^| findstr /v "__pycache__" ^| find /c /v ""') do set python_files=%%i

REM 計算測試檔案數
for /f %%i in ('dir /s /b *test*.py ^| find /c /v ""') do set test_files=%%i

REM 計算文檔檔案數
for /f %%i in ('dir /s /b *.md ^| find /c /v ""') do set doc_files=%%i

REM 計算函數和類別數量
for /f %%i in ('findstr /s /r "^def " *.py ^| find /c /v ""') do set function_count=%%i
for /f %%i in ('findstr /s /r "^class " *.py ^| find /c /v ""') do set class_count=%%i

REM Git 統計
git --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f %%i in ('git rev-list --all --count 2^>nul') do set commit_count=%%i
    for /f %%i in ('git log --format^=%%an ^| sort ^| uniq ^| find /c /v "" 2^>nul') do set contributors=%%i
    for /f %%i in ('git branch --show-current 2^>nul') do set current_branch=%%i
) else (
    set commit_count=N/A
    set contributors=N/A
    set current_branch=main
)

if "!commit_count!"=="" set commit_count=N/A
if "!contributors!"=="" set contributors=N/A
if "!current_branch!"=="" set current_branch=main

echo 📝 更新 README.md...

if exist README.md (
    echo 正在備份和更新 README.md...
    copy README.md README.md.backup >nul
    
    REM 建立新的統計表格
    echo ^| 項目 ^| 數量 ^| 備註 ^| > temp_stats.txt
    echo ^|------^|------^|------^| >> temp_stats.txt
    echo ^| 📁 總檔案數 ^| !total_files! ^| 專案所有檔案 ^| >> temp_stats.txt
    echo ^| 🐍 Python 檔案 ^| !python_files! ^| 源碼檔案數 ^| >> temp_stats.txt
    echo ^| 📝 程式碼行數 ^| N/A ^| Python 程式碼行數 ^| >> temp_stats.txt
    echo ^| 🧪 測試檔案 ^| !test_files! ^| 單元/整合測試 ^| >> temp_stats.txt
    echo ^| 📚 文檔檔案 ^| !doc_files! ^| Markdown 文檔 ^| >> temp_stats.txt
    echo ^| 🏭 支援廠商 ^| 6 ^| 半導體測試廠商 ^| >> temp_stats.txt
    echo ^| 📦 函數數量 ^| !function_count! ^| Python 函數總數 ^| >> temp_stats.txt
    echo ^| 🔧 類別數量 ^| !class_count! ^| Python 類別總數 ^| >> temp_stats.txt
    echo ^| 🌿 Git 提交 ^| !commit_count! ^| 版本控制歷史 ^| >> temp_stats.txt
    echo ^| 👥 貢獻者 ^| !contributors! ^| 開發團隊成員 ^| >> temp_stats.txt
    
    REM 使用 PowerShell 來更新 README.md（更可靠的文字處理）
    powershell -Command "& {$content = Get-Content 'README.md' -Encoding UTF8; $newContent = @(); $inStatsSection = $false; $statsReplaced = $false; foreach($line in $content) { if($line -match '^## \[ROCKET\] \*\*最新更新') { $newContent += '## [ROCKET] **最新更新 (!current_date!)**' } elseif($line -match '^## 📊 專案統計') { $newContent += $line; $newContent += Get-Content 'temp_stats.txt' -Encoding UTF8; $inStatsSection = $true; $statsReplaced = $true } elseif($inStatsSection -and $line -match '^##') { $newContent += ''; $newContent += $line; $inStatsSection = $false } elseif(-not $inStatsSection) { $newContent += $line } } $newContent | Out-File 'README.md' -Encoding UTF8 }"
    
    del temp_stats.txt
    echo ✅ README.md 已更新
) else (
    echo ⚠️ README.md 不存在
)

echo 📋 更新 CHANGELOG.md...

if exist CHANGELOG.md (
    REM 獲取最新的 Git 變更
    git log --oneline -5 --pretty=format:"- %%s (%%h)" > recent_changes.tmp 2>nul
    
    REM 建立新的 CHANGELOG
    echo # 變更日誌 > new_changelog.md
    echo. >> new_changelog.md
    echo ## [!current_date!] - 自動更新 >> new_changelog.md
    echo. >> new_changelog.md
    echo ### 🔄 變更內容 >> new_changelog.md
    type recent_changes.tmp >> new_changelog.md 2>nul
    echo. >> new_changelog.md
    echo ### 📊 專案統計 >> new_changelog.md
    echo - 總檔案數: !total_files! >> new_changelog.md
    echo - Python 檔案: !python_files! >> new_changelog.md
    echo - 測試檔案: !test_files! >> new_changelog.md
    echo - Git 提交: !commit_count! >> new_changelog.md
    echo. >> new_changelog.md
    
    REM 追加原始內容（跳過第一行標題）
    more +2 CHANGELOG.md >> new_changelog.md 2>nul
    move new_changelog.md CHANGELOG.md >nul
    
    del recent_changes.tmp 2>nul
    echo ✅ CHANGELOG.md 已更新
) else (
    REM 建立新的 CHANGELOG.md
    echo # 變更日誌 > CHANGELOG.md
    echo. >> CHANGELOG.md
    echo ## [!current_date!] - 專案初始化 >> CHANGELOG.md
    echo. >> CHANGELOG.md
    echo ### 🚀 初始功能 >> CHANGELOG.md
    echo - Outlook 郵件處理系統 >> CHANGELOG.md
    echo - 多廠商解析器支援 >> CHANGELOG.md
    echo - Web UI 介面 >> CHANGELOG.md
    echo - 自動化測試框架 >> CHANGELOG.md
    echo. >> CHANGELOG.md
    echo ### 📊 專案統計 >> CHANGELOG.md
    echo - 總檔案數: !total_files! >> CHANGELOG.md
    echo - Python 檔案: !python_files! >> CHANGELOG.md
    echo - 測試檔案: !test_files! >> CHANGELOG.md
    echo ✅ 新建 CHANGELOG.md
)

echo 📄 更新專案資訊...

REM 建立 project_info.json
(
echo {
echo   "project_name": "Outlook Summary System",
echo   "last_updated": "!current_datetime!",
echo   "update_source": "git_hooks",
echo   "statistics": {
echo     "total_files": !total_files!,
echo     "python_files": !python_files!,
echo     "test_files": !test_files!,
echo     "doc_files": !doc_files!,
echo     "function_count": !function_count!,
echo     "class_count": !class_count!,
echo     "commit_count": "!commit_count!",
echo     "contributors": "!contributors!"
echo   },
echo   "git_info": {
echo     "current_branch": "!current_branch!"
echo   },
echo   "generated_at": "!current_datetime!"
echo }
) > project_info.json

echo ✅ project_info.json 已更新

echo 📑 生成文檔摘要...

(
echo # 文檔摘要
echo.
echo **更新時間:** !current_datetime!
echo **更新來源:** Git Hooks 自動更新
echo.
echo ## 📚 主要文檔
echo.
echo ### 核心文檔
echo - **README.md** - 專案概述與快速開始
echo - **CLAUDE.md** - AI 程式設計指導規則  
echo - **PYTHON_MIGRATION_PLAN.md** - Python 遷移計畫
echo - **PROJECT_STATUS_TEMPLATE.md** - 專案狀態追蹤
echo.
echo ### 技術文檔
echo - **VBA_TO_PYTHON_MAPPING.md** - 架構對照表
echo - **DOMAIN_MODELS_DESIGN.md** - 領域模型設計
echo - **UPDATED_ARCHITECTURE_WITH_DATABASE.md** - 完整架構文檔
echo.
echo ### 資料檔案
echo - **CHANGELOG.md** - 變更歷史記錄
echo - **project_info.json** - 專案統計資料
echo.
echo ## 📊 文檔統計
echo - Markdown 檔案: !doc_files! 個
echo - 最後更新: !current_datetime!
echo.
echo ## 🔄 自動更新功能
echo - ✅ 專案統計自動更新
echo - ✅ 變更日誌自動生成
echo - ✅ 版本資訊自動同步
echo - ✅ Git 資訊自動提取
echo.
echo ---
echo *此文檔由 Git Hooks 自動生成*
) > DOC_SUMMARY.md

echo ✅ DOC_SUMMARY.md 已生成

echo.
echo 🎉 文檔自動更新完成！
echo 📋 更新摘要:
echo    ✅ README.md - 專案統計和更新時間
echo    ✅ CHANGELOG.md - 最新變更記錄
echo    ✅ project_info.json - 專案資訊
echo    ✅ DOC_SUMMARY.md - 文檔摘要
echo.
echo 📈 目前專案統計:
echo    📁 總檔案: !total_files!
echo    🐍 Python 檔案: !python_files!
echo    🧪 測試檔案: !test_files!
echo    📚 文檔檔案: !doc_files!
echo    🌿 Git 提交: !commit_count!
echo.

endlocal
