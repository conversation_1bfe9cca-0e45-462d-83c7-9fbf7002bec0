# 異步系統效能監控架構設計

## 🎯 監控系統總覽

### 核心目標
- **即時性能監控**: 實時追蹤異步系統各項性能指標
- **預測性分析**: 基於歷史數據預測性能趨勢和潛在問題
- **自動化告警**: 智能檢測異常並觸發相應的處理機制
- **全鏈路追蹤**: 從API請求到數據庫操作的完整性能鏈路

### 監控架構層級
```mermaid
graph TD
    A[API Gateway 監控層] --> B[異步任務監控層]
    B --> C[資源使用監控層]
    B --> D[業務指標監控層]
    C --> E[系統資源監控]
    C --> F[數據庫性能監控]
    C --> G[文件I/O監控]
    D --> H[用戶行為監控]
    D --> I[業務流程監控]
    E --> J[告警系統]
    F --> J
    G --> J
    H --> J
    I --> J
    J --> K[自動化響應系統]
```

---

## 🏗️ 核心效能指標體系 (KPI)

### 1. 異步處理性能指標

#### 1.1 協程和任務指標
```python
class AsyncTaskMetrics:
    """異步任務性能指標"""
    
    CRITICAL_METRICS = {
        # 協程執行指標
        'active_coroutines': {
            'description': '當前活躍協程數量',
            'threshold': {'warning': 100, 'critical': 200},
            'collection_interval': 5  # 秒
        },
        
        'pending_tasks': {
            'description': '等待執行的任務數量',
            'threshold': {'warning': 500, 'critical': 1000},
            'collection_interval': 10
        },
        
        'task_completion_rate': {
            'description': '任務完成率 (每秒)',
            'threshold': {'min': 10, 'warning_below': 5},
            'collection_interval': 30
        },
        
        'avg_task_execution_time': {
            'description': '平均任務執行時間 (毫秒)',
            'threshold': {'warning': 1000, 'critical': 5000},
            'collection_interval': 15
        },
        
        # 事件循環指標
        'event_loop_lag': {
            'description': '事件循環延遲 (毫秒)',
            'threshold': {'warning': 50, 'critical': 200},
            'collection_interval': 1
        },
        
        'slow_callbacks_count': {
            'description': '慢回調數量',
            'threshold': {'warning': 10, 'critical': 50},
            'collection_interval': 60
        },
        
        # 異步I/O指標
        'concurrent_io_operations': {
            'description': '並發I/O操作數',
            'threshold': {'warning': 100, 'critical': 200},
            'collection_interval': 5
        }
    }
```

#### 1.2 連接池和資源指標
```python
class AsyncResourceMetrics:
    """異步資源性能指標"""
    
    RESOURCE_METRICS = {
        # 數據庫連接池
        'db_pool_active_connections': {
            'description': '數據庫活躍連接數',
            'threshold': {'warning': 15, 'critical': 19},
            'max_value': 20
        },
        
        'db_pool_pending_requests': {
            'description': '等待數據庫連接的請求數',
            'threshold': {'warning': 10, 'critical': 50},
            'collection_interval': 5
        },
        
        'avg_connection_wait_time': {
            'description': '平均連接等待時間 (毫秒)',
            'threshold': {'warning': 100, 'critical': 500},
            'collection_interval': 15
        },
        
        # HTTP連接池
        'http_pool_active_connections': {
            'description': 'HTTP連接池活躍連接數',
            'threshold': {'warning': 80, 'critical': 95},
            'max_value': 100
        },
        
        # 文件操作
        'open_file_handles': {
            'description': '開啟的文件句柄數',
            'threshold': {'warning': 500, 'critical': 900},
            'max_value': 1024
        },
        
        'file_operation_queue_size': {
            'description': '文件操作佇列大小',
            'threshold': {'warning': 100, 'critical': 500},
            'collection_interval': 10
        }
    }
```

### 2. API性能指標

#### 2.1 請求響應指標
```python
class APIPerformanceMetrics:
    """API性能監控指標"""
    
    API_METRICS = {
        # 響應時間指標
        'response_time_p50': {
            'description': '50%請求響應時間 (毫秒)',
            'threshold': {'warning': 500, 'critical': 1000},
            'endpoint_specific': True
        },
        
        'response_time_p95': {
            'description': '95%請求響應時間 (毫秒)',
            'threshold': {'warning': 1000, 'critical': 3000},
            'endpoint_specific': True
        },
        
        'response_time_p99': {
            'description': '99%請求響應時間 (毫秒)',
            'threshold': {'warning': 2000, 'critical': 5000},
            'endpoint_specific': True
        },
        
        # 吞吐量指標
        'requests_per_second': {
            'description': '每秒請求數',
            'threshold': {'min': 10, 'warning_below': 5},
            'endpoint_specific': True
        },
        
        'concurrent_requests': {
            'description': '並發請求數',
            'threshold': {'warning': 50, 'critical': 100},
            'collection_interval': 1
        },
        
        # 錯誤率指標  
        'error_rate': {
            'description': '錯誤率 (%)',
            'threshold': {'warning': 1, 'critical': 5},
            'endpoint_specific': True
        },
        
        'timeout_rate': {
            'description': '超時率 (%)',
            'threshold': {'warning': 0.5, 'critical': 2},
            'endpoint_specific': True
        },
        
        # 異步特定指標
        'async_task_queue_depth': {
            'description': '異步任務佇列深度',
            'threshold': {'warning': 100, 'critical': 500},
            'collection_interval': 5
        }
    }
```

### 3. 業務性能指標

#### 3.1 郵件處理指標
```python
class EmailProcessingMetrics:
    """郵件處理業務指標"""
    
    EMAIL_METRICS = {
        # 處理速度指標
        'emails_processed_per_minute': {
            'description': '每分鐘處理郵件數',
            'threshold': {'min': 5, 'warning_below': 3},
            'business_critical': True
        },
        
        'attachment_processing_speed': {
            'description': '附件處理速度 (MB/秒)',
            'threshold': {'min': 1, 'warning_below': 0.5},
            'collection_interval': 30
        },
        
        'email_sync_lag': {
            'description': '郵件同步延遲 (秒)',
            'threshold': {'warning': 300, 'critical': 900},
            'collection_interval': 60
        },
        
        # 處理質量指標
        'parsing_success_rate': {
            'description': '郵件解析成功率 (%)',
            'threshold': {'min': 95, 'warning_below': 90},
            'business_critical': True
        },
        
        'vendor_identification_accuracy': {
            'description': '廠商識別準確率 (%)',
            'threshold': {'min': 90, 'warning_below': 80},
            'collection_interval': 60
        }
    }
```

#### 3.2 文件處理指標
```python
class FileProcessingMetrics:
    """文件處理業務指標"""
    
    FILE_METRICS = {
        # EQC處理指標
        'eqc_processing_time': {
            'description': 'EQC檔案處理時間 (秒)',
            'threshold': {'warning': 30, 'critical': 120},
            'file_size_dependent': True
        },
        
        'eqc_processing_success_rate': {
            'description': 'EQC處理成功率 (%)',
            'threshold': {'min': 95, 'warning_below': 90},
            'business_critical': True
        },
        
        # 文件上傳指標
        'upload_processing_time': {
            'description': '文件上傳處理時間 (秒)',
            'threshold': {'warning': 10, 'critical': 30},
            'async_immediate_response': True
        },
        
        'compression_efficiency': {
            'description': '壓縮解壓效率 (MB/秒)',
            'threshold': {'min': 10, 'warning_below': 5},
            'collection_interval': 60
        },
        
        # 存儲指標
        'disk_space_usage': {
            'description': '磁碟空間使用率 (%)',
            'threshold': {'warning': 80, 'critical': 90},
            'collection_interval': 300
        }
    }
```

---

## 📊 實時監控架構實現

### 1. 監控數據收集系統

#### 1.1 異步指標收集器
```python
import asyncio
import time
import psutil
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
from datetime import datetime
import weakref

@dataclass
class MetricPoint:
    """指標數據點"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str]
    unit: str = None
    
class AsyncMetricsCollector:
    """異步指標收集器"""
    
    def __init__(self, collection_interval: int = 10):
        self.collection_interval = collection_interval
        self.metrics_buffer: List[MetricPoint] = []
        self.active_tasks = weakref.WeakSet()
        self.running = False
        self._collection_task = None
        
    async def start_collection(self):
        """啟動指標收集"""
        self.running = True
        self._collection_task = asyncio.create_task(self._collection_loop())
        
    async def stop_collection(self):
        """停止指標收集"""
        self.running = False
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
                
    async def _collection_loop(self):
        """指標收集主循環"""
        while self.running:
            try:
                # 收集異步任務指標
                await self._collect_async_metrics()
                
                # 收集系統資源指標
                await self._collect_system_metrics()
                
                # 收集業務指標
                await self._collect_business_metrics()
                
                # 發送指標到儲存後端
                await self._flush_metrics()
                
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"指標收集錯誤: {e}")
                await asyncio.sleep(1)
                
    async def _collect_async_metrics(self):
        """收集異步相關指標"""
        # 獲取當前事件循環
        loop = asyncio.get_running_loop()
        
        # 計算活躍任務數
        all_tasks = asyncio.all_tasks(loop)
        active_tasks = [t for t in all_tasks if not t.done()]
        
        self.metrics_buffer.extend([
            MetricPoint(
                name="async.active_tasks",
                value=len(active_tasks),
                timestamp=datetime.now(),
                tags={"component": "async_runtime"}
            ),
            MetricPoint(
                name="async.total_tasks",
                value=len(all_tasks),
                timestamp=datetime.now(),
                tags={"component": "async_runtime"}
            ),
            MetricPoint(
                name="async.completed_tasks",
                value=len([t for t in all_tasks if t.done() and not t.cancelled()]),
                timestamp=datetime.now(),
                tags={"component": "async_runtime"}
            ),
            MetricPoint(
                name="async.cancelled_tasks",
                value=len([t for t in all_tasks if t.cancelled()]),
                timestamp=datetime.now(),
                tags={"component": "async_runtime"}
            )
        ])
        
    async def _collect_system_metrics(self):
        """收集系統資源指標"""
        # CPU 指標
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # 記憶體指標
        memory = psutil.virtual_memory()
        
        # 磁碟指標
        disk = psutil.disk_usage('/')
        
        # 網路指標
        network = psutil.net_io_counters()
        
        self.metrics_buffer.extend([
            MetricPoint(
                name="system.cpu_percent",
                value=cpu_percent,
                timestamp=datetime.now(),
                tags={"component": "system"},
                unit="percent"
            ),
            MetricPoint(
                name="system.memory_percent",
                value=memory.percent,
                timestamp=datetime.now(),
                tags={"component": "system"},
                unit="percent"
            ),
            MetricPoint(
                name="system.memory_used",
                value=memory.used / 1024 / 1024,  # MB
                timestamp=datetime.now(),
                tags={"component": "system"},
                unit="mb"
            ),
            MetricPoint(
                name="system.disk_usage_percent",
                value=(disk.used / disk.total) * 100,
                timestamp=datetime.now(),
                tags={"component": "system"},
                unit="percent"
            )
        ])
        
    async def _collect_business_metrics(self):
        """收集業務相關指標"""
        # 這裡會整合具體的業務指標收集
        # 例如從資料庫查詢處理統計
        pass
        
    async def _flush_metrics(self):
        """將指標發送到儲存後端"""
        if not self.metrics_buffer:
            return
            
        # 發送到時序資料庫 (如 InfluxDB, Prometheus)
        try:
            await self._send_to_backend(self.metrics_buffer)
            self.metrics_buffer.clear()
        except Exception as e:
            logger.error(f"指標發送失敗: {e}")
            # 保留緩衝區資料，下次重試
            
    async def _send_to_backend(self, metrics: List[MetricPoint]):
        """發送指標到後端存儲"""
        # 實現發送邏輯，例如發送到 Prometheus 或 InfluxDB
        pass
```

#### 1.2 API性能監控中間件
```python
import time
from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from typing import Callable

class AsyncAPIMonitoringMiddleware(BaseHTTPMiddleware):
    """異步API性能監控中間件"""
    
    def __init__(self, app, metrics_collector: AsyncMetricsCollector):
        super().__init__(app)
        self.metrics_collector = metrics_collector
        self.active_requests = {}
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 記錄請求開始時間
        start_time = time.perf_counter()
        request_id = id(request)
        
        # 記錄活躍請求
        self.active_requests[request_id] = {
            'method': request.method,
            'path': request.url.path,
            'start_time': start_time
        }
        
        try:
            # 執行請求
            response = await call_next(request)
            
            # 計算響應時間
            end_time = time.perf_counter()
            response_time = (end_time - start_time) * 1000  # 毫秒
            
            # 記錄指標
            await self._record_metrics(
                request, response, response_time, success=True
            )
            
            return response
            
        except Exception as e:
            # 記錄錯誤指標
            end_time = time.perf_counter()
            response_time = (end_time - start_time) * 1000
            
            await self._record_metrics(
                request, None, response_time, success=False, error=str(e)
            )
            
            raise
            
        finally:
            # 清理活躍請求記錄
            self.active_requests.pop(request_id, None)
            
    async def _record_metrics(self, request: Request, response: Response, 
                            response_time: float, success: bool, error: str = None):
        """記錄API請求指標"""
        
        # 基本標籤
        tags = {
            'method': request.method,
            'endpoint': request.url.path,
            'status': 'success' if success else 'error'
        }
        
        if response:
            tags['status_code'] = str(response.status_code)
            
        # 記錄響應時間
        self.metrics_collector.metrics_buffer.append(
            MetricPoint(
                name="api.response_time",
                value=response_time,
                timestamp=datetime.now(),
                tags=tags,
                unit="ms"
            )
        )
        
        # 記錄請求計數
        self.metrics_collector.metrics_buffer.append(
            MetricPoint(
                name="api.request_count",
                value=1,
                timestamp=datetime.now(),
                tags=tags
            )
        )
        
        # 記錄活躍請求數
        self.metrics_collector.metrics_buffer.append(
            MetricPoint(
                name="api.active_requests",
                value=len(self.active_requests),
                timestamp=datetime.now(),
                tags={'component': 'api'}
            )
        )
```

### 2. 資源使用監控

#### 2.1 數據庫連接池監控
```python
class AsyncDatabasePoolMonitor:
    """異步數據庫連接池監控"""
    
    def __init__(self, pool, metrics_collector: AsyncMetricsCollector):
        self.pool = pool
        self.metrics_collector = metrics_collector
        self.monitoring = False
        
    async def start_monitoring(self):
        """啟動連接池監控"""
        self.monitoring = True
        asyncio.create_task(self._monitor_loop())
        
    async def _monitor_loop(self):
        """連接池監控循環"""
        while self.monitoring:
            try:
                # 收集連接池指標
                pool_stats = await self._get_pool_stats()
                
                # 記錄指標
                for metric_name, value in pool_stats.items():
                    self.metrics_collector.metrics_buffer.append(
                        MetricPoint(
                            name=f"db_pool.{metric_name}",
                            value=value,
                            timestamp=datetime.now(),
                            tags={'component': 'database'}
                        )
                    )
                    
                await asyncio.sleep(10)  # 每10秒收集一次
                
            except Exception as e:
                logger.error(f"數據庫連接池監控錯誤: {e}")
                await asyncio.sleep(5)
                
    async def _get_pool_stats(self) -> Dict[str, float]:
        """獲取連接池統計信息"""
        return {
            'active_connections': len(self.pool._active_connections) if hasattr(self.pool, '_active_connections') else 0,
            'idle_connections': len(self.pool._idle_connections) if hasattr(self.pool, '_idle_connections') else 0,
            'pending_requests': self.pool._pending_requests if hasattr(self.pool, '_pending_requests') else 0,
            'max_connections': self.pool._max_connections if hasattr(self.pool, '_max_connections') else 0
        }
```

#### 2.2 文件I/O監控
```python
class AsyncFileIOMonitor:
    """異步文件I/O監控"""
    
    def __init__(self, metrics_collector: AsyncMetricsCollector):
        self.metrics_collector = metrics_collector
        self.active_operations = {}
        
    async def monitor_file_operation(self, operation_name: str, file_path: str):
        """監控文件操作的裝飾器"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                operation_id = f"{operation_name}_{id(args)}"
                
                # 記錄開始操作
                self.active_operations[operation_id] = {
                    'operation': operation_name,
                    'file_path': file_path,
                    'start_time': start_time
                }
                
                try:
                    result = await func(*args, **kwargs)
                    
                    # 記錄成功指標
                    end_time = time.perf_counter()
                    duration = (end_time - start_time) * 1000  # 毫秒
                    
                    await self._record_file_metrics(
                        operation_name, file_path, duration, success=True
                    )
                    
                    return result
                    
                except Exception as e:
                    # 記錄錯誤指標
                    end_time = time.perf_counter()
                    duration = (end_time - start_time) * 1000
                    
                    await self._record_file_metrics(
                        operation_name, file_path, duration, success=False, error=str(e)
                    )
                    
                    raise
                    
                finally:
                    # 清理操作記錄
                    self.active_operations.pop(operation_id, None)
                    
            return wrapper
        return decorator
        
    async def _record_file_metrics(self, operation: str, file_path: str, 
                                 duration: float, success: bool, error: str = None):
        """記錄文件操作指標"""
        
        tags = {
            'operation': operation,
            'file_type': file_path.split('.')[-1] if '.' in file_path else 'unknown',
            'status': 'success' if success else 'error'
        }
        
        # 記錄操作時間
        self.metrics_collector.metrics_buffer.append(
            MetricPoint(
                name="file_io.operation_time",
                value=duration,
                timestamp=datetime.now(),
                tags=tags,
                unit="ms"
            )
        )
        
        # 記錄操作計數
        self.metrics_collector.metrics_buffer.append(
            MetricPoint(
                name="file_io.operation_count",
                value=1,
                timestamp=datetime.now(),
                tags=tags
            )
        )
        
        # 記錄活躍操作數
        self.metrics_collector.metrics_buffer.append(
            MetricPoint(
                name="file_io.active_operations",
                value=len(self.active_operations),
                timestamp=datetime.now(),
                tags={'component': 'file_io'}
            )
        )
```

---

## 🚨 智能告警系統

### 1. 告警規則引擎

#### 1.1 閾值告警
```python
from enum import Enum
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass

class AlertSeverity(Enum):
    INFO = "info"
    WARNING = "warning"  
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class AlertRule:
    """告警規則定義"""
    name: str
    metric_name: str
    condition: str  # >, <, ==, !=
    threshold: float
    severity: AlertSeverity
    duration: int  # 持續時間(秒)
    description: str
    tags: Dict[str, str] = None
    
class SmartAlertEngine:
    """智能告警引擎"""
    
    def __init__(self, metrics_collector: AsyncMetricsCollector):
        self.metrics_collector = metrics_collector
        self.alert_rules: List[AlertRule] = []
        self.active_alerts: Dict[str, Dict] = {}
        self.alert_history: List[Dict] = []
        self.running = False
        
    def add_rule(self, rule: AlertRule):
        """添加告警規則"""
        self.alert_rules.append(rule)
        
    async def start_monitoring(self):
        """啟動告警監控"""
        self.running = True
        asyncio.create_task(self._alert_loop())
        
    async def _alert_loop(self):
        """告警檢查循環"""
        while self.running:
            try:
                # 檢查所有告警規則
                for rule in self.alert_rules:
                    await self._check_rule(rule)
                    
                # 檢查告警恢復
                await self._check_alert_recovery()
                
                await asyncio.sleep(10)  # 每10秒檢查一次
                
            except Exception as e:
                logger.error(f"告警檢查錯誤: {e}")
                await asyncio.sleep(5)
                
    async def _check_rule(self, rule: AlertRule):
        """檢查單個告警規則"""
        # 獲取最近的指標值
        recent_metrics = self._get_recent_metrics(rule.metric_name, rule.duration)
        
        if not recent_metrics:
            return
            
        # 檢查條件
        triggered = False
        if rule.condition == ">":
            triggered = all(m.value > rule.threshold for m in recent_metrics)
        elif rule.condition == "<":
            triggered = all(m.value < rule.threshold for m in recent_metrics)
        elif rule.condition == ">=":
            triggered = all(m.value >= rule.threshold for m in recent_metrics)
        elif rule.condition == "<=":
            triggered = all(m.value <= rule.threshold for m in recent_metrics)
            
        alert_key = f"{rule.name}_{rule.metric_name}"
        
        if triggered and alert_key not in self.active_alerts:
            # 觸發新告警
            await self._trigger_alert(rule, recent_metrics[-1].value)
        elif not triggered and alert_key in self.active_alerts:
            # 告警恢復
            await self._resolve_alert(rule)
            
    async def _trigger_alert(self, rule: AlertRule, current_value: float):
        """觸發告警"""
        alert_key = f"{rule.name}_{rule.metric_name}"
        
        alert_data = {
            'rule_name': rule.name,
            'metric_name': rule.metric_name,
            'severity': rule.severity.value,
            'current_value': current_value,
            'threshold': rule.threshold,
            'description': rule.description,
            'triggered_at': datetime.now(),
            'tags': rule.tags or {}
        }
        
        self.active_alerts[alert_key] = alert_data
        self.alert_history.append({**alert_data, 'action': 'triggered'})
        
        # 發送告警通知
        await self._send_alert_notification(alert_data)
        
        logger.warning(f"告警觸發: {rule.name} - {rule.description} (當前值: {current_value})")
        
    async def _resolve_alert(self, rule: AlertRule):
        """解決告警"""
        alert_key = f"{rule.name}_{rule.metric_name}"
        
        if alert_key in self.active_alerts:
            alert_data = self.active_alerts.pop(alert_key)
            alert_data['resolved_at'] = datetime.now()
            
            self.alert_history.append({**alert_data, 'action': 'resolved'})
            
            # 發送恢復通知
            await self._send_recovery_notification(alert_data)
            
            logger.info(f"告警恢復: {rule.name}")
            
    def _get_recent_metrics(self, metric_name: str, duration: int) -> List[MetricPoint]:
        """獲取最近的指標數據"""
        cutoff_time = datetime.now() - timedelta(seconds=duration)
        
        return [
            m for m in self.metrics_collector.metrics_buffer
            if m.name == metric_name and m.timestamp >= cutoff_time
        ]
        
    async def _send_alert_notification(self, alert_data: Dict):
        """發送告警通知"""
        # 實現通知邏輯 (LINE, Email, Slack等)
        pass
        
    async def _send_recovery_notification(self, alert_data: Dict):
        """發送恢復通知"""
        # 實現恢復通知邏輯
        pass
```

#### 1.2 預設告警規則配置
```python
def setup_default_alert_rules(alert_engine: SmartAlertEngine):
    """設置預設告警規則"""
    
    # 異步任務相關告警
    alert_engine.add_rule(AlertRule(
        name="high_active_tasks",
        metric_name="async.active_tasks",
        condition=">",
        threshold=100,
        severity=AlertSeverity.WARNING,
        duration=60,
        description="活躍異步任務數過高",
        tags={"category": "async_performance"}
    ))
    
    alert_engine.add_rule(AlertRule(
        name="critical_active_tasks",
        metric_name="async.active_tasks",
        condition=">",
        threshold=200,
        severity=AlertSeverity.CRITICAL,
        duration=30,
        description="活躍異步任務數達到臨界值",
        tags={"category": "async_performance"}
    ))
    
    # 事件循環延遲告警
    alert_engine.add_rule(AlertRule(
        name="event_loop_lag",
        metric_name="async.event_loop_lag",
        condition=">",
        threshold=100,  # 100ms
        severity=AlertSeverity.WARNING,
        duration=30,
        description="事件循環延遲過高",
        tags={"category": "async_performance"}
    ))
    
    # API響應時間告警
    alert_engine.add_rule(AlertRule(
        name="api_response_time_high",
        metric_name="api.response_time",
        condition=">",
        threshold=1000,  # 1秒
        severity=AlertSeverity.WARNING,
        duration=120,
        description="API響應時間過長",
        tags={"category": "api_performance"}
    ))
    
    # 錯誤率告警
    alert_engine.add_rule(AlertRule(
        name="high_error_rate",
        metric_name="api.error_rate",
        condition=">",
        threshold=5.0,  # 5%
        severity=AlertSeverity.CRITICAL,
        duration=60,
        description="API錯誤率過高",
        tags={"category": "api_reliability"}
    ))
    
    # 系統資源告警
    alert_engine.add_rule(AlertRule(
        name="high_memory_usage",
        metric_name="system.memory_percent",
        condition=">",
        threshold=85.0,
        severity=AlertSeverity.WARNING,
        duration=300,
        description="記憶體使用率過高",
        tags={"category": "system_resources"}
    ))
    
    alert_engine.add_rule(AlertRule(
        name="high_cpu_usage",
        metric_name="system.cpu_percent",
        condition=">",
        threshold=90.0,
        severity=AlertSeverity.WARNING,
        duration=180,
        description="CPU使用率過高",
        tags={"category": "system_resources"}
    ))
    
    # 數據庫連接池告警
    alert_engine.add_rule(AlertRule(
        name="db_pool_exhaustion",
        metric_name="db_pool.active_connections",
        condition=">",
        threshold=18,  # 假設最大20個連接
        severity=AlertSeverity.CRITICAL,
        duration=30,
        description="數據庫連接池接近耗盡",
        tags={"category": "database_performance"}
    ))
    
    # 業務相關告警
    alert_engine.add_rule(AlertRule(
        name="low_email_processing_rate",
        metric_name="email.processing_rate",
        condition="<",
        threshold=3.0,  # 每分鐘少於3封
        severity=AlertSeverity.WARNING,
        duration=300,
        description="郵件處理速度過慢",
        tags={"category": "business_performance"}
    ))
```

### 2. 自動化響應系統

#### 2.1 自動修復機制
```python
class AutomatedResponseSystem:
    """自動化響應系統"""
    
    def __init__(self, alert_engine: SmartAlertEngine):
        self.alert_engine = alert_engine
        self.response_handlers = {}
        self.running = False
        
    def register_handler(self, rule_name: str, handler: Callable):
        """註冊響應處理器"""
        self.response_handlers[rule_name] = handler
        
    async def start_monitoring(self):
        """啟動自動響應監控"""
        self.running = True
        asyncio.create_task(self._response_loop())
        
    async def _response_loop(self):
        """響應處理循環"""
        while self.running:
            try:
                # 檢查新觸發的告警
                for alert_key, alert_data in self.alert_engine.active_alerts.items():
                    rule_name = alert_data['rule_name']
                    
                    if rule_name in self.response_handlers:
                        # 檢查是否已經處理過
                        if not alert_data.get('auto_response_triggered', False):
                            await self._execute_response(rule_name, alert_data)
                            alert_data['auto_response_triggered'] = True
                            
                await asyncio.sleep(30)  # 每30秒檢查一次
                
            except Exception as e:
                logger.error(f"自動響應系統錯誤: {e}")
                await asyncio.sleep(10)
                
    async def _execute_response(self, rule_name: str, alert_data: Dict):
        """執行自動響應"""
        try:
            handler = self.response_handlers[rule_name]
            await handler(alert_data)
            
            logger.info(f"自動響應已執行: {rule_name}")
            
        except Exception as e:
            logger.error(f"自動響應執行失敗 {rule_name}: {e}")
            
# 自動響應處理器範例
async def handle_high_memory_usage(alert_data: Dict):
    """處理高記憶體使用率"""
    logger.warning("執行記憶體清理程序...")
    
    # 觸發垃圾回收
    import gc
    gc.collect()
    
    # 清理緩存
    # cache.clear()
    
    # 記錄處理動作
    logger.info("記憶體清理完成")
    
async def handle_db_pool_exhaustion(alert_data: Dict):
    """處理數據庫連接池耗盡"""
    logger.warning("檢測到數據庫連接池壓力，執行連接重置...")
    
    # 重置長時間空閒的連接
    # await db_pool.reset_idle_connections()
    
    # 增加連接池大小 (臨時措施)
    # await db_pool.expand_pool_size(5)
    
    logger.info("數據庫連接池優化完成")
    
async def handle_high_active_tasks(alert_data: Dict):
    """處理高活躍任務數"""
    logger.warning("檢測到過多活躍任務，執行任務清理...")
    
    # 取消超時的任務
    current_time = time.time()
    cancelled_count = 0
    
    for task in asyncio.all_tasks():
        if not task.done():
            # 檢查任務是否超時 (簡化邏輯)
            if hasattr(task, '_created_at'):
                if current_time - task._created_at > 300:  # 5分鐘
                    task.cancel()
                    cancelled_count += 1
                    
    logger.info(f"已取消 {cancelled_count} 個超時任務")
```

---

## 📈 監控配置與部署

### 1. 監控系統配置

#### 1.1 配置文件結構
```yaml
# monitoring_config.yaml
monitoring:
  # 基本配置
  enabled: true
  collection_interval: 10  # 秒
  retention_period: "30d"
  
  # 指標收集配置
  metrics:
    async_tasks:
      enabled: true
      interval: 5
      
    system_resources:
      enabled: true
      interval: 15
      
    api_performance:
      enabled: true
      detailed_logging: true
      
    business_metrics:
      enabled: true
      interval: 60
      
  # 告警配置
  alerting:
    enabled: true
    check_interval: 10
    notification_channels:
      - type: "line"
        token: "${LINE_NOTIFY_TOKEN}"
        enabled: true
      - type: "email"
        smtp_server: "smtp.gmail.com"
        enabled: false
        
  # 存儲配置
  storage:
    backend: "influxdb"  # influxdb, prometheus, file
    connection_string: "http://localhost:8086"
    database: "async_monitoring"
    
  # 自動響應配置
  auto_response:
    enabled: true
    handlers:
      high_memory_usage:
        enabled: true
        cooldown: 300  # 5分鐘冷卻期
      db_pool_exhaustion:
        enabled: true
        cooldown: 180
      high_active_tasks:
        enabled: true
        cooldown: 120
```

#### 1.2 監控系統初始化
```python
class MonitoringSystemManager:
    """監控系統管理器"""
    
    def __init__(self, config_path: str = "monitoring_config.yaml"):
        self.config = self._load_config(config_path)
        self.metrics_collector = None
        self.alert_engine = None
        self.auto_response = None
        
    async def initialize(self):
        """初始化監控系統"""
        if not self.config['monitoring']['enabled']:
            logger.info("監控系統已禁用")
            return
            
        # 初始化指標收集器
        self.metrics_collector = AsyncMetricsCollector(
            collection_interval=self.config['monitoring']['collection_interval']
        )
        
        # 初始化告警引擎
        self.alert_engine = SmartAlertEngine(self.metrics_collector)
        setup_default_alert_rules(self.alert_engine)
        
        # 初始化自動響應系統
        if self.config['monitoring']['auto_response']['enabled']:
            self.auto_response = AutomatedResponseSystem(self.alert_engine)
            self._setup_response_handlers()
            
        logger.info("監控系統初始化完成")
        
    async def start(self):
        """啟動監控系統"""
        if not self.config['monitoring']['enabled']:
            return
            
        # 啟動各個組件
        await self.metrics_collector.start_collection()
        await self.alert_engine.start_monitoring()
        
        if self.auto_response:
            await self.auto_response.start_monitoring()
            
        logger.info("監控系統已啟動")
        
    async def stop(self):
        """停止監控系統"""
        if self.metrics_collector:
            await self.metrics_collector.stop_collection()
            
        logger.info("監控系統已停止")
        
    def _load_config(self, config_path: str) -> Dict:
        """載入配置文件"""
        import yaml
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"配置文件不存在: {config_path}，使用預設配置")
            return self._get_default_config()
            
    def _get_default_config(self) -> Dict:
        """預設配置"""
        return {
            'monitoring': {
                'enabled': True,
                'collection_interval': 10,
                'retention_period': '30d',
                'alerting': {
                    'enabled': True,
                    'check_interval': 10
                },
                'auto_response': {
                    'enabled': True
                }
            }
        }
        
    def _setup_response_handlers(self):
        """設置響應處理器"""
        if self.config['monitoring']['auto_response']['handlers']['high_memory_usage']['enabled']:
            self.auto_response.register_handler('high_memory_usage', handle_high_memory_usage)
            
        if self.config['monitoring']['auto_response']['handlers']['db_pool_exhaustion']['enabled']:
            self.auto_response.register_handler('db_pool_exhaustion', handle_db_pool_exhaustion)
            
        if self.config['monitoring']['auto_response']['handlers']['high_active_tasks']['enabled']:
            self.auto_response.register_handler('high_active_tasks', handle_high_active_tasks)
```

### 2. 與現有系統整合

#### 2.1 FastAPI 應用整合
```python
# 在 FastAPI 應用中整合監控
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 創建監控系統管理器
monitoring_manager = MonitoringSystemManager()

# 創建 FastAPI 應用
app = FastAPI(title="Outlook Summary System")

# 添加監控中間件
if monitoring_manager.config['monitoring']['metrics']['api_performance']['enabled']:
    app.add_middleware(
        AsyncAPIMonitoringMiddleware,
        metrics_collector=monitoring_manager.metrics_collector
    )

@app.on_event("startup")
async def startup_event():
    """應用啟動事件"""
    # 初始化並啟動監控系統
    await monitoring_manager.initialize()
    await monitoring_manager.start()
    
    logger.info("應用啟動完成，監控系統已激活")
    
@app.on_event("shutdown")
async def shutdown_event():
    """應用關閉事件"""
    # 停止監控系統
    await monitoring_manager.stop()
    
    logger.info("應用已關閉，監控系統已停止")

# 監控相關 API 端點
@app.get("/monitoring/health")
async def monitoring_health():
    """監控系統健康檢查"""
    return {
        "status": "healthy",
        "monitoring_enabled": monitoring_manager.config['monitoring']['enabled'],
        "active_alerts": len(monitoring_manager.alert_engine.active_alerts) if monitoring_manager.alert_engine else 0
    }
    
@app.get("/monitoring/metrics")
async def get_current_metrics():
    """獲取當前指標"""
    if not monitoring_manager.metrics_collector:
        return {"error": "監控系統未啟用"}
        
    # 返回最近的指標數據
    recent_metrics = monitoring_manager.metrics_collector.metrics_buffer[-50:]  # 最近50個指標點
    
    return {
        "metrics_count": len(recent_metrics),
        "latest_metrics": [asdict(m) for m in recent_metrics]
    }
    
@app.get("/monitoring/alerts")
async def get_active_alerts():
    """獲取活躍告警"""
    if not monitoring_manager.alert_engine:
        return {"error": "告警系統未啟用"}
        
    return {
        "active_alerts": list(monitoring_manager.alert_engine.active_alerts.values()),
        "alert_history": monitoring_manager.alert_engine.alert_history[-20:]  # 最近20條歷史
    }
```

### 3. 部署檢查清單

```yaml
部署前檢查:
  配置檢查:
    - [ ] 監控配置文件已正確設置
    - [ ] 告警閾值符合實際環境
    - [ ] 通知渠道已配置和測試
    - [ ] 存儲後端連接正常
    
  系統整合:
    - [ ] 監控中間件已添加到所有API
    - [ ] 數據庫連接池監控已啟用
    - [ ] 文件I/O監控已整合
    - [ ] 業務指標收集已實現
    
  功能驗證:
    - [ ] 指標收集正常運行
    - [ ] 告警規則觸發測試通過
    - [ ] 自動響應機制測試通過
    - [ ] 監控API端點正常響應
    
  性能影響:
    - [ ] 監控系統CPU佔用 < 5%
    - [ ] 監控系統記憶體佔用 < 100MB
    - [ ] 對業務系統性能影響 < 2%
    - [ ] 指標存儲空間使用合理

部署後驗證:
  運行狀態:
    - [ ] 所有監控組件正常運行
    - [ ] 指標數據正常收集和存儲
    - [ ] 告警系統正常檢查
    - [ ] 自動響應系統正常工作
    
  資料品質:
    - [ ] 指標數據準確性驗證
    - [ ] 告警觸發準確性測試
    - [ ] 歷史數據保留策略生效
    - [ ] 數據清理機制正常
    
  持續監控:
    - [ ] 建立日常監控檢查程序
    - [ ] 設置週期性系統健康報告
    - [ ] 建立監控系統本身的監控
    - [ ] 制定應急響應程序
```

---

## 📋 實施時程與里程碑

### Phase 1: 基礎監控建設 (Week 1)
- Day 1-2: 指標收集系統實現
- Day 3-4: API監控中間件整合
- Day 5-7: 基礎告警系統建設

### Phase 2: 高級監控功能 (Week 2)
- Day 1-3: 資源監控和業務指標
- Day 4-5: 智能告警和自動響應
- Day 6-7: 監控系統測試和調優

### Phase 3: 部署和驗證 (Week 3)
- Day 1-2: 生產環境部署
- Day 3-5: 監控效果驗證
- Day 6-7: 文檔完善和培訓

---

*本文檔提供了完整的異步系統效能監控架構，確保系統在異步升級後能夠實時掌握性能狀況，預防和快速響應各種性能問題。*