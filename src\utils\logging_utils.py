"""
統一日誌工具
解決日誌時間重複顯示問題，提供一致的日誌格式
"""

import time
import sys
from typing import Optional
from src.infrastructure.logging.logger_manager import LoggerManager, LogLevel


class LoggingUtils:
    """統一日誌工具類"""
    
    # ANSI 顏色代碼
    class Colors:
        RED = '\033[91m'
        GREEN = '\033[92m'
        YELLOW = '\033[93m'
        BLUE = '\033[94m'
        MAGENTA = '\033[95m'
        CYAN = '\033[96m'
        WHITE = '\033[97m'
        BOLD = '\033[1m'
        END = '\033[0m'
    
    @staticmethod
    def get_timestamp() -> str:
        """取得統一格式的時間戳"""
        return time.strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def print_status(status: str, message: str) -> None:
        """
        印出狀態資訊，使用統一的時間格式
        
        Args:
            status: 狀態類型 (success, warning, error, info, sync, email)
            message: 訊息內容
        """
        timestamp = LoggingUtils.get_timestamp()
        
        if status == "success":
            print(f"{timestamp} {LoggingUtils.Colors.GREEN}[OK]{LoggingUtils.Colors.END} {message}")
        elif status == "warning":
            print(f"{timestamp} {LoggingUtils.Colors.YELLOW}[WARNING]{LoggingUtils.Colors.END} {message}")
        elif status == "error":
            print(f"{timestamp} {LoggingUtils.Colors.RED}[ERROR]{LoggingUtils.Colors.END} {message}")
        elif status == "info":
            print(f"{timestamp} {LoggingUtils.Colors.BLUE}[INFO]{LoggingUtils.Colors.END} {message}")
        elif status == "sync":
            print(f"{timestamp} {LoggingUtils.Colors.CYAN}[SYNC]{LoggingUtils.Colors.END} {message}")
        elif status == "email":
            print(f"{timestamp} {LoggingUtils.Colors.MAGENTA}[EMAIL]{LoggingUtils.Colors.END} {message}")
        else:
            print(f"{timestamp} {LoggingUtils.Colors.WHITE}{message}{LoggingUtils.Colors.END}")
        sys.stdout.flush()
    
    @staticmethod
    def get_logger_manager() -> LoggerManager:
        """取得統一配置的日誌管理器"""
        return LoggerManager(
            include_caller_info=True,
            enable_colors=True
        )
    
    @staticmethod
    def format_log_message(level: str, logger_name: str, message: str, 
                          filename: Optional[str] = None, 
                          function: Optional[str] = None, 
                          line: Optional[int] = None) -> str:
        """
        格式化日誌訊息，確保統一格式
        
        Args:
            level: 日誌級別
            logger_name: 日誌器名稱
            message: 訊息內容
            filename: 檔案名稱（可選）
            function: 函數名稱（可選）
            line: 行號（可選）
            
        Returns:
            格式化後的日誌訊息
        """
        timestamp = LoggingUtils.get_timestamp()
        
        if filename and function and line:
            return f"{timestamp} [{level}] - {logger_name} - {filename}:{function}:{line} - {message}"
        else:
            return f"{timestamp} [{level}] - {logger_name} - {message}"
    
    @staticmethod
    def create_unified_logger(name: str):
        """
        建立統一配置的日誌器
        
        Args:
            name: 日誌器名稱
            
        Returns:
            配置好的日誌器
        """
        logger_manager = LoggingUtils.get_logger_manager()
        return logger_manager.get_logger(name)


# 提供便捷的全域函數
def log_success(message: str) -> None:
    """記錄成功訊息"""
    LoggingUtils.print_status("success", message)


def log_warning(message: str) -> None:
    """記錄警告訊息"""
    LoggingUtils.print_status("warning", message)


def log_error(message: str) -> None:
    """記錄錯誤訊息"""
    LoggingUtils.print_status("error", message)


def log_info(message: str) -> None:
    """記錄資訊訊息"""
    LoggingUtils.print_status("info", message)


def log_sync(message: str) -> None:
    """記錄同步訊息"""
    LoggingUtils.print_status("sync", message)


def log_email(message: str) -> None:
    """記錄郵件訊息"""
    LoggingUtils.print_status("email", message)


def get_unified_logger(name: str):
    """取得統一配置的日誌器"""
    return LoggingUtils.create_unified_logger(name)