"""
Suqian（宿遷）廠商解析器實作
基於 VBA SuqianInfoFromStrings 邏輯

VBA 邏輯參考：
- 識別條件：通過產品代碼格式 (G|M|AT)\d{1} 和 Suqian 關鍵字
- 解析規則：使用逗號或頓號分隔，解析格式如：台?致新G2262BTR1U，lot：NTK65.2，??批?：TS23031812.1，??良率：95.51%
- 支援多種分隔符：逗號（,）、中文逗號（，）、空格
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


class SuqianParser(VendorParser):
    """
    Suqian（宿遷）廠商郵件解析器
    
    識別條件：
    - 郵件主旨或內文包含 "suqian" 或 "宿遷" 關鍵字
    - 包含 "台?致新" 或類似的廠商標識
    - 包含符合 (G|M|AT)\d 格式的產品代碼
    - 包含 "lot：" 和 "批?：" 格式
    
    解析機制：
    1. 使用正則表達式 \b(G|M|AT)\d+ 尋找產品代碼
    2. 使用多種分隔符（逗號、中文逗號、空格）分割文本
    3. 尋找 "lot：XXX" 格式的 LOT 編號
    4. 尋找 "批?：XXX" 或 "測試批?：XXX" 格式的 MO 編號
    5. 尋找 "良率：XX%" 格式的良率信息
    """
    
    def __init__(self):
        """初始化 Suqian 解析器"""
        super().__init__()
        self._vendor_code = "SUQIAN"
        self._vendor_name = "Suqian"
        self._identification_patterns = [
            "suqian",         # 英文關鍵字
            "宿遷",           # 中文關鍵字
            "台?致新",        # 廠商標識
            "測試批",         # 常見關鍵字
            "良率"            # 常見關鍵字
        ]
        self.set_confidence_threshold(0.7)
        
        # 初始化 logger
        self.logger = LoggerManager().get_logger("SuqianParser")
        
        # Suqian 特有的模式
        self.product_pattern = r'\b(G|M|AT)\d+'     # 產品代碼：G/M/AT 開頭 + 數字

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject = email_data.subject or ""
        body = email_data.body or ""
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        content = (subject + " " + body).lower()
        
        # 檢查 Suqian 關鍵字
        if "suqian" in content or "宿遷" in content:
            matched_patterns.append("suqian")
            confidence_score += 0.8
            
        # 檢查寄件者是否包含 Suqian
        if "suqian" in sender_lower or "宿遷" in sender_lower:
            if "suqian" not in matched_patterns:
                matched_patterns.append("suqian")
            confidence_score += 0.3
        
        # 檢查台?致新標識
        if "台" in content and "致新" in content:
            matched_patterns.append("台?致新")
            confidence_score += 0.6
            
        # 檢查常見的 Suqian 特徵詞
        if "測試批" in content:
            matched_patterns.append("測試批")
            confidence_score += 0.4
            
        if "良率" in content:
            matched_patterns.append("良率")
            confidence_score += 0.3
            
        # 檢查是否有 lot：格式（Suqian 特有）
        if re.search(r'lot[：:]', subject, re.IGNORECASE):
            confidence_score += 0.5
            matched_patterns.append("lot_colon_pattern")
                
        # 檢查是否有產品代碼模式
        if re.search(self.product_pattern, subject, re.IGNORECASE):
            confidence_score += 0.3
            
        # 檢查是否有批?：格式
        if re.search(r'批[?？][：:]', subject, re.IGNORECASE):
            confidence_score += 0.4
            matched_patterns.append("batch_question_colon_pattern")
                
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="suqian_keyword_pattern_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        try:
            # 基於 VBA SuqianInfoFromStrings 邏輯進行解析
            subject = email_data.subject or ""
            body = email_data.body or ""
            
            # 首先嘗試從主旨解析
            suqian_result = self.parse_suqian_pattern(subject)
            
            # 如果主旨解析不完整，嘗試從郵件內文解析補充信息
            if not suqian_result["product"] or not suqian_result["mo_number"]:
                body_result = self.parse_suqian_pattern(body)
                
                # 合併解析結果
                if body_result["product"] and not suqian_result["product"]:
                    suqian_result["product"] = body_result["product"]
                if body_result["mo_number"] and not suqian_result["mo_number"]:
                    suqian_result["mo_number"] = body_result["mo_number"]
                if body_result["lot_number"] and not suqian_result["lot_number"]:
                    suqian_result["lot_number"] = body_result["lot_number"]
                if body_result["yield_value"] and not suqian_result["yield_value"]:
                    suqian_result["yield_value"] = body_result["yield_value"]
                if body_result["method"] != "no_pattern":
                    suqian_result["method"] = body_result["method"] + "_from_body"
            
            # 提取結果
            product_code = suqian_result["product"] if suqian_result["product"] else None
            mo_number = suqian_result["mo_number"] if suqian_result["mo_number"] else None
            lot_number = suqian_result["lot_number"] if suqian_result["lot_number"] else None
            
            # 檢查是否成功解析
            missing_fields = []
            if not product_code:
                missing_fields.append("產品代碼")
            if not mo_number:
                missing_fields.append("MO編號")

            is_success = len(missing_fields) == 0
            error_message = None

            if not is_success:
                error_message = f"Suqian 傳統解析失敗：缺少 {', '.join(missing_fields)}"
                self.logger.warning(f"Suqian 傳統解析失敗詳情:")
                self.logger.warning(f"  主旨: {email_data.subject}")
                self.logger.warning(f"  解析方法: {suqian_result['method']}")
                self.logger.warning(f"  產品代碼: {product_code or '未找到'}")
                self.logger.warning(f"  MO編號: {mo_number or '未找到'}")
                self.logger.warning(f"  LOT編號: {lot_number or '未找到'}")
                self.logger.warning(f"  良率: {suqian_result.get('yield_value', '未找到')}")
                self.logger.warning(f"  缺少欄位: {', '.join(missing_fields)}")
            else:
                self.logger.info(f"Suqian 傳統解析成功:")
                self.logger.info(f"  產品代碼: {product_code}")
                self.logger.info(f"  MO編號: {mo_number}")
                self.logger.info(f"  LOT編號: {lot_number}")
                self.logger.info(f"  良率: {suqian_result.get('yield_value', 'N/A')}")
                self.logger.info(f"  解析方法: {suqian_result['method']}")

            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                product_code=product_code,
                mo_number=mo_number,
                lot_number=lot_number,
                is_success=is_success,
                error_message=error_message,
                extraction_method="traditional",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': suqian_result["product"],
                    'mo_number': suqian_result["mo_number"],
                    'lot_number': suqian_result["lot_number"],
                    'yield_value': suqian_result.get("yield_value", ""),
                    'parsing_method': suqian_result["method"],
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'suqian_comma_separated_parsing'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                extraction_method="traditional",
                is_success=False,
                error_message=f"Suqian parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_suqian_pattern(self, text: str) -> Dict[str, Any]:
        """
        解析 Suqian 模式：基於 VBA SuqianInfoFromStrings 邏輯
        
        VBA 邏輯：
        1. 使用正則表達式 \b(G|M|AT)\d{1} 尋找產品代碼
        2. 使用多種分隔符分割文本：逗號、中文逗號、空格
        3. 在分割後的段落中尋找包含產品代碼的段落
        4. 使用 ExtractProduct 清理產品代碼
        
        範例：台?致新G2262BTR1U，lot：NTK65.2，??批?：TS23031812.1，??良率：95.51%
        解析為：產品: G2262BTR1U, LOT: NTK65.2, MO: TS23031812.1, 良率: 95.51
        """
        if not text:
            return {
                "product": "",
                "mo_number": "",
                "lot_number": "",
                "yield_value": "",
                "method": "no_pattern"
            }
        
        product = ""
        mo_number = ""
        lot_number = ""
        yield_value = ""
        method = "no_pattern"
        
        # VBA: 使用正則表達式尋找產品代碼模式
        # productString = "\b(G|M|AT)\d{1}"
        product_matches = re.findall(self.product_pattern, text, re.IGNORECASE)
        
        if product_matches:
            # VBA: 使用多種分隔符分割文本
            # words = Split(inputString, ",")
            # If UBound(words) = 0 Then words = Split(inputString, "，")
            # If UBound(words) = 0 Then words = Split(inputString, " ")
            
            # 優先使用逗號分隔
            words = text.split(",")
            if len(words) == 1:
                # 使用中文逗號分隔
                words = text.split("，")
            if len(words) == 1:
                # 使用空格分隔
                words = text.split(" ")
            
            # VBA: 在分割的段落中尋找包含產品代碼的段落
            for word in words:
                for product_match in product_matches:
                    if product_match.upper() in word.upper():
                        # 找到包含產品代碼的段落，提取完整產品代碼
                        # VBA: product = Mid(words(i), productMatch.Item(0).FirstIndex + 1)
                        
                        # 尋找完整的產品代碼（可能包含更多字符和括號）
                        # 處理像 "台?致新G2262BTR1U" 這樣的格式
                        product_pattern_full = r'([GM]\d{4}[A-Z0-9]+(?:\([A-Z]+\))?)'
                        product_match_full = re.search(product_pattern_full, word, re.IGNORECASE)
                        
                        if product_match_full:
                            product = product_match_full.group(1)
                            # VBA: product = ExtractProduct(product)
                            # 清理產品代碼（移除不必要的符號，但保留括號）
                            product = re.sub(r'[^\w()]', '', product)
                            method = "product_from_comma_separated"
                            break
                
                if product:
                    break
            
            # 如果分割後仍找不到，直接在原文中搜尋
            if not product:
                # 直接在整個文本中尋找產品代碼
                direct_pattern = r'([GM]\d{4}[A-Z0-9]+(?:\([A-Z]+\))?)'
                direct_match = re.search(direct_pattern, text, re.IGNORECASE)
                if direct_match:
                    product = direct_match.group(1)
                    method = "direct_product_search"
        
        # 尋找 LOT 編號：lot：XXX
        lot_patterns = [
            r'lot[：:]\s*([A-Z0-9.]+)',   # lot：NTK65.2
            r'LOT[：:]\s*([A-Z0-9.]+)'    # 大寫版本
        ]
        
        for pattern in lot_patterns:
            lot_match = re.search(pattern, text, re.IGNORECASE)
            if lot_match:
                lot_number = lot_match.group(1)
                if method == "no_pattern":
                    method = "lot_pattern_found"
                else:
                    method += "_with_lot"
                break
        
        # 尋找 MO 編號：批?：XXX 或 測試批?：XXX
        mo_patterns = [
            r'測試批[?？][：:]\s*([A-Z0-9.]+)',  # 測試批?：TS23031812.1
            r'批[?？][：:]\s*([A-Z0-9.]+)',       # 批?：TS23031812.1
            r'測試批號[：:]\s*([A-Z0-9.]+)',      # 測試批號：XXX
            r'批號[：:]\s*([A-Z0-9.]+)'           # 批號：XXX
        ]
        
        for pattern in mo_patterns:
            mo_match = re.search(pattern, text, re.IGNORECASE)
            if mo_match:
                mo_number = mo_match.group(1)
                if method == "no_pattern":
                    method = "mo_pattern_found"
                else:
                    method += "_with_mo"
                break
        
        # 尋找良率值：良率：XX%
        yield_patterns = [
            r'良率[：:]?\s*(\d+\.?\d*)%?',      # 良率：95.51%
            r'測試良率[：:]?\s*(\d+\.?\d*)%?',  # 測試良率：95.51%
            r'yield[：:]?\s*(\d+\.?\d*)%?'      # yield：95.51%
        ]
        
        for pattern in yield_patterns:
            yield_match = re.search(pattern, text, re.IGNORECASE)
            if yield_match:
                yield_value = yield_match.group(1)
                break
        
        # 如果沒有找到標準模式，嘗試其他可能的組合
        if not product and not mo_number and not lot_number:
            # 尋找完整的 Suqian 格式模式
            full_pattern = r'([GM]\d{4}[A-Z0-9]+).*?lot[：:]([A-Z0-9.]+).*?批[?？][：:]([A-Z0-9.]+)'
            full_match = re.search(full_pattern, text, re.IGNORECASE)
            
            if full_match:
                product = full_match.group(1)
                lot_number = full_match.group(2)
                mo_number = full_match.group(3)
                method = "full_pattern_regex"
        
        return {
            "product": product,
            "mo_number": mo_number,
            "lot_number": lot_number,
            "yield_value": yield_value,
            "method": method
        }

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_formats': [
                'Comma-separated format (台?致新G2262BTR1U，lot：NTK65.2，??批?：TS23031812.1)',
                'LOT colon format (lot：XXX)',
                'Batch question colon format (批?：XXX)',
                'Yield percentage format (良率：XX%)'
            ],
            'confidence_threshold': self.get_confidence_threshold(),
            'chinese_support': True,
            'based_on': 'VBA SuqianInfoFromStrings',
            'extraction_capabilities': [
                'Product code extraction using (G|M|AT)\\d+ pattern',
                'Multi-separator text splitting (comma, Chinese comma, space)',
                'LOT extraction from lot: format',
                'MO extraction from 批?: format',
                'Yield percentage extraction'
            ],
            'special_features': [
                'Multi-separator support (,，space)',
                'Taiwan vendor identifier recognition (台?致新)',
                'Question mark batch format (批?：)',
                'Comprehensive yield format support'
            ]
        }