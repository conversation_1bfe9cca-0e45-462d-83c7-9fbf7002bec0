# 多進程架構設置指南

## 📋 概覽

本文檔提供將現有 `ThreadPoolExecutor` 替換為 `ProcessPoolExecutor` 的完整實施指南，專注於 CPU 密集型任務的進程池配置、進程間通訊和負載均衡策略。

## 🎯 核心目標

- **真正並行處理**：利用多 CPU 核心實現真正的並行計算
- **GIL 問題解決**：繞過 Python GIL 限制，提升 CPU 密集型任務性能
- **資源優化**：更有效的 CPU 和記憶體資源利用
- **穩定性提升**：進程隔離提供更好的錯誤恢復能力

## 🏗️ 現有線程池分析

### 當前 ThreadPoolExecutor 使用情況

```python
# 現有代碼中的線程池使用模式
CURRENT_THREAD_USAGE = {
    "email_processing": {
        "file": "src/application/services/unified_email_processor.py",
        "usage": "CPU密集型Excel處理",
        "workers": "動態配置",
        "bottleneck": "GIL限制"
    },
    "file_operations": {
        "file": "src/infrastructure/adapters/attachments/attachment_manager.py", 
        "usage": "檔案I/O和壓縮解壓",
        "workers": "固定數量",
        "bottleneck": "線程切換開銷"
    },
    "data_processing": {
        "files": ["EQC處理器", "CTA處理器"],
        "usage": "Excel數據處理和轉換",
        "workers": "與CPU核心數相關",
        "bottleneck": "記憶體共享衝突"
    }
}
```

## 🔄 ProcessPoolExecutor 實施策略

### 1. 核心進程池管理器

```python
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor
import asyncio
import os
import pickle
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

class TaskType(Enum):
    """任務類型分類"""
    CPU_INTENSIVE = "cpu_intensive"    # Excel處理、數據計算
    IO_INTENSIVE = "io_intensive"      # 檔案操作、網路請求  
    MIXED = "mixed"                    # 混合型任務

@dataclass
class ProcessPoolConfig:
    """進程池配置"""
    task_type: TaskType
    max_workers: int
    max_tasks_per_child: int = 100  # 防止記憶體洩漏
    initializer: Optional[Callable] = None
    initargs: tuple = ()
    
class UnifiedProcessPoolManager:
    """統一進程池管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.pools: Dict[TaskType, ProcessPoolExecutor] = {}
        self.pool_configs = self._create_default_configs()
        self._initialize_pools()
    
    def _create_default_configs(self) -> Dict[TaskType, ProcessPoolConfig]:
        """創建預設進程池配置"""
        cpu_count = mp.cpu_count()
        
        return {
            TaskType.CPU_INTENSIVE: ProcessPoolConfig(
                task_type=TaskType.CPU_INTENSIVE,
                max_workers=cpu_count,  # CPU密集型使用全部核心
                max_tasks_per_child=50,
                initializer=self._init_cpu_worker,
                initargs=(logging.getLogger().level,)
            ),
            TaskType.IO_INTENSIVE: ProcessPoolConfig(
                task_type=TaskType.IO_INTENSIVE, 
                max_workers=min(cpu_count * 2, 16),  # I/O密集型可以超額配置
                max_tasks_per_child=200,
                initializer=self._init_io_worker,
                initargs=(logging.getLogger().level,)
            ),
            TaskType.MIXED: ProcessPoolConfig(
                task_type=TaskType.MIXED,
                max_workers=max(cpu_count // 2, 1),  # 混合型保守配置
                max_tasks_per_child=100,
                initializer=self._init_mixed_worker,
                initargs=(logging.getLogger().level,)
            )
        }
    
    def _initialize_pools(self):
        """初始化所有進程池"""
        for task_type, config in self.pool_configs.items():
            self.pools[task_type] = ProcessPoolExecutor(
                max_workers=config.max_workers,
                max_tasks_per_child=config.max_tasks_per_child,
                initializer=config.initializer,
                initargs=config.initargs
            )
            self.logger.info(f"初始化 {task_type.value} 進程池: {config.max_workers} 工作者")
    
    @staticmethod
    def _init_cpu_worker(log_level):
        """初始化CPU密集型工作者進程"""
        logging.basicConfig(level=log_level)
        # 設定CPU密集型優化
        os.nice(0)  # 正常優先級
        
        # 導入CPU密集型任務需要的模組
        import pandas as pd
        import numpy as np
        
        # 預熱pandas和numpy (避免首次使用的初始化開銷)
        _ = pd.DataFrame({'test': [1, 2, 3]})
        _ = np.array([1, 2, 3])
    
    @staticmethod
    def _init_io_worker(log_level):
        """初始化I/O密集型工作者進程"""
        logging.basicConfig(level=log_level)
        # 設定I/O密集型優化
        os.nice(-5)  # 稍微提高優先級
        
        # 導入I/O任務需要的模組
        import aiofiles
        import aiohttp
    
    @staticmethod  
    def _init_mixed_worker(log_level):
        """初始化混合型工作者進程"""
        logging.basicConfig(level=log_level)
        # 平衡配置
        os.nice(0)
    
    async def submit_task(
        self, 
        task_type: TaskType, 
        func: Callable, 
        *args, 
        **kwargs
    ) -> Any:
        """提交任務到對應的進程池"""
        if task_type not in self.pools:
            raise ValueError(f"不支援的任務類型: {task_type}")
        
        pool = self.pools[task_type]
        loop = asyncio.get_event_loop()
        
        try:
            # 將同步函數在進程池中執行
            result = await loop.run_in_executor(pool, func, *args, **kwargs)
            return result
        except Exception as e:
            self.logger.error(f"進程池任務執行失敗: {e}")
            raise
    
    async def submit_batch_tasks(
        self,
        task_type: TaskType,
        tasks: List[Dict[str, Any]]
    ) -> List[Any]:
        """批量提交任務"""
        if not tasks:
            return []
        
        pool = self.pools[task_type]
        loop = asyncio.get_event_loop()
        
        # 創建所有任務的Future
        futures = []
        for task in tasks:
            future = loop.run_in_executor(
                pool,
                task['func'],
                *task.get('args', []),
                **task.get('kwargs', {})
            )
            futures.append(future)
        
        # 等待所有任務完成
        try:
            results = await asyncio.gather(*futures, return_exceptions=True)
            return results
        except Exception as e:
            self.logger.error(f"批量任務執行失敗: {e}")
            raise
    
    def get_pool_status(self) -> Dict[str, Dict[str, Any]]:
        """獲取所有進程池狀態"""
        status = {}
        for task_type, pool in self.pools.items():
            # 注意：ProcessPoolExecutor 沒有直接的狀態屬性
            # 這裡提供配置資訊
            config = self.pool_configs[task_type]
            status[task_type.value] = {
                'max_workers': config.max_workers,
                'max_tasks_per_child': config.max_tasks_per_child,
                'is_shutdown': pool._shutdown
            }
        return status
    
    def shutdown_all(self, wait: bool = True):
        """關閉所有進程池"""
        for task_type, pool in self.pools.items():
            self.logger.info(f"關閉 {task_type.value} 進程池")
            pool.shutdown(wait=wait)
        self.pools.clear()
    
    def __del__(self):
        """析構函數，確保資源清理"""
        self.shutdown_all(wait=False)
```

### 2. Excel 處理異步化

```python
import pandas as pd
import openpyxl
from pathlib import Path
import pickle
import tempfile

# 可序列化的Excel處理函數 (在進程池中執行)

def process_excel_data_multiprocess(file_path: str, processing_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    在進程池中處理Excel數據
    
    Args:
        file_path: Excel檔案路徑
        processing_config: 處理配置
        
    Returns:
        處理結果字典
    """
    try:
        # 讀取Excel檔案
        if processing_config.get('engine') == 'openpyxl':
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            sheet_names = workbook.sheetnames
            
            results = {}
            for sheet_name in sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
                
                # 應用處理邏輯
                if 'transformations' in processing_config:
                    for transform in processing_config['transformations']:
                        df = apply_transformation(df, transform)
                
                results[sheet_name] = {
                    'data': df.to_dict('records'),
                    'shape': df.shape,
                    'columns': list(df.columns)
                }
            
            return {
                'success': True,
                'results': results,
                'file_path': file_path,
                'sheet_count': len(sheet_names)
            }
        else:
            # 使用pandas預設引擎
            df = pd.read_excel(file_path, **processing_config.get('read_kwargs', {}))
            
            # 應用轉換
            if 'transformations' in processing_config:
                for transform in processing_config['transformations']:
                    df = apply_transformation(df, transform)
            
            return {
                'success': True,
                'data': df.to_dict('records'),
                'shape': df.shape,
                'columns': list(df.columns),
                'file_path': file_path
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'file_path': file_path
        }

def apply_transformation(df: pd.DataFrame, transform: Dict[str, Any]) -> pd.DataFrame:
    """應用數據轉換"""
    transform_type = transform.get('type')
    
    if transform_type == 'filter':
        # 數據過濾
        condition = transform['condition']
        return df.query(condition)
    
    elif transform_type == 'group_aggregate':
        # 分組聚合
        group_by = transform['group_by']
        agg_funcs = transform['agg_funcs']
        return df.groupby(group_by).agg(agg_funcs).reset_index()
    
    elif transform_type == 'column_rename':
        # 列重命名
        rename_map = transform['rename_map']
        return df.rename(columns=rename_map)
    
    elif transform_type == 'add_calculated_column':
        # 添加計算列
        column_name = transform['column_name']
        formula = transform['formula']
        df[column_name] = df.eval(formula)
        return df
    
    else:
        return df

def batch_excel_processing_multiprocess(
    file_paths: List[str], 
    processing_configs: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    批量Excel處理 (在進程池中執行)
    
    Args:
        file_paths: Excel檔案路徑列表
        processing_configs: 處理配置列表
        
    Returns:
        處理結果列表
    """
    results = []
    for file_path, config in zip(file_paths, processing_configs):
        result = process_excel_data_multiprocess(file_path, config)
        results.append(result)
    return results

# 異步Excel處理服務
class AsyncExcelProcessingService:
    """異步Excel處理服務"""
    
    def __init__(self, process_manager: UnifiedProcessPoolManager):
        self.process_manager = process_manager
        self.logger = logging.getLogger(__name__)
    
    async def process_excel_file_async(
        self, 
        file_path: str, 
        processing_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """異步處理單個Excel檔案"""
        self.logger.info(f"開始異步處理Excel檔案: {file_path}")
        
        result = await self.process_manager.submit_task(
            TaskType.CPU_INTENSIVE,
            process_excel_data_multiprocess,
            file_path,
            processing_config
        )
        
        self.logger.info(f"Excel檔案處理完成: {result.get('success', False)}")
        return result
    
    async def process_excel_batch_async(
        self,
        file_paths: List[str],
        processing_configs: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """異步批量處理Excel檔案"""
        self.logger.info(f"開始批量處理 {len(file_paths)} 個Excel檔案")
        
        # 將大批量分解為小批量以優化記憶體使用
        batch_size = min(self.process_manager.pool_configs[TaskType.CPU_INTENSIVE].max_workers, 10)
        results = []
        
        for i in range(0, len(file_paths), batch_size):
            batch_files = file_paths[i:i+batch_size]
            batch_configs = processing_configs[i:i+batch_size]
            
            batch_tasks = []
            for file_path, config in zip(batch_files, batch_configs):
                task = {
                    'func': process_excel_data_multiprocess,
                    'args': [file_path, config]
                }
                batch_tasks.append(task)
            
            batch_results = await self.process_manager.submit_batch_tasks(
                TaskType.CPU_INTENSIVE,
                batch_tasks
            )
            results.extend(batch_results)
        
        success_count = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
        self.logger.info(f"批量處理完成: {success_count}/{len(results)} 成功")
        
        return results
```

### 3. 進程間通訊優化

```python
import multiprocessing as mp
import queue
import json
import time
from typing import Any, Dict, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class MessageType(Enum):
    """訊息類型"""
    TASK = "task"
    RESULT = "result"
    STATUS = "status"
    ERROR = "error"
    SHUTDOWN = "shutdown"

@dataclass
class ProcessMessage:
    """進程間訊息"""
    id: str
    type: MessageType
    data: Any
    timestamp: float
    source_process: Optional[str] = None
    target_process: Optional[str] = None

class InterProcessCommunicator:
    """進程間通訊管理器"""
    
    def __init__(self, max_queue_size: int = 1000):
        self.logger = logging.getLogger(__name__)
        self.queues: Dict[str, mp.Queue] = {}
        self.max_queue_size = max_queue_size
        self._shared_state = mp.Manager().dict()
    
    def create_queue(self, queue_name: str) -> mp.Queue:
        """創建進程隊列"""
        if queue_name not in self.queues:
            self.queues[queue_name] = mp.Queue(maxsize=self.max_queue_size)
            self.logger.info(f"創建進程隊列: {queue_name}")
        return self.queues[queue_name]
    
    def send_message(
        self, 
        queue_name: str, 
        message: ProcessMessage,
        timeout: Optional[float] = None
    ) -> bool:
        """發送訊息到指定隊列"""
        if queue_name not in self.queues:
            self.logger.error(f"隊列不存在: {queue_name}")
            return False
        
        try:
            # 序列化訊息
            serialized_message = self._serialize_message(message)
            
            if timeout:
                self.queues[queue_name].put(serialized_message, timeout=timeout)
            else:
                self.queues[queue_name].put_nowait(serialized_message)
            
            return True
        except queue.Full:
            self.logger.warning(f"隊列已滿: {queue_name}")
            return False
        except Exception as e:
            self.logger.error(f"發送訊息失敗: {e}")
            return False
    
    def receive_message(
        self, 
        queue_name: str, 
        timeout: Optional[float] = None
    ) -> Optional[ProcessMessage]:
        """從指定隊列接收訊息"""
        if queue_name not in self.queues:
            self.logger.error(f"隊列不存在: {queue_name}")
            return None
        
        try:
            if timeout:
                serialized_message = self.queues[queue_name].get(timeout=timeout)
            else:
                serialized_message = self.queues[queue_name].get_nowait()
            
            # 反序列化訊息
            return self._deserialize_message(serialized_message)
            
        except queue.Empty:
            return None
        except Exception as e:
            self.logger.error(f"接收訊息失敗: {e}")
            return None
    
    def _serialize_message(self, message: ProcessMessage) -> bytes:
        """序列化訊息"""
        try:
            message_dict = asdict(message)
            # 處理Enum類型
            message_dict['type'] = message.type.value
            return pickle.dumps(message_dict)
        except Exception as e:
            self.logger.error(f"序列化訊息失敗: {e}")
            raise
    
    def _deserialize_message(self, data: bytes) -> ProcessMessage:
        """反序列化訊息"""
        try:
            message_dict = pickle.loads(data)
            # 重建Enum類型
            message_dict['type'] = MessageType(message_dict['type'])
            return ProcessMessage(**message_dict)
        except Exception as e:
            self.logger.error(f"反序列化訊息失敗: {e}")
            raise
    
    def set_shared_value(self, key: str, value: Any):
        """設定共享狀態值"""
        self._shared_state[key] = value
    
    def get_shared_value(self, key: str, default: Any = None) -> Any:
        """獲取共享狀態值"""
        return self._shared_state.get(key, default)
    
    def get_queue_status(self) -> Dict[str, Dict[str, Any]]:
        """獲取所有隊列狀態"""
        status = {}
        for name, q in self.queues.items():
            status[name] = {
                'size': q.qsize() if hasattr(q, 'qsize') else 'unknown',
                'full': q.full() if hasattr(q, 'full') else 'unknown',
                'empty': q.empty() if hasattr(q, 'empty') else 'unknown'
            }
        return status
    
    def cleanup(self):
        """清理資源"""
        for name, q in self.queues.items():
            try:
                while not q.empty():
                    q.get_nowait()
                q.close()
                q.join_thread()
            except:
                pass
        self.queues.clear()
```

### 4. 負載均衡和監控

```python
import psutil
import time
from typing import Dict, List
from collections import deque, defaultdict
from dataclasses import dataclass

@dataclass
class ProcessMetrics:
    """進程指標"""
    pid: int
    cpu_percent: float
    memory_mb: float
    start_time: float
    task_count: int
    error_count: int

class ProcessLoadBalancer:
    """進程負載均衡器"""
    
    def __init__(self, process_manager: UnifiedProcessPoolManager):
        self.process_manager = process_manager
        self.logger = logging.getLogger(__name__)
        self.metrics_history = defaultdict(lambda: deque(maxlen=100))
        self.task_distribution = defaultdict(int)
        
    def get_optimal_task_type(
        self, 
        task_characteristics: Dict[str, Any]
    ) -> TaskType:
        """根據任務特徵選擇最優的任務類型"""
        cpu_intensive_score = 0
        io_intensive_score = 0
        
        # 分析任務特徵
        if task_characteristics.get('has_pandas_operations', False):
            cpu_intensive_score += 3
        
        if task_characteristics.get('has_numpy_operations', False):
            cpu_intensive_score += 3
            
        if task_characteristics.get('has_file_operations', False):
            io_intensive_score += 2
            
        if task_characteristics.get('has_network_operations', False):
            io_intensive_score += 3
            
        if task_characteristics.get('estimated_duration_seconds', 0) > 30:
            cpu_intensive_score += 1
            
        # 檢查當前負載
        current_load = self._get_current_load()
        
        if cpu_intensive_score > io_intensive_score:
            if current_load[TaskType.CPU_INTENSIVE] < 0.8:
                return TaskType.CPU_INTENSIVE
            else:
                return TaskType.MIXED
        elif io_intensive_score > cpu_intensive_score:
            if current_load[TaskType.IO_INTENSIVE] < 0.8:  
                return TaskType.IO_INTENSIVE
            else:
                return TaskType.MIXED
        else:
            # 選擇負載最低的
            min_load_type = min(current_load.keys(), key=lambda x: current_load[x])
            return min_load_type
    
    def _get_current_load(self) -> Dict[TaskType, float]:
        """獲取當前負載情況"""
        load = {}
        
        for task_type in TaskType:
            # 簡化的負載計算 (實際情況可能需要更複雜的算法)
            recent_tasks = self.task_distribution.get(task_type, 0)
            max_workers = self.process_manager.pool_configs[task_type].max_workers
            load[task_type] = min(recent_tasks / max_workers, 1.0)
        
        return load
    
    async def distribute_tasks_optimally(
        self,
        tasks: List[Dict[str, Any]]
    ) -> Dict[TaskType, List[Dict[str, Any]]]:
        """最優化任務分配"""
        distribution = defaultdict(list)
        
        for task in tasks:
            characteristics = task.get('characteristics', {})
            optimal_type = self.get_optimal_task_type(characteristics)
            distribution[optimal_type].append(task)
            self.task_distribution[optimal_type] += 1
        
        self.logger.info(f"任務分配結果: {dict([(k.value, len(v)) for k, v in distribution.items()])}")
        return dict(distribution)

class ProcessMonitor:
    """進程監控器"""
    
    def __init__(self, process_manager: UnifiedProcessPoolManager):
        self.process_manager = process_manager
        self.logger = logging.getLogger(__name__)
        self.monitoring_active = False
        self.metrics_collection = defaultdict(list)
    
    async def start_monitoring(self, interval: float = 5.0):
        """開始監控"""
        self.monitoring_active = True
        self.logger.info("開始進程監控")
        
        while self.monitoring_active:
            try:
                metrics = await self.collect_metrics()
                self._store_metrics(metrics)
                self._analyze_performance(metrics)
                await asyncio.sleep(interval)
            except Exception as e:
                self.logger.error(f"監控過程中發生錯誤: {e}")
                await asyncio.sleep(interval)
    
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring_active = False
        self.logger.info("停止進程監控")
    
    async def collect_metrics(self) -> Dict[str, Any]:
        """收集系統指標"""
        system_metrics = {
            'timestamp': time.time(),
            'system_cpu_percent': psutil.cpu_percent(interval=1),
            'system_memory_percent': psutil.virtual_memory().percent,
            'process_pools': {}
        }
        
        # 收集進程池狀態
        pool_status = self.process_manager.get_pool_status()
        system_metrics['process_pools'] = pool_status
        
        # 收集當前Python進程的資源使用
        current_process = psutil.Process()
        system_metrics['main_process'] = {
            'cpu_percent': current_process.cpu_percent(),
            'memory_mb': current_process.memory_info().rss / 1024 / 1024,
            'open_files_count': len(current_process.open_files()),
            'threads_count': current_process.num_threads()
        }
        
        return system_metrics
    
    def _store_metrics(self, metrics: Dict[str, Any]):
        """存儲指標數據"""
        timestamp = metrics['timestamp']
        
        # 存儲到內存中 (實際應用中可能需要持久化到資料庫)
        self.metrics_collection['timestamps'].append(timestamp)
        self.metrics_collection['system_cpu'].append(metrics['system_cpu_percent'])
        self.metrics_collection['system_memory'].append(metrics['system_memory_percent'])
        self.metrics_collection['main_process_cpu'].append(metrics['main_process']['cpu_percent'])
        self.metrics_collection['main_process_memory'].append(metrics['main_process']['memory_mb'])
        
        # 保持最近100個數據點
        for key in self.metrics_collection:
            if len(self.metrics_collection[key]) > 100:
                self.metrics_collection[key] = self.metrics_collection[key][-100:]
    
    def _analyze_performance(self, metrics: Dict[str, Any]):
        """分析性能指標"""
        # 檢查系統資源使用是否過高
        if metrics['system_cpu_percent'] > 90:
            self.logger.warning(f"系統CPU使用率過高: {metrics['system_cpu_percent']:.1f}%")
        
        if metrics['system_memory_percent'] > 90:
            self.logger.warning(f"系統記憶體使用率過高: {metrics['system_memory_percent']:.1f}%")
        
        # 檢查主進程資源使用
        main_process = metrics['main_process']
        if main_process['memory_mb'] > 1000:  # 1GB
            self.logger.warning(f"主進程記憶體使用過高: {main_process['memory_mb']:.1f}MB")
        
        if main_process['open_files_count'] > 500:
            self.logger.warning(f"打開檔案數量過多: {main_process['open_files_count']}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能報告"""
        if not self.metrics_collection['timestamps']:
            return {'error': '沒有可用的指標資料'}
        
        # 計算平均值和趨勢
        recent_cpu = self.metrics_collection['system_cpu'][-10:] if len(self.metrics_collection['system_cpu']) >= 10 else self.metrics_collection['system_cpu']
        recent_memory = self.metrics_collection['system_memory'][-10:] if len(self.metrics_collection['system_memory']) >= 10 else self.metrics_collection['system_memory']
        
        return {
            'monitoring_duration_minutes': (time.time() - self.metrics_collection['timestamps'][0]) / 60,
            'data_points': len(self.metrics_collection['timestamps']),
            'avg_system_cpu_percent': sum(recent_cpu) / len(recent_cpu),
            'avg_system_memory_percent': sum(recent_memory) / len(recent_memory),
            'max_system_cpu_percent': max(self.metrics_collection['system_cpu']),
            'max_system_memory_percent': max(self.metrics_collection['system_memory']),
            'current_main_process_memory_mb': self.metrics_collection['main_process_memory'][-1] if self.metrics_collection['main_process_memory'] else 0
        }
```

### 5. 實際業務整合範例

```python
# 整合到現有的UnifiedEmailProcessor
class EnhancedUnifiedEmailProcessor:
    """增強的統一郵件處理器 (支援多進程)"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("EnhancedUnifiedEmailProcessor")
        
        # 初始化多進程管理器
        self.process_manager = UnifiedProcessPoolManager()
        self.load_balancer = ProcessLoadBalancer(self.process_manager)
        self.monitor = ProcessMonitor(self.process_manager)
        
        # 初始化Excel處理服務
        self.excel_service = AsyncExcelProcessingService(self.process_manager)
        
        # 其他初始化...
        self.parser_factory = ParserFactory()
        self.line_service = LineNotificationService()
        self.database = EmailDatabase()
        
        # 開始監控
        asyncio.create_task(self.monitor.start_monitoring())
    
    async def process_excel_attachments_multiprocess(
        self,
        email_data: EmailData,
        parsing_result: EmailParsingResult
    ) -> Dict[str, Any]:
        """使用多進程處理Excel附件"""
        excel_attachments = [
            att for att in email_data.attachments 
            if att.filename.lower().endswith(('.xlsx', '.xls'))
        ]
        
        if not excel_attachments:
            return {'success': True, 'message': '沒有Excel附件需要處理'}
        
        self.logger.info(f"發現 {len(excel_attachments)} 個Excel附件，使用多進程處理")
        
        # 準備處理配置
        processing_configs = []
        file_paths = []
        
        for attachment in excel_attachments:
            # 保存附件到臨時位置
            temp_path = f"/tmp/{attachment.filename}"
            with open(temp_path, 'wb') as f:
                f.write(attachment.content)
            
            file_paths.append(temp_path)
            
            # 根據廠商和檔案類型設定處理配置
            config = self._create_processing_config(
                attachment.filename,
                parsing_result.vendor_code
            )
            processing_configs.append(config)
        
        try:
            # 使用多進程批量處理
            results = await self.excel_service.process_excel_batch_async(
                file_paths, 
                processing_configs
            )
            
            # 處理結果
            success_count = sum(1 for r in results if r.get('success', False))
            
            return {
                'success': success_count > 0,
                'total_files': len(excel_attachments),
                'success_count': success_count,
                'failed_count': len(results) - success_count,
                'results': results
            }
            
        finally:
            # 清理臨時檔案
            for temp_path in file_paths:
                try:
                    os.remove(temp_path)
                except:
                    pass
    
    def _create_processing_config(
        self, 
        filename: str, 
        vendor_code: Optional[str]
    ) -> Dict[str, Any]:
        """根據檔案和廠商創建處理配置"""
        base_config = {
            'engine': 'openpyxl',
            'transformations': []
        }
        
        # 根據廠商特定配置
        if vendor_code == 'TSMC':
            base_config['transformations'].extend([
                {
                    'type': 'filter',
                    'condition': 'Status == "PASS"'
                },
                {
                    'type': 'add_calculated_column',
                    'column_name': 'yield_rate',
                    'formula': 'Pass_Count / Total_Count * 100'
                }
            ])
        elif vendor_code == 'UMC':
            base_config['transformations'].extend([
                {
                    'type': 'column_rename',
                    'rename_map': {'LOT_ID': 'lot_number', 'PD_ID': 'product_code'}
                }
            ])
        
        # 根據檔案名特定配置
        if 'summary' in filename.lower():
            base_config['transformations'].append({
                'type': 'group_aggregate',
                'group_by': ['product_code'],
                'agg_funcs': {'yield_value': 'mean', 'lot_count': 'count'}
            })
        
        return base_config
    
    async def cleanup(self):
        """清理資源"""
        self.monitor.stop_monitoring()
        self.process_manager.shutdown_all()
```

## 🧪 測試和驗證

### 1. 多進程性能測試

```python
import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
import tempfile
import pandas as pd

class TestMultiprocessing:
    """多進程實施測試"""
    
    @pytest.mark.asyncio
    async def test_process_pool_performance(self):
        """比較線程池vs進程池性能"""
        # 創建測試數據
        test_data = self._create_test_excel_files(10)
        
        # 測試線程池性能
        thread_start = time.time()
        with ThreadPoolExecutor(max_workers=4) as executor:
            thread_futures = [
                executor.submit(self._cpu_intensive_task, data)
                for data in test_data
            ]
            thread_results = [f.result() for f in thread_futures]
        thread_duration = time.time() - thread_start
        
        # 測試進程池性能
        process_manager = UnifiedProcessPoolManager()
        process_start = time.time()
        
        tasks = []
        for data in test_data:
            task = process_manager.submit_task(
                TaskType.CPU_INTENSIVE,
                self._cpu_intensive_task,
                data
            )
            tasks.append(task)
        
        process_results = await asyncio.gather(*tasks)
        process_duration = time.time() - process_start
        
        process_manager.shutdown_all()
        
        # 驗證結果正確性
        assert len(thread_results) == len(process_results)
        
        # 性能分析
        improvement = (thread_duration - process_duration) / thread_duration * 100
        print(f"線程池耗時: {thread_duration:.2f}s")
        print(f"進程池耗時: {process_duration:.2f}s") 
        print(f"性能提升: {improvement:.1f}%")
        
        # 對於CPU密集型任務，進程池應該更快
        assert process_duration < thread_duration
    
    def _create_test_excel_files(self, count: int) -> List[str]:
        """創建測試Excel檔案"""
        files = []
        for i in range(count):
            # 創建大型DataFrame
            df = pd.DataFrame({
                'A': range(10000),
                'B': range(10000, 20000),
                'C': [f'Value_{j}' for j in range(10000)]
            })
            
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            df.to_excel(temp_file.name, index=False)
            files.append(temp_file.name)
        
        return files
    
    def _cpu_intensive_task(self, file_path: str) -> Dict[str, Any]:
        """CPU密集型任務模擬"""
        df = pd.read_excel(file_path)
        
        # 執行複雜計算
        result = {
            'sum_A': df['A'].sum(),
            'mean_B': df['B'].mean(),
            'unique_C': df['C'].nunique(),
            'correlation': df['A'].corr(df['B'])
        }
        
        # 清理臨時檔案
        os.unlink(file_path)
        
        return result
```

## 📊 性能優化建議

### 1. 記憶體管理優化

```python
# 進程池記憶體優化配置
MEMORY_OPTIMIZATION_CONFIG = {
    "max_tasks_per_child": 50,  # 防止記憶體洩漏
    "gc_threshold": (700, 10, 10),  # 垃圾回收閾值
    "memory_limit_mb": 512,  # 單進程記憶體限制
}

def optimize_process_memory():
    """優化進程記憶體使用"""
    import gc
    import resource
    
    # 設定記憶體限制
    resource.setrlimit(
        resource.RLIMIT_AS, 
        (512 * 1024 * 1024, 512 * 1024 * 1024)  # 512MB
    )
    
    # 調整垃圾回收
    gc.set_threshold(700, 10, 10)
    
    # 啟用垃圾回收調試
    gc.set_debug(gc.DEBUG_LEAK)
```

### 2. CPU 親和性設定

```python
import os
import psutil

def set_cpu_affinity(process_type: TaskType):
    """設定CPU親和性"""
    cpu_count = psutil.cpu_count()
    
    if process_type == TaskType.CPU_INTENSIVE:
        # CPU密集型使用高性能核心
        if cpu_count >= 8:
            os.sched_setaffinity(0, list(range(cpu_count // 2, cpu_count)))
    elif process_type == TaskType.IO_INTENSIVE:
        # I/O密集型使用節能核心  
        if cpu_count >= 8:
            os.sched_setaffinity(0, list(range(0, cpu_count // 2)))
```

## 🎯 實施檢查清單

### ✅ 準備階段
- [ ] 分析現有ThreadPoolExecutor使用情況
- [ ] 識別CPU密集型vs I/O密集型任務
- [ ] 創建進程池管理器
- [ ] 設計進程間通訊機制

### ✅ 實施階段
- [ ] 實施UnifiedProcessPoolManager
- [ ] 改寫Excel處理為可序列化函數
- [ ] 實施負載均衡器
- [ ] 實施進程監控系統
- [ ] 整合到現有業務邏輯

### ✅ 測試階段
- [ ] 性能基準測試
- [ ] 記憶體使用測試
- [ ] 穩定性測試
- [ ] 錯誤處理測試
- [ ] 並發測試

### ✅ 監控階段
- [ ] 部署監控系統
- [ ] 設定性能報警
- [ ] 記錄性能指標
- [ ] 定期性能分析
- [ ] 持續優化

## 📈 預期改進效果

### CPU密集型任務改進
- **Excel處理速度**: 提升 200-400%
- **數據計算效能**: 提升 150-300%
- **並行處理能力**: 提升 300-500%

### 系統資源優化
- **GIL限制**: 完全消除
- **CPU利用率**: 提升至 80-95%
- **記憶體效率**: 提升 20-40%
- **系統穩定性**: 進程隔離提供更好的容錯

---

*本指南提供了完整的多進程架構升級路徑，確保系統能夠充分利用多核CPU資源，顯著提升處理性能。*