# 專案結構與組織

## 架構概述

本專案採用**六角架構**（Ports and Adapters），在六個主要層級間有清晰的關注點分離。

## 原始碼結構 (`src/`)

### 核心架構層級

```
src/
├── domain/                 # 核心業務邏輯（內層）
│   ├── entities/          # 業務實體
│   ├── value_objects/     # 不可變值物件
│   ├── services/          # 領域服務
│   └── exceptions/        # 領域特定例外
│
├── application/           # 應用層
│   ├── use_cases/         # 業務使用案例
│   └── interfaces/        # 埠定義
│
├── infrastructure/        # 基礎設施層（外層）
│   ├── adapters/          # 外部系統適配器
│   ├── database/          # 資料庫實作
│   ├── parsers/           # 廠商特定解析器
│   ├── config/            # 配置管理
│   ├── logging/           # 日誌基礎設施
│   ├── llm/               # LLM 整合
│   └── auth/              # 身份驗證
│
├── presentation/          # 展示層
│   ├── api/               # REST API 端點
│   ├── web/               # 網頁介面
│   └── cli/               # 命令列介面
│
├── services/              # 應用服務
├── data_models/           # 資料傳輸物件
└── utils/                 # 共用工具
```

### 關鍵架構原則

1. **依賴方向**：依賴指向領域內部
2. **埠/適配器模式**：基礎設施適配領域介面
3. **單一職責**：每層都有清晰、專注的目的
4. **可測試性**：容易模擬外部依賴

## 廠商解析器結構

```
src/infrastructure/parsers/
├── base_parser.py         # 抽象基礎解析器
├── gtk_parser.py          # GTK 廠商解析器
├── etd_parser.py          # ETD 廠商解析器
├── xaht_parser.py         # XAHT 廠商解析器
├── jcet_parser.py         # JCET 廠商解析器
└── lingsen_parser.py      # LINGSEN 廠商解析器
```

## 測試結構 (`tests/`)

```
tests/
├── unit/                  # 單元測試（隔離元件）
├── integration/           # 整合測試（多個元件）
├── e2e/                   # 端對端測試（完整工作流程）
├── performance/           # 效能與負載測試
└── fixtures/              # 測試資料與固定裝置
```

## 配置與環境

```
├── .env                   # 環境變數（不在 git 中）
├── .env.example           # 環境範本
├── .env.production        # 生產環境
├── pyproject.toml         # Python 專案配置
├── requirements.txt       # Python 依賴
└── Makefile              # 開發指令
```

## 文件結構

```
├── README.md              # 專案概述與快速開始
├── CLAUDE.md              # AI 開發指導原則
├── documentation/         # 技術文件
├── code_documentation/    # 自動生成程式碼文件
└── reports/               # 進度與分析報告
```

## 資料與日誌

```
├── data/                  # 應用程式資料
│   └── email_inbox.db     # SQLite 資料庫
├── logs/                  # 應用程式日誌
├── tmp/                   # 暫存檔案
└── attachments/           # 郵件附件
```

## Docker 與部署

```
├── docker/                # Docker 配置
│   ├── grafana/          # 監控儀表板
│   ├── prometheus/       # 指標收集
│   ├── postgres/         # 資料庫
│   └── nginx/            # 反向代理
└── docker-compose.yml    # 服務編排
```

## 開發工具

```
├── .vscode/              # VS Code 設定
├── .pytest_cache/        # Pytest 快取
├── htmlcov/              # 覆蓋率報告
├── .mypy_cache/          # MyPy 快取
└── __pycache__/          # Python 位元組碼
```

## 命名慣例

### 檔案與目錄
- **Snake_case**：所有 Python 檔案與目錄
- **描述性名稱**：清楚表明目的
- **一致的後綴**：`_parser.py`、`_service.py`、`_test.py`

### 程式碼結構
- **類別**：PascalCase（`EmailProcessor`）
- **函數/變數**：snake_case（`process_email`）
- **常數**：UPPER_SNAKE_CASE（`MAX_RETRY_COUNT`）
- **私有成員**：前導底線（`_internal_method`）

## 匯入組織

```python
# 標準函式庫匯入
import os
from typing import List, Optional

# 第三方函式庫匯入
import pandas as pd
from fastapi import FastAPI

# 本地應用程式匯入
from src.domain.entities import Email
from src.infrastructure.parsers import BaseParser
```

## 檔案組織規則

1. **單一職責**：每個檔案一個類別（小型相關類別除外）
2. **清晰階層**：按功能邏輯分組
3. **最小依賴**：避免循環匯入
4. **介面隔離**：小型、專注的介面
5. **依賴注入**：基於建構子的依賴注入

## 特殊目錄

- **`.kiro/`**：Kiro IDE 配置與導向規則
- **`.roo/`**：AI 助理規則與配置
- **`.taskmaster/`**：任務管理與自動化
- **`.serena/`**：專案記憶與快取
- **`coordination/`**：多代理協調檔案

## 語言考量

- **混合語言支援**：中文與英文文件
- **UTF-8 編碼**：所有檔案使用 UTF-8 編碼
- **Windows 相容性**：Windows 路徑與編碼的特殊處理